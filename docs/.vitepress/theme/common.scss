@import '../../../src/assets/sass/variables';

@keyframes bounceIn {
  0% {
    transform: scale(0.1);
    opacity: 0;
  }
  60% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
  }
}

/* Apply the animation to an element */
.element-to-animate {
  animation: bounceIn 2s ease;
}

.goro-landing{
  .goro-landing-layout-fluid{
    .goro-container-fluid{
      max-width: 80%;
    }
  }
  .goro-block{
    overflow: hidden;
    &.goro-main-block{
      background-size: cover;
      background-image: url('../#{$assetsPath}/img/static-pages/lg_bg-landing-wa-2-4.jpg');
      padding: 3rem 0 10rem 0;
      background-position: center;
      background-repeat: no-repeat;
      position: relative;
      &::before{
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        content: "";
        background-color: var(--primary-color);
        opacity: .7;
      }
      .card{
        background-color: transparent;
        margin-top: 0.5rem;
        .card-body{
          h1{
            font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
            font-weight: 500 !important;
            margin: 30px 0 15px !important;
          }
        }
      }

      .bg-grid{
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        content: "";
        &::before{
          position: absolute;
          bottom: -36%;
          left: -43%;
          height: 100%;
          width: 100%;
          content: "";
          background-image: url('../#{$assetsPath}/img/bg-grid-block.png');
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
        }

        &::after{
          position: absolute;
          top: -39%;
          right: -46%;
          height: 100%;
          width: 100%;
          content: "";
          background-image: url('../#{$assetsPath}/img/bg-grid-block.png');
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
        }
      }

      .navbar{
        .navbar-container{
          max-width: 90%;
        }
      }
    }

    h2{
      margin-bottom: 15px;
    }

    &.goro-block-white{
      position: relative;
      padding: 7rem 0;
      // background-color: #e7f1f2;
      &::before{
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 90%;
        content: "";
        background-image: url('../#{$assetsPath}/img/bg-polygon-block.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }
      &::after{
        position: absolute;
        top: 0;
        right: -100px;
        height: 100%;
        width: 70%;
        content: "";
        background-size: cover;
        background-repeat: no-repeat;
        background-position: right center;
        z-index: -1;
      }
    }

    &.bg-cover{
      padding: 7rem 0;
      position: relative;

      .block-wrapper{
        justify-content: flex-end;
      }

      &::before{
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 65%;
        content: "";
        background-size: cover;
        background-repeat: no-repeat;
        background-position: left center;
        z-index: -1;
      }
      

      &::after{
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        width: 100%;
        background : linear-gradient(90deg, rgba(192, 234, 236, 0) 3.28%, rgba(192, 234, 236, 0) 23.47%, rgba(160, 213, 214, 0.08) 25.69%, rgba(112, 181, 181, 0.23) 29.52%, rgba(72, 154, 153, 0.37) 33.34%, rgba(40, 133, 132, 0.52) 37.06%, rgba(18, 118, 116, 0.65) 40.66%, rgba(5, 109, 107, 0.78) 44.08%, rgba(0, 106, 104, 0.9) 47.15%, rgba(0, 106, 104, 1) 63.96%);
        content: "";
        z-index: -1;
      }
    }
  }

  .goro-block-layout-2{
    &.goro-video-intro{
      height: 100vh;
      min-height: 650px;
      @media screen and (max-width: 992px) {
        max-height: 600px;
      }
      @media screen and (max-width: 767px) {
        min-height: 500px;
      }
      &::before{
        opacity: 0.4;
      }
      &.goro-main-block{
        background-image: none !important;
      }
      video{
        transform: scale(1.1);
        object-fit: cover;
        height: 100vh;
        min-height: 650px;
        width: 100%;
        z-index: -1;
        position: absolute;
        left: 0;
        top: 0;
        @media screen and (max-width: 992px) {
          max-height: 600px;
        }
        @media screen and (max-width: 767px) {
          min-height: 500px;
        }
      }
      nav{
        width: 100%;
        position: absolute;
      }
      .intro-content-panel{
        position: absolute;
        left: 0;
        top: 30%;
        button{
          padding: 15px 40px 12px !important;
        }
        @media screen and (max-width: 767px) {
          top: calc(70px + 10%);
        }
      }
      .learn-more-section{
        position: absolute;
        bottom: 99px;
        max-width: 100%;
        width: 100%;
        margin-top: 99px;
        img{
          cursor: pointer;
          &:hover{
            opacity: .7;
          }
        }
        @media screen and (max-width: 767px) {
          bottom: 49px;
          margin-top: 49px;
        }
      }
    }
    .goro-feature-carousel{
      display: flex;
      justify-content: space-between;
      align-items: self-start;
      // position: relative;
      @media screen and (max-width: 1024px) {
        position: relative;
        width: 100%;
        display: block !important;
      }
      .horizontal-navigation{
        position: relative;
        height: 329px;
        width: 200px;
        margin-top: 70px;
        background-position: left;
        background-repeat: no-repeat;
        background-image: url('../#{$assetsPath}/img/carousel/bg-left.png');
        // margin-right: -30px;
        @media screen and (max-width: 1024px) {
          position: absolute;
          height: 70px !important;
          width: 70px !important;
          background-image: none !important;
          margin-top: 0px !important;
          left: 24px;
          top: 215px;
          z-index: 1;
        }
        @media screen and (max-width: 768px) {
          top: 130px;
          height: 45px !important;
          width: 45px !important;
        }
        &.arrow-right{
          // margin-left: -30px;
          background-position: right;
          background-image: url('../#{$assetsPath}/img/carousel/bg-right.png');
          @media screen and (max-width: 1024px) {
            right: 24px !important;
            left: auto !important;
          }
        }
        .arrow-action{
          width: 70px;
          height: 70px;
          position: absolute;
          top: calc(50% - 35px);
          left: calc(50% - 35px);
          cursor: pointer;
          opacity: .7;
          transition: 0.3s;
          &:hover{
            opacity: 1;
          }
          @media screen and (max-width: 1024px) {
            width: 70px !important;
            height: 70px !important;
            border: 1px solid #dedede;
            border-radius: 100%;
            position: unset !important;
          }
          @media screen and (max-width: 768px) {
            height: 45px !important;
            width: 45px !important;
          }
        }
      }
      .carousel-panel{
        flex: 1;
        width: 100%;
        max-width: 100%;
        // overflow: hidden;
        @media screen and (max-width: 1024px) {
          // position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: auto;
          display: block;
        }
        .carousel__viewport{
          .carousel__track{
            align-items: flex-start;
            justify-content: flex-start;
          }
        }
        section.section-thumbnail{
          .carousel__viewport{
            border-radius: 30px;
            box-shadow: 0px 4px 26.7px rgba(0, 0, 0, 0.40);
          }
        }
        .item-gallery{
          width: 100%;
          .thumb{
            // padding: 30px;
            height: 465px;
            overflow: hidden;
            img{
              object-fit: cover;
              height: 100%;
              max-height: 100%;
              width: 100%;
              // border-radius: 30px;
              // box-shadow: 0px 4px 26.7px rgba(0, 0, 0, 0.40);
            }
            @media screen and (max-width: 1024px) {
              height: 465px;
            }
            @media screen and (max-width: 768px) {
              height: 320px;
            }
            @media screen and (max-width: 767px) {
              height: 259px;
            }
          }
          .feature-name{
            color: var(--layout2-primary-color);
            text-align: center;
            font-style: normal;
            font-weight: 600;
            line-height: 100%; /* 48px */
            margin-top: 46px;
            margin-bottom: 24px;
            @media screen and (max-width: 1024px) {
              margin-top: 36px;
            }
          }
          .feature-description{
            color: var(--layout2-primary-color-text);
            text-align: center;
            font-style: normal;
            font-weight: 500;
            line-height: 149%; /* 47.68px */
          }
        }
      }
    }
    .goro-heading{
      h2.main-heading{
        color: rgba(0, 104, 103, 0.63);
        text-align: center;
        // font-size: 70px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
      &.has-hr{
        display: flex;
        justify-content: flex-start;
        align-items: baseline;
        h2.main-heading{
          margin-right: 10px;
        }
        p.hr{
          border-bottom: 2px solid var(--layout2-primary-color-text);
          opacity: 0.5;
          flex: 1;
        }
      }
    }
    .goro-heading-description{
      color: var(--layout2-primary-color-text);
      font-style: normal;
      font-weight: 382;
      line-height: normal;
      @media screen and (max-width: 767px) {
        text-align: center;
      }
    }
    &.goro-block-why-bali{
      padding: 127px 0;
      @media screen and (max-width: 767px) {
        padding: 70px 0;
      }
      .goro-why-bali-lists{
        .goro-why-bali-item{
          margin-top: 15px;
          padding: 0 18px;
          .item-heading{
            color: var(--layout2-primary-color-light);
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            margin-bottom: 13px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            img{
              width: 45px;
              height: 45px;
              margin-right: 10px;
            }
            label{
              color: var(--layout2-primary-color-light);
              font-style: normal;
              font-weight: 700;
              line-height: normal;
              margin-bottom: 13px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-block-start: 0;
              margin-block-end: 0;
              margin-bottom: -15px;
            }
          }
          .sub-heading{
            color: var(--layout2-primary-color-light);
            font-style: normal;
            font-weight: 500;
            line-height: 36px;
            margin-bottom: 16px;
            text-transform: uppercase;
            @media screen and (max-width: 767px) {
              margin-bottom: 16px;
            }
          }
          .description{
            color: var(--layout2-primary-color-text);
            font-style: normal;
            font-weight: 500;
            text-align: left;
            line-height: 149%; /* 38.74px */
          }
        }
      }
    }

    &.goro-block-our-investors{
      padding-top: 79px;
      padding-bottom: 99px;
      @media screen and (max-width: 767px) {
        padding-top: 49px;
        padding-bottom: 49px;
      }
      .img-graph{
        max-width: max-content !important;
        width: 110%;
        margin-left: -20px;
        @media screen and (max-width: 767px) {
          margin-left: -14px !important;
        }
      }
      .heading{
        color: var(--layout2-primary-color);
        font-size: 38px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
      .description{
        color: var(--layout2-primary-color);
        font-size: 18px;
        font-style: normal;
        line-height: normal;
      }
      .information-caption{
        color: var(--layout2-primary-color);
        font-size: 18px;
        font-style: normal;
        line-height: normal;
        position: absolute;
        left: 0;
        bottom: 35px;
        width: 100%;
        @media screen and (max-width: 1024px) {
          bottom: 0px !important;
        }
      }
    }

    &.goro-block-historical-performance{
      padding-top: 79px;
      padding-bottom: 79px;
      img.goro-chart{
        max-width: max-content !important;
        width: 110%;
        @media screen and (max-width: 767px) {
          width: 100%;
        }
      }
      @media screen and (max-width: 767px) {
        padding-top: 49px;
        padding-bottom: 49px;
      }
    }

    &.goro-block-feature-high-light{
      padding-top: 98px;
      padding-bottom: 89px;
      @media screen and (max-width: 767px) {
        padding-top: 49px;
        padding-bottom: 49px;
      }
      .goro-content-feature{
        .thumb{
          border-radius: 25px;
          width: 100%;
          max-height: 378px;
          object-fit: cover;
          @media screen and (max-width: 767px) {
            min-height: 200px;
            object-fit: cover;
          }
        }
        .goro-list-features{
          margin: 0;
          padding: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 35px;
          .item{
            width: 315px;
            max-width: 315px;
            height: 78px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 25px;
            box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.10);
            margin: 13px;
            border-radius: 20px;
            @media screen and (max-width: 767px) {
              padding: 25px;
            }
            img{
              margin-right: 20px;
              width: 32px;
              @media screen and (max-width: 767px) {
                width: 32px;
              }
            }
            span{
              color: var(--layout2-primary-color);
              font-style: normal;
              font-weight: 382;
              line-height: normal;
              margin-bottom: -7px;
              @media screen and (max-width: 767px) {
                font-size: 20px !important;
              }
            }
          }
        }
      }
    }

    &.goro-block-property-details{
      padding-top: 121px;
      padding-bottom: 55px;
      @media screen and (max-width: 767px) {
        padding-top: 49px;
        padding-bottom: 49px;
      }
    }

    &.goro-testimonials-block{
      padding-top: 88px;
      padding-bottom: 121px;
    }

    &.goro-block-invest-layout-2{
      padding-top: 96px;
      padding-bottom: 96px;
    }
  }

  .goro-best-price-block{
    &::after{
      background-image: url('../#{$assetsPath}/img/static-pages/bg-landing-wa-2-2.jpg');
    }
  }

  .goro-yield-block{
    &::before{
      background-image: url('../#{$assetsPath}/img/static-pages/bg-landing-wa-2-3.jpeg');
    }
  }

  .goro-safe-block{
    &::after{
      background-image: url('../#{$assetsPath}/img/static-pages/bg-landing-wa-2-1.jpg');
    }
  }

  .goro-widthdraw-block{
    &::before{
      background-image: url('../#{$assetsPath}/img/static-pages/bg-landing-wa-2-5.jpg');
    }
  }
  .goro-block-invest{
    padding: 4rem 0;
    .join-goro{
      margin: 0;
      padding: 3.5rem;
    }

    &.goro-block-invest-layout-2{
      .goro-block-content{
        &.bg-light{
          border-radius: 48px !important;
          box-shadow: none !important;
          background-color: var(--layout2-color-light) !important;
        }
      }
    }
  }

  .goro-property-catalog-block{
    padding: 3.5rem 0 2.5rem 0;
  }

  .featured-on-block{
    padding: 5rem 0;
    .tab-header{
      background-color: var(--primary-background-color);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0rem 1rem;
      .item-tab{
        flex: 1;
        position: relative;
        height: 100%;
        padding: 1.5rem 0rem 1rem 0;
        &.active{
          &::after{
            content: "";
            position: absolute;
            bottom: -41px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 27px;
            border-style: solid;
            border-color: transparent transparent #2d8f8f transparent;
          }
        }
        button{
          height: 100%;
          img{
            opacity: .8;
          }
          &.active{
            img{
              opacity: 1 !important;
            }
          }
        }
      }
    }
    .tab-content{
      margin-top: 40px;
      .item-panel{
        background-color: rgb(64 171 171 / 60%);
        border-radius: 20px;
        padding: 3rem;
        p.content{
          width: 70%;
          margin: 0 auto;
          text-align: center;
        }
      }
    }
  }

  .goro-main-en-block{
    .goro-container-fluid{
      max-width: 80%;
      .goro-content{
        padding: 0 15px 7rem 15px;
      }
      .goro-navbar{
        margin-top: 3rem;
      }

      .bg-image{
        background-image: url('../#{$assetsPath}/img/static-pages/md_goro-landing-property-investment.png');
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
      }
    }
  }

  .goro-testimonials-block {
    padding: 5rem 0;
    .w-card-2-a,
    .w-card-4-a {
      padding: 15px 25px 28px;
      margin-top: 10px;
      height: 90%;
      background-color: var(--primary-background-darker-color);
      border-radius: 32px;
  
      h5 {
        margin-top: 5px;
        margin-bottom: 5px;
        font-size: 22px;
        color: var(--primary-color);
      }
  
      p {
        font-size: 16px;
      }

      .review-content{
        position: relative;
        &::before{
          position: absolute;
          content: "";
          top: -13px;
          left: -4px;
          width: 50px;
          height: 50px;
          background-image: url('../#{$assetsPath}/img/quote-prev.png');
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
          z-index: 0;
          opacity: .5;
        }
        p{
          z-index: 1;
        }
      }
    }
    .w-card-2-b,
    .w-card-4-b {
      padding: 10px 25px 15px;
      margin-left: 15px;
      margin-right: 15px;
      background-color: var(--primary-background-darker-color);
      border-radius: 32px;

      h5 {
          margin-top: 5px;
          margin-bottom: 5px;
          font-size: 22px;
          color: var(--primary-color);
      }

      p {
          font-size: 16px;
      }
      .review-content{
        position: relative;
        &::before{
          position: absolute;
          content: "";
          top: -18px;
          left: -10px;
          width: 50px;
          height: 50px;
          background-image: url('../#{$assetsPath}/img/quote-prev.png');
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
          z-index: 0;
          opacity: .5;
        }
        p{
          position: inherit;
          z-index: 1;
        }
      }
    }
  }
}

.cls-custom-carousel-container{
  position: relative;
  .carousel__viewport {
    padding-top: 20px;
    @media screen and (max-width: 992px) {
      padding-top: 0px;
    }
  }
  .cls-active-item-desktop{
    .carousel__viewport {
      @media screen and (min-width: 1200px) {
        .carousel__slide {
          transform: rotateY(0deg) scale(0.9);
        }
        .carousel__slide--active {
          transform: rotateY(0deg) scale(1.1) !important;
        }
      }
    }
  }
  .horizontal-navigation{
    position: absolute;
    bottom: calc(50% - 30px);
    &.arrow-left{
      left: -70px;
    }
    &.arrow-right {
      right: -70px;
    }
    .arrow-action{
      width: 56px;
      height: 56px;
    }
  }
}

.apexcharts-tooltip {
  border-radius: 10px !important;
}

.cls-site-banner-top-container{
  background-color: #F38300;
  padding: 15px;
  .cls-site-banner-content{
    .cls-site-banner-message,
    .cls-site-banner-message .cls-text-link{
      font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
      font-size: 18px;
      font-weight: 700;
      line-height: 25.2px;
      text-align: center;
      color: #FFFFFF;
      @media screen and (max-width: 992px) {
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
      }
    }
    .cls-site-banner-message{
      .cls-text-link{
        color: #FFFFFF;
        text-decoration: underline;
        &:hover,
        &:focus,
        &:active{
          color: #FFFFFF;
          opacity: .8;
        }
      }
    }
  }
}