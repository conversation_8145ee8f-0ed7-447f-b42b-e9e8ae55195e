@import 'bootstrap/scss/bootstrap';

$primary: #3eaf7c !default;
$link-color: $primary !default;
$link-hover-color: lighten($link-color, 15%) !default;

/* Content width fix */
.theme-default-content:not(.custom) {
  box-sizing: content-box;
}

/* Table width fix */
table.table {
  display: table;
}

// Add more CSS here

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6, p, .navbar, .brand, a, .td-name, td {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: "Figtree", Helvetica, sans-serif, serif;
}

h1, .h1, h2, .h2, h3, .h3, h4, .h4 {
  font-weight: bold;
}

h5, .h5 {
  font-size: 1.25em;
  font-weight: bold;
  line-height: 1.4em;
  margin-bottom: 15px;
}

h6, .h6 {
  font-size: 9em;
  font-weight: 600;
  text-transform: uppercase;
}

.tooltip .tooltip-inner {
  background-color: gray;
}