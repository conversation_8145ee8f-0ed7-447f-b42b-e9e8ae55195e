@import '../../../src/assets/sass/variables';

@media screen and (max-width: 1024px) {
  .goro-main-en-block{
    .goro-container-fluid{
      max-width: 97% !important;
      padding: 0 15px !important;
      .goro-content{
        padding: 15px 0 3rem 15px !important;
      }
      .goro-navbar{
        margin-top: 0 !important;
      }
      h1{
        margin-top: 5px !important;
      }
    }
  }
  .goro-landing-layout-fluid{
    .goro-container-fluid{
      max-width: 97% !important;
    }
  }
}
@media screen and (max-width: 960px) {
  .goro-main-en-block{
    .goro-container-fluid{
      max-width: 97% !important;
      padding: 0 15px !important;
      .goro-content{
        padding: 0 15px 0.5rem 15px !important;
      }
    }
  }
  .goro-landing-layout-fluid{
    .goro-container-fluid{
      max-width: 97% !important;
    }
  }
}
@media screen and (max-width: 767px) {
  .goro-landing{
    .goro-block{
      h2{
        margin-bottom: 15px;
      }
      &.goro-main-block{
        padding: 1rem 0 7rem 0 !important;
        background-image: url('../#{$assetsPath}/img/static-pages/md_bg-landing-wa-2-4.jpg') !important;

        .navbar{
          .navbar-container{
            max-width: 100% !important;
          }
        }

        .card{
          margin-top: 0 !important;
        }
      }

      &.goro-block-white{
        padding: 4rem 0 !important;
        background-image: url('../#{$assetsPath}/img/landing_banner_max_w_650.png');
        background-repeat: no-repeat;
        background-position: bottom center;
        background-size: cover;
        &::before{
          background-image: none !important;
        }
        &::after{
          background-image: none !important;
        }
      }
      &.bg-cover{
        padding: 4rem 0 !important;
        background-color: var(--primary-color);
        &::before{
          background-image: url('../#{$assetsPath}/img/bg-grid-block.png') !important;
          background-repeat: no-repeat;
          background-position: bottom center;
          background-size: cover;
          width: 100%;
          height: 100%;
          content: "";
          position: absolute;
          right: -46%;
          top: -30%;
          left: auto;
          z-index: 0 !important;
        }
        &::after{
          background-image: none !important;
        }
      }
    }
    .goro-block-invest{
      padding: 15px !important;
      .join-goro{
        margin: 0 !important;
        padding: 2rem !important;
      }
    }

    .goro-property-catalog-block{
      padding: 2.5rem 0 1rem 0;
    }

    .goro-main-en-block{
      padding: 1rem 0 2rem 0 !important;
      background-image: url('../#{$assetsPath}/img/landing_banner_max_w_650.png');
      background-repeat: no-repeat;
      background-position: bottom center;
      background-size: cover;
      .goro-container-fluid{
        max-width: 100% !important;
        // .goro-content{
        //   img{
        //     width: 110px !important;
        //   }
        // }
        .goro-navbar{
          margin-top: 0 !important;
        }
        .bg-image{
          display: none !important;
        }
      }
    }

    .goro-landing-layout-fluid{
      .goro-container-fluid{
        max-width: 100% !important;
      }
    }

    .featured-on-block{
      padding: 2.5rem 15px !important;
      .tab-lists{
        padding-top: 7px !important;
      }
      .tab-header{
        border-radius: 10px !important;
        padding: 0 !important;
        .item-tab{
          &.active{
            &::after{
              bottom: -26px;
              border-width: 19px !important;
            }
          }
        }
      }
      .tab-content{
        margin-top: 25px !important;
        .item-panel{
          border-radius: 10px;
          padding: 20px !important;
          p.content{
            width: 100% !important;
          }
        }
      }
    }

    .goro-testimonials-block {
      padding: 2rem 0 !important;
    }
  }
}