// https://vitepress.dev/guide/custom-theme
import { h } from 'vue'
import Theme from 'vitepress/theme-without-fonts'
import { createGtm } from '@gtm-support/vue-gtm'

// import './extended_fonts.scss'
import './style.css'
import './styles.scss'
import '../../../src/assets/css/style.css'
import '../../../src/assets/css/mobile.style.css'
import '../../../src/assets/sass/style.scss'
import './common.scss'
import './response.scss'

export default {
  extends: Theme,
  Layout: () => {
    return h(Theme.Layout, null, {
      // https://vitepress.dev/guide/extending-default-theme#layout-slots
    })
  },
  enhanceApp({ app, router, siteData }) {
    app.use(createGtm({
      id: [
        {
          id: import.meta.env.VITE_VUE_APP_GOOGLE_TAG_MANAGER_ID || '',
          queryParams: {
            // Add URL query string when loading gtm.js with GTM ID (required when using custom environments)
            gtm_auth: import.meta.env.VITE_VUE_APP_GOOGLE_TAG_AUTH,
            gtm_preview: import.meta.env.VITE_VUE_APP_GOOGLE_TAG_PREVIEW,
            gtm_cookies_win: 'x',
          },
        },
        {
          id: import.meta.env.VITE_VUE_APP_GOOGLE_TAG_MANAGER_AFRA_ID || '',
          queryParams: {
            // Add URL query string when loading gtm.js with GTM ID (required when using custom environments)
            gtm_auth: import.meta.env.VITE_VUE_APP_GOOGLE_TAG_AUTH_AFRA,
            gtm_preview: import.meta.env.VITE_VUE_APP_GOOGLE_TAG_PREVIEW_AFRA,
            gtm_cookies_win: 'x',
          },
        }
      ],
      loadScript: true, // Whether or not to load the GTM Script (Helpful if you are including GTM manually, but need the dataLayer functionality in your components) (optional)
      // vueRouter: router, // Pass the router instance to automatically sync with router (optional)
      enabled: import.meta.env.VITE_VUE_APP_ENV === 'production', // defaults to true. Plugin can be disabled by setting this to false for Ex: enabled: !!GDPR_Cookie (optional)
    }));
  }
}
