import { defineConfig } from "vitepress";

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: "GORO",
  titleTemplate: "Property for All",
  description:
    "Easy property investing from IDR10,000 and earn up to 10% return per year paid monthly at GORO.",
  themeConfig: {},
  base: "/pages",
  vite: {
    resolve: {
      alias: {
        "@": "../../src",
      },
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import './theme/_variables.scss';`,
      },
    },
  },
  head: [
    ["link", { rel: "icon", href: "/pages/logo.png" }],
    [
      "link",
      { rel: "preconnect", href: "https://fonts.gstatic.com", crossorigin: "" },
    ],
    [
      "link",
      {
        href: "https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,400;0,700;1,400;1,700&display=swap",
        rel: "stylesheet",
      },
    ],
    [
      "link",
      {
        href: "https://fonts.googleapis.com/css2?family=Boogaloo&display=swap",
        rel: "stylesheet",
      },
    ],
    [
      "script",
      {},
      ` !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '301002555738479');`,
    ],
    [
      "script",
      {
        async: '',
        src: "https://www.googletagmanager.com/gtag/js?id=AW-11349568843",
      },
    ],
    [
      "script",
      {},
      ` window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
  
      gtag('config', 'AW-11349568843');`,
    ],
  ],
});
