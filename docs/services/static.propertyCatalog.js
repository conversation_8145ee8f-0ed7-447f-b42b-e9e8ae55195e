const staticPropertyService = {
  getData: () => {
    return new Promise((resolve, reject) => {
      const env = import.meta.env.VITE_VUE_APP_ENV || 'staging'
      import(`../jsons/property-catalog.${env}.json`)
      .then((res) => {
        const result = res && res.data ? res.data : []
        resolve(result)
      })
      .catch((error) => {
        console.error(`Error loading ${env} JSON file:`, error)
        resolve([])
      })
    })
  }
}

export default staticPropertyService
