import axios from "axios";

const gassScriptLoader = {
  run(campaign_id = null, googleTag = null) {
    if (campaign_id) {
      this.loadExternalScript(campaign_id, googleTag);
    }
  },
  loadExternalScript (campaign_id = 1, googleTag = null) {
    let self = this
    const script = document.createElement("script")
    script.src = "https://gass.co.id/gassv3.min.js?v=3"
    document.head.appendChild(script)

    let gassParams = {
      campaign_id: campaign_id,
      subdomain: 'wa.goro.id',
      interval: 2
    }

    if (googleTag) {
      gassParams = {
        ...gassParams,
        adw_tag: googleTag
      }
    }
    
    setTimeout(function () {
      const scriptContent = `gass.run(${JSON.stringify(gassParams)})`;
      const scriptCampaign = document.createElement("script")
      scriptCampaign.type = "text/javascript"
      scriptCampaign.text = scriptContent
      scriptCampaign.async = true
      scriptCampaign.id = "gass_campaign_id_" + campaign_id
      document.head.appendChild(scriptCampaign)
      
      self.afterGassLoaded()
    }, 1000)
  },
  afterGassLoaded() {
    const base_url = import.meta.env.VITE_VUE_APP_API_ENDPOINT
    setTimeout(function () {
        if (window.gass && window.gass.vid) {
          axios.post(base_url + '/gass/store-info', {
            gass_vid: window.gass.vid
          })
            .then(response => {
              console.log("GassScriptLoaded: loadExternalScript", { gass_vid: window.gass.vid })
            })
            .catch(error => {
                // Handle error
            });
        }
      }, 2000)
  },
}

export default gassScriptLoader
