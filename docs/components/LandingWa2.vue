<template>
    <div class="goro-landing goro-landing-page goro-landing-wa-2-page" ref="toTopSection">
        <!-- Main block -->
        <div class="goro-block goro-main-block">
            <div class="bg-grid"></div>
            <nav class="navbar bg-body-tertiary">
                <div class="container-fluid navbar-container">
                    <a class="navbar-brand" :href="getMainPageUrl()" title="Goro - Home page" target="_blank">
                        <img src="@/assets/img/logo-goro-white.png" alt="Goro - Home page" width="110" height="36">
                    </a>
                </div>
            </nav>
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12 p-0">
                        <div class="card border-0">
                            <div class="card-body text-center">
                                <h1 class="card-title font-46 color-white">
                                    Dari Rp10.000, Miliki Vila Dengan <br/><PERSON>unt<PERSON><PERSON> 10% Per Tahun!
                                </h1>
                                <p class="card-text font-22 color-white">
                                    <PERSON>i properti eceran jadi le<PERSON>h mudah, aman, dan terjan<PERSON><PERSON>.
                                </p>
                                <a
                                    class="btn btn-main white mt-3 font-18"
                                    href="https://wa.goro.id/cta" rel="noopener" target="_blank"
                                    style="padding: 15px 50px;">
                                    Gabung Sekarang
                                </a>
                                <!-- <button class="btn btn-main white mt-3 font-18" @click="scrollToWhatsAppSection()"
                                    style="border-radius: 18px; padding: 12px 40px">
                                    Gabung Sekarang
                                </button> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Main block -->

        <!--Best price block-->
        <div class="goro-block goro-best-price-block goro-block-white">
            <div class="container">
                <div class="row block-wrapper">
                    <div class="col-lg-6 col-md-6 col-sm-12 col-12 bg-polygon">
                        <h2 class="font-44 color-main">
                            Properti Terbaik dengan <br>Harga Terjangkau
                        </h2>
                        <p class="font-22 color-main">
                            Nikmati keuntungan dari properti <br>pilihan dengan modal awal mulai <br>dari Rp10.000
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <!--End Best price block-->

        <!--Yield block-->
        <div class="goro-block goro-yield-block bg-cover">
            <div class="container">
                <div class="row block-wrapper">
                    <div class="col-lg-6 col-md-6 col-sm-12 col-12">
                        <h2 class="font-44 color-white">
                            Imbal Hasil hingga <br>10% per Tahun
                        </h2>
                        <p class="font-22 color-white">
                            Dapatkan imbal hasil sewa properti <br>yang maksimal hingga 10% per tahun
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <!--End Yield block-->

        <!--Safe & Transparent price block-->
        <div class="goro-block goro-safe-block goro-block-white">
            <div class="container">
                <div class="row block-wrapper">
                    <div class="col-lg-6 col-md-6 col-sm-12 col-12 bg-polygon">
                        <h2 class="font-44 color-main">
                            Aman dan Transparan
                        </h2>
                        <p class="font-22 color-main">
                            Periksa langsung data aset dan lakukan <br>transaksi dengan aman di platform GORO
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <!--End Safe & Transparent block-->

        <!--Withdraw block-->
        <div class="goro-block goro-widthdraw-block bg-cover">
            <div class="container">
                <div class="row block-wrapper">
                    <div class="col-lg-6 col-md-6 col-sm-12 col-12">
                        <h2 class="font-44 color-white">
                            Cairkan Dana Kapan Saja
                        </h2>
                        <p class="font-22 color-white">
                            Cairkan dana dengan cepat & tanpa <br>biaya tersembunyi di GORO
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <!--End Withdraw block-->

        <!--Property Catalog block-->
        <div class="goro-block goro-property-catalog-block" style="background-color: var(--primary-background-darker-color)">
            <div class="container">
                <div class="row justify-content-center" align-h="center">
                    <div class="col-lg-8 col-xl-6 col-10 text-center" cols="10" lg="8" xl="6">
                        <h2 class="font-44 color-main">
                            Properti Unggulan
                        </h2>
                        <label class="mb-3 font-22 color-main">
                            Kami hanya memilih properti dengan imbal hasil tertinggi
                        </label>
                    </div>
                </div>
                <div class="row justify-content-md-center pt-4 pb-1">
                    <div class="col-md-4 col-lg-4 col-xl-4 col-12 pb-4" cols="12" xl="4" lg="4" md="4"
                        v-for="property in properties" :key="property.id">
                        <property-light-card :property="property" :show-irr-ery=true @on-clicked="scrollToTopSection()" />
                    </div>
                </div>
            </div>
        </div>
        <!--End Property Catalog block-->

        <!--Invest block-->
        <div class="text-center goro-block goro-block-invest" style="background-color: var(--primary-color);">
            <div class="container">
                <div class="row justify-content-around align-items-center" align-h="around" ref="whatsAppSection">
                    <div class="col-12 join-goro" cols="12">
                        <h2 class="font-38 text-center mt-0 mb-1 color-main">
                            Siap untuk wujudkan mimpi memiliki properti?
                        </h2>
                        <p class="font-20 mb-3 color-main">
                            Gabung sekarang dan dapatkan keuntungan maksimal dari properti hanya di GORO.
                        </p>
                        <button class="btn btn-none align-items-center justify-content-center btn-main mb-1" @click="scrollToTopSection()" 
                        style="border-radius: 18px; padding: 12px 40px">
                            Gabung Sekarang
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!--End Invest block-->
    </div>
</template>

<script>
import PropertyLightCard from "./PropertyLightCard.vue";
import { defineRule, Field, Form } from "vee-validate"
import { email, min, required } from "@vee-validate/rules"
import { gtmTrackEvent } from "../../src/helpers/gtm";
import { GTM_EVENT_NAMES } from "../../src/constants/gtm";
import staticPropertyService from '../services/static.propertyCatalog'
import gassScriptLoader from '../services/gass_script_loader';
import {GOOGLE_TAGS} from '../constants/constants';

defineRule("email", email)
defineRule("min", min)
defineRule("required", required)

export default {
    components: {
        PropertyLightCard,
        Form,
        Field,
    },
    data() {
        return {
            form: {
                "email": '',
                "name": '',
                "platform": "web",
                "uuid": '',
            },
            isVerifyOtp: false,
            innerWidth: 0,
            properties: [],
        };
    },
    methods: {
        getMainPageUrl() {
            return import.meta.env.VITE_VUE_APP_URL
        },
        getValidationState({ dirty, validated, valid = null }) {
            return dirty || validated ? valid : null
        },
        handleWindowResize() {
            this.innerWidth = window.innerWidth
        },
        scrollToTopSection() {
            window.fbq("trackCustom", "Landing:JoinNowClicked")
            this.$refs.toTopSection.scrollIntoView({ behavior: "smooth" })
            gtmTrackEvent({
                event: GTM_EVENT_NAMES.USER_CLICKED_JOINNOW,
            })
        },
    },
    mounted() {
        this.handleWindowResize()
        window.addEventListener("resize", this.handleWindowResize)

        const campaign_id = 5
        gassScriptLoader.run(campaign_id)
    },
    async beforeDestroy() {
        window.removeEventListener("resize", this.handleWindowResize)
    },
    created() {
        staticPropertyService.getData()
            .then((data) => {
                this.properties = data
            })
            .catch((error) => {
                console.error('Error loading JSON file:', error)
            })
    }
};
</script>

<style lang="scss">
@import '../../src/assets/sass/variables';
h1 {
    font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
    font-weight: 500;
    color: var(--primary-color);
    margin: 30px 0 15px;
}

p {
    color: var(--primary-color);
}

label {
    color: var(--primary-darker-color);
}

.show-hide {
    width: 40px;
    background-color: rgba(0, 102, 102, 0.9);
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    transition: 0.5s;

    &:hover {
        background-color: rgba(0, 102, 102, 1);
    }
}

.join-goro {
    padding: 2.1rem;
    margin-top: 3.1rem;
    margin-bottom: 1.6rem;
    background-color: white;
    -webkit-box-shadow: 0 5px 20px rgba(7, 55, 99, 0.16);
    box-shadow: 0 5px 20px rgba(7, 55, 99, 0.16);
    border-radius: 32px;
}
</style>
