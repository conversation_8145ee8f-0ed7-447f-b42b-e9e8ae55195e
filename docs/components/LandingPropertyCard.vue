<template>
    <div class="property-card shadow" @click="onClicked">
        <img :src="getAvatar(property.images)" alt="Property Image" class="property-image">
        <div class="card-info">
            <p class="font-18 font-weight-bold m-0" :class="nameClass">{{ property.name }}</p>
            <span class="font-16" :class="descriptionClass">{{ property.metadata.address }}</span>
            <p class="card-separator font-14 pt-2 pb-1" :class="descriptionClass">
                <span class="space">{{ property.num_bed }} {{ numBed }}</span>
                <span class="space">{{ property.num_bath }} {{ numBath }}</span>
                <span>{{ property.sqm }} sqm</span>
            </p>
            <div v-if="showIrrEry" class="card-separator d-flex flex-row">
                <div tabindex="0" class="d-flex flex-row" :id="'tooltip-target-irr-card' + property.id"
                    data-bs-toggle="tooltip" data-bs-placement="top"
                    title="Internal Rate of Return is the total expected return an investor may receive on an annual basis from ERY and ECA">
                    <div>
                        <span class="font-20 font-weight-bold" :class="nameClass">{{ (property.ery || 0) + (property.eca ||
                            0) }}%
                            <span class="font-weight-bold m-0">IRR</span></span>
                    </div>

                    <div><img class="img-info" src="@/assets/img/info-circle-fill.png" alt=""></div>
                </div>

                <div class="d-flex flex-row ml-5" :id="'tooltip-target-ery-card' + property.id" data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="Expected Rental Yield is an estimate return based on the rental income of a property on an annual basis against the property purchase price">
                    <div>
                        <span class="font-20 font-weight-bold" :class="nameClass">{{ property.ery || 0 }}% <span
                                class="font-weight-bold m-0">ERY</span></span>
                    </div>
                    <div><img class="img-info" src="@/assets/img/info-circle-fill.png" alt=""></div>
                </div>
            </div>
            <div class="p-progress pt-2">
                <div class="progress bg-color-progress mb-1" min="0" style="height: 4px;">
                    <div class="progress-bar bg-color-white" role="progressbar" aria-valuemin="0" aria-valuemax="100"
                        :aria-valuenow="getProgress" :style="`width:  ${getProgress}%; `"></div>
                </div>
                <span class="pb-2 font-weight-bold" :class="nameClass">{{ getProgress }}%</span>
                <span class="pb-2 font-weight-bold float-right" :class="nameClass">
                    {{ property.total_tokens - property.display_sold_tokens }} {{ tokenLeft }}
                </span>
            </div>
        </div>
        <div class="invest-now d-flex justify-content-center align-items-center">
            <p class="font-weight-bold">{{ requestInvite }}</p>
        </div>
    </div>
</template>

<script>

export default {
    props: {
        property: {
            type: Object,
            required: true
        },
        isEnglish: {
            type: Boolean,
            default: false
        },
        showIrrEry: {
            type: Boolean,
            default: false
        },
    },
    emits: ["on-clicked"],
    methods: {
        onClicked() {
            this.$emit("on-clicked")
        },
        viewPropertyDetails() {
            // Emit an event to the parent component to show the property details
            this.$emit('view-property-details', this.property.id);
        },
        getAvatar(images) {
            if (images && images.length) {
                return this.urlImage(images[0])
            }
            return ""
        },
        urlImage(image, thumbnail = false) {
            const base_url = import.meta.env.VITE_VUE_APP_IMG_HOST

            if (thumbnail) {
                if (image && image.thumbnail) {
                    return base_url + "/" + image.thumbnail;
                }
            } else {
                if (image && image.image) {
                    return base_url + "/" + image.image;
                }
            }
            return null;
        }
    },
    computed: {
        getProgress() {
            if (!this.property.total_tokens) {
                return 0
            }
            return Math.floor(this.property.display_sold_tokens * 100 / this.property.total_tokens)
        },
        nameClass() {
            return "color-white"
        },
        descriptionClass() {
            return "color-description"
        },
        progressClass() {
            return "bg-color-white"
        },
        numBed() {
            if (this.isEnglish) {
                return "Bedrooms"
            }
            return "Kamar tidur"
        },
        numBath() {
            if (this.isEnglish) {
                return "Bathrooms"
            }
            return "Kamar mandi"
        },
        tokenLeft() {
            if (this.isEnglish) {
                return "token left"
            }
            return "token tersisa"
        },
        requestInvite() {
            if (this.isEnglish) {
                return "Request Invitation"
            }
            return "Beli Sekarang"
        }
    },
    mounted() {
    }
}
</script>


<style lang="scss" scoped>
.property-card {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    height: 435px;
    word-wrap: break-word;
    background-clip: border-box;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: .8s;
    background: var(--primary-color);

    &:hover {
        background: var(--primary-darker-color);
    }

    &:hover .card-info {
        background: var(--primary-darker-color);
    }

    img {
        width: 98%;
        height: 56%;
        margin-left: 1%;
        margin-right: 1%;
        margin-top: 1%;
        object-fit: cover;
        position: absolute;
        border: 0 transparent;
        border-radius: 14px 14px 0 0;
        background-color: var(--primary-background-color);
    }

    p {
        padding: 0;
        margin: 0;
    }

    .darken {
        filter: brightness(70%);
    }

    .card-separator {
        .space {
            margin-right: 10px;
        }

        .img-info {
            width: 14px;
            height: 14px;
            background-color: transparent;
            z-index: 1;
        }
    }

    .card-info {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        transition: .8s;
        background: var(--primary-color);
        padding: 0.5rem 1.25rem 2.7rem 1.25rem;

        .color-white {
            color: white;
        }

        .color-description {
            color: white;
        }

        .bg-color-white {
            background-color: #ffffff;
        }

        .bg-color-progress {
            background-color: var(--primary-lighter-color);
        }
    }

    .invest-now {
        position: absolute;
        bottom: 3px;
        left: 3px;
        right: 3px;
        height: 35px;
        border-radius: 0 0 12px 12px;
        color: var(--primary-darker-color);
        background-color: white;
        text-align: center;
        vertical-align: middle;
        transition: .8s;

        &.disabled {
            background-color: #769B9D;
        }
    }

    h4 {
        margin-top: 0;
        margin-bottom: 0.75rem;
    }
}
</style>