<template>
  <div class="chart-container">
    <div class="investment-info">
      <p class="font-17">
        <PERSON><PERSON> dalam waktu <strong>{{ hoveredYear }}</strong>
      </p>
      <div class="investment-values">
        <p class="font-14">Investasi Awal <strong class="font-17">Rp100.000</strong></p>
        <p class="font-14">Investasi Bulanan <strong class="font-17">Rp100.000</strong></p>
      </div>
    </div>

    <canvas ref="chartCanvas" width="400" height="300"></canvas>

    <div class="custom-legend">
      <div v-for="(label, index) in customLegend" :key="index" class="legend-item">
        <span :style="{ backgroundColor: label.color }"></span>
        <p class="legend-name">{{ label.name }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted, ref } from "vue";
import { Chart } from "chart.js/auto";

export default {
  name: "MyChart",
  setup() {
    const chartCanvas = ref(null);
    const customLegend = [
      { name: "GORO", color: "#176B5C" },
      { name: "Deposito", color: "#7F3FBF" },
      { name: "Tabungan", color: "#E22828" },
    ];

    const hoveredYear = ref("3 Tahun"); // Default text

    onMounted(() => {
      const myChart = new Chart(chartCanvas.value, {
        type: "line",
        data: {
          labels: ["1 Tahun", "3 Tahun", "5 Tahun"],
          datasets: [
            {
              label: "GORO",
              data: [1.4, 4.4, 8.1],
              borderColor: "#176B5C",
              backgroundColor: "#176B5C",
              borderWidth: 2,
              pointRadius: (ctx) => (5), // Hide point at "1 Tahun"
              pointBackgroundColor: "#176B5C",
              cubicInterpolationMode: 'monotone',
              pointHoverRadius: 7,
            },
            {
              label: "Deposito",
              data: [1.3, 3.9, 6.7],
              borderColor: "#7F3FBF",
              backgroundColor: "#7F3FBF",
              borderWidth: 2,
              pointRadius: (ctx) => (5),
              pointBackgroundColor: "#7F3FBF",
              cubicInterpolationMode: 'monotone',
              pointHoverRadius: 7,
            },
            {
              label: "Tabungan",
              data: [1.3, 3.8, 6.4],
              borderColor: "#E22828",
              backgroundColor: "#E22828",
              borderWidth: 2,
              pointRadius: (ctx) => (5),
              pointBackgroundColor: "#E22828",
              cubicInterpolationMode: 'monotone',
              pointHoverRadius: 7,
            },
          ],
        },
        options: {
          responsive: true,
          plugins: {
            legend: { display: false },
            tooltip: {
              mode: 'index',
              intersect: false,
              callbacks: {
                title: (tooltipItems) => {
                  hoveredYear.value = tooltipItems[0].label;
                  return `${tooltipItems[0].label}`;
                },
                label: (tooltipItem) => {
                  const value = tooltipItem.raw;
                  const label = tooltipItem.dataset.label;
                  return `${label}: Rp${value} juta`;
                },
              },
              displayColors: true,
              titleColor: "#333",
              bodyColor: "#333",
              borderWidth: 1,
              borderColor: "#ddd",
              backgroundColor: "#F5F5F5",
              bodyColor: "#333",
              borderColor: "#ddd",
              usePointStyle: true,
              padding: 10,
            },
          },
          scales: {
            y: {
              beginAtZero: false,
              min: 1,
              max: 8.5,
              position: "right",
              ticks: {
                callback: (value) => `${value}jt`,
                color: "#666",
                font: { size: 12 },
              },
              grid: {
                color: "#ddd",
                lineWidth: 1,
                borderDash: [5, 5],
              },
            },
            x: {
              type: "category",
              labels: ["1 Tahun", "3 Tahun", "5 Tahun"],
              ticks: {
                color: "#666",
                font: { size: 12 },
                autoSkip: false,
              },
              grid: {
                color: "transparent",
              },
            },
          },
          onHover: (event, chartElement) => {
            if (chartElement.length === 0) {
              hoveredYear.value = "3 Tahun";
            }
          },
        },
      });
    });

    return { chartCanvas, customLegend, hoveredYear };
  },
};
</script>

<style lang="scss" scoped>
.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.investment-info {
  text-align: center;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid #ddd;
  box-shadow: 0px 0px 3.96px 0px #00000040;
  color: #333333;
  font-weight: 500;
  p{
    color: #333333;
  }
  strong{
    color: #00B7B6;
    font-weight: 700;
  }
  @media screen and (max-width: 992px) {
    width: 100%;
  }
}

.investment-values {
  display: flex;
  justify-content: space-around;
  margin-top: 0px;
  font-size: 14px;
  color: #333333;
  p{
    margin: 0 5px;
    text-align: center;
    strong{
      font-weight: 700;
      display: block;
      color: #00B7B6;
    }
  }
}
.custom-legend {
  display: flex;
  gap: 12px;
  font-size: 14px;
  margin-top: 16px;
}
.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.legend-item span {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.legend-name {
  color: #000;
  font-weight: bold;
}
</style>
