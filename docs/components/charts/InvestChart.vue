<template>
  <div class="chart-container">
    <div class="investment-info">
      <p class="font-17">
        <PERSON><PERSON> dalam waktu <strong>{{ hoveredYear }}</strong>
      </p>
      <div class="investment-values">
        <p class="font-14">Investasi Awal <strong class="font-17">Rp100.000</strong></p>
        <p class="font-14">Investasi Bulanan <strong class="font-17">Rp100.000</strong></p>
      </div>
    </div>

    <component v-if="apexchart" :is="apexchart" type="line" :options="chartOptions" :series="seriesData" style="width: 100%; height: auto;" />

    <div class="custom-legend">
      <div v-for="(label, index) in customLegend" :key="index" class="legend-item">
        <span :style="{ backgroundColor: label.color }"></span>
        <p class="legend-name">{{ label.name }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from "vue";

export default {
  name: "MyChart",
  data() {
    return {
      hoveredYear: "3 Tahun",
      customLegend: [
        { name: "GORO", color: "#006867" },
        { name: "Deposito", color: "#7300FF" },
        { name: "Tabungan", color: "#FF0000" },
      ],
      seriesData: [
        { name: "GORO", data: [1.4, 4.4, 8.1] },
        { name: "Deposito", data: [1.3, 3.9, 6.7] },
        { name: "Tabungan", data: [1.3, 3.8, 6.4] },
      ],
      chartOptions: null,
      apexchart: null, // ApexChart component placeholder
    };
  },
  mounted() {
    if (typeof window !== "undefined") {
      import("vue3-apexcharts").then((module) => {
        this.apexchart = module.default;
        this.chartOptions = {
          chart: {
            type: "line",
            width: "100%",
            toolbar: { show: false },
          },
          stroke: {
            curve: 'straight',
            width: 4
          },
          markers: { size: 5, shape: "circle", hover: { size: 7 } },
          colors: ["#006867", "#7300FF", "#FF0000"],
          xaxis: {
            categories: ["1 Tahun", "3 Tahun", "5 Tahun"],
            labels: {
              offsetX: 5,
              style: {
                colors: "#666",
                fontSize: "12px"
              }
            },
          },
          yaxis: {
            min: 1,
            max: 8.1,
            opposite: true,
            tickAmount: 4,
            axisBorder: { show: true },
            labels: {
              // formatter: (val) => `${Math.round(val * 10) / 10} juta`,
              formatter: (val) => `${(Math.round(val * 10) / 10).toFixed(1).replace('.', ',')} juta`,
              style: { colors: "#666", fontSize: "12px" },
            },
          },
          grid: { borderColor: "#ddd", strokeDashArray: 5 },
          tooltip: {
            shared: true,
            intersect: false,
            custom: ({ series, dataPointIndex }) => {
              const categories = ["1 Tahun", "3 Tahun", "5 Tahun"];
              this.hoveredYear = categories[dataPointIndex] || "3 Tahun";
              const dataPoints = series.map((s, index) => {
                const value = s[dataPointIndex];
                const color = this.customLegend[index].color;
                return `
                  <div style="display: flex; align-items: center; margin-bottom: 5px;">
                    <div style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 15px;"></div>
                    <div style="color: #333333;"><strong>${this.customLegend[index].name}</strong><p style="color:${color};">Rp${(Math.round(value * 10) / 10).toFixed(1).replace('.', ',')} juta</p></div>
                  </div>`;
              }).join('');
              return `<div class="custom-tooltip" style="background: #F5F5F5; padding: 15px; border-radius: 10px;overflow: hidden;min-width:131px;">${dataPoints}</div>`;
            },
          },
          legend: { show: false },
        };
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.custom-tooltip {
  background: #F5F5F5;
  padding: 10px;
  border-radius: 5px;
  min-width: 100px;
  position: relative;
  z-index: 1;
}

.investment-info {
  text-align: center;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid #ddd;
  box-shadow: 0px 0px 3.96px 0px #00000040;
  color: #333333;
  font-weight: 500;
  p {
    color: #333333;
  }
  strong {
    color: #00b7b6;
    font-weight: 700;
  }
  @media screen and (max-width: 992px) {
    width: 100%;
  }
}

.investment-values {
  display: flex;
  justify-content: space-around;
  margin-top: 0px;
  font-size: 14px;
  color: #333333;
  p {
    margin: 0 5px;
    text-align: center;
    strong {
      font-weight: 700;
      display: block;
      color: #00b7b6;
    }
  }
}
.custom-legend {
  display: flex;
  gap: 12px;
  font-size: 14px;
  margin-top: 16px;
}
.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.legend-item span {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.legend-name {
  color: #000;
  font-weight: bold;
}
</style>
