<template>
    <div class="property-card light shadow cls-landing-wa-id-card new-cls-landing-wa-id-card" @click="onClicked">
        <div class="card-item">
            <div class="card-header">
                <div class="card-header-content">
                    <p class="font-18 cls-turnover-label mb-1">
                        {{ property.turnover_label }}
                    </p>
                    <p class="font-boogaloo-regular font-24 cls-turnover">
                        Rp{{ property.turnover }} juta
                    </p>
                </div>
            </div>
            <img :src="`/pages/public_assets/landing-wa-id/properties/${property.image}`" :alt="property.name" class="property-image"/>
            <div class="card-info">
                <p class="font-13 d-flex justify-content-start align-items-center card-address">
                    <img :src="`/pages/public_assets/icons/icon-location.svg`" class="icon mr-2"/> {{ property.address }}
                </p>
                <p class="font-21 font-weight-bold m-0 card-name pt-2" :class="nameClass">
                    {{ property.name }}
                </p>
            </div>
        </div>
    </div>
</template>

<script>

import numeral from "numeral"
export default {
    props: {
        property: {
            type: Object,
            required: true
        },
        isEnglish: {
            type: Boolean,
            default: false
        },
        showIrrEry: {
            type: Boolean,
            default: false
        },
    },
    emits: ["on-clicked"],
    methods: {
        onClicked() {
            this.$emit("on-clicked")
        },
        getTotalInvested(invested) {
            return `Rp${numeral(invested).format("0,0").replace(",", ".")}`
        },
    },
    computed: {
        nameClass() {
            return "color-stock"
        },
        descriptionClass() {
            return "color-description"
        },
        progressClass() {
            return "bg-color-in-progress"
        },
        tokenLeft() {
            if (this.isEnglish) {
                return "token left"
            }
            return "token tersisa"
        },
    },
}
</script>


<style lang="scss" scoped>
@import '../../src/assets/sass/variables';
.cls-landing-wa-id-card{
    *{
        font-family: "NewAcuminVariableConcept", Helvetica, sans-serif;
    }
    &.light {
        &.property-card {
            position: relative;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction: column;
            flex-direction: column;
            min-width: 0;
            max-height: 395px;
            word-wrap: break-word;
            background-clip: border-box;
            border-radius: 16px;
            overflow: hidden;
            cursor: pointer;
            transition: .8s;
            background: var(--primary-light-color);
            padding: 10px;
            padding-top: 0;
            justify-content: flex-start;
            height: 100%;
            width: 100%;
            &:hover {
                background: var(--primary-background-color);
                
                img{
                    filter: brightness(80%);
                    transition: .8s;
                }
            }

            &:hover .card-info {
                
            }

            img.property-image {
                width: 100%;
                height: 305px;
                object-fit: cover;
                border: 0 transparent;
                border-radius: 14px;
                background-color: var(--primary-background-color);
                max-height: 305px;
                overflow: hidden;
                @media screen and (max-width: 992px) {
                    height: 235px;
                    max-height: 235px;
                }
            }

            .mark-new-property{
                position: absolute;
                top: 5px;
                right: 5px;
                width: 89px;
                height: 89px;
                background-image: url("#{$assetsPath}/img/icons/mark_new_property_en.png");
                background-size: cover;
                background-repeat: no-repeat;
                background-position: top;
                &.lang-id{
                    background-image: url("#{$assetsPath}/img/icons/mark_new_property_id.png") !important;
                }
                }

            p {
                padding: 0;
                margin: 0;
            }

            .darken {
                filter: brightness(70%);
            }

            .card-separator {
                &.card-rate-limit{
                    padding: 7px 0;
                }
                .space {
                    margin-right: 10px;
                }

                .img-info {
                    width: 14px;
                    height: 14px;
                    background-color: transparent;
                    z-index: 1;
                }
            }

            .card-item{
                position: relative;
                border-radius: 16px;
                overflow: hidden;
                .card-header{
                    background-color: transparent;
                    text-align: center;
                    padding-top: 0;
                    border: 0 !important;
                    .card-header-content{
                        display: inline-block;
                        margin: 0 auto;
                        background-color: #006867;
                        border-radius: 0 0 11px 11px;
                        padding: 10px 24px;
                        @media screen and (max-width: 992px) {
                            padding: 2px 24px;
                        }
                        .cls-turnover-label{
                            font-weight: 500;
                            line-height: 21.6px;
                            text-align: center;
                            color: #fff;
                            @media screen and (max-width: 992px) {
                                margin-bottom: 0 !important;
                            }
                        }
                        .cls-turnover{
                            font-family: "Boogaloo", sans-serif;
                            font-weight: 400;
                            line-height: 28.54px;
                            text-align: center;
                            color: #fff;
                        }
                    }
                }
            }

            &:hover .card-item .card-header-content {
                background-color: #006666;
            }

            .card-info {
                width: 100%;
                transition: .8s;
                background: var(--primary-light-color);
                padding: 10px 15px;
                position: absolute;
                bottom: 0;
                left: 0;
                background: linear-gradient(90deg, #036564 38.5%, rgba(0, 206, 204, 0) 100%);
                .card-address{
                    color: #fff;
                    font-weight: 500;
                    line-height: 14px;
                    letter-spacing: 0.01em;
                    text-align: left;
                    .icon{
                        width: 14px;
                        background-color: transparent !important;
                    }
                    @media screen and (max-width: 992px) {
                        font-size: 13px !important;
                        font-weight: 500;
                        line-height: 14px;
                    }
                }
                .card-name {
                    font-weight: 700;
                    line-height: 22.4px;
                    letter-spacing: 0.5161550641059875px;
                    text-align: left;
                    color: #fff !important;
                    @media screen and (max-width: 992px) {
                        font-size: 16px !important;
                        font-weight: 700;
                        line-height: 22.4px;
                    }
                }
                .card-yeild{
                    font-weight: 382;
                    line-height: 20px;
                    text-align: left;
                    color: #A2A2A2;
                    @media screen and (max-width: 992px) {
                        font-weight: 500;
                    }
                }

                .card-invested{
                    font-weight: 500;
                    line-height: 20.4px;
                    text-align: left;
                    color: #00B7B6;
                    @media screen and (max-width: 992px) {
                        font-size: 16px !important;
                        font-weight: 500;
                    }
                }

                .color-white {
                    color: white;
                }

                .color-stock {
                    color: var(--primary-color);
                }

                .color-description {
                    color: var(--primary-color);
                }

                .bg-color-white {
                    background-color: #ffffff;
                }

                .bg-color-in-progress {
                    background-color: var(--primary-lighter-color);
                }

                .bg-color-sold-out {
                    background-color: #769B9D;
                }

                .bg-color-progress {
                    background-color: var(--color-in-progress);
                }
            }

            .invest-now {
                border-radius: 12px;
                color: var(--primary-light-color);
                background-color: var(--primary-color);
                text-align: center;
                vertical-align: middle;
                transition: .8s;
                padding: 8px;

                .text {
                    color: var(--primary-light-color);
                }
                
                &.disabled {
                    background-color: #769B9D;
                }

                &:hover {
                    background-color: var(--primary-hover-color);
                }
            }

            h4 {
                margin-top: 0;
                margin-bottom: 0.75rem;
            }
        }
    }
    &.cls-landing-wa-id-card{
        &.property-card {
            .card-item{
                .card-header{
                    padding: 15px !important;
                    padding-top: 0 !important;
                    .card-header-content{
                        width: 100%;
                        padding: 10px 20px;
                        background-color: #1D557E;
                    }
                }
            }
            .card-info{
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                background: linear-gradient(0deg, rgba(0, 0, 0, 0.79) 35.4%, rgba(0, 0, 0, 0) 100%);
                min-height: 93px;
                @media screen and (max-width: 992px) {
                    min-height: 73px;
                }
                .card-address{
                    justify-content: center !important;
                }
                .card-name{
                    text-align: center;
                }
            }
        }
    }
}
</style>