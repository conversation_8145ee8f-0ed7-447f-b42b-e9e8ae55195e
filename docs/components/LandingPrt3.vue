<template>
    <div :id="`page-${pageId}`" class="goro-landing goro-landing-page goro-landing-wa-id-page new-goro-landing-wa-id-page" ref="toTopSection">
        <div class="cls-site-banner-top-container text-center">
            <div class="cls-site-banner-content">
                <p class="cls-site-banner-message text-center">
                    <a :href="sanboxDocumentUrl" class="cls-text-link p-0" target="_blank">
                        GORO adalah peserta Regulatory Sandbox Otoritas Jasa Keuangan (OJK) berdasarkan Surat OJK no. S-548/IK.01/2024 dan S-549/IK.01/2024
                    </a>
                </p>
            </div>
        </div>

        <!-- Main block -->
        <div class="goro-block goro-main-en-block goro-banner-block" style="background-color: var(--primary-background-color);">
            <div class="container-fluid goro-container-fluid">
                <div class="row goro-banner-row">
                    <div class="col-12 col-flex-1">
                        <nav class="navbar goro-navbar mb-0 p-0 mt-0">
                            <div class="navbar-container">
                                <a class="navbar-brand" :href="getMainPageUrl()" title="Goro - Home page" target="_blank">
                                    <img src="@/assets/img/logo_with_text.png" alt="Goro - Home page" width="191" height="105" class="cls-logo">
                                </a>
                            </div>
                        </nav>
                    </div>
                    <div class="col-lg-6 col-12 goro-content cls-banner-left">
                        <h1 class="font-48 cls-heading pb-4 mb-0">
                            Imbal hasil di <span class="goro font-48">GORO</span> <span class="profit font-54">2<span class="x font-40">X</span></span> <br><span class="cls-highligh-text">lebih cuan<img :src="`/pages/public_assets/landing-wa-id/text-highlight.png`"/></span> dibanding <br>nabung di Bank
                        </h1>
                        <p class="cls-heading-description font-32">
                            Mulai dari <span class="invest font-32">Rp10.000</span> bisa punya vila di Bali
                        </p>
                        <div class="cls-getstarted pt-2 pt-sm-3 pt-lg-5">
                            <!-- <button id="btn_LandingWaIdPage_ScrollToWa" class="cls-btn-getstarted btn btn-none btn-main mt-0 font-24 font-weight-700" @click="scrollToWaSection()"
                                style="border-radius: 8px; padding: 10px 40px">
                                Konsultasi GRATIS
                            </button> -->
                            <a
                                :id="`btn-top-${pageId}`" @click="trackingClickButtonTop" class="cls-btn-getstarted btn btn-none btn-main mt-0 font-40 font-weight-700"
                                :href="redirectInvitationLink" target="_blank">
                                Registrasi GRATIS
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 cls-banner d-flex align-items-start justify-content-center">
                        <img class="show-desktop" :src="`/pages/public_assets/landing-wa-id/new-banner.png`" alt="Imbal hasil di GORO 2X lebih cuan dibanding nabung di Bank"/>
                        <img class="show-mobile" :src="`/pages/public_assets/landing-wa-id/new-banner-mobile.png`" alt="Imbal hasil di GORO 2X lebih cuan dibanding nabung di Bank"/>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Main block -->

        <div v-if="!isOnMobile" class="goro-block pb-4 pt-4 pb-lg-5 pt-lg-5 cls-profits-block" style="background-color: #00565B;">
            <div class="container-fluid goro-container-fluid">
                <div class="row justify-content-center" align-h="center">
                    <div class="col-lg-8 col-xl-8 col-10 text-center" cols="10" lg="8" xl="8">
                        <h2 class="font-36 mt-0 mb-5 cls-h2-heading">
                            Kenapa harus investasi di GORO?
                        </h2>
                    </div>
                </div>

                <div class="row pt-3 cls-profits-items">
                    <div class="col-lg-6 col-12 cls-card" 
                        cols="12" xl="3" lg="3" 
                        v-for="(item, index) in profits"
                        :key="index"
                        :class="item.class"
                    >
                        <div class="cls-card-item row m-0 p-0">
                            <div class="col-lg-6 col-12 cls-left">
                                <p class="font-28 cls-heading">
                                    <label>{{item.title}}</label> {{item.description}}
                                </p>
                            </div>
                            <div class="col-lg-6 col-12 cls-right">
                                <img :src="item.image" :alt="`${item.title} ${item.description}`"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="isOnMobile" class="goro-block pb-4 pt-4 pb-lg-5 pt-lg-5 cls-profits-block" style="background-color: #00565B;">
            <div class="container-fluid goro-container-fluid">
                <div class="row justify-content-center" align-h="center">
                    <div class="col-lg-8 col-xl-8 col-10 text-center" cols="10" lg="8" xl="8">
                        <h2 class="font-36 mt-0 mb-3 mb-lg-5 cls-h2-heading">
                            Kenapa harus <br>investasi di GORO?
                        </h2>
                    </div>
                </div>

                <div class="pt-3 cls-profits-items">
                    <Carousel
                        v-bind="profitSliderSettings"
                        class="carousel"
                        :wrap-around="false"
                        :breakpoints="profitSliderBreakpoints"
                        ref="profitSlider"
                        >
                            <!-- Slide Content -->
                            <Slide
                                v-for="(item, index) in profits"
                                :key="index"
                                class="cls-card"
                                :class="item.class"
                            >
                                <div class="cls-card-item row m-0 p-0">
                                    <div class="col-lg-6 col-12 cls-left">
                                        <p class="font-28 cls-heading">
                                            <label>{{item.title}}</label> {{item.description}}
                                        </p>
                                    </div>
                                    <div class="col-lg-6 col-12 cls-right">
                                        <img :src="item.image" :alt="`${item.title} ${item.description}`"/>
                                    </div>
                                </div>
                            </Slide>
                    </Carousel>
                </div>
            </div>
        </div>

        <!--Property Catalog block-->
        <div class="goro-block goro-property-catalog-block pb-4 pt-4 pb-lg-5 pt-lg-5" style="background-color: #00565B;">
            <div class="container-fluid goro-container-fluid">
                <div class="row justify-content-center" align-h="center">
                    <div class="col-lg-10 col-12 text-center" cols="12" lg="10">
                        <div class="cls-highlight-heading mb-3">
                            <h2 class="font-36 font-weight-bold color-white cls-heading mb-0">
                                <p class="font-36 color-white font-weight-bold cls-heading-content">
                                    Total imbal hasil properti premium ini Rp5 Miliar+
                                </p>
                            </h2>
                        </div>
                        <label class="mb-3 font-24 cls-sub-heading">
                            Berikut adalah properti terbaik dengan imbal hasil maksimal dari GORO
                        </label>
                    </div>
                </div>
                <div class="pt-4 pb-1 cls-card-items">
                    <Carousel
                        v-bind="propertysSliderSettings"
                        class="carousel"
                        :wrap-around="false"
                        :breakpoints="propertySliderBreakpoints"
                        ref="propertySlider"
                        >
                            <!-- Slide Content -->
                            <Slide
                                v-for="(property, index) in properties"
                                :key="index"
                                class="pb-4 cls-card-item"
                            >
                                <LandingWaIdPropertyLightCard :property="property" :isEnglish="true" :show-irr-ery=true @on-clicked="scrollToWaSection()" />
                            </Slide>
                    </Carousel>
                </div>
            </div>
        </div>
        <!--End Property Catalog block-->

        <div class="goro-block goro-benefit-block pb-4 pt-4 pb-lg-5 pt-lg-5" style="background-color: #fff;">
            <div class="container-fluid goro-container-fluid benefit-container">
                <div class="row justify-content-around align-items-start" align-h="around">
                    <div class="col-lg-6 col-xl-6 col-12 text-center cls-protift-table-wrapper" cols="12" xl="6" lg="6">
                       <h2 class="font-36 cls-heading">
                            Pilihan Terbaik!
                        </h2>
                        <label class="mb-3 font-24 cls-sub-heading">
                            Raih cuan tinggi dengan risiko rendah
                        </label>
                        <div class="cls-profit-table pt-5">
                            <img :src="`/pages/public_assets/landing-wa-id/profit-bar-aset.png`" alt="Raih cuan tinggi dengan risiko rendah"/>
                        </div>
                    </div>
                    <div class="col-lg-6 col-xl-6 col-12 mt-4 mt-lg-0" cols="12" xl="6" lg="6">
                        <InvestChart></InvestChart>
                    </div>
                </div>
            </div>
        </div>

        <!--Real Investors, Real-life Impact block-->
        <div class="goro-block goro-testimonials-block text-center pb-4 pt-4 pb-lg-5 pt-lg-5" style="background-color: #00565B">
            <div class="container-fluid goro-container-fluid">
                <div class="row justify-content-center" align-h="center">
                    <div class="col-lg-10 col-12 text-center" cols="12" lg="10">
                        <h2 class="font-36 cls-heading">
                            Testimoni
                        </h2>
                    </div>
                </div>
                <div class="pt-2 pt-lg-4 pb-1 cls-testimonial-items">
                    <div class="carousel-container cls-custom-carousel-container pt-0 pt-lg-4">
                        <div class="horizontal-navigation d-flex justify-content-start arrow-left">
                            <button class="arrow-action arrow-left-action" @click="scrollToPrev">
                                <img :src="`/pages/public_assets/carousel/arrow-left.svg`">
                            </button>
                        </div>
                        <div class="cls-active-item-desktop">
                            <Carousel
                            v-bind="testimonialsSliderSettings"
                            class="carousel"
                            :wrap-around="!isOnMobile"
                            :breakpoints="testimonialsSliderBreakpoints"
                            ref="testimonialsSlider"
                            >
                                <!-- Slide Content -->
                                <Slide
                                    v-for="(item, index) in testimonials"
                                    :key="index"
                                    class="pb-4 cls-card-item"
                                >
                                    <div class="cls-card-item-content">
                                    <div class="cls-reviewer d-flex align-items-center justify-content-start">
                                        <img :src="item.imgSrc" :alt="item.alt" />
                                        <div class="cls-reviewer-info">
                                        <h3 class="font-24 font-weight-bold text-name">{{ item.name }}</h3>
                                        <p class="font-16 text-position">{{ item.position }}</p>
                                        </div>
                                    </div>
                                    <div class="cls-message pt-3">
                                        <p class="font-14">{{ item.message }}</p>
                                    </div>
                                    </div>
                                </Slide>
                            </Carousel>
                        </div>
                        <div class="horizontal-navigation d-flex justify-content-end arrow-right">
                            <button class="arrow-action arrow-right-action" @click="scrollToNext">
                                <img :src="`/pages/public_assets/carousel/arrow-right.svg`">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--End Real Investors, Real-life Impact block-->

        <!--Invest block-->
        <div class="text-center goro-block goro-block-invest" style="background-color: var(--primary-color);">
            <div class="container">
                <div class="row justify-content-around align-items-center" align-h="around" ref="toWaSection">
                    <div class="col-md-9 col-12 join-goro join-goro-message" cols="12">
                        <h2 class="font-64 text-center mt-0 mb-2 mb-lg-4 cls-invest-heading">
                            Pilih investasi yang hasilkan cuan maksimal
                        </h2>
                        <p class="font-40 mt-0 mb-0 cls-description">
                            Kalau bisa cuan maksimal, kenapa pilih yang biasa?
                        </p>
                    </div>
                    <div class="col-md-10 col-12 join-goro join-goro-btn pt-3" cols="12">
                        <a :id="`btn-bottom-${pageId}`" @click="trackingClickButtonBottom" class="font-48 btn btn-none align-items-center justify-content-center btn-main mb-1 mt-1 mt-lg-3 cls-invest-btn"
                            :href="redirectInvitationLink" target="_blank">
                            Registrasi Sekarang
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!--End Invest block-->
    </div>
</template>

<script>
import LandingWaIdPropertyLightCard from "./LandingWaIdPropertyLightCard.vue";
import { gtmTrackEvent } from "../../src/helpers/gtm";
import { GTM_EVENT_NAMES } from "../../src/constants/gtm";
import LandingWaIdChart from "./charts/LandingWaIdChart.vue";
import InvestChart from "./charts/InvestChart.vue";
import { Carousel, Slide, Navigation } from 'vue3-carousel';
import "vue3-carousel/dist/carousel.css"
import externalSites from "@/constants/externalSites"

export default {
    components: {
        LandingWaIdPropertyLightCard,
        LandingWaIdChart,
        InvestChart,
        Carousel, Slide,
        Navigation
    },
    data() {
        return {
            pageId: "landing-prt3",
            pageEventPrefix: "LandingPrt3",
            redirectInvitationLink: "http://www.goro.id/invite/GORO.PAKAR",
            showChart: false,
            isVerifyOtp: false,
            innerWidth: 0,
            properties: [],
            activeTab: 'tech-in-asia',
            activeTabIdx: 0,
            intervalId: null,
            isOnMobile: false,
            sanboxDocumentUrl: externalSites.SANDBOX,
            testimonials: [
                { imgSrc: '/pages/public_assets/landing-wa-id/testimonials-2.png', alt: 'Endriyani - Ibu Rumah Tangga', name: 'Endriyani', position: 'Ibu Rumah Tangga', message: 'Baru kali ini nemu platform yang bisa kasih passive income tiap bulan, tanpa ribet mikirin manajemen properti. Investasinya juga mulai dari Rp10.000 jadi sangat2 terjangkau buat semua kalangan.' },
                { imgSrc: '/pages/public_assets/landing-wa-id/testimonials-3.png', alt: 'Hidayat - Karyawan BUMD', name: 'Hidayat', position: 'Karyawan BUMD', message: 'GORO membuktikan bahwa berinvestasi properti bisa dimulai dengan modal yg kecil. Dengan segala keterbukaan dalam sistem, operasional, dan pelaporan hasil pendapatan serta kemudahan dalam penggunaan aplikasinya.' },
                { imgSrc: '/pages/public_assets/landing-wa-id/img-testimonials-1.png', alt: 'Yanuario Bagas - Karyawan Swasta', name: 'Yanuario Bagas', position: 'Karyawan Swasta', message: 'GORO cocok banget buat jadi platform investasi properti buat Gen Z. Platform-nya juga newbie-friendly jadi cocok banget buat yang mau investasi tapi bingung mulai dari mana.' },
            ],
            profits: [
                {
                    title: "100 ribu+",
                    description: "juragan vila sudah dapat cuan",
                    image: "/pages/public_assets/landing-wa-id/img-invest-1.png",
                    class: "cls-card-1"
                },
                {
                    title: "Rp60 miliar+",
                    description: "aset properti yang tumbuh terus nilainya",
                    image: "/pages/public_assets/landing-wa-id/img-invest-2.png",
                    class: "cls-card-2"
                },
                {
                    title: "Rp5 miliar+",
                    description: "cuan imbal hasil yang sudah dibagikan",
                    image: "/pages/public_assets/landing-wa-id/img-invest-3.png",
                    class: "cls-card-3"
                },
                {
                    title: "20+ properti",
                    description: "dengan nilai investasi gak kaleng-kaleng",
                    image: "/pages/public_assets/landing-wa-id/img-invest-4.png",
                    class: "cls-card-4"
                }
            ],
            testimonialsSliderSettings: {
                autoplay: 0,
                itemsToShow: 1.15,
                itemsToScroll: 1,
                snapAlign: "start",
                transition: 300,
                wrapAround: false
            },
            testimonialsSliderBreakpoints: {
                300: {
                    itemsToShow: 1.15,
                    snapAlign: "start",
                },
                500: {
                    itemsToShow: 1.25,
                    snapAlign: "start",
                },
                768: {
                    itemsToShow: 1.75,
                    snapAlign: "start",
                },
                900: {
                    itemsToShow: 2.25,
                    snapAlign: "start",
                },
                1200: {
                    itemsToShow: 3,
                    snapAlign: "center",
                }
            },
            propertySliderSettings: {
                autoplay: 0,
                itemsToShow: 1.15,
                itemsToScroll: 1,
                snapAlign: "start",
                transition: 300,
                wrapAround: false
            },
            propertySliderBreakpoints: {
                300: {
                    itemsToShow: 1.15,
                    snapAlign: "start",
                },
                500: {
                    itemsToShow: 1.5,
                    snapAlign: "start",
                },
                768: {
                    itemsToShow: 1.75,
                    snapAlign: "start",
                },
                900: {
                    itemsToShow: 2.25,
                    snapAlign: "start",
                },
                1200: {
                    itemsToShow: 3.15,
                    snapAlign: "start",
                },
                1300: {
                    itemsToShow: 4,
                    snapAlign: "start",
                }
            },
            profitSliderSettings: {
                autoplay: 0,
                itemsToShow: 1.15,
                itemsToScroll: 1,
                snapAlign: "start",
                transition: 300,
                wrapAround: false
            },
            profitSliderBreakpoints: {
                300: {
                    itemsToShow: 1.15,
                    snapAlign: "start",
                },
                500: {
                    itemsToShow: 1.5,
                    snapAlign: "start",
                },
                768: {
                    itemsToShow: 1.75,
                    snapAlign: "start",
                },
                900: {
                    itemsToShow: 2.25,
                    snapAlign: "start",
                },
                1200: {
                    itemsToShow: 3.15,
                    snapAlign: "start",
                },
                1300: {
                    itemsToShow: 4,
                    snapAlign: "start",
                }
            },
        };
    },
    methods: {
        getMainPageUrl() {
            return import.meta.env.VITE_VUE_APP_URL
        },
        getValidationState({ dirty, validated, valid = null }) {
            return dirty || validated ? valid : null
        },
        handleWindowResize() {
            this.innerWidth = window.innerWidth

            if (window.innerWidth <= 992) {
                this.isOnMobile = true
            } else {
                this.isOnMobile = false
            }
        },
        scrollToWaSection() {
            window.fbq("trackCustom", `${this.pageEventPrefix}:JoinNowClicked`)
            this.$refs.toWaSection.scrollIntoView({ behavior: "smooth" })
            gtmTrackEvent({
                event: GTM_EVENT_NAMES.USER_CLICKED_JOINNOW,
                page: `${this.pageEventPrefix}`
            })
        },
        scrollToNext () {
            this.$refs.testimonialsSlider.next()
        },
        scrollToPrev () {
            this.$refs.testimonialsSlider.prev()
        },
        trackingClickButtonTop() {
            window.fbq("trackCustom", `${this.pageEventPrefix}:${GTM_EVENT_NAMES.USER_CLICKED_JOINNOW}`)
            gtmTrackEvent({
                event: GTM_EVENT_NAMES.USER_CLICKED_JOINNOW,
                pageId: this.pageId,
                section: 'top'
            })
        },
        trackingClickButtonBottom() {
            window.fbq("trackCustom", `${this.pageEventPrefix}:${GTM_EVENT_NAMES.USER_CLICKED_JOINNOW}`)
            gtmTrackEvent({
                event: GTM_EVENT_NAMES.USER_CLICKED_JOINNOW,
                pageId: this.pageId,
                section: 'bottom'
            })
        },
    },
    mounted() {
        this.handleWindowResize()
        window.addEventListener("resize", this.handleWindowResize)
    },
    async beforeDestroy() {
        window.removeEventListener("resize", this.handleWindowResize)
    },
    created() {
        this.properties = [
            {
                id: 1,
                name: "Vila Lembah Giri",
                address: "Ubud, Bali",
                image: "Vila-Lembah-Giri.png",
                yield: "11%",
                invested: 75474131,
                turnover_label: "Omzet vila per bulan",
                turnover: 95
            },
            {
                id: 2,
                name: "Villa Jenggala",
                address: "Ubud, Bali",
                image: "Villa-Jenggala.png",
                yield: "11%",
                invested: 21294842,
                turnover_label: "Omzet vila per bulan",
                turnover: 88
            },
            {
                id: 3,
                name: "Villa The Kayu",
                address: "Ubud, Bali",
                image: "Villa-The-Kayu.png",
                yield: "11%",
                invested: 77615412,
                turnover_label: "Omzet vila per bulan",
                turnover: 110
            },
            {
                id: 4,
                name: "Villa Talun",
                address: "Ubud, Bali",
                image: "Villa-Talun.png",
                yield: "11%",
                invested: 82887795,
                turnover_label: "Omzet vila per bulan",
                turnover: 92
            },
        ]
    }
};
</script>

<style lang="scss" scoped>
@import '../../src/assets/sass/variables';
@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

h1 {
    // font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
    font-weight: 500;
    color: var(--primary-color);
    margin: 30px 0 15px;
}

p {
    color: var(--primary-color);
}

label {
    color: var(--primary-darker-color);
}

.goro-landing-wa-id-page{
    *{
        font-family: "NewAcuminVariableConcept", Helvetica, sans-serif;
    }
    .goro-block{
        .goro-container-fluid{
            max-width: 80%;
            @media screen and (max-width: 992px) {
                max-width: 100%;
            }
        }
        .cls-h2-heading{
            font-weight: 700;
            line-height: 46px;
            text-align: center;
            color: #fff;
            @media screen and (max-width: 992px) {
                font-size: 24px;
                font-weight: 700;
                line-height: 32px;
                text-align: center;
            }
        }
        .cls-sub-heading{
            font-weight: 500;
            line-height: 46px;
            text-align: center;
            color: #fff;
            @media screen and (max-width: 992px) {
                font-size: 14px;
                font-weight: 500;
                line-height: 22px;
                text-align: center;
            }
        }
    }
    .goro-banner-block{
        background-color: #00565B !important;
        background-image: url('/pages/public_assets/landing-wa-id/background-banner.png');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: right bottom;
        padding-bottom: 120px;
        @media screen and (max-width: 992px) {
            background-color: #fff !important;
            padding-top: 0 !important;
            .cls-banner-left{
                order: 1;
                flex: 2;
            }
        }
        .goro-banner-row{
            @media screen and (max-width: 992px) {
                min-height: 100vh;
                flex-direction: column;
                justify-content: space-around;
            }
        }
        .goro-navbar{
            margin-top: 20px !important;
            margin-bottom: 0 !important;
            @media screen and (max-width: 992px) {
                margin-top: 0 !important;
                justify-content: center;
            }
            .navbar-brand{
                .cls-logo{
                    @media screen and (max-width: 992px) {
                        width: 155px !important;
                    }
                }
            }
        }
        h1.cls-heading{
            font-size: 48px;
            font-weight: 700;
            line-height: 55.2px;
            text-align: left;
            color: #006867;
            
            .goro{
                color: #006867;
                font-weight: 600;
            }
            .profit{
                color: #FF0000;
                font-weight: 600;
                font-size: 54px;
                .x{
                    font-size: 40px;
                }
            }
            .cls-highligh-text{
                position: relative;
                img{
                    opacity: 0;
                    animation: fadeIn 0.5s ease-in 0.1s forwards;
                    width: 100%;
                    position: absolute;
                    left: 0;
                    bottom: -13px;
                    height: 13px;
                    @media screen and (max-width: 992px) {
                        bottom: -10px;
                    }
                }
            }
            @media screen and (max-width: 992px) {
                font-size: 28px;
                font-weight: 700;
                line-height: 36px;
                letter-spacing: -0.05em;
                text-align: center;
                padding-bottom: 40px !important;
                .goro{
                    font-size: 28px;
                    font-weight: 700;
                }
                .profit{
                    font-size: 32px;
                    font-weight: 700;
                    .x{
                        font-size: 24px;
                    }
                }
            }
            @media screen and (max-width: 992px) and (max-height: 884px) {
                padding-bottom: 20px !important;
            }
            @media screen and (max-width: 992px) and (max-height: 667px) {
                padding-bottom: 20px !important;
            }
            @media screen and (max-width: 992px) and (max-height: 568px) {
                padding-bottom: 10px !important;
            }
            @media screen and (max-width: 992px) and (max-height: 480px) {
                padding-bottom: 30px !important;
            }
        }
        .cls-getstarted{
            .cls-btn-getstarted{
                border-radius: 18px;
                padding: 14px 61px;
                &:focus,
                &:active{
                    background-color: var(--primary-color) !important;
                    border-color: var(--primary-darker-color) !important;
                    color: white !important;
                }
                @media screen and (max-width: 992px) {
                    font-size: 20px !important;
                    padding: 11px 44px;
                    border-radius: 10px;
                }
            }
            @media screen and (max-width: 992px) {
                text-align: center;
                padding-top: 30px !important;
            }
            @media screen and (max-width: 992px) and (max-height: 884px) {
                padding-top: 20px !important;
            }
            @media screen and (max-width: 992px) and (max-height: 667px) {
                padding-top: 20px !important;
            }
            @media screen and (max-width: 992px) and (max-height: 568px) {
                padding-top: 10px !important;
            }
            @media screen and (max-width: 992px) and (max-height: 480px) {
                padding-top: 30px !important;
            }
        }
        .cls-heading-description{
            font-weight: 600;
            line-height: 36px;
            text-align: left;
            color: #006867;
            max-width: 520px;
            .invest{
                color: #FF0000;
            }
            @media screen and (max-width: 992px) {
                font-size: 20px !important;
                font-weight: 600;
                line-height: 30px;
                text-align: center;
                padding: 0 30px;
                .invest{
                    font-size: 20px !important;
                    font-weight: 600;
                    line-height: 30px;
                    text-align: center;
                }
            }
        }
        .cls-banner{
            padding-bottom: 60px;
            img{
                object-fit: cover;
                width: 100%;
                height: auto;
                margin-top: -40px;
                @media screen and (max-width: 992px) {
                    max-width: 80%;
                }
                @media screen and (max-width: 992px) and (max-height: 884px) {
                    max-width: 100%;
                }
                @media screen and (max-width: 992px) and (max-height: 667px) {
                    max-width: 80%;
                }
            }
            @media screen and (max-width: 992px) {
                padding-top: 30px;
                padding-bottom: 20px;
                order: 0;
                flex: 1;
            }
        }
        .col-flex-1{
            @media screen and (max-width: 992px) {
                flex: 1;
            }
        }
    }

    .cls-profits-block{
        .cls-profits-items{
            .cls-card{
                margin-bottom: 30px;
                @media screen and (max-width: 992px) {
                    padding: 0 7px;
                }
                .cls-card-item{
                    display: flex;
                    align-items: flex-start;
                    justify-content: start;
                    box-shadow: 0px 4px 4px 0px #00000040;
                    border-radius: 10px;
                    overflow: hidden;
                    height: 100%;
                    position: relative;
                    &::before{
                        background-image: url("/pages/public_assets/landing-wa-id/background-invest-1.png");
                        content: "";
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: right bottom;
                        height: 100%;
                        position: absolute;
                        width: 110%;
                        z-index: 0;
                        opacity: 0.5;
                        right: 0;
                        bottom: 1px;
                        border-radius: 10px;
                        @media screen and (max-width: 992px) {
                            right: -38%;
                        }
                    }
                    @media screen and (max-width: 992px) {
                        flex-direction: row;
                        align-items: flex-end;
                        justify-content: center;
                    }
                    .cls-left{
                        // width: 50%;
                        padding: 20px 0 0 20px;
                        @media screen and (max-width: 992px) {
                            padding: 15px 25px;
                        }
                        .cls-heading{
                            font-weight: 382;
                            line-height: 33.6px;
                            text-align: left;
                            color: #fff;
                            label{
                                color: #fff;
                                font-weight: 800;
                                display: block;
                            }
                            @media screen and (max-width: 992px) {
                                font-weight: 500;
                                font-size: 20px !important;
                                text-align: center;
                                line-height: 24px;
                                label{
                                    font-size: 20px !important;
                                    font-weight: 800;
                                    line-height: 24px;
                                    display: inline-block;
                                    margin: 0;
                                }
                            }
                        }

                    }
                    .cls-right{
                        min-height: 320px;
                        padding: 5px;
                        @media screen and (max-width: 992px) {
                            height: 220px;
                            display: flex;
                            flex-direction: column;
                            align-items: flex-end;
                            justify-content: end;
                            min-height: auto;
                        }
                        
                        img{
                            position: absolute;
                            bottom: 10px;
                            // width: 80%;
                            height: 80%;
                            right: 10px;
                            z-index: 2;
                            @media screen and (max-width: 992px) {
                                position: unset;
                                height: 90%;
                                width: auto;
                                margin: 0 auto;
                                margin-bottom: 10px;
                            }
                        }
                    }
                }
            }
            .cls-card-1{
                .cls-card-item{
                    background-color: #E93661;
                }
            }
            .cls-card-2{
                .cls-card-item{
                    background-color: #08A687;
                    &::before{
                        background-image: url("/pages/public_assets/landing-wa-id/background-invest-2.png");
                    }
                    .cls-right{
                        img{
                            right: 40px;
                        }
                    }
                }
            }
            .cls-card-3{
                .cls-card-item{
                    background-color: #0FA0CE;
                    &::before{
                        background-image: url("/pages/public_assets/landing-wa-id/background-invest-3.png");
                    }
                }
            }
            .cls-card-4{
                .cls-card-item{
                    background-color: #343644;
                    &::before{
                        background-image: url("/pages/public_assets/landing-wa-id/background-invest-3.png");
                    }
                }
            }
        }
    }

    .goro-block-invest{
        background-image: url("/pages/public_assets/landing-wa-id/Background-Footer-Web.svg");
        background-size: cover;
        background-repeat: no-repeat;
        background-position: top;
        @media screen and (max-width: 992px) {
            background-image: url("/pages/public_assets/landing-wa-id/Background-Footer-Mobile-Web.svg");
        }
        .join-goro{
            background-color: transparent !important;
            padding: 2rem !important;
            border: none !important;
            box-shadow: none !important;
            @media screen and (max-width: 992px) {
                &.join-goro-message{
                    padding-bottom: 10px !important;
                }
                &.join-goro-btn{
                    padding-top: 10px !important;
                }
            }
            .cls-invest-heading{
                font-weight: 700;
                line-height: 64px;
                text-align: center;
                color: #fff;
                text-shadow: 1px 1px 10px #000000;
                @media screen and (max-width: 992px) {
                    font-size: 22px;
                    font-weight: 700;
                    line-height: 24px;
                    text-align: center;
                }
            }
            .cls-description{
                font-size: 40px;
                font-weight: 500;
                line-height: 48px;
                text-align: center;
                text-shadow: 1px 1px 10px #000000;
                color: #fff;
                @media screen and (max-width: 992px) {
                    font-size: 14px;
                    line-height: 24px;
                }
            }
            .cls-invest-btn{
                padding: 33px 65px 33px 65px;
                border-radius: 80px !important;
                font-weight: 700;
                line-height: 50px;
                text-align: center;
                color: #fff;
                background-color: #FD4755;
                border: 4px solid #FFFFFF;
                &:hover{
                    opacity: .8;
                }
                &:focus,
                &:active{
                    color: #fff !important;
                    background-color: #FD4755 !important;
                    border: 4px solid #FFFFFF !important;
                }
                @media screen and (max-width: 992px) {
                    font-size: 20px;
                    font-weight: 700;
                    line-height: 16px;
                    text-align: center;
                    padding: 16px 32px;
                }
            }
        }
    }

    .goro-testimonials-block{
        border-radius: 80px 80px 0 0;
        @media screen and (max-width: 992px) {
            border-radius: 0 !important;
        }
        .cls-custom-carousel-container{
            min-height: 385px;
            @media screen and (max-width: 992px) {
                min-height: auto;
            }
            .horizontal-navigation{
                display: flex !important;
                @media screen and (max-width: 992px) {
                    display: none !important;
                }
            }
        }
        .cls-testimonial-items{
            .cls-card-item{
                padding-left: 7px;
                padding-right: 7px;
                .cls-card-item-content{
                    box-shadow: 0px 0px 4px 0px #00000040;
                    padding: 16px 16px 30px 16px;
                    border-radius: 12px;
                    background-color: #fff;
                    height: 100%;
                    .cls-reviewer{
                        img{
                            width: 100px;
                            height: 100px;
                            border-radius: 100%;
                            margin-right: 15px;
                        }
                        .cls-reviewer-info{
                            .text-name{
                                line-height: 28.8px;
                                text-align: left;
                                color: #333333;
                                @media screen and (max-width: 992px) {
                                    margin-bottom: 0;
                                }
                            }
                            .text-position{
                                font-weight: 300;
                                line-height: 19.2px;
                                text-align: left;
                                color: #333333;
                                @media screen and (max-width: 992px) {
                                    font-size: 14px !important;
                                }
                            }
                        }
                    }
                    .cls-message{
                        p{
                            font-weight: 500;
                            line-height: 20px;
                            text-align: left;
                            color: #333333;
                            @media screen and (max-width: 992px) {
                                font-size: 14px !important;
                            }
                        }
                    }
                }
                &.carousel__slide--active{
                    @media screen and (min-width: 1200px) {
                        .cls-card-item-content{
                            .cls-message{
                                p{
                                    font-size: 20px !important;
                                    font-weight: 500 !important;
                                    line-height: 25px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .goro-benefit-block{
        .cls-protift-table-wrapper{
            .cls-heading{
                font-weight: 700;
                line-height: 33.73px;
                text-align: center;
                color: #333333;
            }
            .cls-sub-heading{
                font-weight: 500;
                line-height: 33.73px;
                text-align: center;
                color: #707070;
            }
        }
    }

    .goro-property-catalog-block{
        .cls-highlight-heading{
            text-align: center;
            margin: 0 auto;
            display: inline-block;
            border-radius: 10px;
            background-color: #282522;
            padding-bottom: 6px;
            h2.cls-heading{
                border-radius: 10px;
                background-color: #E0990A;
                padding: 5px;
                position: relative;
                p.cls-heading-content{
                    line-height: 46px;
                    text-align: center;
                    color: #fff;
                    padding: 10px 25px 12px 25px;
                    border: 1px solid #FFFFFF;
                    border-radius: 10px;
                    @media screen and (max-width: 992px) {
                        font-size: 24px;
                        font-weight: 700;
                        line-height: 30px;
                        text-align: center;
                    }
                }
            }
        }
        .cls-card-items{
            .cls-card-item{
                padding: 0 7px;
            }
        }
    }
    &.new-goro-landing-wa-id-page{
        .goro-block{
            .cls-h2-heading{
                color: #006867;
            }
        }
        .goro-banner-block{
            background-color: #CBECEE !important;
            .goro-banner-row{
                @media screen and (max-width: 992px) {
                    min-height: calc(100vh - 90px);
                }
            }
        }
        .cls-profits-block{
            background-color: #CBECEE !important;
        }
        .goro-property-catalog-block{
            background-color: #CBECEE !important;
            .cls-highlight-heading{
                h2.cls-heading{
                    background-color: #006867;
                }
            }
            .cls-sub-heading{
                color: #006867;
            }
        }
        .goro-testimonials-block{
            background-color: #CBECEE !important;
            h2.cls-heading{
                color: #006867;
            }
        }
    }
}

.show-hide {
    width: 40px;
    background-color: rgba(0, 102, 102, 0.9);
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    transition: 0.5s;

    &:hover {
        background-color: rgba(0, 102, 102, 1);
    }
}

.join-goro {
    padding: 2.1rem;
    margin-top: 3.1rem;
    margin-bottom: 1.6rem;
    background-color: white;
    -webkit-box-shadow: 0 5px 20px rgba(7, 55, 99, 0.16);
    box-shadow: 0 5px 20px rgba(7, 55, 99, 0.16);
    border-radius: 32px;
}
</style>
