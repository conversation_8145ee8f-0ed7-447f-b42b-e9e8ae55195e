  const path = require('path')
const CompressionPlugin = require("compression-webpack-plugin");

const myCompressionPlug = new CompressionPlugin({
  algorithm: "gzip",
  test: /\.js$|\.css$|\.png$|\.svg$|\.jpg$|\.woff2$/i,
  deleteOriginalAssets: false,
});

module.exports = {
  lintOnSave: false,
  devServer: {
    client: {
      overlay: false,
    },
  },
  pluginOptions: {
    i18n: {
      locale: "en",
      fallbackLocale: "en",
      localeDir: "locales",
      enableInSFC: false,
    },
  },
  chainWebpack: (config) => {
    // --- vue-recaptcha ---
      config.module
          .rule('ts')
          .test(/\.ts$/)
          .include
          .add(path.resolve(__dirname, 'src'))
          .add(path.resolve(__dirname, 'node_modules/vue-recaptcha'))
          .end()
          .use('ts-loader')
          .loader('ts-loader')
          .options({
              transpileOnly: true,
              appendTsSuffixTo: [/\.vue$/]
          })
    // --- end vue-recaptcha ---

    config.resolve.alias.set("vue", "@vue/compat");
    config.plugin("CompressionPlugin").use(myCompressionPlug);
    config.plugin("define").tap((definitions) => {
      Object.assign(definitions[0], {
        // ... rest of your injected vars here
        // get rid of vue-i18n warning
        __VUE_I18N_FULL_INSTALL__: JSON.stringify(true),
        __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
        __VUE_I18N_LEGACY_API__: JSON.stringify(false),
      });

      return definitions;
    });

    config.module
      .rule("vue")
      .use("vue-loader")
      .tap((options) => {
        return {
          ...options,
          compilerOptions: {
            compatConfig: {
              MODE: 2,
            },
          },
        };
      });
  },
  configureWebpack: {
    plugins: [],
    optimization: {
      splitChunks: {
        cacheGroups: {
          default: false,
          vendors: false,
          vue: {
            chunks: "all",
            test: /[\\/]node_modules[\\/]((vue).*)[\\/]/,
            priority: 20,
          },
          bootstrap: {
            chunks: "all",
            test: /[\\/]node_modules[\\/]((bootstrap).*)[\\/]/,
            priority: 20,
          },
          vendor: {
            chunks: "all",
            test: /[\\/]node_modules[\\/]((?!(vue|bootstrap|apollo)).*)[\\/]/,
            priority: 20,
          },
          // common chunk
          common: {
            test: /[\\/]src[\\/]/,
            minChunks: 2,
            chunks: "all",
            priority: 10,
            reuseExistingChunk: true,
            enforce: true,
          },
        },
      },
    },
  },
};
