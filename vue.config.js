  const path = require('path')
const CompressionPlugin = require("compression-webpack-plugin");

const myCompressionPlug = new CompressionPlugin({
  algorithm: "gzip",
  test: /\.js$|\.css$|\.png$|\.svg$|\.jpg$|\.woff2$/i,
  deleteOriginalAssets: false,
});

module.exports = {
  lintOnSave: false,
  css: {
    extract: {
      ignoreOrder: true
    }
  },
  devServer: {
    client: {
      overlay: false,
    },
  },
  pluginOptions: {
    i18n: {
      locale: "en",
      fallbackLocale: "en",
      localeDir: "locales",
      enableInSFC: false,
    },
  },
  chainWebpack: (config) => {
    // --- vue-recaptcha ---
      config.module
          .rule('ts')
          .test(/\.ts$/)
          .include
          .add(path.resolve(__dirname, 'src'))
          .add(path.resolve(__dirname, 'node_modules/vue-recaptcha'))
          .end()
          .use('ts-loader')
          .loader('ts-loader')
          .options({
              transpileOnly: true,
              appendTsSuffixTo: [/\.vue$/]
          })
    // --- end vue-recaptcha ---

    config.resolve.alias.set("vue", "@vue/compat");
    config.plugin("CompressionPlugin").use(myCompressionPlug);
    config.plugin("define").tap((definitions) => {
      Object.assign(definitions[0], {
        // ... rest of your injected vars here
        // get rid of vue-i18n warning
        __VUE_I18N_FULL_INSTALL__: JSON.stringify(true),
        __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
        __VUE_I18N_LEGACY_API__: JSON.stringify(false),
      });

      return definitions;
    });

    config.module
      .rule("vue")
      .use("vue-loader")
      .tap((options) => {
        return {
          ...options,
          compilerOptions: {
            compatConfig: {
              MODE: 2,
            },
          },
        };
      });
  },
  configureWebpack: {
    plugins: [],
    performance: {
      hints: 'warning',
      maxAssetSize: 500000, // 500kb
      maxEntrypointSize: 1000000, // 1MB
      assetFilter: function(assetFilename) {
        // Don't warn about large fonts and specific large images
        return !(/\.(woff2?|ttf|otf|eot)$/.test(assetFilename) ||
                /general_bg\.png$/.test(assetFilename) ||
                /tab_sultan_selected\.svg$/.test(assetFilename) ||
                /Acumin-Variable-Concept\.(ttf|otf)$/.test(assetFilename));
      }
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        maxInitialRequests: 20,
        maxAsyncRequests: 20,
        cacheGroups: {
          default: false,
          vendors: false,
          // Vue and related libraries
          vue: {
            chunks: "all",
            test: /[\\/]node_modules[\\/]((vue|@vue).*)[\\/]/,
            priority: 30,
            name: 'vue-vendor',
          },
          // UI libraries
          ui: {
            chunks: "all",
            test: /[\\/]node_modules[\\/]((bootstrap|element-ui|vuetify).*)[\\/]/,
            priority: 25,
            name: 'ui-vendor',
          },
          // Large libraries
          large: {
            chunks: "all",
            test: /[\\/]node_modules[\\/]((lodash|moment|axios|apollo).*)[\\/]/,
            priority: 25,
            name: 'large-vendor',
          },
          // Other vendor libraries
          vendor: {
            chunks: "all",
            test: /[\\/]node_modules[\\/]/,
            priority: 20,
            name: 'vendor',
            minChunks: 1,
          },
          // Common application code
          common: {
            test: /[\\/]src[\\/]/,
            minChunks: 2,
            chunks: "all",
            priority: 10,
            reuseExistingChunk: true,
            enforce: true,
            name: 'common',
          },
        },
      },
    },
  },
};
