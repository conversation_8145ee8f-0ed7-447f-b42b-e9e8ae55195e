import { getCurrentInstance, reactive } from 'vue'
import { useChallengeV2, useChallengeV3, useRecaptchaProvider } from 'vue-recaptcha/head'
import { notify } from '@/helpers/common'

/**
 * @param {Object} recaptchaActions
 *   e.g. { FORGOT_PASSWORD: 'forgotPassword', LOGIN: 'login' }
 */
export function useRecaptcha(recaptchaActions) {
    useRecaptchaProvider()

    // ── RECAPTCHA V2 ────────────────────────────────────────────
    const vm = getCurrentInstance().proxy
    const showRecaptchaV2 = reactive(
        Object.fromEntries(Object.keys(recaptchaActions).map(k => [k, false]))
    )
    const recaptchaTokenV2 = reactive(
        Object.fromEntries(Object.keys(recaptchaActions).map(k => [k, null]))
    )
    const recaptchaV2Checkbox = {}
    const validateRecaptchaV2 = {}
    const resetRecaptchaV2 = {}
    Object.keys(recaptchaActions).forEach(key => {
        const {
            root,
            onVerify,
            onError,
            onExpired
        } = useChallengeV2({ options: { size: 'normal', theme: 'light' } })
        onVerify(tokenV2 => {
            recaptchaTokenV2[key] = tokenV2
        })
        onError(err => {
            notify({ text: vm.$t('RECAPTCHA.ERROR'), type: 'error' })
        })
        onExpired(() => {
            notify({ text: vm.$t('RECAPTCHA.EXPIRED'), type: 'error' })
        })
        validateRecaptchaV2[key] = () => {
            if (showRecaptchaV2[key] && !recaptchaTokenV2[key]) {
                notify({ text: vm.$t('RECAPTCHA.REQUIRED'), type: 'error' })
                return false
            }
            return true
        }
        resetRecaptchaV2[key] = () => {
            if (recaptchaTokenV2[key]) {
                showRecaptchaV2[key] = false
                recaptchaTokenV2[key] = null
            }
        }
        recaptchaV2Checkbox[key] = root
    })

    // ── RECAPTCHA V3 ────────────────────────────────────────────
    const rawExecV3 = {}
    const recaptchaV3Exec = {}
    Object.entries(recaptchaActions).forEach(([key, action]) => {
        const { execute } = useChallengeV3(action)
        rawExecV3[key] = execute
    })
    Object.keys(rawExecV3).forEach(key => {
        recaptchaV3Exec[key] = async () => {
            if (recaptchaTokenV2[key]) {
                return null
            }
            return rawExecV3[key]()
        }
    })

    return {
        recaptchaV3Exec,
        recaptchaV2Checkbox,
        recaptchaTokenV2,
        showRecaptchaV2,
        validateRecaptchaV2,
        resetRecaptchaV2
    }
}
