const REGEX = {
  LEGAL_NAME: /^[a-zA-Z,.']+(?:\s[a-zA-Z,.']+)*$/
}

const INDO = {
  CALLING_CODE: "+62",
  COUNTRY_CODE: "62",
  ISO_COUNTRY_CODE: "ID",
  CURRENCY: "IDR",
}

const FOREIGNER = {
  LOCALE: "en-UK",
  CURRENCY: "USD",
  ROUTE_PATH_PREFIX: "/en",
  ROUTE_NAME_SUFFIX: "Foreigner"
}
const OTP_METHOD = {
  INPUT_OTP: "input_otp",
  REVERSE_OTP: "reverse_otp",
}
const OTP_TYPE = {
  SMS: "sms",
  RADIST: "waba_radist",
  TWILIO: "waba_twilio",
}
const OTP_STATUS = {
  QUEUED: "Queued",
  DELIVERED: "Delivered",
  UNDELIVERED: "Undelivered",
  ERROR: "Error",
  READ: "Read",
  FAILED: "Failed",
  SENT: "Sent",
}
const STATUS = {
  WAITING: "WAITING",
  PENDING: "PENDING",
  APPROVED: "APPROVED",
  REJECTED: "REJECTED",
  RECEIVED: "RECEIVED",
  FAILED: "FAILED",
  PAID: "PAID",
  EXPIRED: "EXPIRED",
  COMPLETED: "COMPLETED",
  REFUNDED: "REFUNDED",
  CANCELLED: "CANCELLED",
  SWAPPED: "SWAPPED",
  UNCLAIM: "UNCLAIM"
}

const PROPERTY_APPROVAL_STATUS = {
  PRE_APPROVED: "PRE_APPROVED",
  DRAFT: "DRAFT",
  PENDING: "PENDING",
  APPROVED: "APPROVED"
}

const PROPERTY_APPROVAL_STATUS_LIST = [
  { value: "", text: "Property Approval Status" },
  { value: "PRE_APPROVED", text: "Pre Approved" },
  { value: "DRAFT", text: "Draft" },
  { value: "PENDING", text: "Pending" },
  { value: "APPROVED", text: "Approved" },
]

const STATUS_CODE = {
  HTTP_UNAUTHORIZED: 401,
  HTTP_NOT_FOUND: 404,
  HTTP_TOO_EARLY: 425,
  HTTP_TOO_MANY_REQUESTS: 429,
  HTTP_UPGRADE_REQUIRED: 426,
  HTTP_PRECONDITION_REQUIRED: 428,
}

const TRANSACTION_TYPE = {
  ALL: "ALL",
  BUY_TOKEN: "BUY_TOKEN",
  ORDER_TOKEN: "ORDER_TOKEN",
  SELL_TOKEN: "SELL_TOKEN",
  REFERRAL_BONUS: "REFERRAL_BONUS",
  REFERRAL_BONUS_TOKEN: "REFERRAL_BONUS_TOKEN",
  WITHDRAWAL: "WITHDRAWAL",
  RENTAL_DISTRIBUTION: "RENTAL_DISTRIBUTION",
  ADMIN_ADD_BALANCE: "ADMIN_ADD_BALANCE",
  BUY_TOKEN_VIRTUAL_BALANCE: "BUY_TOKEN_VIRTUAL_BALANCE",
  VIRTUAL_RENTAL_DISTRIBUTION: "VIRTUAL_RENTAL_DISTRIBUTION",
  ADMIN_DEDUCT_BALANCE: "ADMIN_DEDUCT_BALANCE",
  SYSTEM_ADD_BALANCE: "SYSTEM_ADD_BALANCE",
}

const PENDING_TASK_TYPE = {
  AGREE_TO_CONTRACT: "AGREE_TO_CONTRACT",
  CLAIM_VIRTUAL_RENTAL_INCOME: "CLAIM_VIRTUAL_RENTAL_INCOME",
  CLAIM_REFERRAL_BONUS: "CLAIM_REFERRAL_BONUS"
}

const TASK_STATUSES = {
  PENDING: "PENDING",
  EXPIRED: "EXPIRED",
  COMPLETED: "COMPLETED",
}

const ERROR_CODE = {
  KYC_REQUIRED: "ERROR_KYC_REQUIRED",
  RECAPTCHA_ERROR_INVALID_TOKEN: 'RECAPTCHA_ERROR_INVALID_TOKEN',
  RECAPTCHA_ERROR_INVALID_TOKEN_V2: 'RECAPTCHA_ERROR_INVALID_TOKEN_V2',
  RECAPTCHA_ERROR_SCORE_TOO_LOW: 'RECAPTCHA_ERROR_SCORE_TOO_LOW',
  RECAPTCHA_ERROR_ACTION_DOES_NOT_MATCH: 'RECAPTCHA_ERROR_ACTION_DOES_NOT_MATCH',
}

const FINANCIAL_SUBHEADER = {
  ASSET_VALUE: "ASSET_VALUE",
  ANNUAL_RETURN: "ANNUAL_RETURN",
}

const PAYMENT_METHOD = {
  XENDIT: "XENDIT",
  STRIPE: "STRIPE"
}

const WITHDRAWAL_METHOD = {
  XENDIT: "XENDIT",
  PAYPAL: "PAYPAL",
  BANK_ACCOUNT: "BANK_ACCOUNT"
}

const BANK_TYPE = {
  FOREIGNER: "FOREIGNER",
  INDO_USER: "INDO_USER"
}

const PROPERTY_STATUSES = {
  SOLD: "sold"
}

const PROPERTY_STATUSES_LIST = [
  { value: "", text: "Property status" },
  { value: "available", text: "Available" },
  { value: "promo", text: "Promo" },
  { value: "presale", text: "Presale" },
  { value: "sold", text: "Sold Out" },
  { value: "coming_soon", text: "Coming Soon" },
  { value: 'draft', text: 'Draft' },
]

const PROPERTY_CUSTOM_ACTIONS = {
  UPDATE_RENTAL_DISTRIBUTION: {
    label: 'Rental Distribution',
    key: 'update-rental-distribution'
  },
  VIEW_PROPERTY_HOLDER: {
    label: 'Holders',
    key: 'view-property-holder'
  },
  VIEW_PROPERTY_IMAGE: {
    label: 'Images Visibility',
    key: 'view-property-image'
  },
  VIEW_PROPERTY_MILESTONE: {
    label: 'Milestone',
    key: 'view-all-milestone'
  },
  VIEW_ALL_PROPERTY_FINANCIALS: {
    label: 'Financials',
    key: 'view-all-property-financials'
  },
  UPDATE_PROPERTY_STATUS_AND_TOKEN: {
    label: 'Update Status and Token',
    key: 'update-property-status-and-token'
  },
  VIEW_PROPERTY: {
    label: 'View',
    key: 'view-property'
  },
  UPDATE_PROPERY_DETAIL: {
    label: 'Modify',
    key: 'update-property-detail'
  },
  COPY_PROPERTY: {
    label: 'Copy',
    key: 'copy-property'
  },
  APPROVE_PROPERTY_PUBLISH: {
    label: 'Approve',
    key: 'approve-property-publish'
  },
}

const USER_CUSTOM_ACTIONS = {
  VIEW_USER_ASSETS: {
    label: 'Assets',
    key: 'view-user-assets'
  },
  VIEW_USER_VIRTUAL_ASSETS: {
    label: 'Virtual Assets',
    key: 'view-user-virtual-assets'
  },
  VIEW_USER_KTP_PASSPORT: {
    label: 'KTP/Passport',
    key: 'view-user-ktp-passport'
  },
  VIEW_USER_VERIFY_SELFIE: {
    label: 'Verify Selfie',
    key: 'view-user-verify-selfie'
  },
}

const PROPERTY_SHOW_MODE_STATUS = {
  ALL: "all"
}

const TRANSACTION_CUSTOM_ACTIONS = {
  VIEW_TRANSACTION_DETAILS: {
    label: 'Details',
    key: 'view-transaction-detail'
  },
}

const BLOCKCHAIN = {
  POLYGON_NETWORK: process.env.VUE_APP_POLYGON_NETWORK,
  POLYGON_SCAN_ADDRESS_URL: process.env.VUE_APP_POLYGON_SCAN_ADDRESS_URL,
  POLYGON_SCAN_TRANSACTION_URL: process.env.VUE_APP_POLYGON_SCAN_TRANSACTION_URL,
}

const ROUTE_META_PAGE_TYPES = {
  ADMIN: 'ADMIN',
  ACCOUNT: 'ACCOUNT',
  GUEST: 'GUEST'
}

const ADMIN_LOGS_CUSTOM_ACTIONS = {
  SHOW_CHANGES: {
    label: 'Show Changes',
    key: 'show-changes'
  }
}

const PARTNER_PICK_DATE_VALUES = {
  DEFAULT: 31
}

const DATE_TIME_FORMATS = {
  PICK_DATE: "DD/MM/YYYY"
}

const STORAGE_KEYS = {
  AUTHORIZATION: { key: "Authorization", type: "standard" },
  SELL_TOKEN_TRANSACTION_FEE_CONFIRMATION: { key: "sell_token_transaction_fee_confirmation", type: "standard" },
  SWAP_TOKEN_TRANSACTION_FEE_CONFIRMATION: { key: "swap_token_transaction_fee_confirmation", type: "standard" },

  EVENT_ACTIVES: { key: "active_events", type: "permanent" },
  REFERRAL_CODE: { key: "referral_code", type: "permanent" },
  PREFERRED_LOCALE: { key: "preferred_locale", type: "permanent" },
  PRE_REGISTERED_UUID: { key: "pre_registered_uuid", type: "permanent" },
  SANDBOX_MODAL_EXPIRY: { key: "sandbox_modal_expiry", type: "permanent" },
}

const STORAGE_ACTION_KEYS = {
  GET_UNREAD_COUNT: "get-unread-count",
  GET_PLAYLISTS: "get-playlists",
  GET_BANKS: "get-banks",
}

const DOCUMENT_VERSIONING_TYPES = {
  USER_CONSENT: "USER_CONSENT",
  PRIVACY_POLICY: "PRIVACY_POLICY",
  TERMS_AND_CONDITION: "TERMS_AND_CONDITION"
}

const EVENT_TYPES = {
  TYPE_STANDARD: "STANDARD",
  TYPE_SULTAN_JURAGAN: "SULTAN_JURAGAN",
  TYPE_REFERRAL: "REFERRAL"
}

const EVENT_STATUS = {
  UPCOMING: "Upcoming",
  ONGOING: "Ongoing",
  ENDED: "Ended",
}

const KYC_CARD_TYPES = {
  INDONESIA_KTP: 'indonesia_ktp',
  PASSPORT: 'passport'
}

const PRE_REGISTERED_STATUSES = {
  VERIFY_OTP: "VERIFY_OTP",
  UPDATE_INFO: "UPDATE_INFO",
  REVIEW_INFO: "REVIEW_INFO",
  PARENTAL_KYC: "PARENTAL_KYC",
  PARENTAL_KYC_REVIEWED: "PARENTAL_KYC_REVIEWED",
  PARENTAL_KYC_SUCCESS: "PARENTAL_KYC_SUCCESS",
  PARENTAL_KYC_FAILED: "PARENTAL_KYC_FAILED",
  CREATE_PASSWORD: "CREATE_PASSWORD",
  COMPLETED: "COMPLETED",
}

const MARITAL_STATUS = {
  SINGLE: 'single',
  MARRIED: 'married',
  DIVORCED: 'divorced',
  WIDOWED: 'widowed',
}

const CONFIG = {
  FEE_TYPE_BALANCE: 'BALANCE',
  FEE_TYPE_PERCENTAGE: 'PERCENTAGE',
}

const VOUCHER = {
  TYPE_SINGLE: 'SINGLE',
  TYPE_MULTIPLE: 'MULTIPLE',

  REWARD_TYPE_REDUCE_ANY_FEES: 'REDUCE_ANY_FEES',
  // Buy
  REWARD_TYPE_DISCOUNT: 'DISCOUNT',
  REWARD_TYPE_REDUCE_FEE_BUY: 'REDUCE_FEE_BUY',
  REWARD_TYPE_REDUCE_TRANSACTION_FEE_BUY: 'REDUCE_TRANSACTION_FEE_BUY',
  REWARD_TYPE_REDUCE_ANY_FEES_BUY: 'REDUCE_ANY_FEES_BUY',
  // Sell
  REWARD_TYPE_REDUCE_TRANSACTION_FEE_SELL: 'REDUCE_TRANSACTION_FEE_SELL',
  // Swap
  REWARD_TYPE_REDUCE_TRANSACTION_FEE_SWAP: 'REDUCE_TRANSACTION_FEE_SWAP',
  // Withdrawal
  REWARD_TYPE_REDUCE_FEE_WITHDRAWAL: 'REDUCE_FEE_WITHDRAWAL',
  //REWARD_TYPE_REDUCE_TRANSACTION_FEE_WITHDRAWAL: 'REDUCE_TRANSACTION_FEE_WITHDRAWAL',

  REWARD_AMOUNT_UNIT_BALANCE: 'BALANCE',
  REWARD_AMOUNT_UNIT_PERCENTAGE: 'PERCENTAGE',

  REDEEM_ACTION_PAYMENT: "PAYMENT",
  REDEEM_ACTION_ORDER: "ORDER",
  REDEEM_ACTION_SELL: "SELL",
  REDEEM_ACTION_SWAP: "SWAP",
  REDEEM_ACTION_WITHDRAWAL: "WITHDRAWAL",
}

export {
  REGEX,
  INDO,
  FOREIGNER,
  OTP_METHOD,
  OTP_TYPE,
  OTP_STATUS,
  STATUS,
  STATUS_CODE,
  TRANSACTION_TYPE,
  PENDING_TASK_TYPE,
  ERROR_CODE,
  FINANCIAL_SUBHEADER,
  PAYMENT_METHOD,
  WITHDRAWAL_METHOD,
  BANK_TYPE,
  PROPERTY_STATUSES,
  PROPERTY_STATUSES_LIST,
  PROPERTY_CUSTOM_ACTIONS,
  USER_CUSTOM_ACTIONS,
  PROPERTY_SHOW_MODE_STATUS,
  BLOCKCHAIN,
  ROUTE_META_PAGE_TYPES,
  ADMIN_LOGS_CUSTOM_ACTIONS,
  PARTNER_PICK_DATE_VALUES,
  DATE_TIME_FORMATS,
  TRANSACTION_CUSTOM_ACTIONS,
  STORAGE_KEYS, STORAGE_ACTION_KEYS,
  TASK_STATUSES,
  DOCUMENT_VERSIONING_TYPES,
  PROPERTY_APPROVAL_STATUS,
  PROPERTY_APPROVAL_STATUS_LIST,
  EVENT_TYPES, EVENT_STATUS,
  KYC_CARD_TYPES,
  PRE_REGISTERED_STATUSES,
  MARITAL_STATUS,
  CONFIG, VOUCHER,
}
