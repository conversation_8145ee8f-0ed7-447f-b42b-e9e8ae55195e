<!-- CalendlyWidgetLoader.vue -->
<template>
  <div class="calendly-inline-widget" data-url="https://calendly.com/robert-hoving"
       style="min-width:320px;height:700px;"></div>
</template>

<script>
export default {
  mounted () {
    // Load the external scripts when the component is mounted
    this.loadExternalScripts()
  },
  methods: {
    loadExternalScripts () {
      const script = document.createElement("script")
      script.src = "https://assets.calendly.com/assets/external/widget.js"
      script.type = "text/javascript"
      script.async = true
      document.head.appendChild(script)
    },
  },
}
</script>
