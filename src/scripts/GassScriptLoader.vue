<template>
  <!-- Empty div placeholder -->
  <div></div>
</template>

<script>
import gassService from "../services/gass.service"

export default {
  mounted () {
    this.loadExternalScript()
  },
  methods: {
    loadExternalScript (campaign_id = 1) {
      let self = this
      const script = document.createElement("script")
      script.src = "https://gass.co.id/gassv3.min.js?v=3"
      document.head.appendChild(script)
      
      setTimeout(function () {
        const scriptContent = `gass.run({campaign_id:${campaign_id}, subdomain:'wa.goro.id', interval:2});`
        const scriptCampaign = document.createElement("script")
        scriptCampaign.type = "text/javascript"
        scriptCampaign.text = scriptContent
        scriptCampaign.async = true
        scriptCampaign.id = "gass_campaign_id_" + campaign_id
        document.head.appendChild(scriptCampaign)

        self.afterGassLoaded()
      }, 1000)
    },
    afterGassLoaded () {
      setTimeout(function () {
        if (window.gass && window.gass.vid) {
          console.log("GassScriptLoaded: loadExternalScript", { gass_vid: window.gass.vid })
          gassService.storeInfo({ gass_vid: window.gass.vid }).then()
        }
      }, 2000);
    },
  },
}
</script>
