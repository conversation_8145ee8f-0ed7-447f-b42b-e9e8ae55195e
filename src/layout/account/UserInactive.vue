<template>
	<div class="container">
		<div class="content d-flex flex-column align-items-center mt-5">
			<p class="inactive-text">{{ $t('account.ACCOUNT_IS_INACTIVE') }}</p>
			<p v-if="!referUser && !isLoading" class="referral-text mt-2">{{ $t('REFERRAL.PLEASE_INPUT_REFERRAL_CODE') }}</p>
			<p v-if="referUser && !isLoading" class="referral-text mt-2">{{ useReferralCodeMessage }}</p>
			<div class="form col-md-12 col-lg-7 d-flex flex-row mt-4">
				<input :disabled="referUser" type="text" v-model="code">
				<button id="btn_submitReferralCode" type="button" @click="submitReferralCode">{{ $t('account.submit') }}</button>
			</div>
			<p class="mt-3" v-html="supportMessage"></p>
		</div>
	</div>
</template>

<script>
import { PAYMENT_METHOD } from '../../constants/constants';

import externalSites from '../../constants/externalSites';
import commonService from "../../services/common.service";
import store from '../../store/store';

export default {
	data() {
		return {
			code: this.$route.query.code,
			contact: externalSites.CUSTOMER_EMAIL,
			contactMailTo: externalSites.MAIL_TO.CUSTOMER_SUPPORT,
			referUser: null,
		};
	},

	async mounted() {
		await this.getReferralUser();
	},

	methods: {
		async submitReferralCode() {
      if (this.code && this.code !== '') {
				await commonService.checkTransactionAccess(this.code);
				await store.dispatch('refreshUserProfile');
				await store.dispatch('getReferralCode');
			}
		},

		async getReferralUser() {
			if (this.referralCode != null) {
				const response = await commonService.getReferralInviter(this.referralCode)
				if (response.name) {
					this.referUser = response.name
				}
			}
		},
	},

	computed: {
		referralCode() {
			return this.$route.query.code
		},

		isLoading() {
			return this.$store.getters.isLoading;
		},

		userProfile() {
			return this.$store.getters.userProfile;
		},

		userPaymentMethodStripe() {
			if (this.userProfile && this.userProfile.payment_method === PAYMENT_METHOD.STRIPE) {
				return true
			}
			return false
		},

		useReferralCodeMessage() {
			return this.$t('REFERRAL.USE_THIS_REFERRAL_CODE_FROM', { value: this.referUser });
		},

		supportMessage() {
			let whatsapp = externalSites.WHATSAPP_SUPPORTS['id']
			if (this.userPaymentMethodStripe) {
				whatsapp = externalSites.WHATSAPP_SUPPORTS['en-UK']
			}
			return `${ this.$t('account.FOR_CUSTOMER_SUPPORTING', {
				whatsapp,
				contactMailTo: this.contactMailTo,
				contact: this.contact
			}) }`
		}
	},
}
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	margin-top: 20px;

	.content {
		background-color: white;
		box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
		border-radius: 16px;
		padding: 20px;

		.inactive-text {
			font-size: 25px;
			font-weight: 700;
			text-align: center;
		}

		.referral-text {
			font-size: 18px;
			text-align: center;
		}

		.form {
			min-height: 50px;

			input {
				width: 100%;
				background-color: #cacee2;
				border: none;
				border-radius: 30px 0px 0px 30px;
				padding-left: 1rem;
				padding-right: 1rem;
				color: var(--primary-color);
				font-size: 18px;
			}

			button {
				min-width: 100px;
				border: none;
				border-radius: 0px 30px 30px 0px;
				color: white;
				background-color: var(--primary-color);
			}
		}
	}
}
</style>
