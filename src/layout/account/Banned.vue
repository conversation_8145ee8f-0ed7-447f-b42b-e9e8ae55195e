<template>
    <div class="container">
        <div class="content d-flex align-items-center flex-column">
            <p class="inactive-text">{{$t('account.ACCOUNT_IS_SUSPENDED')}}</p>
            <p class="mt-2">{{$t('account.FOR_SUPPORTING_CONTACT')}} <a :href="contactMailTo">{{contact}}</a></p>
        </div>
    </div>
</template>

<script>

import externalSites from '../../constants/externalSites';

export default {
    data() {
        return {
            code: '',
            contact: externalSites.CUSTOMER_EMAIL,
            contactMailTo: externalSites.MAIL_TO.CUSTOMER_SUPPORT,
        };
    },

    methods: {
    },
}
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    margin-top: 20px;

    .content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding: 20px;

        .inactive-text {
            font-size: 25px;
            font-weight: 700;
            text-align: center;
        }
    }
}
</style>
