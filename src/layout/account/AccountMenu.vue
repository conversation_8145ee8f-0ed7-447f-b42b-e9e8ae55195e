<template>
  <div class="account-layout">
    <location-reminder ref="locationReminder" class="location-reminder fixed-top"></location-reminder>
    <div v-show="isOnMobile" class="">
      <sidebar-menu :collapsed="collapsed" width="280px" :menu="menu"
                    @update:collapsed="onToggleCollapse" @item-click="onItemClick" :showOneChild="true" class="goro-site-navigation-account">
        <template v-if="(!collapsed && isOnMobile) || !isOnMobile" v-slot:header>
          <div class="account-logo">
            <router-link to="/">
              <img class="logo-img" :class="{ 'collapsed': collapsed }" src="@/assets/img/logo.png" alt="">
            </router-link>
          </div>
        </template>

        <!-- <template v-slot:footer>footer</template> -->

        <template v-slot:toggle-icon>
          <b-icon v-if="collapsed" icon="arrow-right-square"></b-icon>
          <b-icon v-else icon="arrow-left-square"></b-icon>
        </template>

        <template v-slot:dropdown-icon="{ isOpen }">
          <b-icon v-if="isOpen" icon="chevron-down"></b-icon>
          <b-icon v-else icon="chevron-right"></b-icon>
        </template>
      </sidebar-menu>
    </div>
    <div class="account page-container" :class="[{ 'collapsed': collapsed }, { 'on-mobile': isOnMobile }]">
      <div :class="containerClass">
        <b-container v-show="isShowTopNavigation" class="top-nav d-block d-lg-none " :style="{ width: isOnMobile ? '100%' : `${topNavWidth}px` }">
          <b-row align-v="center" align-h="between">
            <b-button id="btn_accountMenu_OpenMenu" v-if="isOnMobile" @click="openMenu" title="Align left" class="ml-0 ml-lg-1" size="sm"
                      style="background:var(--primary-color);">
              <b-icon icon="list" aria-hidden="true"></b-icon>
            </b-button>
            <b-col :cols="isOnMobile ? '10' : '12'" xl="6" lg="6" class="order-1 order-lg-2 p-1">
              <b-row :align-h="isOnMobile ? 'between' : 'end'" align-v="center">
                <b-row class="ml-0" align-h="center" align-v="center">
                  <locale-switcher/>
                  <notification class="mr-0 mr-lg-0"/>
                </b-row>
                <user-avatar/>
              </b-row>
            </b-col>
          </b-row>
        </b-container>
        <earn-referral-message/>
        <div :class="{ 'content-mobile': isOnMobile }">
          <slot></slot>
        </div>
      </div>
      <div v-if="isOnMobile && !collapsed" class="sidebar-overlay" @click="collapsed = true"></div>
    </div>
  </div>
</template>

<script>

import "vue-sidebar-menu/dist/vue-sidebar-menu.css"
import "@/assets/css/account.style.css"
import {SidebarMenu} from "vue-sidebar-menu"
import LocationReminder from "../ui/LocationReminder"
import UserAvatar from "../../components/UserAvatar.vue"
import EarnReferralMessage from "./EarnReferralMessage.vue"
import Notification from "@/components/Notification"
import LocaleSwitcher from "@/components/LocaleSwitcher"
import accountService from "../../services/account.service"
import {INDO, ROUTE_META_PAGE_TYPES} from "@/constants/constants"
import externalSites from "@/constants/externalSites";
import {GTM_EVENT_NAMES} from "@/constants/gtm";
import {gtmTrackEvent} from "@/helpers/gtm";

export default {
  components: {
    SidebarMenu,
    LocationReminder,
    LocaleSwitcher,
    UserAvatar,
    Notification,
    EarnReferralMessage,
  },

  data () {
    return {
      contentPaddingTop: "0px",
      menu: this.getMenu(),
      collapsed: false,
      isOnMobile: true,
      topNavWidth: window.innerWidth - 320,
      enabledKYC: false,
      isShowTopNavigation: false,
      gtmEvent: {
        FAQS: GTM_EVENT_NAMES.FAQS,
      },
    }
  },

  mounted () {
    this.handleWindowResize()
    window.addEventListener("resize", this.handleWindowResize)
    this.updateMenuIcon(this.$route.path)
    this.getOwningTokens()
  },

  beforeDestroy () {
    window.removeEventListener("resize", this.handleWindowResize)
  },

  watch: {
    $route (to) {
      this.updateMenuIcon(to.path)
      this.checkShowTopNavigation(to)
    },

    "$i18n.locale" (newVal, oldVal) {
      this.menu = this.getMenu()
      this.updateMenuIcon(this.$route.path)
    },

    "$store.state.pendingTasksCount" (newVal, oldVal) {
      this.menu = this.getMenu()
      this.updateMenuIcon(this.$route.path)
    }
  },

  computed: {
    containerClass () {
      const routeMeta = this.$route && this.$route.meta ? this.$route.meta : null
      if (routeMeta && routeMeta.containerClass) {
        return routeMeta.containerClass
      }
      return 'container'
    },
    isIndonesian () {
      if (this.$store.getters.userProfile) {
        return this.$store.getters.userProfile.iso_country_code === INDO.ISO_COUNTRY_CODE
      } else if (this.$store.getters.geoLocation) {
        return this.$store.getters.geoLocation.country_code === INDO.COUNTRY_CODE
          || this.$store.getters.geoLocation.iso_country_code === INDO.ISO_COUNTRY_CODE
      }
      return false
    },
    referralLink () {
      return this.$store.getters.referralCode &&
        `${window.location.origin}/invite/${this.$store.getters.referralCode.code}`
    },
    kycTokenLimit () {
      if (!this.$store.getters.configs) {
        return 0
      }
      return this.$store.getters.configs.kyc_token_limit || 0
    },
  },

  methods: {
    handleWindowResize () {
      this.contentPaddingTop = this.$refs.locationReminder && this.$refs.locationReminder.$el.clientHeight
        ? `${this.$refs.locationReminder.$el.clientHeight}px`
        : "0px"
      if (window.innerWidth <= 991) {
        this.isOnMobile = true
        this.collapsed = true
      } else {
        this.isOnMobile = false
        this.collapsed = false
      }

      this.updateTopBar()
      this.checkShowTopNavigation(this.$route)
    },
    checkShowTopNavigation (to) {
      if (to && to.meta && to.meta.pageType === ROUTE_META_PAGE_TYPES.ACCOUNT) {
        if (this.isOnMobile) {
          this.isShowTopNavigation = true
        } else {
          this.isShowTopNavigation = false
        }
      } else {
        this.isShowTopNavigation = false
      }
    },
    onToggleCollapse (collapsed) {
      this.collapsed = collapsed
      this.updateTopBar()
    },
    onItemClick (event, item) {
      if (this.isOnMobile && item.href) {
        if (item.external) {
          if (item.title === "FAQ") {
            this.trackGtmEvent(this.gtmEvent.FAQS)
          }
          window.open(item.href, "_blank")
        }
        this.collapsed = true
      }
    },
    updateTopBar () {
      if (this.collapsed) {
        this.topNavWidth = window.innerWidth - 100
      } else {
        this.topNavWidth = window.innerWidth - 310
      }
    },
    openMenu () {
      this.collapsed = false
    },
    findMenuItemByPath (menu, path, parent = null) {
      for (const item of menu) {
        // Check if the current item's href matches the path
        if (item.href === path) {
          if (parent) {
            item.parent = parent
          }
          return item;
        }

        // Check if the item has children and recursively search them
        if (item.child && Array.isArray(item.child)) {
          const found = this.findMenuItemByPath(item.child, path, item);
          if (found) {
            return found;
          }
        }
      }

      // If no match is found, return null
      return null;
    },
    updateMenuIcon (selectedPath) {
      const menuActive = this.findMenuItemByPath(this.menu, selectedPath)

      this.deselectAll()
      if (menuActive) {
        this.setSelected(menuActive.icon)

        if (menuActive.parent && menuActive.parent.icon) {
          this.setSelected(menuActive.parent.icon)
        }
      }
    },
    deselectAll () {
      this.menu.forEach(m => {
        m.icon.attributes.src = this.getImage(m.icon.name)
        if (m.child) {
          m.child.forEach(c => {
            c.icon.attributes.src = this.getImage(c.icon.name)
          })
        }
      })
    },
    setSelected (icon) {
      icon.attributes.src = this.getImage(`${icon.name}-selected`)
    },
    getImage (name) {
      const images = require.context("@/assets/img/account/", false, /\.svg$/)
      return images("./" + `${name}.svg`)
    },
    trackGtmEvent (event) {
      gtmTrackEvent({
        event: event,
      })
    },
    getGoLearnMenus (prefix) {
      const goLearnPlaylists = this.$store.getters.goLearnPlaylists
      let menu = {}
      const childs = []

      if (goLearnPlaylists && goLearnPlaylists.length > 0) {
        goLearnPlaylists.map((playlist) => {
          childs.push({
            href: `${prefix}/golearn/${playlist.slug}`,
            title: `${playlist.title}`,
            icon: {
              element: 'img',
              name: 'golearn',
              attributes: {
                src: require("@/assets/img/account/golearn.svg"),
                width: '19',
              },
            },
          })
        })
      }

      childs.push({
        href: externalSites.FAQ,
        title: "FAQ",
        external: true,
        icon: {
          element: 'img',
          name: 'golearn',
          attributes: {
            src: require("@/assets/img/account/golearn.svg"),
            width: '19',
          },
        },
      })

      menu = {
        title: this.$t('GO_LEARN.GO_LEARN'),
        icon: {
          element: 'img',
          name: 'golearn',
          attributes: {
            src: require("@/assets/img/account/golearn.svg"),
          },
        },
        child: childs
      }

      if (childs && childs.length === 0) {
        menu = {
          ...menu,
          href: `${prefix}/golearn`,
        }
      }

      return menu
    },
    getMenu () {
      let prefix = ''
      if (this.$route.path.startsWith('/en')) {
        prefix = '/en'
      }
      let myAccountChildren = [
        {
          href: `${prefix}/account/my-account/basic-info`,
          title: this.$t('account.BASIC_INFO'),
          icon: {
            element: 'img',
            name: 'basic-info',
            attributes: {
              src: require("@/assets/img/account/basic-info.svg"),
              width: '28',
            },
          },
        },
      ]
      if (this.enabledKYC) {
        myAccountChildren.push(
          {
            href: `${prefix}/account/my-account/kyc`,
            title: 'KYC',
            icon: {
              element: 'img',
              name: 'kyc',
              attributes: {
                src: require("@/assets/img/account/kyc.svg"),
                width: '28',
              },
            },
          }
        )
      }

      let goLearnMenus = this.getGoLearnMenus(prefix)
      let dataMenus = [
        {
          href: `${prefix}/account/assets-overview`,
          title: this.$t('account.ASSETS_OVERVIEW'),
          icon: {
            element: 'img',
            name: 'assets-overview',
            attributes: {
              src: require("@/assets/img/account/assets-overview.svg"),
            },
          },
        },
        {
          href: `${prefix}/marketplace`,
          title: this.$t('HEADER.MARKETPLACE'),
          icon: {
            element: 'img',
            name: 'buy-properties',
            attributes: {
              src: require("@/assets/img/account/buy-properties.svg"),
            },
          },
        },
        {
          href: `${prefix}/account/referrals`,
          title: this.$t('REFERRAL.REFERRALS'),
          icon: {
            element: 'img',
            name: 'referrals',
            attributes: {
              src: require("@/assets/img/account/referrals.svg"),
            },
          },
        },
        {
          href: `${prefix}/account/transactions`,
          title: this.$t('account.TRANSACTIONS'),
          icon: {
            element: 'img',
            name: 'transaction',
            attributes: {
              src: require("@/assets/img/account/transaction.svg"),
            },
          },
        },
        // {
        //   href: "/account/sell-request-history",
        //   title: this.$t("SELL_TOKEN.SELL_TOKEN_HISTORY"),
        //   icon: {
        //     element: "img",
        //     name: "withdrawal-history",
        //     attributes: {
        //       src: require("@/assets/img/account/withdrawal-history.svg"),
        //     },
        //   },
        // },
        {
          href: `${prefix}/account/pending-tasks`,
          title: this.$t("PENDING_TASKS.PENDING_TASKS"),
          icon: {
            element: "img",
            name: "pending-action",
            attributes: {
              src: require("@/assets/img/account/pending-action.svg"),
            },
          },
          badge: {
            text: this.$store.getters.pendingTasksCount,
            class: this.$store.getters.pendingTasksCount >= 0 ? "vsm--badge_default" : "vsm--badge--empty"
          }
        },
        {
          href: `${prefix}/account/rental-income-reports`,
          title: this.$t("RENTAL_INCOME.RENTAL_INCOME_REPORTS"),
          icon: {
            element: 'img',
            name: 'rental-income',
            attributes: {
              src: require("@/assets/img/account/rental-income.svg"),
            },
          },
        },
        {
          title: this.$t('WITHDRAWALS.WITHDRAWALS'),
          icon: {
            element: 'img',
            name: 'withdrawals',
            attributes: {
              src: require("@/assets/img/account/withdrawals.svg"),
            },
          },
          child: [
            {
              href: `${prefix}/account/withdrawals/history`,
              title: this.$t('WITHDRAWALS.WITHDRAWAL_HISTORY'),
              icon: {
                element: 'img',
                name: 'withdrawal-history',
                attributes: {
                  src: require("@/assets/img/account/withdrawal-history.svg"),
                  width: '28',
                },
              },
            },
            {
              href: `${prefix}/account/withdrawals/bank-account-history`,
              title: this.$t('WITHDRAWALS.BANK_ACCOUNT'),
              icon: {
                element: 'img',
                name: 'bank-account',
                attributes: {
                  src: require("@/assets/img/account/bank-account.svg"),
                  width: '28',
                },
              },
            },
          ],
        },
        {
          ...goLearnMenus
        },
        {
          href: `${prefix}/events`,
          title: this.$t("EVENT.EVENTS"),
          icon: {
            element: 'img',
            name: 'events',
            attributes: {
              src: require("@/assets/img/account/events.svg"),
            },
          },
        },
        {
          title: this.$t('account.MY_ACCOUNT'),
          icon: {
            element: 'img',
            name: 'my-account',
            attributes: {
              src: require("@/assets/img/account/my-account.svg"),
            },
          },
          child: myAccountChildren,
        },
        {
          title: this.$t('ACCOUNT_SECURITY.TITLE'),
          icon: {
            element: 'img',
            name: 'security',
            attributes: {
              src: require("@/assets/img/account/security.svg"),
            },
          },
          child: [
            {
              href: `${prefix}/account/security/password`,
              title: this.$t('ACCOUNT_SECURITY.PASSWORD.TITLE'),
              icon: {
                element: 'img',
                name: 'password-menu',
                attributes: {
                  src: require("@/assets/img/account/password-menu.svg"),
                  width: '28',
                },
              },
            },
            {
              href: `${prefix}/account/security/pin`,
              title: this.$t('PIN_SECURITY.CHANGE_PIN'),
              icon: {
                element: 'img',
                name: 'pin',
                attributes: {
                  src: require("@/assets/img/account/pin.svg"),
                  width: '28',
                },
              },
            },
            {
              href: `${prefix}/account/security/two-factor-auth`,
              title: this.$t('TWO_FA.TWO_FACTOR_AUTHENTICATION'),
              icon: {
                element: 'img',
                name: 'two-factor-auth',
                attributes: {
                  src: require("@/assets/img/account/two-factor-auth.svg"),
                  width: '28',
                },
              },
            },
          ],
        },
      ]
      dataMenus = dataMenus.filter(item => Object.keys(item).length > 0);

      return dataMenus
    },
    async getOwningTokens () {
      const res = await accountService.getOwningTokens(false)
      if (res) {
        if (res.data > this.kycTokenLimit) {
          this.enabledKYC = true
          this.menu = this.getMenu()
          this.updateMenuIcon(this.$route.path)
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.location-reminder {
  z-index: 10000;
}

.account-layout {
  @media (max-width: 991.98px) {
    padding-top: 0 !important;
  }

  .account {
    &.page-container {
      padding-top: 20px;
      padding-bottom: 80px;
      @media (max-width: 991.98px) {
        padding-top: 10px;
      }
    }

    // padding-left: 280px;
    transition: 0.3s ease;

    &.collapsed {
      // padding-left: 65px;
    }

    &.on-mobile {
      padding-left: 0;
    }

    .sidebar-overlay {
      position: fixed;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background-color: #000;
      opacity: 0.5;
      z-index: 900;
    }

    .container {
      @media (max-width: 400px) {
        padding-left: 8px;
        padding-right: 8px;
      }

      .top-nav {
        background-color: white;
        position: relative;
        top: 0;
        z-index: 100;
      }

      // .content-mobile {
      //   margin-top: -15px;
      // }
    }

    u {
      cursor: pointer;
    }
  }

  .account-logo {
    margin: 1.5rem auto;

    .logo-img {
      width: 70px;
      transition: 0.3s ease;

      &.collapsed {
        width: 40px;
      }
    }
  }
}
</style>
