<template>
  <b-container v-if="showEarnMessage && referralMessage" class="goro-earn-referral-message mt-2 mb-2">
    <b-row align-v="center">
      <b-col cols="12" class="d-flex justify-content-center pl-0 pr-0">
        <div class="goro-earn-message-content text-center">
          <span class="font-16 text-center message" :class="!canCopy ? 'd-block mb-1': ''" v-html="referralMessage"></span>
          <span class="font-16 ml-2" :class="!canCopy ? 'd-block': ''">
            <u class="btn btn-secondary bg-main-color color-white btn-action font-14" v-if="canCopy" @click="copyReferralLink">
              {{$t("REFERRAL.COPY_REFERRAL_LINK") }}
            </u>
            <span v-else>{{ referralLink }}</span>
          </span>
        </div>
      </b-col>
    </b-row>
  </b-container>
</template>

<script>

import "vue-sidebar-menu/dist/vue-sidebar-menu.css"
import "@/assets/css/account.style.css"
import store from "@/store/store"
import { INDO } from "../../constants/constants"
import {exchange, notify} from "@/helpers/common"

export default {
  methods: {
    copyReferralLink () {
      if (this.referralLink) {
        navigator.clipboard.writeText(this.referralLink)
        notify({ text: this.$t("common.COPIED") })
      }
    },
  },
  computed: {
    showEarnMessage() {
      if (this.$store.getters.userProfile) {
        return true
      }
      return false
    },
    referralBonusPercentForReferrer () {
      if (this.$store.getters.referralCode && this.$store.getters.referralCode.referrer_bonus_percent) {
        return this.$store.getters.referralCode.referrer_bonus_percent
      } else if (store.state.configs && store.state.configs.referral_bonus_non_indo_referrer_percent) {
        return store.state.configs.referral_bonus_non_indo_referrer_percent
      }
      return 0
    },
    referralTokenAmountForReferrer () {
      if (store.state.configs && store.state.configs.referral_token_amount_for_referrer) {
        return store.state.configs.referral_token_amount_for_referrer
      }
      return 0
    },
    referralTokenMinimumPurchaseForReferee() {
      if (store.state.configs && store.state.configs.referral_token_minimum_purchase) {
        return store.state.configs.referral_token_minimum_purchase;
      }
      return 0
    },
    isEnableReferralTokenForIndo () {
      if (store.state.configs && store.state.configs.enable_referral_token_for_indo) {
        return store.state.configs.enable_referral_token_for_indo
      }
      return false
    },
    isIndonesian () {
      if (this.$store.getters.userProfile) {
        return this.$store.getters.userProfile.iso_country_code === INDO.ISO_COUNTRY_CODE
      } else if (this.$store.getters.geoLocation) {
        return this.$store.getters.geoLocation.country_code === INDO.COUNTRY_CODE
          || this.$store.getters.geoLocation.iso_country_code === INDO.ISO_COUNTRY_CODE
      }
      return false
    },
    referralMessage() {
      const routeMeta = this.$route && this.$route.meta ? this.$route.meta : null
      if (routeMeta && routeMeta.hideEarnMessage) {
        return null
      }

      if (this.$route.path && (this.$route.path.includes("pay/success") || this.$route.path.includes("pay/failed"))) {
        return null
      } else {
        if (this.isIndonesian && this.isEnableReferralTokenForIndo && this.referralTokenAmountForReferrer > 0) {
          const pricePerToken = 10000;
          const tokenBonusRequired = this.referralTokenMinimumPurchaseForReferee > 0
            ? this.$t("REFERRAL.EARN_FREE_TOKEN_FOR_EVERY_REFERRAL_REQUIRED", {
              token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
              token_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
            }) : ""
          return this.$t("REFERRAL.EARN_FREE_TOKEN_FOR_EVERY_REFERRAL", {
            token: this.referralTokenAmountForReferrer,
            token_label: this.referralTokenAmountForReferrer > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase(),
            token_value: exchange(this.referralTokenAmountForReferrer * pricePerToken, 100, false, "IDR"),
            token_bonus_required: tokenBonusRequired
          })
        } else if (this.referralBonusPercentForReferrer > 0) {
          return this.$t("REFERRAL.EARN_CASHBACK_FOR_EVERY_REFERRAL", { percent: this.referralBonusPercentForReferrer })
        } else {
          return null
        }
      }
    },
    referralLink () {
      return this.$store.getters.referralCode &&
        `${window.location.origin}/invite/${this.$store.getters.referralCode.code}`
    },
    canCopy () {
      return window.isSecureContext && navigator.clipboard
    },
  },
}
</script>

<style lang="scss" scoped>
  .goro-earn-referral-message{
    .goro-earn-message-content{
      border: 1px solid #FFA705;
      background-color: #FFEED9;
      border-radius: 10px;
      font-family: "AcuminVariableConcept", Helvetica, sans-serif;
      font-weight: 500;
      line-height: 16px;
      letter-spacing: -0.03em;
      color: #E06C00;
      // max-width: 555px;
      padding: 10px 15px 9px 15px;
      .message{
        b{
          font-weight: 700;
        }
      }
      .btn-action{
        font-weight: 600;
        line-height: 12px;
        text-align: center;
        color: var(--primary-light-color);
        padding: 8px 12px 6px 12px;
        border-radius: 4px;
        text-decoration: none;
        background-color: var(--layout2-primary-color);
        &:hover,
        &:focus,
        &:active{
          opacity: .8;
          background-color: var(--layout2-primary-color);
        }
      }
      @media (max-width: 768px) {
        max-width: 90%;
      }
    }
  }
</style>
