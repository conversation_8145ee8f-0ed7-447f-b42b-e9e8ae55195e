import {createI18n} from "vue-i18n"

function loadLocaleMessages() {
  const locales = require.context(
    "./locales",
    true,
    /[A-Za-z0-9-_,\s]+\.json$/i
  );
  const messages = {};
  locales.keys().forEach(key => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i);
    if (matched && matched.length > 1) {
      const locale = matched[1];
      messages[locale] = locales(key);
    }
  });
  return messages;
}

function checkDefaultLanguage () {
  let matchedLanguage = null
  let languages = Object.getOwnPropertyNames(loadLocaleMessages())
  languages.forEach(lang => {
    if (lang === navigator.language) {
      matchedLanguage = lang
    }
  })
  if (!matchedLanguage) {
    languages.forEach(lang => {
      let languagePartials = navigator.language.split("-")[0]
      if (lang === languagePartials) {
        matchedLanguage = lang
      }
    })
  }
  return matchedLanguage
}

export default createI18n({
  locale: checkDefaultLanguage() || process.env.VUE_APP_I18N_LOCALE || "en",
  fallbackLocale: process.env.VUE_APP_I18N_FALLBACK_LOCALE || "en",
  silentTranslationWarn: true,
  messages: loadLocaleMessages(),
  allowComposition: true
});
