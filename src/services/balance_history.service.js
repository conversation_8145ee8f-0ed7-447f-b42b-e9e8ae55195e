import serverApi from "../utils/serverApi"

const balanceHistoryService = {

    getFilterDates: () => serverApi({ uri: 'account/balance-history/filter-dates', method: 'GET' }),

    getHistories: (body) => serverApi({ uri: 'account/balance-history/index', method: 'GET', data: body }),

    getHistoryDetail: (transactionUuid) => serverApi({ uri: `account/balance-history/detail/${transactionUuid}`, method: 'GET' }),

};

export default balanceHistoryService;
