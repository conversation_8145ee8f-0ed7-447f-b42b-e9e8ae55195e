import serverApi from "../utils/serverApi"

const contractsService = {

  getContractStatus: (uuid) => server<PERSON>pi({ uri: `contracts/get-status/${uuid}`, method: "GET" }),

  getContractPreview: data => server<PERSON>pi({ uri: "contracts/transactions/get-preview", data, method: "POST" }),

  getContractPreviewForExisting: data => server<PERSON><PERSON>({
    uri: "contracts/transactions/get-preview-for-existing",
    data,
    method: "POST"
  }),

  getContractDocuments: data => server<PERSON>pi({ uri: "contracts/transactions/get-documents", data, method: "POST" }),

  createContractForExisting: data => server<PERSON>pi({
    uri: "contracts/transactions/create-for-existing",
    data,
    method: "POST"
  }),

  getContractTemplates: data => server<PERSON>pi({ uri: "contracts/templates", data, method: "GET" }),

  createContractTemplate: async data => {
    return await server<PERSON>pi({ uri: "contracts/templates/create", data, method: "POST" })
  },

  updateContractTemplate: async data => {
    return await server<PERSON>pi({ uri: `contracts/templates/update`, data, method: "PUT" })
  },

  deleteContractTemplate: async id => serverApi({ uri: `contracts/templates/delete/${id}`, method: "DELETE" }),
}

export default contractsService
