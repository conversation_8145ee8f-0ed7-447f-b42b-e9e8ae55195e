import serverApi from "../utils/serverApi"

const swapTokensService = {
  createSwapTokenTransaction: (body, isNotify = true, isManualErrorHandling = false) =>
      serverApi({
        uri: "swap-token-transactions/create",
        method: "POST",
        data: body,
        isNotify: isNotify,
        isManualErrorHandling: isManualErrorHandling,
      }),

  getSwapTokenTransaction: (body) => serverApi({ uri: "swap-token-transactions/get", method: "POST", data: body }),

}

export default swapTokensService
