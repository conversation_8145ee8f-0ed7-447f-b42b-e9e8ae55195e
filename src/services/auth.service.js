import serverApi from "../utils/serverApi"

const authService = {
    requestOtp: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/request-otp",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling
        }),

  getOtpStatus: (body) => serverApi({ uri: "auth/get-otp-status", method: "POST", data: body, isLoading: false }),

    getReverseOtpStatus: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/phone-verification-status",
            method: "POST",
            data: body,
            isLoading: false,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling
        }),

    requestInputOtpMethod: (body, isNotify = true, isManualErrorHandling = false) =>
        server<PERSON>pi({
            uri: "auth/request-input-otp-method",
            method: "POST",
            data: body,
            isLoading: false,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling
        }),

    register: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling
        }),

  registerWithGoogle: (body) => serverApi({
    uri: "auth/register-with-google",
    method: "POST",
    data: body,
  }),

  registerWithFacebook: (body) => serverApi({
    uri: "auth/register-with-facebook",
    method: "POST",
    data: body
  }),

  registerWithApple: (body) => serverApi({
    uri: "auth/register-with-apple",
    method: "POST",
    data: body,
  }),

    syncAppleAccount: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/sync-apple-account",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    verifyEmail: (params) => serverApi({ uri: "auth/verify-email", method: "GET", data: params }),

    login: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/login",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    loginWithGoogle: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/login-with-google",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    loginWithFacebook: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/login-with-facebook",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    loginWithApple: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/login-with-apple",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

  logout: () => serverApi({ uri: "auth/logout", method: "POST" }),

  forgotPassword: (body, isNotify = true, isManualErrorHandling = false) =>
      serverApi({
        uri: "auth/forgot-password",
        method: "POST",
        data: body,
        isNotify: isNotify,
        isManualErrorHandling: isManualErrorHandling,
      }),

  resetPassword: (body) => serverApi({ uri: "auth/reset-password", method: "PUT", data: body }),

  getUserProfile: (isLoading = true) => serverApi({ uri: "auth/user-profile", method: "GET", isLoading }),

  resendVerifyEmail: () => serverApi({ uri: "auth/resend-verify-email", method: "PUT" }),

  updateLocale: (body) => serverApi({
    uri: "auth/update-locale",
    method: "PUT",
    data: body,
    isLoading: false,
  }),

  requestVerificationCode: (body) => serverApi({
    uri: "auth/verification-code/request",
    method: "POST",
    data: body,
    isLoading: true,
    isNotify: true,
    isManualErrorHandling: true
   }),

  validVerificationCode: (body) => serverApi({
    uri: "auth/verification-code/valid",
    method: "POST",
    data: body,
    isLoading: true,
    isNotify: true,
    isManualErrorHandling: true
  }),
}

export default authService
