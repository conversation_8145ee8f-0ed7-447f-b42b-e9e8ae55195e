import serverApi from "../utils/serverApi"

const preRegisteredService = {
    getRegister: (params, isLoading = true) => serverApi({ uri: "auth/register_v2", method: "GET", data: params, isLoading }),

    requestOtp: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/request-otp",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    resendOtp: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/resend-otp",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    getReverseOtpStatus: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/phone-verification-status",
            method: "POST",
            data: body,
            isLoading: false,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    requestInputOtpMethod: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/request-input-otp-method",
            method: "POST",
            data: body,
            isLoading: false,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    verifyOtp: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/verify-otp",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    updateInfo: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/update-info",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    createPassword: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/create-password",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    confirmRegister: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/confirm-account-information",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    goBack: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/go-back",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),

    goNext: (body, isNotify = true, isManualErrorHandling = false) =>
        serverApi({
            uri: "auth/register_v2/go-next",
            method: "POST",
            data: body,
            isNotify: isNotify,
            isManualErrorHandling: isManualErrorHandling,
        }),
}

export default preRegisteredService
