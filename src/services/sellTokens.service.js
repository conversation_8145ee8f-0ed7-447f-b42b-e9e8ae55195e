import serverApi from "../utils/serverApi"

const sellTokensService = {
  getSellTokenRequests: (data) => server<PERSON>pi({ uri: "sell-token-requests", method: "GET", data }),

  approveSellTokenRequest: (body) => server<PERSON>pi({ uri: "sell-token-requests/approve", method: "PUT", data: body }),

  rejectSellTokenRequest: (body) => server<PERSON><PERSON>({ uri: "sell-token-requests/reject", method: "PUT", data: body }),

  getSellTokenRequestHistory: () => server<PERSON>pi({ uri: "sell-token-requests/history", method: "GET" }),

  createSellTokenRequest: (body, isNotify = true, isManualErrorHandling = false) =>
      serverApi({
        uri: "sell-token-requests/create",
        method: "POST",
        data: body,
        isNotify: isNotify,
        isManualErrorHandling: isManualErrorHandling,
      }),

  getSellTokenRequest: (body) => server<PERSON>pi({ uri: "sell-token-requests/get", method: "POST", data: body }),

  cancelSellTokenRequest: (body) => serverApi({ uri: "sell-token-requests/cancel", method: "PUT", data: body }),
}

export default sellTokensService
