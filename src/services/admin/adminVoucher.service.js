import serverApi from "../../utils/serverApi";

const voucherService = {
  getVoucher: (data) => serverApi({ uri: "admin/voucher/vouchers", method: "GET", data }),
  createVoucher: (data) => serverApi({ uri: "admin/voucher/vouchers", method: "POST", data: data }),
  updateVoucher: (id, data) => server<PERSON>pi({ uri: `admin/voucher/vouchers/${id}`, method: "PUT", data: data }),

  getTags: (data) => serverApi({ uri: "admin/voucher/tags", method: "GET", data }),
  createTag: (data) => serverApi({ uri: "admin/voucher/tags", method: "POST", data: data }),
  updateTag: (id, data) => serverApi({ uri: `admin/voucher/tags/${id}`, method: "PUT", data: data }),

  getVoucherCodes: (data) => serverApi({ uri: "admin/voucher/voucher-codes", method: "GET", data }),
  getRedemptions: (data) => server<PERSON>pi({ uri: "admin/voucher/redemptions", method: "GET", data }),
}

export default voucherService;
