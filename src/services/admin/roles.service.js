import serverApi from "../../utils/serverApi";

const rolesService = {
  getList: (data) => server<PERSON>pi({ uri: "admin/roles", method: "GET", data }),

  getAll: (data) => server<PERSON>pi({ uri: "admin/roles/all", method: "GET", data }),

  create: (data) => server<PERSON>pi({ uri: "admin/roles", method: "POST", data }),

  update: (id, data) => serverApi({ uri: `admin/roles/${id}`, method: "PUT", data }),

  delete: (id) => server<PERSON>pi({ uri: `admin/roles/${id}`, method: "DELETE" }),

  createOrUpdate: (data) =>
    server<PERSON>pi({ uri: "admin/roles/create-or-update", method: "POST", data }),
};

export default rolesService;
