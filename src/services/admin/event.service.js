import serverApi from "../../utils/serverApi";

const eventService = {
    getList: (data) => serverApi({ uri: "admin/events", method: "GET", data }),

    createEvent: (data) => serverApi({ uri: "admin/events", method: "POST", data }),

    updateEvent: (id, data) => serverApi({ uri: `admin/events/${id}`, method: "PUT", data }),

    deleteEvent: (id) => serverApi({ uri: `admin/events/${id}`, method: "DELETE" }),
};

export default eventService;
