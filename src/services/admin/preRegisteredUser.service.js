import serverApi from "../../utils/serverApi";

const preRegisteredUserService = {
    getList: (data) => serverApi({ uri: "admin/pre-registered-users", method: "GET", data }),

    updateCardInfo: (id, data) =>
        serverApi({ uri: `admin/pre-registered-users/${id}/upload-card`, method: "POST", data }),

    uploadSelfie: (id, data) =>
        serverApi({ uri: `admin/pre-registered-users/${id}/upload-selfie`, method: "POST", data }),

    getIdCard: (id) =>
        serverApi({ uri: `admin/pre-registered-users/${id}/card`, method: "GET" }),
};

export default preRegisteredUserService;
