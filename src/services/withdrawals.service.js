import serverApi from "../utils/serverApi"

const withdrawalsService = {
    addBankAccount: (body) => serverApi({ uri: 'bank-account', method: 'POST', data: body }),

    changeBankAccount: (body) => serverApi({ uri: 'bank-account', method: 'PUT', data: body }),

    getPendingBankAccount: () => serverApi({ uri: 'bank-account', method: 'GET' }),

    getBankAccountHistory: (data) => serverApi({ uri: 'bank-account/history', method: 'GET', data }),

    confirmBankAccount: (body) => serverApi({ uri: 'bank-account/confirm', method: 'PUT', data: body }),

    createWithdrawal: (body) => serverApi({ uri: 'withdrawals', method: 'POST', data: body }),

    getWithdrawalHistory: (data) => serverApi({ uri: 'withdrawals', method: 'GET', data }),

    cancelWithdrawalRequest: (body) => serverApi({ uri: 'withdrawals/cancel', method: 'PUT', data: body }),

    verifyAccountHolder: (body) => serverApi({ uri: 'bank-account/verify-account-holder', method: 'PUT', data: body }),
};

export default withdrawalsService;
