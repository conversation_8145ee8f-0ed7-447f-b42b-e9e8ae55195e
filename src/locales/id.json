{"Details": "<PERSON><PERSON><PERSON>", "Financials": "<PERSON><PERSON><PERSON>", "Documents": "Dokumen", "Market": "Pasar", "TIMELINE": "Linimasa", "BUY_PROPERTY": "Investasi", "SELL_PROPERTY": "<PERSON><PERSON>", "SWAP_PROPERTY": "<PERSON><PERSON>", "Bedrooms": "<PERSON><PERSON>", "Bathrooms": "<PERSON><PERSON> mandi", "Location": "<PERSON><PERSON>", "INVEST_NOW": "Investasi <PERSON>", "WHAT_IS_GOGO": "Apa itu GORO?", "YOUR_BROWSER_SESSION_ID": "ID sesi browser Anda", "GORO_ID": "ID GORO", "common": {"error": "<PERSON><PERSON><PERSON>", "NOTICE": "Melihat", "example": "Contoh:", "DOWNLOAD": "<PERSON><PERSON><PERSON>", "BACK": "Kembali", "COPIED": "Disalin ke clipboard", "CONGRATS": "Se<PERSON>at", "CLOSE": "<PERSON><PERSON><PERSON>", "SOMETHING_WRONG_HAPPENED": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, harap coba kembali.", "COMING_SOON": "<PERSON><PERSON><PERSON> hadir", "NOTIFICATION": "Notif<PERSON><PERSON>", "NOTIFICATIONS": "Notif<PERSON><PERSON>", "WELCOME": "Selamat datang!", "JOIN_NOW": "<PERSON><PERSON><PERSON>", "MARK_ALL_AS_READ": "Tandai semua telah dibaca", "NOTIFICATION_DETAILS": "Detail Notif<PERSON>", "EXPIRES_ON": "<PERSON><PERSON><PERSON> pada {value}", "EXPIRED_ON": "<PERSON><PERSON><PERSON> pada {value}", "RENTAL_INCOME_HAS_BEEN_CLAIMED": "Pendapatan sewa telah diklaim", "CLAIM_NOW": "<PERSON><PERSON><PERSON>", "VIEW_DETAILS": "<PERSON><PERSON>", "OK": "<PERSON>e", "CANCEL": "<PERSON><PERSON>", "REFERRAL_BONUS_HAS_BEEN_CLAIMED": "Bonus referral telah <PERSON><PERSON>", "TOKEN": "Token", "TOKENS": "Token", "MAX": "MAKS", "AVAILABLE": "tersedia", "NEARLY_THERE": "<PERSON><PERSON><PERSON>", "AND": "dan", "SUBMIT": "<PERSON><PERSON>", "PREVIOUS": "Sebelumnya", "NEXT": "Berikutnya", "DAY": "hari", "DAYS": "hari", "HOUR": "jam", "HOURS": "jam", "MINUTE": "menit", "MINUTES": "menit", "DONE": "Se<PERSON><PERSON>", "UPLOAD": "Mengunggah", "CLAIM_BEFORE_EXPIRED": "<PERSON>laim sekarang juga sebelum habis masa berlakunya!", "MALE": "Pria", "FEMALE": "Perempuan", "REGISTERED_ON": "Terdaftar Di", "COMPANY": "<PERSON><PERSON><PERSON><PERSON>", "PRODUCT": "Produk", "HIDE_ZERO_ASSETS": "Sembunyikan Aset 0", "EDIT": "Ubah", "SAVE_EDIT": "<PERSON><PERSON><PERSON>", "SAVE": "Simpan", "CANCEL_CHANGE": "Batalkan", "FREE_TOKEN": "Token gratis", "FREE_TOKENS": "Token gratis", "FREE": "GRATIS", "PROCEED": "Lanjutkan", "WARNING": "Peringatan!", "OR": "<PERSON><PERSON>", "APPLY": "Terapkan", "RESET": "<PERSON><PERSON>", "AGREE": "<PERSON><PERSON><PERSON>", "MONTH": "bulan", "MONTHS": "bulan", "RETRY_AFTER": "Coba lagi setelah: {retryAfter}", "DO_NOT_SHOW_FOR_TODAY": "<PERSON>an tampilkan hari ini", "DO_NOT_SHOW_IN_DAYS": "<PERSON><PERSON> tamp<PERSON> dalam {value}"}, "FILTER": {"ALL_LOCATION": "<PERSON><PERSON><PERSON>", "ALL_PROPERTIES": "<PERSON><PERSON><PERSON>", "SELECT_MONTH_AND_YEAR": "<PERSON><PERSON><PERSON> dan <PERSON>", "FILTER_MONTH_AND_YEAR": "Bulan & Tahun"}, "status": {"Active": "Aktif", "Sold Out": "<PERSON><PERSON><PERSON><PERSON>", "Presale": "Prapenjualan", "Promo": "Promo", "All": "<PERSON><PERSON><PERSON>"}, "HEADER": {"HOME": "Be<PERSON><PERSON>", "MARKETPLACE": "Marketplace", "SELL_MY_PROPERTY": "jual-bu.com", "ABOUT_US": "<PERSON><PERSON><PERSON>", "LOCATION_SUGGEST": "We have noticed you are visiting from {country}. To see information specific to your location, click on \"Switch to English Site.\" If you prefer to browse the Indonesian version, click on \"Stay on Indonesian Site\".", "LOCATION_STAY_HERE": "Stay on Indonesian Site", "LOCATION_SWITCH": "Switch to English Site"}, "FOOTER": {"CONTACT_US": "<PERSON><PERSON><PERSON><PERSON> kami", "CAREERS": "<PERSON><PERSON><PERSON>", "PRIVACY_POLICY": "<PERSON><PERSON><PERSON><PERSON>", "TERMS_AND_CONDITIONS": "Syarat & Ketentuan", "COPYRIGHT": "© PT. Teknologi Gotong Royong. GORO Technology Pte. Ltd. Seluruh hak cipta dilindungi undang-undang.", "TOKEN_PURCHASE_NOTE": "*<PERSON><PERSON><PERSON> pem<PERSON> akan dikonversikan ke dalam mata u<PERSON> (IDR)", "DISCLAIMER": "GORO.id adalah situs web yang dioperasikan oleh PT Teknologi Gotong Royong (“GORO”), anak perusa<PERSON>an yang sepenuhnya dimiliki oleh GORO Technology Pte. Ltd., dan dengan mengakses situs web dan halamannya, Anda setuju untuk terikat dengan Ketentuan Layanan dan <PERSON>an Privasi yang ada. GORO tidak mendukung atau mempromosikan peluang apa pun yang berada pada situs web ini dan tidak membuat rekomendasi atas tawaran apa pun kepada pengguna mana pun. Calon pengguna tidak boleh menafsirkan apa pun di situs web sebagai nasihat investasi, bisnis, hukum atau pajak. Konten yang terkandung di sini bukan merupakan tawaran GORO untuk menjual instrumen efek. Setiap informasi yang tersedia dari situs web ini atau tautan ke situs ini, bukan merupakan ajakan untuk membeli atau menjual properti apa pun. Ini juga bukan tawaran nasihat investasi, jasa atau panduan pada investasi atau transaksi tertentu. Pembelian langsung dan tidak langsung properti melibatkan risiko yang signifikan dan investasi dapat kehilangan nilai dan tidak dijamin oleh instansi pemerintah mana pun dan juga tidak dijamin oleh GORO. Pengguna bertanggung jawab untuk memverifikasi integritas dan keaslian informasi yang tersedia. Performa imbal hasil terdahulu, perkiraan imbal hasil yang diharapkan, atau pun proyeksi potensi tidak mencerminkan atau menjamin performa masa depan yang sebenarnya. Pengguna harus mampu menanggung kerugian atas investasi yang dilakukan.", "CUSTOMER_SERVICES": "Layanan <PERSON>dua<PERSON> Konsumen", "CUSTOMER_SERVICES_ADDRESS": "PT. Teknologi Gotong Royong\nGoWork Lippo Mall Puri\nLippo Mall Puri Ground Floor, Jl. <PERSON><PERSON> Raya blok U1, Kembangan Selatan, Kembangan, Jakarta Barat, DKI Jakarta 11610", "CUSTOMER_SERVICES_EMAIL": "Email: hello{'@'}goro.id", "CUSTOMER_SERVICES_WHATSAPP": "WhatsApp: +62 812 9090 4676", "CUSTOMER_SERVICES_EMAIL_SUPPORT": "hello{'@'}goro.id", "CUSTOMER_SERVICES_WHATSAPP_SUPPORT": "+62 812 9090 4676", "CONSUMER_COMPLAINT_DIRECTORATE_GENERAL": "Direktorat Jenderal Perlindungan Konsumen dan <PERSON><PERSON><PERSON>", "CONSUMER_COMPLAINT_MINISTRY_OF_TRADE": "Kementerian Perdagangan Republik Indonesia", "CONSUMER_COMPLAINT_CONTACT": "WhatsApp: +62 853 1111 1010", "DOWNLOAD_APPLICATION": "<PERSON><PERSON><PERSON>"}, "NOT_FOUND": {"NOT_FOUND": "Tidak ditem<PERSON>n", "PAGE_DOES_NOT_EXIST": "Halaman ini tidak tersedia"}, "MARKETPLACE": {"MARKETPLACE": "Pasar", "ABOUT_THE_PROPERTY": "Tentang Properti Ini", "LOCATION": "<PERSON><PERSON>", "PROPERTY_TYPE": "<PERSON><PERSON>", "STATUS": "Status", "MONTHLY_ANNUALIZED_RETURNS": "<PERSON><PERSON>", "ANNUALIZED_RETURNS": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>an", "AVERAGE": "<PERSON>a-rata"}, "AUTH": {"GOOGLE": "Google", "FACEBOOK": "Facebook", "APPLE": "Apple", "GET_STARTED": "<PERSON><PERSON>", "REGISTER": "<PERSON><PERSON><PERSON>", "REGISTER_WITH": "<PERSON><PERSON><PERSON> den<PERSON>", "REGISTER_NOW": "<PERSON><PERSON><PERSON>", "LOGIN": "<PERSON><PERSON><PERSON>", "LOGIN_WITH": "<PERSON><PERSON><PERSON>", "FULL_NAME": "<PERSON><PERSON> le<PERSON> sesuai KTP", "NAME": "<PERSON><PERSON>", "EMAIL": "Email", "PHONE": "Telepon", "PHONE_HINT": "Masukkan nomor hp Anda", "COUNTRY_CODE": "Kode negara", "PASSWORD": "<PERSON>a sandi", "PASSWORD_CONFIRM": "<PERSON><PERSON><PERSON><PERSON><PERSON> sandi", "PASSWORD_FORGOT": "<PERSON>pa kata sandi", "PASSWORD_FORGOT_SEND_LINK": "<PERSON><PERSON> reset kata sandi", "PASSWORD_RESET": "Reset kata sandi", "ADDRESS": "<PERSON><PERSON><PERSON>", "OR_FILL_IN_THIS_FORM": "atau lengkapi formulir ini untuk membuat akun!", "COMPLETE_YOUR_PROFILE": "Lengkapi Profil And<PERSON>", "I_ACCEPT_TERMS_AND_PRIVACY": "<PERSON><PERSON> & Ketentuan dan <PERSON> Privasi GORO", "ALREADY_HAVE_AN_ACCOUNT": "Sudah memiliki akun?", "OR_LOGIN_WITH_CREDENTIALS": "atau masuk dengan email", "DONT_HAVE_AN_ACCOUNT": "Belum memiliki akun?", "REMEMBER_ME": "<PERSON>gat saya", "EMAIL_VERIFICATION": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VERIFICATION": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VERIFY": "Periksa", "2FA_VERIFY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ENTER_THE_OTP": "<PERSON><PERSON><PERSON><PERSON> (One Time Password) yang telah dikirimkan ke nomor ponsel Anda yang terdaftar", "CHANGE_PHONE_NUMBER": "Ubah nomor telepon", "PASSWORD_OPTIONAL": "<PERSON><PERSON> (Opsional)", "EMAIL_CORRECTION": "<PERSON><PERSON><PERSON> email", "OTP_REQUIRED": "OTP harus dimasukkan", "OTP_NOTICE": "OTP akan diki<PERSON> me<PERSON>, pastikan nomor Anda aktif dan terdaftar di WhatsApp", "OTP_DELIVERY_STATUS": "Status pengiriman OTP:", "OTP_DID_NOT_RECEIVE": "Sudah mengirim pesan?", "OTP_RESEND": "<PERSON><PERSON>", "OTP_RESEND_AFTER": "<PERSON><PERSON>", "ENTER_YOUR_PHONE_NUMBER": "Ma<PERSON>kkan nomor telepon Anda", "TEL_INPUT_SEARCH_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> nama atau kode negara", "INVALID_PHONE_NUMBER": "Nomor telepon tidak valid", "THE_PHONE_IS_REQUIRED": "<PERSON>lom nomor telepon wajib diisi", "VERIFICATION_CODE": "<PERSON><PERSON> veri<PERSON>", "SEND_VERIFICATION_CODE": "<PERSON><PERSON>", "SEND_VERIFICATION_CODE_FAILED": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat mengirim kode verifikasi. <PERSON><PERSON><PERSON> coba lagi.", "VALID_VERIFICATION_CODE_FAILED": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memverifikasi kode verifikasi. <PERSON><PERSON>an coba lagi.", "REQUIRE_VERIFICATION_CODE_BEFORE_CHANGE_PASSWORD": "Memerlukan verifi<PERSON>i kode sebelum mengubah kata sandi.", "VERIFIED_CODE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "REQUIRE_VERIFICATION_CODE_TOO_MANY_REQUEST": "<PERSON><PERSON> telah men<PERSON>kan kode verifikasi ke email Anda. <PERSON><PERSON>an periksa kotak masuk atau folder spam Anda.", "NOTIFY_CHECK_MAIL_TO_GET_VERIFICATION_CODE": "<PERSON><PERSON><PERSON> perik<PERSON> email {email} untuk mendapatkan kode verifikasi.", "REGISTER_NAME_NOTE": "<PERSON>a ini akan digunakan dalam <PERSON>an Transaksi Properti Fraksional (Token)", "I_ACCEPT_THE": "<PERSON><PERSON>", "CONFIRM_NEW_PASSWORD": "Konfirmasi <PERSON>", "LOGIN_TO_YOUR_ACCOUNT": "<PERSON><PERSON><PERSON> ke Akun Anda", "LOGIN_WITH_GOOGLE": "Ma<PERSON>k <PERSON>", "LOGIN_WITH_FACEBOOK": "Ma<PERSON>k <PERSON>", "LOGIN_WITH_APPLE": "<PERSON><PERSON><PERSON>", "LOGIN_FAILED": "Email atau kata sandi salah.", "LOGIN_FAILED_WARNING": "{remainingAttempts} percobaan tersisa sebelum akun Anda terkunci.", "LOGIN_FAILED_WARNING_2": "{remainingAttempts} percobaan lagi. Akun Anda akan dikunci jika terus salah. Coba ", "LOGIN_FAILED_WARNING_3": "Percobaan terakhir! Akun Anda akan dikunci jika salah lagi. Coba ", "LOGIN_FAILED_BANNED": "<PERSON><PERSON><PERSON>a telah terkunci sementara karena terlalu banyak percobaan login yang gagal. <PERSON><PERSON><PERSON> hubungi <a href='{whatsapp}' target='_blank'>Customer Service</a> untuk bantuan.", "REGISTRATION_COMPLETE": {"HEADLINE": "Selamat Datang di GORO! Registrasi Anda Berhasil.", "TEXT": "Te<PERSON> kasih telah mendaftar di GORO! Akun Anda telah aktif, dan kini Anda bisa mulai berinvestasi properti dengan mudah di GORO!", "REDIRECT_TEXT": "<PERSON>a akan dialihkan dalam {seconds} detik, atau {link} untuk melanjutkan.", "CLICK_HERE": "klik di sini"}, "CREATE_ACCOUNT": "<PERSON><PERSON><PERSON>", "REGISTER_STEP_PROGRESS": "<PERSON><PERSON><PERSON> {currentStep} dari {totalStep}", "TITLE_COMPLETE_ACCOUNT_SETUP": "Lengkapi Informasi Akun Anda", "ACCOUNT_CONFIRMATION": "Konfirmasi <PERSON>", "ACCOUNT_CONFIRMATION_WARNING_TEXT": "Pastikan informasi berikut ini benar.", "CONTINUE": "Lanjutkan", "BACK": "Kembali", "SUBMIT": "<PERSON><PERSON>", "TITLE_OTP_VERIFICATION": "Verifikasi OTP", "ENTER_THE_OTP_TEXT": "<PERSON><PERSON>an masukkan kode 6 digit yang dikirim ke", "CHANGE": "Mengubah", "MOTHER_MAIDEN_NAME": "<PERSON><PERSON>", "GENDER": "<PERSON><PERSON>", "MALE": "Pria", "FEMALE": "Perempuan", "DATE_OF_BIRTH": "<PERSON><PERSON> lahir", "WARNING_NAME_TEXT": "<PERSON>a ini akan digunakan dalam <PERSON>an Transaksi Properti Fraksional (Token)", "DISABILITY_QUESTION": "<PERSON><PERSON><PERSON><PERSON> Anda seorang penyandang disabilitas?", "HAVE_DISABILITY": "Ya, saya memiliki disabilitas.", "NOT_HAVE_DISABILITY": "<PERSON><PERSON><PERSON>, saya tidak memiliki disabilitas.", "MARITAL_STATUS": "Status Perkawinan", "MARRIED": "<PERSON><PERSON>", "NOT_MARRIED": "<PERSON><PERSON>", "DIVORCED": "<PERSON><PERSON>", "WIDOWED": "<PERSON><PERSON>", "FULL_NAME_VALID": "<PERSON><PERSON> le<PERSON>", "PARENT_KYC_MESSAGE": "Untuk melanjutkan proses registrasi, <PERSON><PERSON> harus melengkapi data orang tua atau wali dengan menghubungi <a href='{whatsapp}' target='_blank'><strong>WhatsApp GORO</strong></a>", "PASSWORD_MUST_CONTAIN": "Kata sandi harus berisi:", "REFERRAL_CODE": "Kode Referral", "PASSWORD_RULES": {"LEAST8CHAR": "<PERSON><PERSON><PERSON>", "LEAST1NUMBER": "Angka", "LEAST1CHARCASE": "<PERSON><PERSON><PERSON> k<PERSON> besar", "LEAST1SPECIAL": "<PERSON><PERSON><PERSON>"}, "VERIFY_ACCOUNT_VIA_WHATSAPP": "Verifikas<PERSON>a melalui WhatsApp", "VERIFY_ACCOUNT_VIA_WHATSAPP_DESCRIPTION": "Ketuk tombol di bawah ini untuk mengirim pesan verifikasi melalui WhatsApp. Langkah ini diperlukan untuk menyelesaikan proses autentikasi Anda.", "VERIFY_ACCOUNT_VIA_WHATSAPP_WARNING": "<PERSON><PERSON> jangan mengubah pesan yang telah diisi sebelumnya agar verifikasi berhasil.", "SEND_VERIFICATION_VIA_WHATSAPP": "<PERSON><PERSON> WhatsApp", "VERIFYING_YOUR_CODE": "Memverifikasi Kode Anda...", "VERIFYING_YOUR_CODE_DESCRIPTION": "<PERSON><PERSON>g memverifikasi pesan Anda secara otomatis.\n<PERSON><PERSON> tunggu beberapa saat.", "CHECK_VERIFICATION_STATUS": "Cek Status Verifikasi", "VERIFYING_YOUR_CODE_WARNING": "Pastikan koneksi internet Anda stabil.", "NEED_HELP_HERE": "Butuh bantuan? Hubungi layanan pelanggan kami di <a href='{whatsapp}' target='_blank'>sini</a>.", "UNDER_18_AND_NOT_MARRIED": "<PERSON>a terdeteksi di bawah umur dan belum menikah. Harap isi data diri dengan pendampingan orang tua/wali.", "CONTACT_US": "<PERSON><PERSON><PERSON><PERSON>", "CONTACT_US_PREFILLED_MESSAGE": "<PERSON><PERSON> tim <PERSON>, saya ingin melanju<PERSON> proses registrasi. <PERSON>a adalah pengguna di bawah usia 18 tahun dan belum menikah. Apa saja data yang perlu disiapkan?", "CONTACTED_PARENTAL_KYC": "<PERSON><PERSON> su<PERSON> mengh<PERSON><PERSON>i tim GORO dan menyerahkan dokumen yang diperlukan.", "PARENTAL_KYC": {"HEADLINE": "Status KYC", "HEADLINE_REVIEWED": "Sedang Ditinjau", "HEADLINE_SUCCESS": "Selamat!", "HEADLINE_FAILED": "Maaf!", "REVIEWED_TITLE": "Proses Verifikasi Data KYC", "REVIEWED_TEXT": "Kami sedang memverifikasi detail KYC Anda. Proses ini mungkin memakan waktu hingga 72 jam.", "SUCCESS_TITLE": "Verifikasi KYC Berhasil", "FAILED_TITLE": "Verifikasi Data Anda Gagal!", "FAILED_TEXT": "Data Anda belum berhasil diverifikasi. Silakan coba lagi dalam {value}.", "REFRESH": "Perbarui Status", "DONE": "Se<PERSON><PERSON>", "CONTINUE": "Lanjutkan"}}, "LANDING": {"REGISTERED_USERS": "Pengguna", "INVESTED_IN_PROPERTY": "<PERSON><PERSON>", "TOTAL_INCOME_PAID": "<PERSON><PERSON><PERSON>", "PROPERTIES": "Properti", "TITLE": "Dapatkan keuntungan hingga 10% dengan berinvestasi di properti.", "DESCRIPTION": "Investasi properti mulai dari Rp10.000", "DESCRIPTION_EN": "Invest in fractions of rental properties from $1*", "HOW_IT_WORKS": "<PERSON> kerja GO<PERSON>", "HOW_IT_WORKS_DESCRIPTION": "Investasi properti tidak pernah semudah ini", "BROWSE": "<PERSON><PERSON><PERSON>", "BROWSE_DESCRIPTION": "Tinjau properti pilihan GORO berdasarkan potensi pendapatannya", "PURCHASE": "Investasi", "PURCHASE_DESCRIPTION": "<PERSON><PERSON><PERSON> bagian dari properti GORO mulai dari Rp 10.000", "EARN": "<PERSON><PERSON>l hasil", "EARN_DESCRIPTION": "Dapatkan pendapatan sewa dan tarik kapan saja", "EXIT": "<PERSON><PERSON>", "EXIT_DESCRIPTION": "<PERSON><PERSON><PERSON>, beli kembali, atau jual kepemilikan manfaat properti Anda setiap saat", "FEATURED_PROPERTIES": "Properti Unggulan", "FEATURED_PROPERTIES_DESCRIPTION": "Kami hanya memilih properti dengan imbal hasil tertinggi", "VIEW_ALL_PROPERTIES": "<PERSON><PERSON>", "FEATURED_ON": "Liputan Media", "OUR_MISSION": "<PERSON><PERSON> kami mendemokratisasi kepemilikan properti.", "OUR_MISSION_DESCRIPTION": "90% milyuner mendapatkan kekayaannya melalui properti, tetapi membeli properti membutuhkan uang yang banyak, tidak mudah untuk dijual, dan ribet.", "OUR_MISSION_1": "Investasi properti mulai dengan modal sesukamu", "OUR_MISSION_1_DESCRIPTION": "Dengan kepemilikan properti secara p<PERSON>, <PERSON><PERSON> tidak membutuhkan KPR atau uang muka yang besar", "OUR_MISSION_2": "Bangun portofolio terdiversifikasi", "OUR_MISSION_2_DESCRIPTION": "<PERSON><PERSON><PERSON> bagian dari properti sewaan terbaik dan kelola portofolio terdiversifikasi dari mana saja", "OUR_MISSION_3": "<PERSON><PERSON><PERSON> properti tanpa ribet", "OUR_MISSION_3_DESCRIPTION": "<PERSON><PERSON> se<PERSON> proses p<PERSON><PERSON><PERSON>, men<PERSON><PERSON>, dan men<PERSON>. Hemat waktu dan uang <PERSON>!", "OUR_MISSION_4": "Dapatkan imbal hasil tertinggi", "OUR_MISSION_4_DESCRIPTION": "<PERSON>a mendapatkan akses yang memiliki imbal hasil tertinggi, meski jauh dari tempat <PERSON>a tinggal", "WHY": "Properti mengkombinasikan yang terbaik dari seluruh kelas aset", "WHY_DESCRIPTION": "Properti adalah salah satu kelas aset terpenting untuk dimiliki dalam membangun kekayaan jangka panjang", "VALUE_APPRECIATION": "Apresiasi nilai aset", "VALUE_APPRECIATION_DESCRIPTION": "<PERSON><PERSON> penda<PERSON>tan sewa bulanan, <PERSON><PERSON> mengalami apresiasi se<PERSON>ga menciptakan kekayaan jangka panjang bagi para pemilik token", "HEDGE_FOR_INFLATION": "<PERSON><PERSON><PERSON><PERSON> terhadap inflasi", "HEDGE_FOR_INFLATION_DESCRIPTION": "Properti adalah kelas aset tertua dalam sejarah dan selalu menjadi lindung nilai yang baik selama masa inflasi, seperti halnya emas", "PASSIVE_INCOME": "Pendapatan pasif", "PASSIVE_INCOME_DESCRIPTION": "Properti menghasilkan pendapatan pasif yang konsisten seperti deposito atau obligasi negara, dalam bentuk pembayaran sewa bulanan", "STOREHOLD_OF_WEALTH": "Penyimpan harta", "STOREHOLD_OF_WEALTH_DESCRIPTION": "<PERSON><PERSON><PERSON> orang bercita-cita memiliki rumah, men<PERSON><PERSON><PERSON> properti sebagai penyimpan kekayaan dengan jaminan nilai yang sangat tinggi", "JOIN_THE_FUTURE_TODAY": "Sudah siap bergabung?", "ONLY_TAKE_5_MINUTES": "<PERSON><PERSON> butuh 5 menit untuk memiliki properti GORO", "SKIP_WAITLIST": "<PERSON><PERSON>", "OUR_INVESTOR_BENEFIT": "Keuntungan Pemilik Token", "OUR_INVESTOR_BENEFIT_CAPTION": "*Dalam 1 tahun", "HIGH_RETURNS_AND_LOW_VOLATILITY": "<PERSON><PERSON> pengembalian tinggi dan volatilitas rendah", "TESTIMONIALS": "Testimoni", "TESTIMONIAL_1_NAME": "<PERSON><PERSON><PERSON><PERSON>", "TESTIMONIAL_1_TITLE": "Sales & Marketing Manager", "TESTIMONIAL_1_COMMENT": "“Sebagai pelaku industri properti sepanjang karir saya, GORO merupakan sebuah terobosan besar di industri properti Indonesia. Sekarang siapapun jadi bisa memiliki properti sewa produktif tanpa perlu cash keras ataupun kredit bank.”", "TESTIMONIAL_2_NAME": "<PERSON><PERSON><PERSON>", "TESTIMONIAL_2_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TESTIMONIAL_2_COMMENT": "“Sebagai seorang wirausahawan dan latar belakang saya sebagai anggota partai politik, saya merasakan nilai luar biasa GORO dalam investasi properti. Dengan GORO, saya dapat dengan mudah masuk ke dalam pasar real estat dengan modal minimal. Terima kasih GORO atas peluang yang bermanfaat ini.”", "TESTIMONIAL_3_NAME": "<PERSON><PERSON>", "TESTIMONIAL_3_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TESTIMONIAL_3_COMMENT": "“Sebagai pengusaha muda yang telah menjalani berbagai macam usaha, GORO tepat bagi saya karena bisa mendapatkan imbal hasil yang relatif tinggi per bulannya di pasar yang sudah teruji dan stabil. Terima kasih GORO.”", "REQUEST_INVITATION": "<PERSON><PERSON>", "SCHEDULE_A_CALL": "<PERSON><PERSON><PERSON><PERSON> panggilan untuk meminta undangan", "READY_TO_OWN_RENTAL_PROPERTY": "Siap Memiliki Properti Sewa Hari Ini?", "CONSULT_DIRECTLY_WITH_GORO": "Langsung konsultasi dengan property specialist GORO!", "DOWNLOAD_OUR_APP": "<PERSON><PERSON><PERSON>", "CALCULATOR_SIMULATION": "<PERSON><PERSON><PERSON> Imbal <PERSON>", "SELECT_PROPERTY": "<PERSON><PERSON><PERSON>", "ONE_TIME": "Investasi Sekali", "MONTHLY": "<PERSON><PERSON><PERSON>", "INITIAL_PURCHASE": "<PERSON><PERSON><PERSON><PERSON> ({value})", "MONTHLY_PURCHASE": "Investasi setiap bulan ({value})", "INITIAL_PURCHASE_HINT": "Kelipatan dari {value}", "PERIOD": "<PERSON><PERSON> ({value} tahun)", "EXPECTED_RENTAL_YIELD": "Expected <PERSON><PERSON> (ERY)", "LAST_MONTH": "<PERSON><PERSON><PERSON>", "AVERAGE": "<PERSON>a-rata", "INCLUDE_EXPECTED_CAPITAL_APPRECIATION": "Sertakan Expected Capital Appreciation", "COMPOUND_BY_AUTO_PURCHASE": "Investasi otomatis dari imbal hasil", "COMPOUND_BY_AUTO_PURCHASE_TOOLTIP": "Pembelian kembali senilai satu token akan dilakukan setiap kali imbal hasil mencapai {value}", "PROJECTED_INCOME_RETURN": "Proyeksi Pendapatan", "YEAR": "{value} tahun", "YEARS": "{value} tahun", "EXPECTED_INCOME": "<PERSON><PERSON><PERSON><PERSON> imbal hasil", "ESTIMATED_RENTAL_YIELD_RECEIVED": "<PERSON><PERSON><PERSON><PERSON> imbal hasil yang akan di<PERSON>ima dalam <b>{year}</b>", "EXPECTED_INCOME_WITH_COMPOUND": "<PERSON><PERSON><PERSON><PERSON> jumlah imbal hasil tambahan dengan investasi otomatis", "TOTAL_ASSET": "Aset Total", "TOTAL_ASSETS_IN_YEAR": "Total aset dalam <b>{year}</b>", "PURCHASE_TOKEN_DESCRIPTION": "Investasi sebesar {purchase} = {token} {token_label}", "CALCULATOR_DISCLAIMER": "<b>Per<PERSON>ian:</b> <PERSON><PERSON><PERSON> ditujukan hanya untuk ilustrasi, dan informasi yang didapatkan tidak boleh dijadikan sebagai saran hukum atau keuangan, atau sebagai jaminan apa pun. Hasil yang ditunjukkan merupakan perkiraan berdasarkan angka yang dimasukkan oleh pengguna dan tidak mencerminkan hasil sebenarnya.", "BUY_PROPERTY_NOW": "Investasi <PERSON>"}, "propertyDetail": {"Bedrooms": "<PERSON><PERSON>", "Bathrooms": "<PERSON><PERSON> mandi", "Occupied": "<PERSON><PERSON><PERSON><PERSON>", "total_invest_value": "Total Nilai Investasi", "unit_price": "Patokan harga", "notary_fee": "<PERSON><PERSON><PERSON>", "goro_fee": "Biaya GORO", "projected_annual_roi": "Proyeksi ROI Tahunan", "not_1": "Biaya GORO dikurangi dari 5% menjadi 2% sebagai diskon peluncuran.", "not_2": "Hal ini dapat dicapai dengan beralih dari sewa bulanan ke sewa harian.", "token left": "token tersisa", "minimum": "minimum", "ERY": "ERY", "ERY_TOOLTIP": "Expected <PERSON><PERSON> Yield adalah estimasi imbal hasil dari penghasilan sewa atas suatu properti selama 1 tahun terhadap nilai investasi properti", "ERY_ANNUAL": "ERY (Annual)", "ERY_ANNUAL_TOOLTIP": "Expected <PERSON><PERSON> Yield adalah estimasi imbal hasil dari penghasilan sewa atas suatu properti selama 1 tahun terhadap nilai investasi properti", "ACCOUNT_NOT_ACTIVE_TOOLTIP": "Status akun Anda saat ini tidak aktif. Silakan aktifkan akun Anda untuk mulai membeli token", "IRR": "IRR", "IRR_TOOLTIP": "Internal Rate of Return adalah total estimasi imbal hasil suatu properti yang dihitung berdasarkan ERY dan ECA selama 1 tahun", "ECA": "ECA", "ECA_TOOLTIP": "Expected Capital Appreciation per annum", "ECA_ANNUAL": "ECA (Annual)", "ECA_ANNUAL_TOOLTIP": "Expected Capital Appreciation adalah estimasi kenaikan nilai suatu properti selama 1 tahun", "IRR_ANNUAL": "IRR (Annual)", "IRR_ANNUAL_TOOLTIP": "Internal Rate of Return adalah total estimasi imbal hasil suatu properti yang dihitung berdasarkan ERY dan ECA selama 1 tahun", "TOP_TOKEN_HOLDERS": "Pemegang token teratas", "THIS_PROPERTY_HAS_NO_OWNERS": "Properti ini tidak memiliki pemilik", "VIEW_MORE": "<PERSON><PERSON> lebih banyak", "VIEW_LESS": "<PERSON><PERSON> lebih sedikit", "PROPERTY_TIMELINE": "Linimasa Properti", "ENDED": "<PERSON><PERSON><PERSON>", "END_IN": "<PERSON><PERSON><PERSON> {value}", "PRESALE_NOTE": "Properti ini sedang dalam persiapan sehingga belum menghasilkan pendapatan sewa. Rencananya properti ini akan mulai menghasilkan pendapatan sewa dari tanggal: {value}", "CANCEL": "<PERSON><PERSON>", "CONTINUE": "Lanjutkan", "PROPERTY_PRESALE": "Properti ini dalam masa prajual", "ASSET_VALUE": "<PERSON><PERSON>", "ANNUAL_RETURN": "<PERSON><PERSON><PERSON><PERSON>", "TOKEN_HOLDERS_LEADERBOARD": "Papan Peringkat Pemegang Token"}, "account": {"ASSETS_OVERVIEW": "<PERSON><PERSON><PERSON><PERSON>", "share": "Bagikan", "leaderboard": "<PERSON><PERSON>", "TRANSACTIONS": "Transaksi", "wallet": "Dompet", "taxes": "<PERSON><PERSON>", "MY_ACCOUNT": "<PERSON><PERSON><PERSON>", "marketplace": "Investasi <PERSON>", "shareAndEarn": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>!", "shareYourLink": "Bagikan tautan <PERSON>a di bawah ini untuk memulai", "INVITES_LEFT": "<PERSON><PERSON> memiliki {value} undangan yang tersisa dalam {period}", "copyLink": "<PERSON><PERSON>an", "pleaseCompleteInfo": "Harap melengkapi informasi yang\ndiminta berikut ini", "invalidPhoneNumber": "Nomor telepon tidak valid", "countryRegion": "Negara/Wilayah", "streetAddress": "<PERSON><PERSON><PERSON>", "city": "Kotamadya/Kabupaten", "stateProvince": "<PERSON><PERSON><PERSON>", "zipPostal": "<PERSON><PERSON> pos", "ssn": "SSN untuk Pen<PERSON> (Khusus AS)", "dob": "<PERSON><PERSON> lahir", "submit": "<PERSON><PERSON>", "PERSONAL_INFO": "Informasi\nPribadi", "PLEASE_SELECT_YOUR_ID_CARD": "Klik Untuk Mengunggah KTP (Kartu Tanda Penduduk) Anda", "SUCCESS_VERIFIED_ID_CARD": "Anda telah berhasil memverifikasi kartu identitas Anda.", "ID_CARD_INVALID": "Kartu identitas tidak valid. Silakan unggah lagi!", "PLEASE_SELECT_YOUR_PASSPORT": "Unggah paspor Anda", "SUCCESS_VERIFIED_PASSPORT": "Anda telah berhasil memverifikasi paspor Anda.", "PASSPORT_INVALID": "Paspor tidak valid. Silakan unggah lagi!", "UPLOAD_YOUR_ID_CARD_PASSPORT": "Silahkan unggah KTP/Paspor Anda", "SELFIE": "Swafoto", "SELFIE_IS_NOT_VALID": "Swafoto tidak valid. Silakan coba lagi!", "SUCCESS_SELFIE_CHECKING": "<PERSON><PERSON><PERSON>, <PERSON>a telah berhasil mengun<PERSON>ah swafoto Anda", "LOGOUT": "<PERSON><PERSON><PERSON>", "MY_PROFILE": "<PERSON>il saya", "MANAGE": "Mengelola", "INVITED_USERS": "<PERSON><PERSON><PERSON> yang <PERSON>", "JOINED_AT": "Bergabung sejak", "DO_NOT_HAVE_TRANSACTION_ACCESS": "<PERSON><PERSON>, <PERSON>a belum dapat bertransaksi. <PERSON><PERSON>an minta seseorang yang sudah dapat bertransaksi untuk mengundang Anda!", "TO_UPDATE_PROFILE": "Untuk memperbarui informasi pribadi Anda. Mohon hubungi", "TO_UPDATE_PROFILE_V2": {"WA": "Jika terdapat kendala pada akun Anda. <PERSON><PERSON><PERSON> hubungi Whatsapp GORO di", "EMAIL": "atau hubungi"}, "FOR_SUPPORTING_CONTACT": "Untuk dukungan pelanggan silakan hubungi", "ACCOUNT_IS_INACTIVE": "<PERSON>kun Anda saat ini tidak aktif", "PLEASE_INPUT_REFERRAL_CODE": "<PERSON><PERSON>an masukkan kode undangan untuk mengaktifkan akun Anda", "ACCOUNT_IS_SUSPENDED": "<PERSON><PERSON>n <PERSON>a saat ini diblokir", "THERE_ARE_NO_TRANSACTIONS": "Belum ada transaksi. Lakukan transaksi pertama sekarang.", "ASKING_UPDATE_KYC_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menyelesaikan KYC sekarang?", "DO_IT_LATER": "lakukan nanti", "I_WILL_DO_IT_LATER": "<PERSON><PERSON> akan melakukannya nanti", "BASIC_INFO": "<PERSON><PERSON>", "SUCCESSFULLY_KYC": "Proses Verifikasi KYC Anda Telah Berhasil", "IMAGE_SIZE_TOO_LARGE": "Gambar terlalu besar. <PERSON>p perkecil ukuran gambar sebelum mencoba kembali.", "IMAGE_SIZE_TOO_SMALL": "Ukuran gambar terlalu kecil. Harap perbesar ukuran gambar sebelum mencoba lagi.", "KTP_SIZE_NOTE": "Ukuran file antara 110KB dan 2MB", "CHECKING_COMPLETED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "YOUR_INFO_LOOKS_GOOD": "Data Anda sudah lengkap!", "FOUND_INFO_IN_GLOBAL_SANCTIONS": "<PERSON><PERSON>, kami belum dapat melanjutkan proses pendaftaran Anda.", "PLEASE_CONTACT": "<PERSON><PERSON> hub<PERSON>i", "FOR_SUPPORTING": "untuk bantuan.", "RESEND": "<PERSON><PERSON>", "VERIFIED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NOT_VERIFIED": "Tidak terverifikasi", "PLEASE_VERIFY_YOUR_EMAIL_ADDRESS": "Harap veri<PERSON><PERSON><PERSON> al<PERSON> email Anda", "DO_YOU_HOLD_KTP": "Apakah Anda Warga Negara Indonesia? Pemegang visa yang diterbitkan Indonesia (termasuk pemegang kartu KITAS/KITAP), silakan pilih TIDAK.", "YES": "Ya", "NO": "Tidak", "WE_ARE_PROCESSING_YOUR_CARD": "Kami sedang memproses info kartu Anda. Harap tunggu sebentar", "VIRTUAL_BALANCE": "Saldo Virtual", "TAKE_SELFIE_NOT_SUPPORT": "<PERSON><PERSON><PERSON> Anda tidak mendukung atau izin akses ke kamera ditolak. Harap hubungi hello{'@'}goro.id.", "CLICK_TO_CONFIRM": "Klik untuk mengonfirmasi rekening bank Anda", "SETTINGS": "<PERSON><PERSON><PERSON><PERSON>", "DISPLAY_CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "OTHER_CURRENCIES_ARE_REFERENCES_ONLY": "Setara dengan {value}, Konversi ke mata uang lain hanya untuk referensi. Jumlah dibulatkan ke bawah hingga 2 angka di belakang koma.", "ARE_REFERENCES_ONLY": "{value} hanya referensi. <PERSON><PERSON><PERSON> dibulatkan ke bawah hingga 2 tempat desimal.", "MY_PORTFOLIO": "<PERSON><PERSON><PERSON>a", "UPLOAD_IDENTITY_CARD": "Unggah Kartu\nIdentitas", "IDENTITY_CONFIRMATION": "Konfirmasi\nIdentitas", "VERIFY_INFORMATION": "Verifikasi\nIdentitas", "ARE_YOU_AN_INDONESIAN": "<PERSON><PERSON><PERSON><PERSON>a WNI (Warga Negara Indonesia)?", "CLICK_TO_UPLOAD_KTP": "Klik Untuk Mengunggah KTP (Kartu Tanda Penduduk) Anda", "CLICK_TO_UPLOAD_PASSPORT": "Klik Untuk Mengunggah Paspor", "VERIFYING_YOUR_IDENTITY": "Identitas Anda Sedang\nMelalui <PERSON>", "PLEASE_CHECK_YOUR_VERIFICATION_STATUS": "Mohon cek secara berkala mengenai\nstatus verifikasi anda", "YOUR_IDENTITY_HAS_VERIFIED": "Identitas Anda Telah\nTerverifikasi", "PLEASE_PROCEED_TO_NEXT_STAGE": "Silahkan lanjut ke tahap berikutnya", "SELFIE_NOTE": "<PERSON><PERSON><PERSON> kamera anda aktif dan\nwajah anda terlihat jelas tidak\ntertutup masker", "HAPPY_INVESTING": "<PERSON>am tumbuh bersama!", "FINISH": "Menyelesaikan", "VERIFICATION_FAILED": "Ma<PERSON>, Verifikasi Identitas\nAnda Gagal", "KYC_FIELDS": {"CITY": "Kota", "NAME": "<PERSON><PERSON>", "RTRW": "RT/RW", "GENDER": "<PERSON><PERSON>", "ADDRESS": "<PERSON><PERSON><PERSON>", "VILLAGE": "<PERSON><PERSON>", "DISTRICT": "<PERSON><PERSON><PERSON>", "IDNUMBER": "Nomor ID", "PROVINCE": "<PERSON><PERSON><PERSON>", "RELIGION": "<PERSON><PERSON>a", "BLOODTYPE": "<PERSON><PERSON><PERSON>", "EXPIRYDATE": "<PERSON><PERSON>", "OCCUPATION": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NATIONALITY": "<PERSON><PERSON><PERSON><PERSON>", "MARITALSTATUS": "Status Pernikahan", "BIRTHPLACEBIRTHDAY": "Tempat dan <PERSON>", "DATEOFBIRTH": "<PERSON><PERSON>", "ZIPPOSTAL": "Kode Pos"}, "COULD_NOT_DETECT_SOME_INFO": "<PERSON>mi tidak dapat mendeteksi beberapa informasi: {value}, silakan coba unggah lagi!", "CONTINUE_TO_PAY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VIEW_PHOTO": "<PERSON><PERSON>", "TAKE_A_PHOTO": "Mengambil foto", "CHOOSE_A_PHOTO": "<PERSON><PERSON><PERSON>", "PHOTO_CROPPER": "Pemotong Foto", "KYC_CORRECT_EXAMPLE": "<PERSON><PERSON><PERSON>", "KYC_WRONG_EXAMPLE": "<PERSON><PERSON><PERSON>", "KYC_ID_CARD_CORRECT_NOTE": "Kartu identitas terlihat jelas dan terbaca", "KYC_ID_CARD_WRONG_NOTE": "Kartu identitas tidak terbaca (buran, terlalu jauh, atau terdapat pantulan cahaya)", "PASSPORT_CORRECT_NOTE": "Passport terlihat jelas dan terbaca", "PASSPORT_WRONG_NOTE": "Passport tidak terbaca (buram, terlalu jauh, atau terdapat pantulan cahaya)", "IDENTITY_CONFIRMATION_TITLE": "Konfirmasi Identitas", "KTP_MAKE_SURE_INFO_CORRECT": "Pastikan Informasi di samping sesuai dengan KTP Anda.", "PASSPORT_MAKE_SURE_INFO_CORRECT": "Pastikan Informasi di samping sesuai dengan Paspor Anda.", "SELFIE_GUIDE": "Panduan Swafoto", "SELFIE_GUIDE_NOTE": "Pastikan swafoto yang diambil\nsesuai dengan ketentuan berikut ini", "SELFIE_CORRECT_NOTE": "Pastikan wajah terlihat jelas dengan pencahayaan yang cukup", "SELFIE_WRONG_NOTE_1": "JANGAN gunakan kacamata", "SELFIE_WRONG_NOTE_2": "JANGAN menutupi wajah", "SELFIE_WRONG_NOTE_3": "JANGAN menghadap atas atau bawah", "ID_NUMBER": "Nomor ID", "GENDER": "<PERSON><PERSON>", "BLOOD_TYPE": "<PERSON><PERSON><PERSON> darah", "DISTRICT": "<PERSON><PERSON><PERSON>", "VILLAGE": "<PERSON><PERSON>", "PROVINCE": "<PERSON><PERSON><PERSON>", "ADDRESS": "<PERSON><PERSON><PERSON>", "RELIIGION": "<PERSON><PERSON>a", "EXPIRY_DATE": "<PERSON><PERSON> kadalua<PERSON>", "OCCUPATION": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NATIONALITY": "<PERSON><PERSON><PERSON><PERSON>", "MARITAL_STATUS": "Status pernikahan", "DATE_OF_BIRTH": "<PERSON><PERSON> lahir", "BIRTH_PLACE": "Tempat lahir", "PASSPORT_NUMBER": "Nomor paspor", "SURNAME": "<PERSON><PERSON>", "GIVEN_NAME": "<PERSON><PERSON> pem<PERSON>", "PLACE_OF_BIRTH": "Tempat Lahir", "COUNTRY": "Negara", "DATE_OF_ISSUE": "<PERSON><PERSON> pengeluaran", "DATE_OF_EXPIRY": "<PERSON><PERSON>", "ISSUING_AUTHORITY": "Mengeluarkan autoritas", "SELECT_GENDER": "<PERSON><PERSON><PERSON> jeni<PERSON> k<PERSON>", "SELECT_BLOOD_TYPE": "<PERSON><PERSON><PERSON> golongan darah", "IDENTITY_CARD_PHOTO_GUIDE": "Panduan Foto\nKartu Identitas", "IDENTITY_CARD_PHOTO_GUIDE_DESC": "Pastikan foto yang Anda ambil sesuai\ndengan panduan berikut ini", "PASSPORT_PHOTO_GUIDE": "Panduan Foto Paspor", "PASSPORT_PHOTO_GUIDE_DESC": "Pastikan foto yang Anda ambil sesuai\ndengan panduan berikut ini", "LIST_PROPERTIES": "Daftar Properti", "CURRENCY": {"NOTE_LABEL": "Catatan", "NOTES": "mata uang lain hanya sebagai referensi. Ju<PERSON>lah dibulatkan ke bawah hingga 2 desimal."}, "USERNAME": "Username", "KYC": "KYC", "FOR_CUSTOMER_SUPPORTING": "Untuk du<PERSON>ngan p<PERSON>, silakan hubungi GORO melalui Whatsapp di <a href='{whatsapp}' target='_blank'>{whatsapp}</a> atau hubungi <a href='{contactMailTo}'>{contact}</a>", "WARNING_REVIEW_KYC_INFO": "<PERSON><PERSON> tinjau sebelum melanjutkan dan pastikan semua informasi sudah benar.", "WARNING_NAME_MATCH_CARD": "Pastikan nama anda sudah sesuai dengan yang tertera di KTP anda", "WARNING_FORMAT_OF_DOB": "Format DD-MM-YYYY", "KYC_TITLE": "Verifikasi KYC", "WAIL_PROCESS_KYC_TEXT": "Please wait while we process your KYC verification.", "KYC_STATUS": {"HEADLINE": "Status KYC", "VERIFICATION_PROCESS_HEADLINE": "Proses Verifikasi Data KYC", "VERIFICATION_PROCESS_TEXT": "<PERSON><PERSON> me<PERSON>n proses verifikasi data KYC, harap tunggu 1x24 jam", "VERIFICATION_SUCCESSFUL_HEADLINE": "Verifikasi KYC Berhasil", "VERIFICATION_FAILED_HEADLINE": "Verifikasi Data KYC Gagal", "VERIFICATION_FAILED_TEXT": "Data Anda belum ber<PERSON><PERSON>, silakan hubungi tim <PERSON> kami di {phone}", "REFRESH": "<PERSON><PERSON>", "DONE": "Se<PERSON><PERSON>"}}, "PAYMENT": {"EACH": "token", "TOKEN": "Token", "TOKENS": "Token", "CURRENCY": "IDR", "INVESTMENT_SUMMARY": "Pembelian <PERSON>", "TOKEN_QUANTITY": "Kuantitas Token", "ORDER_SUMMARY": "<PERSON><PERSON><PERSON>", "ORDER_TOTAL": "<PERSON><PERSON><PERSON>", "USE_BALANCE": "<PERSON><PERSON><PERSON>", "TOTAL_PAYMENT": "Total pembayaran", "CONTINUE_TO_PAYMENT": "Lanjutkan ke Pembayaran", "PAYMENT_CONFIRMATION": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PAY_NOW": "<PERSON><PERSON>", "CHECKOUT": "Periksa", "SUCCESS": "Selamat!", "SUCCESSFULLY_PURCHASED": "<PERSON><PERSON><PERSON><PERSON> membeli <b>{value}</b> {value2}", "YOU_ARE_NOW_OWNER_OF": "Anda sekarang memiliki total <b>{numOfTokens}</b> {tokenLabel} di {propertyName}!", "YOU_ARE_NOW_OWNER_OF_SWAP": "<PERSON>a sekarang memiliki <b>{numOfTokens}</b> {tokenLabel} di {propertyName}!", "BACK_TO_PROPERTY": "Ke<PERSON><PERSON> ke properti", "PAYMENT_FAILED": "<PERSON><PERSON><PERSON><PERSON> gagal", "PAYMENT_FAILED_DETAIL": "Pembayaran Anda untuk", "PAYMENT_FAILED_DETAIL_2": "gagal", "PAYMENT_FAILED_GUIDE": "<PERSON><PERSON>an periksa informasi pembayaran Anda dan coba lagi atau email <b>hello{'@'}goro.id</b> untuk bantuan.", "PAYMENT_PROCESSING_FEE": "<PERSON><PERSON>ya <PERSON>", "NEXT_EXPECTED_PAYOUT_DATE": "Pembagian sewa selanjutnya paling lambat", "NEXT_EXPECTED_PAYOUT_DATE_TITLE": "PENTING!", "NEXT_EXPECTED_PAYOUT_DATE_DESCRIPTION": "Untuk pembagian hasil sewa bulan {currentRent}, yang akan di<PERSON> paling lambat {nextPayoutDate}, Anda hanya akan menerima bagi hasil sewa untuk {totalDaysReceiveRent} {totalDaysReceiveRentLabel} ({receiveRentRangeDate})", "NEXT_EXPECTED_PAYOUT_DATE_DESCRIPTION_UNDERLINE": "apabila tidak melakukan penjualan token pada bulan {currentMonth}.", "USE_VIRTUAL_BALANCE": "Use Virtual Balance", "PAY_NOW_PROCESSING_FEE": "<PERSON><PERSON>ya <PERSON>", "CREDIT_CARD": "Kartu kredit", "BANK_TRANSFER": "Transfer Bank", "BANK_TRANSFER_PROCESSING_FEE": "Biaya Proses Transfer Bank", "PLEASE_COMPLETE_PROFILE": "GORO mewajibkan kelengkapan KYC bagi pengguna yang memiliki lebih {value} token, Kamu akan dialihkan ke halaman KYC.  Kamu dapat melanjutkan transaksi ini setelah proses KYC lengkap telah selesai.", "CONTINUE_TO_KYC": "Lanjutkan ke KYC", "I_HAVE_READ_AND_AGREE": "<PERSON>gan men<PERSON>ang kotak, saya menyatakan bahwa saya telah membaca dan memahami", "TOKEN_TRANSACTION_AGREEMENT": "<PERSON><PERSON><PERSON><PERSON> Transaksi Properti Fraksional (Token)", "I_AGREE_TO_BE_BOUND": "(\"<PERSON><PERSON>ji<PERSON>\"). <PERSON>a setuju untuk terikat oleh ketentuan-ketentuannya dan kegagalan saya untuk membaca Perjanjian tidak menjadi alasan untuk tidak patuh. <PERSON>a mengesamp<PERSON>kan klaim apa pun bahwa <PERSON> ini tidak dapat diberlakukan karena kegagalan saya untuk membaca atau memahaminya.", "PLEASE_AGREE_TO_AGREEMENT": "<PERSON><PERSON><PERSON> buka dan baca <PERSON>an Transaksi Properti Fraksional (Token) dan centang kotak sebelum Anda melanjutkan pembayaran.", "PLEASE_FILL_THE_BALANCE_OR": "<PERSON><PERSON> masukkan jumlah saldo atau matikan penggunaan saldo untuk melanjutkan", "PAYMENT_METHOD": "<PERSON>", "SUCCESSFULLY_PURCHASED_TITLE": "Selamat!", "TOKENS_BOUGHT": "To<PERSON> di<PERSON>i"}, "ORDER": {"SUCCESSFUL": "<PERSON><PERSON><PERSON>!", "SUCCESSFUL_DETAIL": "<PERSON><PERSON><PERSON> <PERSON>a be<PERSON>. <PERSON>a sekarang resmi menjadi pemilik", "REJECTED": "<PERSON><PERSON><PERSON>!", "REJECTED_DETAIL": "<PERSON><PERSON><PERSON>a untuk membeli {numOfTokens} token dari properti {propertyInfo} telah di<PERSON>.", "REJECTED_REASON": "Alasan: {reason}", "PLACED": "Pesanan Anda Telah Ditempatkan!", "INFO": "<PERSON><PERSON>", "UUID": "UUID", "REFERENCE_ID": "ID Referensi Transaksi", "TRANSACTION_ID": "ID Transaksi", "PROPERTY_NAME": "<PERSON><PERSON>", "REPLACE_PROPERTY_UUID": "Mengganti Properti Uuid", "NUM_OF_TOKENS": "<PERSON><PERSON><PERSON>", "PRICE": "<PERSON><PERSON>", "FEE": "Biaya", "BALANCE_USED": "<PERSON><PERSON>", "REMAINING_AMOUNT_TO_TRANSFER": "<PERSON><PERSON><PERSON> yang <PERSON> Ditransfer", "NOTE": "<PERSON><PERSON><PERSON> biaya transaksi yang relevan akan ditanggung oleh pelanggan.", "TOKEN_NOTE": "Pengiriman token mungkin memerlukan waktu hingga 3 hari kerja untuk dikreditkan ke akun Anda.", "TRANSACTION_NOTE": "Transaksi akan di<PERSON>ukan dalam mata u<PERSON> (IDR)", "CONTACT_INFO": "<PERSON><PERSON>a memiliki per<PERSON>, silakan hubungi hello{'@'}goro.id", "BANK_TRANSFER_INTRO": "Anda dapat mentransfer dana ke akun GORO Aspire dari akun bank mana pun menggunakan SWIFT/Telegraphic transfer. Dana yang Anda kirimkan ke akun di bawah ini akan diterima oleh The Currency Cloud Limited atas nama GORO. Detail akun GORO adalah sebagai berikut:", "PAY_NOW_ON_SGQR": "PayNow di SGQR", "SCAN_TO_PAY": "PINDAI UNTUK MEMBAYAR", "BANK": "Bank:", "BANK_NAME": "Nama Bank:", "BANK_CODE": "Kode Bank:", "BRANCH_CODE": "<PERSON><PERSON>:", "BANK_ADDRESS": "Alamat Bank:", "BANK_COUNTRY": "Negara Bank:", "ACCOUNT_HOLDER_NAME": "<PERSON><PERSON>:", "SGD_GORO_BANK_DETAILS": "Rincian rekening bank dolar Singapura:", "SGD_IF_FAST_ACT_GIRO": "• Jika Anda mengirim melalui FAST/ACT/GIRO:", "SGD_IF_FAST_ACT_GIRO_VALUE": "GORO PROPERTY PTE. LTD.", "SGD_IF_FAST_MEPS_LOCAL_TT": "• Jika Anda mengirim melalui MEPS/Local TT:", "SGD_IF_FAST_MEPS_LOCAL_TT_VALUE": "Aspire FT Pte. Ltd. on behalf of GORO PROPERTY PTE. LTD.", "SGD_ACCOUNT_NUMBER": "Nomor Rekening:", "SGD_ACCOUNT_NUMBER_VALUE": "8852-1592-5748", "SGD_BANK_NAME_VALUE": "DBS Bank Ltd", "SGD_BANK_CODE_VALUE": "7171", "SGD_BRANCH_CODE_VALUE": "001", "USD_GORO_BANK_DETAILS": "Rincian rekening bank dolar AS:", "USD_ACCOUNT_NUMBER": "Nomor Rekening/IBAN:", "USD_BANK_SWIFT_BIC": "SWIFT/BIC Bank:", "USD_ACCOUNT_HOLDER_NAME_VALUE": "GORO PROPERTY PTE. LTD.", "USD_ACCOUNT_NUMBER_VALUE": "**********************", "USD_BANK_NAME_VALUE": "The Currency Cloud Limited", "USD_BANK_SWIFT_BIC_VALUE": "TCCLGB3L", "USD_BANK_ADDRESS_VALUE": "12 Steward Street, The Steward Building, London, E1 6FQ, GB", "USD_BANK_COUNTRY_VALUE": "GB", "TRANSFER_CONTENT": "Deskripsi Transfer:", "TRANSFER_CONTENT_VALUE": "{value}", "TRANSFER_AMOUNT": "Jumlah Transfer:", "EURO_GORO_BANK_DETAILS": "Rincian rekening bank Euro:", "EURO_ACCOUNT_HOLDER_NAME_VALUE": "GORO PROPERTY PTE. LTD.", "EURO_ACCOUNT_NUMBER": "Nomor Rekening/IBAN:", "EURO_ACCOUNT_NUMBER_VALUE": "BE05 9677 2837 4275", "EURO_BANK_NAME_VALUE": "WISE EUROPE S.A", "EURO_BANK_SWIFT_BIC": "SWIFT/BIC Bank:", "EURO_BANK_SWIFT_BIC_VALUE": "TRWIBEB1XXX", "EURO_BANK_ADDRESS_VALUE": "Rue du Trône 100, 3rd floor, Brussels, 1050, Belgium", "EURO_BANK_COUNTRY_VALUE": "Belgium", "AUD_GORO_BANK_DETAILS": "Rincian rekening bank dolar Australia:", "AUD_ACCOUNT_HOLDER_NAME_VALUE": "GORO PROPERTY PTE. LTD.", "AUD_ACCOUNT_NUMBER": "Nomor Rekening:", "AUD_ACCOUNT_NUMBER_VALUE": "*********", "AUD_BSB_NUMBER": "Nomor BSB:", "AUD_BSB_NUMBER_VALUE": "774001", "AUD_BANK_ADDRESS": "<PERSON><PERSON><PERSON>:", "AUD_BANK_ADDRESS_VALUE": "Suite 1. Level 11,66 Goulburn Street, Sydney, 2000, Australia", "AUD_BANK_COUNTRY_VALUE": "Australia", "UK_GORO_BANK_DETAILS": "Rincian rekening bank poundsterling Inggris:", "UK_ACCOUNT_HOLDER_NAME_VALUE": "GORO PROPERTY PTE. LTD.", "UK_ACCOUNT_NUMBER": "Nomor Rekening:", "UK_ACCOUNT_NUMBER_VALUE": "********", "UK_SORT_CODE": "<PERSON>ru<PERSON><PERSON>:", "UK_SORT_CODE_VALUE": "23-14-70", "UK_IBAN": "IBAN:", "UK_IBAN_VALUE": "GB86 TRWI 2314 7087 3663 59", "UK_BANK_NAME_VALUE": "<PERSON>", "UK_BANK_ADDRESS_VALUE": "56 Shoreditch High Street, London, E16JJ, United Kingdom", "UK_BANK_COUNTRY_VALUE": "United Kingdom", "IMPORTANT_UPDATE": "Pembaruan Penting:", "BANK_ACCOUNT_DETAILS_CHANGED": "Detail Rekening Bank Telah Berubah", "IMPORTANT_UPDATE_CONTENT": "<PERSON><PERSON> te<PERSON> memperbarui informasi rekening bank untuk <b><PERSON><PERSON> (SGD)</b> dan <b>Dolar Amerika Serikat (USD)</b>. <PERSON>hon <b>periksa detail terbaru di bawah ini dengan seksama</b> dan <b>jangan transfer ke rekening lama</b> untuk menghindari kegagalan transaksi."}, "SELL_TOKEN": {"UUID": "<PERSON><PERSON>", "ADMIN_REQUEST_ID": "Admin Request Id", "NAME": "<PERSON><PERSON>", "EMAIL": "Email", "PROPERTY_NAME": "<PERSON><PERSON>", "NUM_OF_TOKENS": "<PERSON><PERSON><PERSON>", "AMOUNT": "<PERSON><PERSON><PERSON>", "AMOUNT_FROM_BALANCE": "<PERSON><PERSON><PERSON>", "RECEIVED_AMOUNT": "<PERSON><PERSON><PERSON>", "FEE": "Biaya", "FEE_PERCENT": "Persentase Biaya", "DESCRIPTION": "<PERSON><PERSON><PERSON><PERSON>", "REASON": "<PERSON><PERSON><PERSON>", "STATUS": "Status", "SELL_TOKEN_HISTORY": "Riwayat Penjualan Token", "SELL_TOKEN_REQUEST_HISTORY": "Riwayat Permintaan Penjualan Token", "THERE_ARE_NO_SELL_TOKEN_REQUEST_HISTORY": "Tidak ada riwayat permintaan penjualan token", "CANCEL_REQUEST": "Batalk<PERSON>", "SELLABLE_TOKENS": "Token yang dapat dijual", "NOT_HAVE_SELLABLE_TOKENS": "Anda tidak memiliki token yang dapat dijual untuk properti ini. (Klik untuk detail)", "REQUEST_SELL_TOKEN": "<PERSON><PERSON><PERSON><PERSON>", "SUMMARY": "<PERSON><PERSON><PERSON>", "TOKEN_QUANTITY": "<PERSON><PERSON><PERSON>", "PRICE_PER_TOKEN": "Harga per <PERSON>", "SUBTOTAL": "Subtotal", "PROCESSING_FEE": "Biaya Proses", "TOTAL_RECEIVE": "Total Diterima", "CONFIRM_SELL_REQUEST": "Berikutnya", "SUBMIT_SELL_REQUEST": "Lanjutkan", "SELL_REQUEST_CONFIRMATION": "Konfirmasi <PERSON>", "SELL_REQUEST_SUBMITTED": "<PERSON><PERSON><PERSON><PERSON>a untuk menjual {tokenValue} {propertyValue} {status}.", "PENDING_APPROVAL": "sedang diproses dan akan selesai dalam waktu maksimal 72 jam", "VIEW_MY_TRANSACTIONS": "<PERSON><PERSON>", "YOUR_STATUS_ON": "Status untuk {propertyName}", "AVAILABLE_TOKENS_TO_SELL": "Total token yang dapat dijual: {value}", "OWNING_TOKENS": "Token yang dimiliki: {value}", "LOCKED_TOKENS": "Token terkunci: {value}", "PENDING_TOKENS": "Token yang diminta untuk dijual: {value}", "OWNING_TOKENS_LABEL": "Token yang dimiliki", "LOCKED_TOKENS_LABEL": "{value} Token terkunci", "TOKEN_SOLD": "<PERSON><PERSON> ter<PERSON>al"}, "SELL_TOKEN_TABLE_HEADER": {"STATUS": "Status", "PROPERTY": "Properti", "NO_OF_TOKENS": "<PERSON><PERSON><PERSON>", "RECEIVED_AMOUNT": "<PERSON><PERSON><PERSON>", "FEE": "Biaya", "NOTE": "Catatan", "DATE": "Tanggal"}, "SELL_TOKEN_STATUSES": {"PENDING": "Tertunda", "APPROVED": "Disetuju<PERSON>", "REJECTED": "<PERSON><PERSON><PERSON>", "CANCELLED": "Di<PERSON><PERSON><PERSON>"}, "SWAP_TOKEN": {"SWAP_TOKEN": "<PERSON><PERSON>", "SWAP_TOKEN_SUMMARY": "<PERSON><PERSON><PERSON>", "AVAILABLE_TOKENS_TO_SWAP": "Total token yang tersedia untuk ditukar: {value}", "NOT_HAVE_SWAPPABLE_TOKENS": "Anda tidak memiliki token yang dapat ditukar untuk properti ini. (Klik untuk detail)", "CURRENT_PROPERTY": "Properti Saat Ini", "NEW_PROPERTY": "Properti Baru", "YOUR_TOKEN": "To<PERSON>", "PROPERTY_TOKEN": "Token Properti", "TOKEN_TO_SWAP": "Token untuk <PERSON>", "TOKEN_TO_RECEIVE": "Token untuk Diterima", "TOKEN_AVAILABLE": "{value} Token Tersedia", "TOTAL_TOKEN": "Total Tukar Token", "NUMBER_OF_TOKEN_MUST_BE_POSITIVE": "Jumlah token harus positif. Mohon periksa sebelum melanjutkan ke langkah berikutnya.", "NUMBER_OF_TOKEN_MUST_BE_POSITIVE_FEE_APPLIED": "Biaya berlaku untuk transaksi ini. Pastikan jumlah token lebih dari {value}.", "PLEASE_READ_AND_AGREE_TO_AGREEMENT_OF_CURRENT_PROPERTY": "<PERSON><PERSON><PERSON> baca dan setujui <b><PERSON><PERSON><PERSON><PERSON> (Token) Fraksional</b> dari properti saat ini sebelum melanjutkan.", "CONTINUE_TO_SWAP": "Lanjutkan untuk <PERSON>", "SWAP_NOW": "<PERSON><PERSON>", "SUCCESSFULLY_SWAPPED": "<PERSON><PERSON><PERSON><PERSON> <b>{numOfToken}</b> token {currentPropertyName} dengan {newPropertyName}", "CURRENT_EXPECTED_PAYOUT_DESCRIPTION": "Pembagian hasil sewa {currentPropertyName} sampai dengan tanggal {yesterday} akan tetap didistribusikan sesuai dengan jadwal pembagian hasil sewa berikutnya.", "PRESALE_EXPECTED_PAYOUT_DESCRIPTION": "Properti {propertyName} ini sedang dalam persiapan sehingga belum menghasilkan pendapatan sewa. <PERSON><PERSON><PERSON>a properti ini akan mulai menghasilkan pendapatan sewa dari tanggal: {firstLiveOn}.", "NEXT_EXPECTED_PAYOUT_DESCRIPTION": "Untuk {newPropertyName}, pembagian hasil sewa berikutnya akan didistribusikan sebelum {nextPayoutDate}. Pembagian hasil sewa Anda akan disesuaikan secara pro-rata berdasarkan tanggal {receiveRentFrom} hingga {receiveRentTo} ({totalDaysReceiveRent} {totalDaysReceiveRentLabel}).", "NEXT_EXPECTED_PAYOUT_DESCRIPTION_UNDERLINE": "Ini hanya berlaku jika Anda tidak menjual atau <PERSON>kar token <PERSON>a sebelum akhir {currentMonth}.", "SWAP_DETAILS": "<PERSON><PERSON>", "YOUR_BALANCE": "Saldo GORO Anda", "CREDITED_TO_BALANCE": "Dikreditkan ke Saldo", "BALANCE_AFTER_SWAP": "<PERSON><PERSON>", "DEDUCTED_METHOD": "Deduction Fee Method", "DEDUCTED_FROM_BALANCE": "Dipotong dari saldo GORO", "DEDUCTED_FROM_PROCEEDS": "<PERSON><PERSON><PERSON> dari hasil trans<PERSON>i", "TOTAL_RECEIVED_TOKEN": "Total Token diterima", "TOKENS_DEDUCTED_FOR_FEE": "Token Dipotong untuk Biaya", "SWAP_TOKEN_WARNING": "Biaya berlaku untuk transaksi ini. Pastikan jumlah token lebih dari {token}."}, "TRANSACTION": {"TRANSACTION_DETAILS": "<PERSON><PERSON><PERSON>", "TRX_ID": "ID Transaksi", "TYPE": "<PERSON><PERSON>", "TOKENS": "Token", "NUM_OF_TOKENS": "<PERSON><PERSON><PERSON>", "VALUE": "<PERSON><PERSON>", "DESCRIPTION": "Keterangan", "DATE": "Tanggal", "EXTERNAL_ID": "ID Transaksi", "BLOCKCHAIN_TRANSACTION_ID": "Rantai Blok ID Transaksi", "ORDER_UUID": "<PERSON><PERSON>", "SELL_REQUEST_UUID": "<PERSON><PERSON><PERSON><PERSON>", "REFERENCE_ID": "ID Referensi Transaksi", "PRICE_PER_TOKEN": "Harga per <PERSON>", "STATUS": "Status", "PAYMENT_METHOD": "<PERSON><PERSON>"}, "ASSETS": {"VIEW_MY_ASSETS": "<PERSON><PERSON>", "MY_BALANCE": "<PERSON><PERSON>", "WITHDRAW": "<PERSON><PERSON>", "CURRENT_ACCOUNT_VALUE": "<PERSON><PERSON>", "TOTAL_PROPERTY_VALUE": "Total Nilai Properti", "LAST_RENT_EARNED": "Pendapatan Sewa Terakhir", "TOTAL_RENT_EARNED": "Total Sewa yang Diperoleh", "PROPERTIES_OWNED": "Properti yang <PERSON>", "CURRENT_VALUE": "<PERSON><PERSON>", "YOUR_ARY_RETURN": "Imbal hasil/ARY <PERSON>a", "NO_PROPERTY": "Ayo mulai investasi properti pertama Anda! lalu lihat kepemilikan properti, ni<PERSON>, a<PERSON><PERSON><PERSON><PERSON>, dan pendapatan sewa Anda di sini. Anda juga dapat menarik pendapatan sewa Anda dari menu ini. <PERSON><PERSON> di bawah untuk memulai.", "VIEW_PROPERTIES": "Investasi <PERSON>", "ANNUALIZED_PERCENT": "(% disetahunkan)", "TOTAL_VALUE": "<PERSON><PERSON> total", "LOCKED_TOKENS": "Token terkunci", "CLICK_TO_SEE_DETAILS": "Klik untuk melihat detailnya.", "LOCKED_TOKENS_OF": "Token terkunci dari {propertyName}", "LOCKED_UNTIL": "Terkunci sampai", "THIS_PROPERTY_HAS_NO_LOCKED_TOKENS": "Properti ini tidak memiliki token yang terkunci", "TOKEN_AVAILABLE": "Token tersedia", "SWAP": "<PERSON><PERSON>", "SELL": "<PERSON><PERSON>", "CONTRACT": "Kontrak"}, "REFERRAL": {"REFERRALS": "Undangan", "REFERRAL_CODE": "Kode Referral (opsional)", "REFERRAL_JUST_GIFTED": "memberi Anda akses prioritas", "REFERRAL_SIGN_UP_TO_INVEST": "<PERSON><PERSON><PERSON> sekarang dan mulai investasi properti.", "PLEASE_INPUT_REFERRAL_CODE": "<PERSON><PERSON>an masukkan kode undangan untuk mengaktifkan akun Anda", "USE_THIS_REFERRAL_CODE_FROM": "<PERSON>akan kode undangan ini dari {value} untuk mengaktifkan akun Anda", "COPY_REFERRAL_LINK": "<PERSON><PERSON> Rujukan", "JOIN_GORO_TO_START_INVESTING": "Bergabunglah dengan GORO sekarang untuk dapatkan pendapatan pasif bulanan dari properti pilihan kami", "REFERRAL_OWN_PROPERTIES": "{name} miliki properti menak<PERSON>kan ini. Bergabunglah dengan {name} sekarang di GORO dengan mendaftar akun dan {bonus_text} untuk pembelian pertama{bonus_required_text}.", "REFERRAL_OWN_PROPERTIES_CASHBACK": "dapatkan bonus cashback {cashback_percent}", "REFERRAL_OWN_PROPERTIES_FREE_TOKEN": "dapatkan {token_bonus} {token_label} senilai {token_value}", "REFERRAL_OWN_PROPERTIES_FREE_TOKEN_REQUIRED": " minimal {token_minimum_required} {token_label}", "REFERRAL_BONUS_INFO": "Undang teman dan dapatkan CASHBACK {percent}% setelah mereka melakukan pembelian pertama di GORO", "REFERRAL_BONUS_TOKEN_INFO": "Undang teman dan dapatkan {token_bonus} {token_label} senilai {token_value} setelah mereka menyelesaikan pembelian pertama{token_bonus_required} di GORO.", "REFERRAL_BONUS_TOKEN_REQUIRED_INFO": " minimal {token_minimum_required} {token_label}", "REFERRAL_BONUS_AMOUNT_FOR_REFEREE_INFO": "Buruan! <PERSON>a mungkin masih bisa dapatkan cashback <b>{percent}</b>% dengan melakukan transaksi pertama Anda di GORO!", "REFERRAL_BONUS_TOKEN_FOR_REFEREE_INFO": "Buruan! Anda mungkin masih bisa mendapatkan {token_bonus} {token_label} senilai {token_value} dengan melakukan pembelian pertama{token_bonus_required} di GORO!", "REFERRAL_BONUS_TOKEN_FOR_REFEREE_REQUIRED_INFO": " minimal {token_minimum_required} {token_label}", "REFERRAL_BONUS_TOKEN_FOR_REFEREE_REMIND_INFO": "<PERSON>an lewatkan! Dapatkan bonus {token_bonus} {token_label} senilai {token_value} dengan pembelian minimal {token_minimum_required} {token_minimum_required_label} untuk transaksi pertama Anda. Tambahkan jumlah token Anda sekarang untuk klaim hadiah spesial ini!", "EARN_CASHBACK_FOR_EVERY_REFERRAL": "Dapatkan cashback sebesar <b>{percent}</b>% untuk setiap referral", "EARN_FREE_TOKEN_FOR_EVERY_REFERRAL": "Dapatkan <b>{token}</b> {token_label} senilai {token_value} untuk setiap referral{token_bonus_required}.", "EARN_FREE_TOKEN_FOR_EVERY_REFERRAL_REQUIRED": " (pembelian pertama minimal {token_minimum_required} {token_label})", "BUY_TOKENS_TO_EARN_MORE_INVITES": "Beli token untuk dapatkan lebih banyak undangan!", "HOW_IT_WORK": "<PERSON><PERSON><PERSON>", "INVITED_USERS": "<PERSON><PERSON><PERSON> yang <PERSON>", "HOW_IT_WORK_ITEMS": {"INVITE_YOUR_FRIEND": {"HEADING": "<PERSON>dan<PERSON>", "DESCRIPTION": "<PERSON><PERSON><PERSON> tautan unik Anda ke teman-teman Anda dan undang mereka untuk menggunakan GORO."}, "MAKE_FIRST_PURCHASE": {"HEADING": "Lakukan Pembelian Pertama", "DESCRIPTION": "<PERSON><PERSON><PERSON> teman Anda mendaftar ke GORO dan melakukan pembelian pertama, <PERSON><PERSON> berdua akan dapatkan hadiah!{token_bonus_required}", "DESCRIPTION_REQUIRED": " *pembelian minimal {token_minimum_required} {token_label}"}, "GET_REWARDS": {"HEADING": "<PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "*Anda akan dapatkan {referrer_bonus} dan teman <PERSON>a akan dapatkan {referee_bonus} gratis!", "DESCRIPTION_CASHBACK": "<PERSON><PERSON> akan dapatkan cashback {referrer_bonus} dan teman Anda akan dapatkan cashback {referee_bonus}!"}}, "TOP_REFERRER": "<PERSON><PERSON><PERSON>", "WEEKLY": "Mingguan", "MONTHLY": "Bulanan", "ALL_TIME": "All Time", "RANK": "Rank", "INVITED_USER": "<PERSON><PERSON><PERSON>", "NAME": "Username", "GORO_ID": "ID", "NO_RECORD_TITLE": "Belum Ada Peringkat Saat Ini!", "NO_RECORD_CONTENT": "In<PERSON>h kesempatanmu untuk menjadi yang pertama! A<PERSON> teman-temanmu sebanyak mungkin dan raih posisi teratas di leaderboard.", "INVITES_WILL_EXPIRE_SOON": "{invites} undangan akan kedal<PERSON> dalam {period}.", "USE_THEM_SOON": "Gunakan segera agar tidak melewatkan kesempatan untuk mengajak teman."}, "WITHDRAWALS": {"WITHDRAWALS": "Penarikan", "BANK_ACCOUNT": "Rekening Bank", "HISTORY": "Riwayat", "WITHDRAWAL_HISTORY": "Riwayat Penarikan", "MAKE_YOUR_FIRST_TRANSACTION_TO_WITHDRAWAL_MONEY": "Lakukan transaksi pertama Anda sebelum mengakses halaman penarikan.", "ADD_BANK_ACCOUNT": "Tambahkan Rekening Bank", "SELECT_BANK_NAME": "Pilih Bank", "BANK_ACCOUNT_HOLDER": "<PERSON><PERSON> Pem<PERSON>k <PERSON>", "BANK_ACCOUNT_NUMBER": "Nomor <PERSON>", "CURRENT_BANK_ACCOUNT": "Rekening Bank Saat Ini", "CHANGE_BANK_ACCOUNT": "Ubah Rekening Bank", "CONFIRM_BANK_ACCOUNT": "Konfirmasi Rekening Bank", "CONFIRM_BANK_ACCOUNT_SUCCESS": "<PERSON><PERSON> telah ber<PERSON><PERSON> mengkonfirmasi rekening Bank baru Anda.", "REDIRECT_IN": "Otomatis di<PERSON> dalam {value} detik", "CREATE_WITHDRAWAL": "Buat Penarikan", "CURRENT_BALANCE": "Saldo saat ini", "INPUT_AMOUNT_TO_WITHDRAWAL": "<PERSON><PERSON><PERSON><PERSON> jumlah yang akan ditarik", "MINIMUM_WITHDRAWAL_AMOUNT": "<PERSON><PERSON><PERSON>", "WITHDRAWAL_AMOUNT": "<PERSON><PERSON><PERSON>", "FEE": "Biaya", "TOTAL": "Total", "ACCOUNT_BALANCE_LEFT": "<PERSON><PERSON> akun setelah penarikan dana", "WITHDRAWAL_DESCRIPTION": "Keterangan", "WITHDRAWAL_VOUCHER": "Masukkan voucher (optional)", "MAKE_WITHDRAWAL": "Ajukan penarikan uang", "THERE_ARE_NO_CHANGES_HISTORY_YET": "Belum ada riwayat per<PERSON>han rekening", "THERE_ARE_NO_WITHDRAWAL_HISTORY": "Belum ada riwayat penarikan", "WITHDRAWAL_DETAILS": "Detail penarikan", "AMOUNT": "<PERSON><PERSON><PERSON>", "BALANCE_DEDUCTED": "<PERSON><PERSON>", "BANK_NAME": "Nama Bank", "ACCOUNT_HOLDER": "Pemilik <PERSON>", "ACCOUNT_NUMBER": "Nomor <PERSON>", "NOTE": "<PERSON><PERSON>", "DATE": "Tanggal", "CANCEL_REQUEST": "Batalkan penarikan", "NEXT": "<PERSON><PERSON><PERSON><PERSON>", "CONFIRM": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BACK": "Kembali", "WITHDRAWAL_FEE": "<PERSON><PERSON>ya <PERSON>", "REASON": "<PERSON><PERSON><PERSON>", "STATUS": "Status", "CLICK_TO_VERIFY": "Konfirmasi nama rekening untuk mengaktivasi rekening", "TAP_HERE": "klik disini", "VERIFY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LAST_CHANGED": "<PERSON><PERSON><PERSON>", "CANCEL": "<PERSON><PERSON>", "BANK_ACCOUNT_NOT_CONFIGURED": "Rekening bank belum ada", "DETAILS": "<PERSON><PERSON><PERSON>", "ACCOUNT_NUMBER_MUST_AT_LEAST_8_DIGITS": "Nomor rekening minimal harus 8 digit", "HAVE_PENDING_BANK_ACCOUNT": "Anda memiliki detail rekening bank baru untuk dikonfirmasi, si<PERSON><PERSON> perik<PERSON> email <PERSON><PERSON>.", "HAVE_PENDING_BANK_ACCOUNT_NAME": "Anda memiliki detail rekening bank baru untuk dikonfirmasi.", "CONFIRM_NOW": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE_CAN_NOT_ADD_BANK_ACCOUNT": "<PERSON><PERSON>, kamu hanya dapat mengakses laman ini jika kamu memiliki setidaknya 1 token. <PERSON><PERSON>an melakukan investasi properti terlebih dahulu.", "BANK_WARNING_MESSAGE": "mohon pastikan bahwa anda tidak memasukkan nomor virtual account, nomor telepon, dan nomor kartu atm.", "PAYPAL_ACCOUNT_EMAIL": "<PERSON><PERSON>", "YOUR_PAYPAL_ACCOUNT_EMAIL": "<PERSON><PERSON> akun <PERSON>", "MANDATORY": "* <PERSON><PERSON><PERSON>", "WITHDRAWAL_FORM": "<PERSON><PERSON><PERSON>", "MESSAGE_CONFIRM_BANK_ACCOUNT": "<PERSON><PERSON>kah Anda yakin ini rekening bank yang benar?", "EXCHANGE_RATES": "<PERSON><PERSON> tuka<PERSON>", "BANK_ACCOUNT_HOLDER_NAME": "<PERSON><PERSON> Pem<PERSON>k <PERSON>", "BANK_ADDRESS": "Alamat Bank", "BANK_COUNTRY": "Negara Bank", "NOTES": "<PERSON><PERSON>", "PLEASE_MAKE_SURE_BANK_CORRECT": "Pastikan informasi rekening bank yang Anda masukkan sudah benar.", "YOUR_BANK_ACCOUNT_IS_BEING_VERIFIED": "Rekening bank Anda sedang diverifikasi", "PLEASE_WAIT_FOR_A_MAXIMUM": "<PERSON><PERSON>gu maksimal 72 jam", "EXCHANGE_RATES_NOTE": "Informasi nilai tukar di atas hanya sebagai indikasi dan dikenakan biaya transfer bank.", "NOTICE_CHANGE_BANK_ACCOUNT": "Penggantian akun bank hanya dapat dilakukan satu kali dalam sebulan. Pastikan kamu memilih akun bank dengan tepat.", "NOTICE_WITHDRAW_TAKE_TIME": "<PERSON><PERSON><PERSON> dana paling lambat {take_time} Jam", "RECEIVE_AMOUNT": "<PERSON><PERSON><PERSON> yang di<PERSON>ima", "WANT_TO_ENJOY_RETURNS": "Penarikan untuk membeli tiket liburan.", "WITHDRAWAL_SUMMARY": "<PERSON><PERSON><PERSON>", "CONTINUE": "Lanjutkan", "WITHDRAWAL_CONFIRMATION": "<PERSON>n<PERSON><PERSON><PERSON>", "PLEASE_ENSURE_YOUR_BANK_CORRECT": "Pastikan informasi rekening bank Anda benar", "WITHDRAWAL_DESCRIPTION_TOOLTIP": "<PERSON><PERSON>an isi deskripsi penarikan untuk catatan Anda.", "DISCOUNT_APPLIED_LABEL": "Be<PERSON><PERSON><PERSON> diskon:", "AMOUNT_RECEIVED": "<PERSON><PERSON><PERSON>"}, "RENTAL_INCOME": {"RENTAL_INCOME_REPORTS": "<PERSON><PERSON>an Pendapa<PERSON> Sewa", "RENTAL_INCOME_DETAIL": "Rincian Pendapatan Sewa", "DETAILS": "<PERSON><PERSON><PERSON>", "SUMMARY_REPORT": "<PERSON><PERSON><PERSON><PERSON>oran", "RENTAL_DISTRIBUTION_REPORT": "Laporan distribusi sewa {propertyName} selama {monthAndYear}", "RENTAL_INCOME_NET": "Pendapatan Sewa Bersih ({value} hari)", "RENTAL_INCOME_GROSS": "Pendapatan Sewa Kotor", "TOTAL_TOKENS": "Total Token", "EARNINGS_PER_TOKEN_PER_DAY": "<PERSON><PERSON><PERSON><PERSON> per Token per Hari", "RENTAL_DEDUCTIONS": "Biaya Operasional Sewa", "THERE_ARE_NO_RENTAL_INCOME": "Anda belum memiliki pendapatan sewa. <PERSON>ka Anda baru membeli token pada bulan {currentMonth}, maka kami baru akan membagikan pendapatan sewa paling lambat {nextPayoutDate}.", "BREAK_DOWN_REPORT": "<PERSON><PERSON><PERSON>", "RENTAL_MONTH": "<PERSON><PERSON><PERSON>", "VIRTUAL_RENTAL_INCOME_REPORTS": "Laporan Pendapatan Sewa Virtual", "RECEIVED": "DITERIMA", "NON_IDR_REFERENCE_ONLY": "* setiap jumlah non-IDR hanya untuk referensi", "NOTES": "<PERSON><PERSON>", "DEDUCTION": "<PERSON><PERSON><PERSON><PERSON>", "SUMMARY": "<PERSON><PERSON><PERSON>", "DISTRIBUTION_DATE": "Tanggal Distribusi"}, "ABOUT_US": {"VIEW_PROPERTIES": "<PERSON><PERSON>", "VIEW_OPEN_POSITIONS": "<PERSON><PERSON> posisi yang terbuka", "WE_ARE_ON_MISSION": "Membuka Akses Investasi Properti untuk Semua", "THROUGH_FRACTIONAL_INVESTING": "<PERSON><PERSON> percaya bahwa investasi properti bukan hanya untuk segelintir orang. GORO hadir agar siapa pun bisa berinvestasi dengan mudah dan nyaman.", "OUR_INVESTORS": "Investor <PERSON>", "OUR_ANGLES": "Angel Investor & Penasihat <PERSON>", "JOIN_GORO": "Bergabunglah dengan GORO", "ARE_YOU_PASSIONATE": "Apaka<PERSON> Anda menyukai bidang properti dan ingin menjadi bagian dari masa depan kepemilikan properti? Ayo bergabunglah dengan GORO!"}, "VIRTUAL_BALANCE": {"VIEW_OUR_PROPERIES": "View Our Properties", "CONTINUE_TO_BUY": "Lanjutkan untuk Membeli", "YOUR_VIRTUAL_BALANCE": "Saldo Virtual Anda", "ORDER_CONFIRMATION": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>an", "BUY_NOW": "Investasi <PERSON>", "TOOLTIP_MESSAGE": "Saldo virtual ini tidak dapat ditarik, tetapi setiap hasil sewa yang diperoleh akan menjadi milik <PERSON>. <PERSON><PERSON><PERSON> jangka waktu te<PERSON>tu, saldo virtual ini akan kedalu<PERSON>.", "EXPIRES_ON": "<PERSON><PERSON><PERSON> pada:", "EXPIRED_ON": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada:", "VIRTUAL_BALANCE": "Saldo Virtual:", "VIRTUAL_BALANCE_EARN": "<PERSON><PERSON><PERSON> pendapatan sewa NYATA senilai {value} hari menggunakan saldo virtual Anda.", "REGISTER_TO_BRING": "Daftar untuk mendapatkan penghasilan sewa NYATA menggunakan saldo virtual Anda", "YOU_ARE_NOW_OWNED_TOKENS": "<PERSON>a sekarang memiliki {value} {value2} {value3}!", "EXPIRED": "Kedaluwarsa:", "VIRTUAL_ASSETS": "Aset Virtual ({value1} Properti, {value3} Token), {value5}", "TOKEN_EXPIRED_ON": "Token {token} akan kedalu<PERSON>sa pada {date}"}, "BANK_STATUS": {"PENDING": "Tertunda", "FAILED": "Gaga<PERSON>", "NEED_CONFIRM_NAME": "<PERSON><PERSON> konfirmasi nama", "NEED_CONFIRM_EMAIL": "<PERSON>lu konfirmasi email", "CONFIRMED": "<PERSON><PERSON><PERSON><PERSON>", "DEACTIVATED": "Dinonaktifkan"}, "BANK_STATUS_NOTE": {"PENDING": "Menunggu pemeriksaan", "NEED_CONFIRM_NAME": "<PERSON><PERSON> kon<PERSON>rmasikan nama pemilik akun <PERSON>", "NEED_CONFIRM_EMAIL": "Periksa email untuk mengonfirmasi rekening bank Anda", "CONFIRMED": "Rekening bank berhasil dikonfirmasi", "DEACTIVATED": "Rekening bank dinonaktifkan"}, "BANK_ACCOUNT_HISTORY_TABLE_HEADER": {"STATUS": "Status", "BANK": "Bank", "ACCOUNT_NUMBER": "Nomor <PERSON>", "ACCOUNT_HOLDER": "Pemilik <PERSON>", "DATE": "Tanggal W<PERSON>"}, "WITHDRAWAL_HISTORY_TABLE_HEADER": {"STATUS": "Status", "AMOUNT": "<PERSON><PERSON><PERSON>", "FEE": "Biaya", "BANK": "Bank", "ACCOUNT_NUMBER": "Nomor <PERSON>", "NOTE": "Keterangan", "DATE": "Tanggal W<PERSON>"}, "WITHDRAWAL_STATUSES": {"PENDING": "Tertunda", "APPROVED": "Disetuju<PERSON>", "REJECTED": "<PERSON><PERSON><PERSON>", "CANCELLED": "Di<PERSON><PERSON><PERSON>", "COMPLETED": "Se<PERSON><PERSON>", "FAILED": "Gaga<PERSON>", "PENDING_PROCESS": "Proses Tertunda", "PROCESSING": "<PERSON><PERSON><PERSON>"}, "RENTAL_INCOME_TABLE_HEADER": {"TRANSACTION_ID": "ID Transaksi", "PROPERTY": "Properti", "VALUE": "<PERSON><PERSON>", "STATUS": "Status", "RENTAL_MONTH": "<PERSON><PERSON><PERSON>"}, "TRANSACTIONS_TABLE_HEADER": {"PROPERTY": "Properti", "TYPE": "<PERSON><PERSON><PERSON>", "TOKEN": "Token", "VALUE": "<PERSON><PERSON>", "STATUS": "Status", "DESCRIPTION": "Keterangan", "DATE": "Tanggal W<PERSON>"}, "TRANSACTIONS_TYPES": {"ALL": "<PERSON><PERSON><PERSON>", "BUY_TOKEN": "Beli token", "ORDER_TOKEN": "<PERSON><PERSON> pesanan", "SELL_TOKEN": "Jual token", "REFERRAL_BONUS": "Bonus rujukan", "REFERRAL_BONUS_TOKEN": "Bonus rujukan token", "WITHDRAWAL": "Penarikan", "RENTAL_DISTRIBUTION": "Distribusi sewa", "ADMIN_ADD_BALANCE": "<PERSON><PERSON> tambah saldo", "BUY_TOKEN_VIRTUAL_BALANCE": "Beli token menggunakan saldo virtual", "VIRTUAL_RENTAL_DISTRIBUTION": "Distribusi sewa saldo virtual", "ADMIN_DEDUCT_BALANCE": "<PERSON>min memotong saldo", "SYSTEM_ADD_BALANCE": "<PERSON><PERSON>"}, "TRANSACTIONS_STATUS": {"RECEIVED": "Diterima", "FAILED": "Gaga<PERSON>", "PAID": "Lunas", "EXPIRED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COMPLETED": "Se<PERSON><PERSON>", "PENDING": "Tertunda", "REJECTED": "<PERSON><PERSON><PERSON>", "APPROVED": "Disetuju<PERSON>", "CANCELLED": "Di<PERSON><PERSON><PERSON>", "SWAPPED": "<PERSON><PERSON><PERSON>", "REFUNDED": "<PERSON><PERSON><PERSON><PERSON>", "UNCLAIM": "Tidak diklaim", "PENDING_PROCESS": "Proses Tertunda", "PROCESSING": "<PERSON><PERSON><PERSON>"}, "RENTAL_INCOME_SUMMARY_HEADER": {"NUM_OF_TOKENS": "<PERSON><PERSON><PERSON>", "OWNERSHIP_DAYS": "<PERSON>", "SUB_TOTAL": "Subtotal"}, "RENTAL_INCOME_DEDUCTION_HEADER": {"DEDUCTION_TYPE": "<PERSON><PERSON>", "DESCRIPTION": "Keterangan", "AMOUNT": "<PERSON><PERSON><PERSON>"}, "CONTRACT": {"YOUR_CONTRACT_ON_THIS_ASSET": "<PERSON><PERSON>rak <PERSON>a atas aset ini", "YOUR_CONTRACT_DOCUMENTS_OF_PROPERTY": "Do<PERSON>men kontrak properti {value} anda", "DOCUMENT_IS_BEING_PROCESSED": "(Dokumen sedang diproses...)"}, "PENDING_TASKS": {"TABLE_HEADER_TASK": "<PERSON><PERSON><PERSON>", "TABLE_HEADER_TYPE": "<PERSON><PERSON>", "TABLE_HEADER_STATUS": "Status", "TABLE_HEADER_EXPIRES_ON": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada", "TABLE_HEADER_ACTION": "", "TABLE_HEADER_DATE": "Tanggal", "PENDING_TASKS": "<PERSON><PERSON><PERSON>", "THERE_ARE_NO_PENDING_TASKS": "Tidak ada aksi yang tertunda.", "COMPLETE_PENDING_TASK": "Selesaikan tugas yang tertunda", "PLEASE_READ_AND_AGREE_TO_AGREEMENT": "<PERSON><PERSON> baca dan setujui <b><PERSON><PERSON><PERSON>an Transaksi Properti Fraksional (Token)</b> sebelum melanjutkan.", "READ_AND_AGREE_TOKEN_TRANSACTION_AGREEMENT": "Baca dan setujui <b><PERSON><PERSON><PERSON>an Transaksi Properti Fraksional (Token)</b> properti: <b>{value}</b>", "ACTION_SIGN_NOW": "Sign Now", "CLAIM_RENTAL_INCOME": "Klaim <b>{amount}</b> dari <b>Pendapatan Sewa</b> properti: <b>{value}</b> pada <b>{month}</b>", "CLAIM": "<PERSON><PERSON><PERSON>", "CLAIM_REFERRAL_BONUS": "Klaim bonus sebesar <b>{amount}</b> dari program referral GORO"}, "COMPLETED_TASKS": {"CASHBACK": "<PERSON><PERSON> telah men<PERSON> <b>{amount}</b> dari program referral.", "CONTRACT": "<PERSON>a telah menandatangani kontrak untuk <b>{property_name}</b>", "THERE_ARE_NO_COMPLETED_TASKS": "Tidak ada aksi yang se<PERSON>ai.", "EXPIRED": "Bonus <b>{amount}</b> ini telah kedaluwarsa dan tidak dapat diklaim lagi.", "STATUS_COMPLETED": "Se<PERSON><PERSON>", "STATUS_EXPIRED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "PROPERTY_STATUS": {"COMING_SOON": "<PERSON><PERSON><PERSON>", "SOLD_OUT": "<PERSON><PERSON><PERSON><PERSON>", "PROMO": "Promo", "PRESALE": "<PERSON><PERSON><PERSON>", "AVAILABLE": "Tersedia"}, "PIN_SECURITY": {"PIN": "PIN", "CHANGE_PIN": "Ubah PIN", "CREATE_NEW_PIN": "Buat PIN Baru", "AVOID_USING": "<PERSON><PERSON><PERSON> pengg<PERSON>an PIN yang berisi angka berulang\nberturut-turut, atau tanggal lahir <PERSON>a", "RE_ENTER_PIN": "Masukkan kembali PIN", "RE_ENTER_6_DIGITS": "Masukkan kembali 6 digit PIN yang baru saja Anda buat", "PIN_DOES_NOT_MATCH": "PIN tidak cocok. Silakan periksa lagi", "PIN_CREATED_SUCCESSFULLY": "PIN Berhasil Dibuat", "PIN_UPDATED_SUCCESSFULLY": "PIN Berhasil <PERSON>", "OKAY": "<PERSON>e", "ENTER_YOUR_CURRENT_PIN": "Masukkan PIN Anda Saat Ini", "ENTER_6_DIGITS": "Masukkan 6 digit PIN Anda", "FORGOT_PIN": "Lupa PIN?", "ACCOUNT_BLOCKED": "<PERSON><PERSON><PERSON>", "ACCOUNT_BLOCKED_MESSAGE": "<PERSON><PERSON><PERSON> Anda telah diblokir\nkarena Anda salah memasukkan PIN sebanyak 5 kali", "PLEASE_CONTACT": "<PERSON><PERSON><PERSON> hub<PERSON> {email} atau WhatsApp\n{phone} untuk membuka blokir akun <PERSON>a", "SEND_EMAIL": "Mengirim email", "WE_WILL_SEND_EMAIL_TO": "<PERSON><PERSON> akan men<PERSON>kan email ke", "CLICK_THE_LINK_TO_CHANGE_YOUR_PIN": "<PERSON><PERSON> tautan yang diberikan pada email untuk mengubah PIN Anda", "EMAIL_SENT": "Email terkirim!", "PLEASE_CHECK_THE_EMAIL_WE_SENT_TO": "<PERSON><PERSON><PERSON> perik<PERSON> email yang kami kirimkan", "TO_CHANGE_YOUR_PIN": "untuk mengubah PIN Anda", "WRONG_PIN_ATTEMPTS": "Anda salah memasukkan PIN {value} kali.\nJika Anda salah memasukkan PIN sebanyak 5 kali,\nakun <PERSON>a akan diblokir"}, "CHANGE_PASSWORD": {"HEADER": "Ganti kata sandi", "CHANGE_PASSWORD": "Ganti kata sandi", "NEW_PASSWORD": "Kata sandi baru", "CONFIRM_PASSWORD": "Konfirmasi kata sandi", "SUCCESSFULLY": "<PERSON>a sandi be<PERSON><PERSON><PERSON>, silakan masuk kembali dengan kata sandi baru.", "CHECKLISTS": {"LEAST1CHAR": "Setidaknya 8 karakter", "LEAST1NUMBER": "Setidaknya 1 angka", "LEAST1CHARCASE": "Setidaknya 1 huruf kecil dan 1 huruf besar", "LEAST1SPECIAL": "Setidaknya 1 simbol atau tanda baca"}}, "ACCOUNT_SECURITY": {"TITLE": "<PERSON><PERSON><PERSON>", "MODIFY": "Ubah", "PASSWORD": {"TITLE": "<PERSON><PERSON>", "DESCRIPTION": "Digunakan untuk masuk ke akun Anda.", "WARNING": {"TITLE": "<PERSON><PERSON><PERSON>", "WARNING_1": "Pengguna sebaiknya mengubah sandi secara teratur dalam jangka waktu maksimal 3 bulan.", "WARNING_2": "Pengguna sebaiknya menghindari penggunaan ulang sandi.", "WARNING_3": "Pengguna dilarang membagikan sandi."}}}, "TEXT_BANNER": {"TEXT": "GORO Menjadi Peserta Pertama dalam Sandbox OJK untuk Tokenisasi Properti"}, "SHARING": {"GORO_ID": "GORO ID: {value}", "MEMBER_SINCE": "Member sejak", "TOTAL_PROPERTIES": "Total properti", "TOTAL_EARNINGS": "Total imbal hasil (% Disetahunkan)", "FAVORITE_PROPERTIES": "Properti favorit", "JOIN_WITH_REFERRAL_CODE": "<PERSON>akan kode undangan ini dan miliki properti dengan GORO sekarang:", "INVITE_MESSAGE": "Bergabunglah dengan saya untuk berinvestasi di properti mulai dari IDR10.000 dengan GORO! Saya mendapatkan hasil sewa bersih hingga 10% per tahun, yang diterima setiap bulan! Lihat portofolio properti saya di sini:"}, "GORO_OFFICE": {"GORO_BALI_OFFICE": {"NAME": "GORO Bali Office", "ADDRESS": "Jalan Canggu Shortcut No.7, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ka<PERSON><PERSON><PERSON>, Bali 80351"}, "GORO_JAKARTA_OFFICE": {"NAME": "GORO Jakarta Office", "ADDRESS": "Plaza Sentral Main Building Lt. 14, J<PERSON>. <PERSON><PERSON>. Sudirman No. 47, Jakarta Selatan, 12930"}}, "TWO_FA": {"TWO_FACTOR_AUTHENTICATION": "Otentikasi Dua Faktor", "RECOVERY_CODES": "<PERSON><PERSON>", "ADD_EXTRA_SECURITY_TO_YOUR_ACCOUNT": "Tambahkan keamanan ekstra ke akun Anda", "DESCRIPTION": "Otentikasi dua faktor melindungi akun Anda dengan meminta kode tambahan saat Anda masuk ke akun Anda.", "AUTHENTICATION_APP": "Aplikasi otentikasi", "AUTHENTICATION_APP_DESCRIPTION": "<PERSON><PERSON> akan merekomendasikan aplikasi untuk diunduh jika Anda belum memilikinya. Aplikasi ini akan menghasilkan kode yang Anda butuhkan untuk masuk ke akun Anda.", "INSTRUCTION_FOR_SETUP": "Petunjuk <PERSON>yi<PERSON>n", "INSTRUCTION_STEP_1": "Langkah 1. Unduh aplikasi otentikasi", "INSTRUCTION_STEP_1_DESC": "<PERSON><PERSON> sa<PERSON>an untuk mengunduh T<PERSON><PERSON> jika Anda belum mengins<PERSON>. Anda dapat mengunduh aplikasi T<PERSON><PERSON> melalui tautan ini.", "INSTRUCTION_STEP_2": "Langkah 2. <PERSON><PERSON><PERSON> kode QR ini atau salin kuncinya", "INSTRUCTION_STEP_2_DESC": "Pindai kode QR ini di aplikasi otentikasi atau salin kuncinya dan tempelkan di aplikasi otentikasi.", "INSTRUCTION_STEP_3": "Langkah 3. <PERSON><PERSON> dan masukkan kode 6 digit", "INSTRUCTION_STEP_3_DESC": "Setelah kode QR dipindai atau kunci dimasukkan, aplikasi otentikasi Anda akan men<PERSON> kode 6 digit. <PERSON>in kode tersebut lalu kembali ke GORO untuk memasukkannya.", "COPY_KEY": "<PERSON><PERSON> kunci", "GET_YOUR_CODE": "Dapatkan kode dari aplikasi otentikasi Anda", "ENTER_THE_6_DIGITS": "Masukkan kode 6 digit yang dihasilkan oleh aplikasi otentikasi Anda", "ENTER_CODE": "<PERSON><PERSON><PERSON><PERSON>", "TWO_FA_IS_ON": "Otentikasi dua faktor aktif", "TWO_FA_IS_ON_DESC": "<PERSON><PERSON> se<PERSON>ng akan meminta kode login kapan pun Anda masuk ke akun Anda.", "DOWNLOAD_AS_TXT": "Unduh sebagai .txt", "COPY_CODES": "<PERSON><PERSON> kode", "GET_NEW_CODES": "Dapatkan kode baru", "RECOVERY_CODES_DESC": "Anda dapat menggunakan setiap kode cadangan satu kali. Simpan kode cadangan Anda di tempat yang aman. Tanpa kode cadangan ini, <PERSON>a mungkin tidak dapat masuk ke akun Anda jika Anda kehilangan akses ke ponsel atau tidak dapat masuk menggunakan otentikasi dua faktor.", "ADDITIONAL_METHODS": "<PERSON><PERSON>", "ADDITIONAL_METHODS_DESC": "<PERSON><PERSON> metode keamanan Anda yang lain tidak tersedia, <PERSON><PERSON> ma<PERSON>h dapat masuk dengan opsi ini", "BACKUP_CODES": "Kode <PERSON>", "BACKUP_CODES_DESC": "Gunakan ini ketika Anda tidak memiliki akses ke ponsel Anda", "TURN_OFF": "<PERSON><PERSON><PERSON>", "TURN_ON": "<PERSON><PERSON><PERSON><PERSON>", "ENABLE_RECOVERY_CODES_DESC": "Kode cadangan Gunakan kode cadangan untuk masuk jika Anda kehilangan akses ke ponsel atau tidak dapat masuk menggunakan otentikasi dua faktor", "TURN_OFF_BACKUP_CODES": "<PERSON>ikan kode cadangan?", "TURN_OFF_BACKUP_CODES_DESC": "Jika Anda mematikan kode cadangan, <PERSON>a tidak akan lagi dapat menggunakannya sebagai metode cadangan untuk mengonfirmasi login ke akun Anda. Metode keamanan Anda yang lain akan tetap diaktifkan untuk otentikasi dua faktor.", "VERIFY_TWO_FA_DESC": "Buka aplikasi pengautentikasi dua faktor atau ekstensi peramban untuk melihat kode otentikasi Anda", "HAVING_PROBLEMS": "<PERSON><PERSON><PERSON> masalah?", "USE_RECOVERY_CODE": "<PERSON><PERSON>n <PERSON>", "USE_AUTHENTICATION_CODE": "<PERSON><PERSON><PERSON>", "LOGIN_TO_GORO": "Masuk ke GORO", "ENTER_CODE_AUTHENTICATION_APP": "Masukkan kode 6 digit untuk akun ini dari aplikasi otentikasi dua faktor yang telah <PERSON>a si<PERSON>kan.", "ENTER_RECOVERY_CODE": "<PERSON><PERSON><PERSON><PERSON> salah satu kode cadangan sekali pakai Anda", "REMEMBER_THIS_DEVICE": "Ingat perangkat ini", "TURN_OFF_AUTHENTICATION_APP": "<PERSON>ikan aplik<PERSON> otentikasi?", "TURN_OFF_AUTHENTICATION_APP_DESC": "<PERSON><PERSON> Anda mematikan aplik<PERSON> o<PERSON>ti<PERSON>, keamanan akun GORO Anda akan berkurang.", "CONTACT_US": "<PERSON><PERSON><PERSON><PERSON> kami", "IF_YOU_ARE_LOCKED_OUT": "jika kamu terkunci di luar", "CLICK_HERE": "klik di sini!"}, "TAB_CONTENT_HEADER": {"PENDING": "Tertunda", "DONE": "Se<PERSON><PERSON>"}, "MODALS": {"COMMON": {"CANCEL": "Batalkan", "CONTINUE": "Lanjutkan"}, "SELL_SOLD_OUT": {"HEADER": "Anda yakin ingin menjual properti ini?", "CONTENT": "Properti ini sangat populer dan sudah terjual habis. Ji<PERSON> Anda memutuskan untuk menjual properti ini, <PERSON><PERSON> mungkin tidak akan dapat memiliki properti ini lagi."}}, "MAINTENANCE": {"THIS_SITE_UNDER_MAINTENANCE": "Situs ini sedang dalam pemeli<PERSON>an", "WE_WILL_BACK_LATER": "Kami akan kembali lagi!"}, "PROOF_NOTIFICATION": {"MESSAGE": "Ada {award_label} baru saja membeli {token} {token_label}", "MESSAGE_TOTAL_ALL_PURCHASE": "pembelian dalam {hour} jam terakhir", "MESSAGE_TOTAL_PURCHASE": "pembelian dalam {hour} jam terakhir", "GROWER": "GROWER", "COLLECTOR": "JURAGAN", "CHAMPION": "SULTAN"}, "USERNAME": {"TERMS_OF_CHANGE": "Ketentuan penggantian username:", "TERM_1": "Anda harus memiliki setidaknya 10 token properti untuk dapat mengubah username <PERSON><PERSON>.", "TERM_2": "Username harus terdiri dari minimal 6 karakter dan maksimal 14 karakter.", "TERM_3": "<PERSON><PERSON><PERSON> hanya boleh terdiri dari huruf dan angka.", "TERM_4": "<PERSON>rname hanya dapat diubah satu kali.", "TERM_5": "<PERSON>rname tidak boleh mengandung kata “GORO” dan “Admin”.", "TERM_6": "<PERSON>rname tidak boleh mengandung kata-kata yang tidak pantas.", "TERM_7": "Username Anda akan dikembalikan jika total aset Anda kurang dari 10 token. <PERSON>a dapat kehilangan username <PERSON><PERSON> kepada pengguna lain secara permanen.", "TERM_8": "GORO berhak untuk menonaktifkan fitur username dan mengatur ulang username Anda ke username awal, jika Anda melanggar ketentuan.", "USERNAME_CAN_BE_USE": "Username {value} dapat digunakan", "ARE_YOU_SURE_TO_USE": "Anda yakin menggunakan username ini?", "USERNAME_CHANGE_NOTE": "Anda harus memiliki 10 token untuk mengubah username Anda satu kali. <PERSON><PERSON>an beli lebih banyak token untuk mengubah username <PERSON><PERSON>.", "CHANGE_USERNAME": "Ubah Username", "ARE_YOU_SURE_TO_LEAVE_THIS_PAGE": "Anda yakin ingin kembali?", "THE_CHANGES_NOT_BE_SAVED": "<PERSON>bahan yang telah Anda lakukan tidak akan disimpan"}, "BALANCE_HISTORY": {"TITLE": "Riwayat Saldo", "ALL_DATES": "<PERSON><PERSON><PERSON>", "ALL_TYPES": "<PERSON><PERSON><PERSON>", "THERE_ARE_NO_HISTORIES": "Tidak ada Riwayat", "STATUS": "Status", "SALES_DETAILS": "Detail <PERSON>", "TOTAL_WITHDRAW": "Total Penarikan", "DEPOSITED_DETAILS": "Detail Deposit", "TOTAL_DEPOSITED": "Total Deposit", "DEDUCTED_DETAILS": "<PERSON><PERSON>", "TOTAL_DEDUCTED": "Total Pengurangan", "TYPE_MAP": {"SELL": "<PERSON><PERSON>", "BUY": "<PERSON><PERSON>", "WITHDRAW": "<PERSON><PERSON><PERSON>", "ADD": "<PERSON><PERSON>", "DEDUCT": "<PERSON><PERSON><PERSON><PERSON>"}, "GORO_BALANCE": "Saldo GORO", "PAYMENT_DETAILS": "<PERSON><PERSON><PERSON>", "PAYMENT_METHOD": "<PERSON><PERSON>", "TODAY": "<PERSON> ini", "LAST_X_DAYS": "{value} <PERSON>", "WITHDRAW_DETAILS": "Detail penarikan", "PICK_DATE": "<PERSON><PERSON><PERSON>", "APPLY": "Terapkan", "SELECT_DATES_UP_TO": "<PERSON>a dapat memilih tanggal sampai 30 hari terakhir.", "SELECT_EXCEED_30_DAYS": "<PERSON><PERSON> yang dipilih lebih dari 30 hari, silakan pilih lagi."}, "BANK_ACCOUNT_HISTORY": {"TITLE": "Detail Rekening Bank", "CONFIRM_NAME_NOTE": "<PERSON><PERSON>an isi kolom di atas sesuai dengan nama yang tertera di rekening bank Anda untuk memastikan bahwa Anda adalah pemilik rekening tersebut."}, "NAVBAR": {"ACTIVITY_CENTER": "Pusat Kegiatan", "WITHDRAWLS": "Penarikan", "SECURITY": "<PERSON><PERSON><PERSON>"}, "ONBOARDING": {"QUIZ_HEADING": "Kuis GORO", "WELCOME_MESSAGE": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>! Selamat datang di Kuis GORO. Kuis ini akan membantu Anda memahami cara kerja GORO.", "CORRECT": "Benar!", "INCORRECT": "<PERSON>ah!", "GREAT_JOB": "Benar!", "NOT_QUITE": "<PERSON>ah!", "START": "<PERSON><PERSON>", "NEXT": "Berikutnya", "TRY_AGAIN": "<PERSON><PERSON>", "STEP_QUESTION": "<PERSON><PERSON><PERSON>", "FINISHED": "Se<PERSON><PERSON>", "FINISHED_MESSAGE": "Selamat! Anda telah berhasil menyelesaikan Kuis GORO. Yuk, gabung ke grup komunitas GORO di Telegram dengan lebih dari {members} ribu anggota, agar Anda mendapatkan informasi tentang properti yang kembali tersedia dan berbincang langsung dengan pendiri GORO:", "CONGRATULATIONS": "Selamat!", "CLICK_HERE": "klik di sini!"}, "BLOCKCHAIN": {"TITLE": "Rantai Blok", "CONTRACT_ADDRESS": "<PERSON><PERSON><PERSON>", "NETWORK": "<PERSON><PERSON><PERSON>", "TOKEN_ID": "ID Token", "WALLET": "Dompet", "WALLET_ADDRESS": "<PERSON><PERSON><PERSON>", "WALLET_WARNING_1": "<PERSON><PERSON> men<PERSON> aset kripto apa pun ke alamat dompet ini. Kami tidak bertanggung jawab atas kehilangan aset yang dikirimkan ke alamat dompet ini.", "WALLET_WARNING_2": "<PERSON><PERSON> bagikan alamat do<PERSON>et <PERSON>, karena selu<PERSON>h aset dan transaksi <PERSON>a akan dapat terlihat oleh orang lain."}, "PARTNER": {"LOGIN_HEADER": "LOGIN PARTNER", "LOGIN_DESCRIPTION": "<PERSON><PERSON><PERSON> masukkan login dan kata sandi Anda!", "PROPERTY": {"TOKEN_HOLDERS": "<PERSON><PERSON><PERSON><PERSON>"}, "PICK_DATE": "<PERSON><PERSON><PERSON>", "SELECT_DATES_UP_TO": "Anda dapat memilih tanggal hingga {limit} hari terakhir", "SELECT_EXCEED_LIMIT_DAYS": "<PERSON><PERSON> yang dipilih lebih dari {limit} hari, silakan pilih lagi.", "APPLY": "Terapkan", "USER": {"VIEW_USER_ASSETS": "<PERSON><PERSON>", "ASSETS_LIST": "<PERSON><PERSON><PERSON>"}, "TRANSACTION": {"DETAIL": "Detail", "UUID": "<PERSON><PERSON>", "TRANSACTION_DETAILS": "<PERSON><PERSON><PERSON>", "EXTERNAL_ID": "ID Transaksi", "BLOCKCHAIN_TRANSACTION_ID": "ID Blockchain Transaksi", "TOKENS": "Token", "VALUE": "<PERSON><PERSON>", "DESCRIPTION": "Keterangan", "STATUS": "Status"}}, "SANDBOX": {"TITLE": "GORO Resmi Jadi Peserta Regulatory Sandbox OJK", "HEADER_FOOTER": "GORO adalah peserta Regulatory Sandbox Otoritas Jasa Keuangan (OJK) berdasarkan Surat OJK no. S-548/IK.01/2024 dan S-549/IK.01/2024", "BODY_CONTENT_1": "GORO telah resmi menjadi peserta Regulatory Sandbox OJK berdasarkan Surat OJK Nomor <strong>S-548/IK.01/2024</strong> dan <strong>S-549/IK.01/2024</strong> tertanggal 7 November 2024. Partisipasi ini menegaskan komitmen kami untuk menghadirkan inovasi di bidang investasi properti yang aman dan tepercaya.", "BODY_CONTENT_2": "GORO, platform investasi properti fraksional pertama dan terbesar di Indonesia dengan lebih dari <strong>{number}</strong> pengg<PERSON>, memungkinkan siapa saja untuk berinvestasi properti mulai dari <strong>Rp10.000</strong>, dan mendapatkan passive income setiap bulan.", "DO_NOT_SHOW_IN_DAYS": "<PERSON>an tampil<PERSON> dalam 30 hari", "CLOSE": "<PERSON><PERSON><PERSON>"}, "GO_LEARN": {"GO_LEARN": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "EPS": "Eps.", "EPISODE": "{total} Episode", "SEE_MORE": "<PERSON><PERSON>", "SEE_LESS": "Sembunyikan", "COMING_TITLE": "<PERSON><PERSON><PERSON><PERSON> akan segera tiba", "COMING_MESSAGE": "Temukan pengalaman belajar yang lebih seru dan bermanfaat. Pantau terus untuk pembaruan berikutnya!"}, "ATTESTATION_REPORT": {"HEADING": "<PERSON><PERSON><PERSON>", "QUARTER_SYMBOL": "Q"}, "EVENT": {"EVENTS": "Events", "STATUS_UPCOMING": "Mendatang", "STATUS_ONGOING": "<PERSON><PERSON><PERSON><PERSON>g", "STATUS_ENDED": "<PERSON><PERSON><PERSON>", "RULES": "<PERSON><PERSON><PERSON>", "JOIN_LEADERBOARD": "Belum masuk leaderboard? <PERSON><PERSON>, tamba<PERSON> invest<PERSON>, kejar peringkat dan jadilah pemenang!", "ADD_TOKEN_TO_JOIN_LEADERBOARD": "Ayo tambah {token} {token_label} lagi untuk mendapatkan kesempatan masuk kedalam leaderboard!", "ADD_TOKEN_TO_NEXT_RANK": "Tambah {token} {token_label} lagi untuk sampai ke peringkat berikutnya", "MAINTAIN_YOUR_LEAD": "Selamat! Kamu sudah di posisi #1\nTerus pertahankan posisimu!", "MAINTAIN_YOUR_TOP": "<PERSON><PERSON>! <PERSON><PERSON> di p<PERSON>i {top_scorer} besar.\n<PERSON><PERSON> berinvestasi untuk tetap di puncak!", "STARTING_SOON": "Event akan segera dimulai! Siap rebut posisi puncak leaderboard?", "STAY_TUNE": "Pantau terus!", "EVENT_ENDED": "Event telah berakhir! Terima kasih atas partisipasi Anda.", "READ_FOR_NEXT_CHALLENGE": "Jangan lewatkan event selanjutnya!", "RANKING": "<PERSON><PERSON><PERSON>", "PARTICIPANT": "<PERSON><PERSON><PERSON>", "TOTAL_INVITE": "Total Invite", "TOKEN_VOLUME": "Token Volume", "YOU_ARE_ON_TEAM": "<PERSON><PERSON> {team}", "JURAGAN": "Juragan", "SULTAN": "<PERSON>", "LEADERBOARD_REFRESH_NOTE": "Update leaderboard terakhir: {last_updated}\nLeaderboard akan diperbarui secara berkala setiap 1 menit.", "REFERRAL_LEADERBOARD_NOT_ON": "Anda belum masuk leaderboard. <PERSON><PERSON>, naikkan peringkatmu dengan undang teman dan pastikan mereka bertransaksi!", "REFERRAL_LEADERBOARD_ON_VISIBLE": "Mantap! Anda berada di leaderboard. Pertahankan posisi dengan ajak lebih banyak teman dan pastikan mereka bertransaksi.", "REFERRAL_LEADERBOARD_ON_NOT_VISIBLE": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> sudah masuk leaderboard! Ajak lebih banyak teman dan pastikan mereka bertransaksi untuk naik peringkat.", "REFERRAL_LEADERBOARD_RANK1": "Selamat! Anda berada di posisi #1. Pertahankan posisi Anda di puncak leaderboard"}, "VALIDATION": {"THE_DOB_IS_REQUIRED": "<PERSON><PERSON><PERSON> wajib diisi", "THE_DATE_OF_ISSUE_IS_REQUIRED": "<PERSON><PERSON><PERSON> pengeluaran wajib diisi", "THE_DATE_OF_EXPIRY_IS_REQUIRED": "<PERSON><PERSON><PERSON> wajib diisi"}, "RECAPTCHA": {"REQUIRED": "Verifikasi CAPTCHA diperlukan.", "EXPIRED": "CAPTCHA kedaluwarsa. <PERSON><PERSON>an coba kembali.", "ERROR": "<PERSON>cha tidak tersedia. <PERSON><PERSON>an muat ulang halaman atau coba lagi nanti."}, "PROCESSING_FEE": {"TITLE": "<PERSON><PERSON><PERSON>", "TOOLTIP": "<PERSON><PERSON>ya tambahan akan disesuaikan dengan metode pembayaran yang digunakan.", "DESCRIPTION_BALANCE": "Biaya {processing_fee}", "DESCRIPTION_PERCENTAGE": "Biaya {processing_fee_percent}%", "DESCRIPTION_PERCENTAGE_WITH_MAX": "Biaya {processing_fee_percent}% dengan maksimum {max_transaction_fee}"}, "TRANSACTION_FEE": {"TITLE": "Biaya Transaksi", "TOOLTIP": "Biaya transaksi dikenakan sebagai dukungan untuk menjaga dan meningkatkan kualitas layanan GORO.", "DESCRIPTION_BALANCE": "Biaya {transaction_fee}", "DESCRIPTION_PERCENTAGE": "Biaya {transaction_fee_percent}%", "DESCRIPTION_PERCENTAGE_WITH_MAX": "Biaya {transaction_fee_percent}% dengan maksimum {max_transaction_fee}", "NOTE_FOR_SWAP": "Biaya akan dipotong dari saldo Anda jika tersedia, atau dari token yang ditukar jika saldo Anda tidak mencukupi.", "SELL_TOKEN_CONFIRMATION_MESSAGE": "Biaya akan dipotong langsung dari uang yang Anda terima dari hasil penjualan token.", "SELL_TOKEN_CONFIRMATION_WARNING": "<PERSON><PERSON>, <PERSON><PERSON> bahwa jumlah dana yang diterima akan berkurang sesuai biaya yang berlaku.", "SWAP_TOKEN_CONFIRMATION_MESSAGE": "Saldo Anda saat ini tidak mencukupi untuk membayar biaya transaksi.\nBiaya akan dipotong langsung dari jumlah token yang Anda tukar.", "SWAP_TOKEN_CONFIRMATION_WARNING": "<PERSON><PERSON>, <PERSON><PERSON> bahwa jumlah token yang diterima akan berkurang sesuai biaya yang berlaku."}, "VOUCHER": {"PROMO_CODE": "<PERSON><PERSON>", "PROMO_DISCOUNT": "Diskon voucher", "INPUT_PROMO_CODE": "Masukkan kode voucher", "APPLIED": "Diterapkan", "DISCOUNT_BALANCE": "Diskon {amount}{discount_on}{required_note}", "DISCOUNT_PERCENTAGE": "Diskon {percent}%{discount_on}{required_note}", "DISCOUNT_PERCENTAGE_WITH_MAX": "Diskon {percent}% dengan maksimum {max_amount}{discount_on}{required_note}", "ON_TRANSACTION_FEE": " tentang biaya transaksi", "ON_PROCESSING_FEE": " tentang biaya pemrosesan", "ON_FEES": " tentang biaya", "REQUIRED_MIN_PURCHASE_NOTE": " (memerlukan pembelian minimum sebesar {required_transaction_amount})", "REQUIRED_MIN_SALE_NOTE": " (memer<PERSON>an penjualan minimum sebesar {required_transaction_amount})", "REQUIRED_MIN_SWAP_NOTE": " (memerlukan swap minimum sebesar {required_transaction_amount})", "REQUIRED_MIN_WITHDRAWAL_NOTE": " (memerlukan penarikan minimum sebesar {required_transaction_amount})", "WARNING_NO_DISCOUNT": "Kode voucher And<PERSON> valid, tetapi tidak memberikan diskon apa pun. Harap tinjau ketentuan voucher."}}