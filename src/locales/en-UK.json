{"Details": "Details", "Financials": "Financials", "Documents": "Documents", "Market": "Market", "TIMELINE": "Timeline", "BUY_PROPERTY": "Invest", "SELL_PROPERTY": "<PERSON>ll", "SWAP_PROPERTY": "<PERSON><PERSON><PERSON>", "Bedrooms": "Bedrooms", "Bathrooms": "Bathrooms", "Location": "Location", "INVEST_NOW": "Invest Now", "WHAT_IS_GOGO": "What is GORO?", "YOUR_BROWSER_SESSION_ID": "Your browser session id", "GORO_ID": "GORO ID", "common": {"error": "Error", "NOTICE": "Notice", "example": "Example:", "DOWNLOAD": "Download", "BACK": "Back", "COPIED": "Copied to clipboard", "CONGRATS": "Congrats", "CLOSE": "Close", "SOMETHING_WRONG_HAPPENED": "Oops, something went wrong. Please try again.", "COMING_SOON": "Coming soon", "NOTIFICATIONS": "Notifications", "WELCOME": "Welcome!", "JOIN_NOW": "Join Now", "MARK_ALL_AS_READ": "Mark all as read", "NOTIFICATION_DETAILS": "Notification Details", "EXPIRES_ON": "Expires on {value}", "EXPIRED_ON": "Expired on {value}", "RENTAL_INCOME_HAS_BEEN_CLAIMED": "Rental income has been claimed", "CLAIM_NOW": "Claim Now", "VIEW_DETAILS": "View Details", "OK": "OK", "CANCEL": "Cancel", "REFERRAL_BONUS_HAS_BEEN_CLAIMED": "Referral bonus has been claimed", "TOKEN": "Token", "TOKENS": "Tokens", "MAX": "MAX", "AVAILABLE": "available", "NEARLY_THERE": "Nearly There", "AND": "and", "SUBMIT": "Submit", "PREVIOUS": "Previous", "NEXT": "Next", "DAY": "day", "DAYS": "days", "DONE": "Done", "UPLOAD": "Upload", "CLAIM_BEFORE_EXPIRED": "Claim now before it expires!", "MALE": "Male", "FEMALE": "Female", "REGISTERED_ON": "Registered On", "COMPANY": "Company", "PRODUCT": "Product", "HIDE_ZERO_ASSETS": "Hide 0 Assets", "EDIT": "Edit", "SAVE_EDIT": "Save Changes", "SAVE": "Save", "CANCEL_CHANGE": "Cancel", "FREE_TOKEN": "Free token", "FREE_TOKENS": "Free tokens", "FREE": "FREE", "PROCEED": "Proceed", "WARNING": "Warning!", "OR": "Or", "APPLY": "Apply", "RESET": "Reset", "AGREE": "Agree", "ATTEMPT": "attempt", "ATTEMPTS": "attempts", "MONTH": "month", "MONTHS": "months"}, "FILTER": {"ALL_LOCATION": "All Location", "ALL_PROPERTIES": "All Properties", "SELECT_MONTH_AND_YEAR": "Select Month and Year", "FILTER_MONTH_AND_YEAR": "Month & Year"}, "status": {"Active": "Active", "Sold Out": "Sold Out", "Presale": "Presale", "Promo": "Promo", "All": "All"}, "HEADER": {"HOME": "Home", "MARKETPLACE": "Marketplace", "SELL_MY_PROPERTY": "jual-bu.com", "ABOUT_US": "About Us", "LOCATION_SUGGEST": "<PERSON><PERSON> melihat Anda berkunjung dari {country}. Untuk melihat informasi khusus untuk lokasi Anda, klik \"Beralih ke Situs Bahasa Indonesia\". <PERSON><PERSON> Anda lebih suka menjelajahi versi bahasa <PERSON>, klik \"Tetap di Situs Bahasa Inggris\".", "LOCATION_STAY_HERE": "Tetap di situs Bahasa Inggris", "LOCATION_SWITCH": "<PERSON><PERSON><PERSON> ke Situs Bahasa Indonesia"}, "FOOTER": {"CONTACT_US": "Contact Us", "CAREERS": "Careers", "PRIVACY_POLICY": "Privacy Policy", "TERMS_AND_CONDITIONS": "Terms & Conditions", "COPYRIGHT": "© PT. Teknologi Gotong Royong. GORO Technology Pte. Ltd. All rights reserved.", "TOKEN_PURCHASE_NOTE": "*Any purchases will be converted into Indonesian Rupiah (IDR)", "DISCLAIMER": "GORO.id is a website operated by PT Teknologi Gotong Royong (“GORO”), a wholly owned subsidiary of GORO Technology Pte. Ltd., and by accessing the website and any pages thereof, you agree to be bound by its terms of service and privacy policy. GORO does not prima facie endorse or promote any of the opportunities on this website nor make any recommendations to any customer. Prospective customers should not construe anything on the website as investment, business, legal or tax advice. The content contained herein does not constitute an offer by GORO to sell securities. Any information made available from our website or links to websites does not represent a solicitation of an offer to buy or sell any property. It also does not constitute an offer to provide investment advice, service or assistance on a particular investment or transaction. Direct and indirect property investment involves significant risk, and investments may lose value and are not insured by any governmental agency, nor are they guaranteed by GORO. It is the responsibility of the recipient to verify the integrity and authenticity of the information made available. Any historical returns, expected returns or probability projections might not reflect actual future performance. Customer must be able to afford to bear losses on investments made.", "CUSTOMER_SERVICES": "Customer Services", "CUSTOMER_SERVICES_ADDRESS": "PT. Teknologi Gotong Royong\nGoWork Lippo Mall Puri\nLippo Mall Puri Ground Floor, Jl. <PERSON><PERSON> Raya blok U1, Kembangan Selatan, Kembangan, Jakarta Barat, DKI Jakarta 11610", "CUSTOMER_SERVICES_EMAIL": "Email: hello{'@'}goro.id", "CUSTOMER_SERVICES_WHATSAPP": "WhatsApp: +62 857 5192 5122", "CUSTOMER_SERVICES_EMAIL_SUPPORT": "hello{'@'}goro.id", "CUSTOMER_SERVICES_WHATSAPP_SUPPORT": "+62 812 9090 4676", "CONSUMER_COMPLAINT_DIRECTORATE_GENERAL": "Directorate General of Consumer Protection and Orderly Commerce", "CONSUMER_COMPLAINT_MINISTRY_OF_TRADE": "Ministry of Trade of the Republic of Indonesia", "CONSUMER_COMPLAINT_CONTACT": "WhatsApp: +62 853 1111 1010", "DOWNLOAD_APPLICATION": "Download Application"}, "NOT_FOUND": {"NOT_FOUND": "Not Found", "PAGE_DOES_NOT_EXIST": "It seems this page does not exist"}, "MARKETPLACE": {"MARKETPLACE": "Marketplace", "ABOUT_THE_PROPERTY": "About the Property", "LOCATION": "Location", "PROPERTY_TYPE": "Property type", "STATUS": "Status", "MONTHLY_ANNUALIZED_RETURNS": "Monthly Annualized Net Rental Yields", "ANNUALIZED_RETURNS": "Annualized returns", "AVERAGE": "Average"}, "AUTH": {"GOOGLE": "Google", "FACEBOOK": "Facebook", "APPLE": "Apple", "GET_STARTED": "Get Started", "REGISTER": "Register", "REGISTER_WITH": "Register with", "REGISTER_NOW": "Register Now", "LOGIN": "<PERSON><PERSON>", "LOGIN_WITH": "Log in with", "FULL_NAME": "Full name according to passport/identity card", "NAME": "Name", "EMAIL": "Email", "PHONE": "Phone", "PHONE_HINT": "Enter your phone number", "COUNTRY_CODE": "Country Code", "PASSWORD": "Password", "PASSWORD_CONFIRM": "Confirm Password", "PASSWORD_FORGOT": "Forgot Password", "PASSWORD_FORGOT_SEND_LINK": "Request Password Reset Link", "PASSWORD_RESET": "Reset Password", "ADDRESS": "Address", "OR_FILL_IN_THIS_FORM": "or complete the form to create an account!", "COMPLETE_YOUR_PROFILE": "Complete Your Profile", "I_ACCEPT_TERMS_AND_PRIVACY": "I accept the Terms & Conditions & Privacy Policy", "ALREADY_HAVE_AN_ACCOUNT": "Already have an account?", "OR_LOGIN_WITH_CREDENTIALS": "or login with your credentials", "DONT_HAVE_AN_ACCOUNT": "Don’t have an account?", "REMEMBER_ME": "Remember me", "EMAIL_VERIFICATION": "Email Verification", "VERIFICATION": "Verification", "VERIFY": "Verify", "2FA_VERIFY": "Verify", "ENTER_THE_OTP": "Enter the OTP (One Time Password) that has been sent to your registered mobile number", "CHANGE_PHONE_NUMBER": "Change Phone Number", "PASSWORD_OPTIONAL": "Password (Optional)", "EMAIL_CORRECTION": "Did you mean", "OTP_REQUIRED": "OTP must be entered", "OTP_NOTICE": "The OTP will be sent via WhatsApp, please make sure your number is active and registered on WhatsApp", "OTP_DELIVERY_STATUS": "OTP delivery status:", "OTP_DID_NOT_RECEIVE": "Did not receive the code?", "OTP_RESEND": "Resend Verification", "OTP_RESEND_AFTER": "Resend Verification", "ENTER_YOUR_PHONE_NUMBER": "Enter your phone number", "TEL_INPUT_SEARCH_PLACEHOLDER": "Enter the country name or code", "INVALID_PHONE_NUMBER": "Invalid phone number", "THE_PHONE_IS_REQUIRED": "The Phone is required", "VERIFICATION_CODE": "Verification code", "SEND_VERIFICATION_CODE": "Send Code", "SEND_VERIFICATION_CODE_FAILED": "An error while sending the verification code. Please try again.", "VALID_VERIFICATION_CODE_FAILED": "An error while verifing the verification code. Please try again.", "REQUIRE_VERIFICATION_CODE_BEFORE_CHANGE_PASSWORD": "Require verification of the code before changing the password.", "VERIFIED_CODE": "Verified", "REQUIRE_VERIFICATION_CODE_TOO_MANY_REQUEST": "We have sent a verification code to your email. Please check your inbox or spam.", "NOTIFY_CHECK_MAIL_TO_GET_VERIFICATION_CODE": "Please check the email {email} to get the verification code.", "REGISTER_NAME_NOTE": "This name will be used in the Fractional Property (Token) Transaction Agreement", "I_ACCEPT_THE": "I accept the", "CONFIRM_NEW_PASSWORD": "Confirm New Password", "LOGIN_TO_YOUR_ACCOUNT": "Login to your Account", "LOGIN_WITH_GOOGLE": "Login with Google", "LOGIN_WITH_FACEBOOK": "Login with Facebook", "LOGIN_WITH_APPLE": "Login with Apple", "LOGIN_FAILED": "Incorrect email or password.", "LOGIN_FAILED_WARNING": "You have {remainingAttempts} tries left before your account is locked.", "LOGIN_FAILED_WARNING_2": "You have {remainingAttempts} tries left. Your account will be locked if it keeps happening. Try ", "LOGIN_FAILED_WARNING_3": "{remainingAttempts} try left. Your account will be locked if it's wrong again. Try ", "LOGIN_FAILED_BANNED": "Your account is locked due to too many wrong attempts. Please contact <a href='{whatsapp}' target='_blank'>Customer Service</a>.", "REGISTRATION_COMPLETE": {"HEADLINE": "Welcome to GORO! Your Registration is Complete.", "TEXT": "Thank you for registering with GORO! Your account is now ready, and you can start exploring exclusive fractional property investments.", "REDIRECT_TEXT": "You will be redirected in {seconds} seconds, or {link} to continue.", "CLICK_HERE": "click here"}, "CREATE_ACCOUNT": "Create Account", "REGISTER_STEP_PROGRESS": "Step {currentStep} of {totalStep}", "TITLE_COMPLETE_ACCOUNT_SETUP": "Complete Account Setup", "ACCOUNT_CONFIRMATION": "Account Confirmation", "ACCOUNT_CONFIRMATION_WARNING_TEXT": "Make sure the following information is correct.", "CONTINUE": "Continue", "BACK": "Back", "SUBMIT": "Submit", "TITLE_OTP_VERIFICATION": "OTP Verification", "ENTER_THE_OTP_TEXT": "Please enter the 6 digit code sent to", "CHANGE": "Change", "MOTHER_MAIDEN_NAME": "<PERSON>’s Maiden Name", "GENDER": "Gender", "MALE": "Male", "FEMALE": "Female", "DATE_OF_BIRTH": "Date of Birth", "WARNING_NAME_TEXT": "This name will be used in the Fractional Property (Token) Transaction Agreement", "DISABILITY_QUESTION": "Are you a person with a disability?", "HAVE_DISABILITY": "Yes, I have a disability", "NOT_HAVE_DISABILITY": "No, I do not have a disability", "MARITAL_STATUS": "Marital Status", "MARRIED": "Married", "NOT_MARRIED": "Not Married", "DIVORCED": "Divorced", "WIDOWED": "Widowed", "FULL_NAME_VALID": "Full name", "PARENT_KYC_MESSAGE": "To continue the registration process, you must complete the parent or guardian data by contacting <a href='{whatsapp}' target='_blank'><strong>WhatsApp GORO</strong></a>", "PASSWORD_MUST_CONTAIN": "Password must contain:", "REFERRAL_CODE": "Referral Code", "PASSWORD_RULES": {"LEAST8CHAR": "Characters", "LEAST1NUMBER": "Numbers", "LEAST1CHARCASE": "Lowercase Uppercase", "LEAST1SPECIAL": "Special Characters"}, "VERIFY_ACCOUNT_VIA_WHATSAPP": "Verify Your Account via WhatsApp", "VERIFY_ACCOUNT_VIA_WHATSAPP_DESCRIPTION": "Tap the button below to send a verification message via WhatsApp. This is required to complete your authentication.", "VERIFY_ACCOUNT_VIA_WHATSAPP_WARNING": "Please do not edit the prefilled message to ensure a successful verification.", "SEND_VERIFICATION_VIA_WHATSAPP": "Send Verification via Whatsapp", "VERIFYING_YOUR_CODE": "Verifying Your Code...", "VERIFYING_YOUR_CODE_DESCRIPTION": "We are automatically verifying your message.\nThis may take a few moments.", "CHECK_VERIFICATION_STATUS": "Check Verification Status", "VERIFYING_YOUR_CODE_WARNING": "Please make sure your internet connection is stable.", "NEED_HELP_HERE": "Need help? Contact our customer service <a href='{whatsapp}' target='_blank'>here</a>.", "UNDER_18_AND_NOT_MARRIED": "You are under 18 and not married. Please complete the registration with a parent or legal guardian.", "CONTACT_US": "Contact Us", "CONTACT_US_PREFILLED_MESSAGE": "Hello GORO team, I would like to continue the registration process. I am under 18 years old and not married. What information do I need to prepare?", "CONTACTED_PARENTAL_KYC": "I have contacted the GORO team and submitted the required documents.", "PARENTAL_KYC": {"HEADLINE": "KYC Status", "HEADLINE_REVIEWED": "Under Review", "HEADLINE_SUCCESS": "Congratulations!", "HEADLINE_FAILED": "Sorry!", "REVIEWED_TITLE": "KYC Data Verification Process", "REVIEWED_TEXT": "We are verifying your KYC details. This process may take up to 72 hours.", "SUCCESS_TITLE": "Successful KYC Verification", "FAILED_TITLE": "Your Data Verification Failed!", "FAILED_TEXT": "Your data has not been successfully verified. Please try again in {value}.", "REFRESH": "Refresh Status", "DONE": "Done", "CONTINUE": "Continue"}}, "LANDING": {"REGISTERED_USERS": "Users", "INVESTED_IN_PROPERTY": "Invested in Property", "TOTAL_INCOME_PAID": "Rental Income Paid", "PROPERTIES": "Properties", "TITLE": "Earn up to 10% per year by investing in rental properties.", "DESCRIPTION": "Start investing from $1*", "DESCRIPTION_EN": "Invest in fractions of rental properties from $1", "HOW_IT_WORKS": "How it works", "HOW_IT_WORKS_DESCRIPTION": "Property investing has never been easier", "BROWSE": "Browse", "BROWSE_DESCRIPTION": "Review our portfolio of high-yielding rental properties", "PURCHASE": "Invest", "PURCHASE_DESCRIPTION": "Select the properties you want to invest in starting from $1", "EARN": "<PERSON><PERSON><PERSON>", "EARN_DESCRIPTION": "Receive monthly rental income and withdraw anytime", "EXIT": "Exit", "EXIT_DESCRIPTION": "Hold, repurchase or sell whenever you want", "FEATURED_PROPERTIES": "Featured Properties", "FEATURED_PROPERTIES_DESCRIPTION": "We only select properties with the highest income potential", "VIEW_ALL_PROPERTIES": "View All Properties", "FEATURED_ON": "Featured On", "OUR_MISSION": "We are on a mission to democratise property investing.", "OUR_MISSION_DESCRIPTION": "90% of the worlds millionaires made their fortunes through property, but it’s highly inaccessible, illiquid, and complicated - that’s where we come in!", "OUR_MISSION_1": "Invest in property with any amount you like", "OUR_MISSION_1_DESCRIPTION": "With fractionalised properties, there is no mortgage or large down payments required", "OUR_MISSION_2": "Build a diversified property portfolio", "OUR_MISSION_2_DESCRIPTION": "Buy tokens of prime rental properties and manage your diversified portfolio from the comfort of your home", "OUR_MISSION_3": "Low-effort, high-return property investing", "OUR_MISSION_3_DESCRIPTION": "We handle the entire sales process, screen tenants, and manage the property, saving you time and money!", "OUR_MISSION_4": "Access the highest yielding markets", "OUR_MISSION_4_DESCRIPTION": "You can access markets that have the highest returns, even if those markets are far from where you live", "WHY": "Real estate combines the best of all asset classes", "WHY_DESCRIPTION": "It is one of the most important asset classes for building long-term wealth", "VALUE_APPRECIATION": "Value appreciation", "VALUE_APPRECIATION_DESCRIPTION": "In additional to monthly passive income, property appreciates in value like equities, creating long-term wealth for property owners", "HEDGE_FOR_INFLATION": "Hedge for inflation", "HEDGE_FOR_INFLATION_DESCRIPTION": "Property is the oldest asset class in history and has always been a great hedge during inflation times, just like gold", "PASSIVE_INCOME": "Passive income", "PASSIVE_INCOME_DESCRIPTION": "Property generates a consistent passive income like bonds, in the form of reliable monthly rental payments", "STOREHOLD_OF_WEALTH": "Storehold of wealth", "STOREHOLD_OF_WEALTH_DESCRIPTION": "Everybody aspires to own a home, making property a tangible store of wealth with a very high recovery value", "JOIN_THE_FUTURE_TODAY": "Ready to start investing in property?", "ONLY_TAKE_5_MINUTES": "Invest in GORO properties in under 5 minutes", "SKIP_WAITLIST": "<PERSON><PERSON> Waitlist", "OUR_INVESTOR_BENEFIT": "Our Token Holders' Benefit", "OUR_INVESTOR_BENEFIT_CAPTION": "*Per annum", "HIGH_RETURNS_AND_LOW_VOLATILITY": "From high returns and low volatility", "TESTIMONIALS": "Testimonials", "TESTIMONIAL_1_NAME": "<PERSON><PERSON><PERSON>", "TESTIMONIAL_1_TITLE": "IT Director", "TESTIMONIAL_1_COMMENT": "“GORO made investing in high-yielding Indonesian property a breeze! Their hassle-free experience and expert guidance are unmatched. Impressive returns and exceptional service.”", "TESTIMONIAL_2_NAME": "Karan S.", "TESTIMONIAL_2_TITLE": "Private Banker", "TESTIMONIAL_2_COMMENT": "“With GORO I get exposure to the best-performing asset class in history without the cumbersome process of identifying properties.”", "TESTIMONIAL_3_NAME": "<PERSON>.", "TESTIMONIAL_3_TITLE": "Analyst", "TESTIMONIAL_3_COMMENT": "“GORO’s fractionalized property product really helped me to get into the habit of building fixed income channel with low barrier of entry.”", "REQUEST_INVITATION": "Request Invitation", "SCHEDULE_A_CALL": "Schedule a call to request invitation", "READY_TO_OWN_RENTAL_PROPERTY": "Ready to Own a Rental Property Today?", "CONSULT_DIRECTLY_WITH_GORO": "Consult directly with a GORO property specialist!", "DOWNLOAD_OUR_APP": "Download Our App", "CALCULATOR_SIMULATION": "Return Calculator", "SELECT_PROPERTY": "Select Property", "ONE_TIME": "One Time", "MONTHLY": "Monthly", "INITIAL_PURCHASE": "Initial Investment Amount ({value})", "MONTHLY_PURCHASE": "Monthly Investment Amount ({value})", "INITIAL_PURCHASE_HINT": "Multiple of {value}", "PERIOD": "Period ({value} year)", "EXPECTED_RENTAL_YIELD": "Expected <PERSON><PERSON> (ERY)", "LAST_MONTH": "Last Month", "AVERAGE": "Average", "INCLUDE_EXPECTED_CAPITAL_APPRECIATION": "Include Expected Capital Appreciation", "COMPOUND_BY_AUTO_PURCHASE": "Auto reinvest rental income", "COMPOUND_BY_AUTO_PURCHASE_TOOLTIP": "One token purchase will be made every time the rental yield reaches {value}", "PROJECTED_INCOME_RETURN": "Projected Income", "YEAR": "{value} year", "YEARS": "{value} years", "EXPECTED_INCOME": "Expected Income", "ESTIMATED_RENTAL_YIELD_RECEIVED": "Estimated rental yield to be received in <b>{year}</b>", "EXPECTED_INCOME_WITH_COMPOUND": "Expected additional income with auto reinvest", "TOTAL_ASSET": "Total Asset", "TOTAL_ASSETS_IN_YEAR": "Total assets in <b>{year}</b>", "PURCHASE_TOKEN_DESCRIPTION": "Investment of {purchase} = {token} {token_label}", "CALCULATOR_DISCLAIMER": "<b>Disclaimer:</b> The calculator is intended solely for illustrative purposes, and the generated information should not be construed as legal or financial advice, nor as a guarantee of any kind. The results produced by this calculator are estimates based on the input provided by the user and do not reflect actual results.", "BUY_PROPERTY_NOW": "Invest now"}, "propertyDetail": {"Bedrooms": "Bedrooms", "Bathrooms": "Bathrooms", "Occupied": "Occupied", "total_invest_value": "Total Asset Value", "unit_price": "Unit Price", "notary_fee": "Notary Fee", "goro_fee": "GORO Fee", "projected_annual_roi": "Projected Annual ROI", "not_1": "GORO fee reduced from 5% to 2% as launch discount.", "not_2": "This can be achieved by switching from monthly rentals to daily rentals.", "token left": "tokens left", "minimum": "minimum", "ERY": "ERY", "ERY_TOOLTIP": "Expected Rental Yield is an estimate return based on the rental income of a property on an annual basis against the property investment price", "ERY_ANNUAL": "ERY (Annual)", "ERY_ANNUAL_TOOLTIP": "Expected Rental Yield is an estimate return based on the rental income of a property on an annual basis against the property investment price", "ACCOUNT_NOT_ACTIVE_TOOLTIP": "Your account's status is currently not active. Please activate your account to start investing in GORO property", "IRR": "IRR", "IRR_TOOLTIP": "Internal Rate of Return is the total expected return an investor may receive on an annual basis from ERY and ECA", "ECA": "ECA", "ECA_TOOLTIP": "Expected Capital Appreciation per annum", "ECA_ANNUAL": "ECA (Annual)", "ECA_ANNUAL_TOOLTIP": "Expected Capital Appreciation is an estimate of a property's appreciation in value on an annual basis", "IRR_ANNUAL": "IRR (Annual)", "IRR_ANNUAL_TOOLTIP": "Internal Rate of Return is the total expected return an investor may receive on an annual basis from ERY and ECA", "TOP_TOKEN_HOLDERS": "Top token holders", "THIS_PROPERTY_HAS_NO_OWNERS": "This property has no investors", "VIEW_MORE": "View more", "VIEW_LESS": "View less", "PROPERTY_TIMELINE": "Property Timeline", "ENDED": "Ended", "END_IN": "Ends in {value}", "PRESALE_NOTE": "This property is still being finalized and thus does not produce any rental income yet. It is expected to start generating income from: {value}", "CANCEL": "Cancel", "CONTINUE": "Continue", "PROPERTY_PRESALE": "This property is on presale", "ASSET_VALUE": "Asset Value", "ANNUAL_RETURN": "Annual Return", "TOKEN_HOLDERS_LEADERBOARD": "Token Holders Leaderboard"}, "account": {"ASSETS_OVERVIEW": "Assets Overview", "share": "Share", "leaderboard": "Leaderboard", "TRANSACTIONS": "Transactions", "wallet": "Wallet", "taxes": "Taxes", "MY_ACCOUNT": "My Account", "marketplace": "Invest Now", "shareAndEarn": "<PERSON><PERSON> and <PERSON><PERSON>n!", "shareYourLink": "Share your unique referral code now", "INVITES_LEFT": "You have {value} invites left", "copyLink": "Copy Link", "pleaseCompleteInfo": "Please complete the following\nrequested information", "invalidPhoneNumber": "Invalid phone number", "countryRegion": "Country/Region", "streetAddress": "Street Address", "city": "City", "stateProvince": "State/Province", "zipPostal": "Postal code", "ssn": "SSN for Annual Tax Filing (US Only)", "dob": "Date of Birth", "submit": "Submit", "PERSONAL_INFO": "Personal\nInformation", "PLEASE_SELECT_YOUR_ID_CARD": "Upload your Indonesian identity card (KTP)", "SUCCESS_VERIFIED_ID_CARD": "We have successfully verified your ID Card.", "ID_CARD_INVALID": "ID Card is invalid. Please upload again!", "PLEASE_SELECT_YOUR_PASSPORT": "Upload your passport", "SUCCESS_VERIFIED_PASSPORT": "We have successfully verified your passport.", "PASSPORT_INVALID": "Passport is invalid. Please upload again!", "UPLOAD_YOUR_ID_CARD_PASSPORT": "Please upload your KTP/Passport", "SELFIE": "<PERSON><PERSON>", "SELFIE_IS_NOT_VALID": "Oops, your selfie is not valid. Please try again!", "SUCCESS_SELFIE_CHECKING": "Congrats, you have successfully uploaded your selfie", "LOGOUT": "Logout", "MY_PROFILE": "My Profile", "MANAGE": "Manage", "INVITED_USERS": "Invited Users", "JOINED_AT": "Joined On", "DO_NOT_HAVE_TRANSACTION_ACCESS": "Sorry, you need an invite code to access GORO. Please ask someone to refer you!", "TO_UPDATE_PROFILE": "To update your personal information. Please contact", "TO_UPDATE_PROFILE_V2": {"WA": "If there are problems with your account. Please contact GORO via Whatsapp at", "EMAIL": "or contact"}, "FOR_SUPPORTING_CONTACT": "For customer support, please contact", "ACCOUNT_IS_INACTIVE": "Your account is currently inactive", "ACCOUNT_IS_SUSPENDED": "Your account is suspended", "THERE_ARE_NO_TRANSACTIONS": "There are no transactions. Make your first purchase now.", "ASKING_UPDATE_KYC_MESSAGE": "Would you like to complete KYC now?", "DO_IT_LATER": "do it later", "I_WILL_DO_IT_LATER": "I will do it later", "BASIC_INFO": "Basic Info", "SUCCESSFULLY_KYC": "You have successfully verified your account", "IMAGE_SIZE_TOO_LARGE": "The image size is too large. Please reduce image size before retrying.", "IMAGE_SIZE_TOO_SMALL": "The image size is too small. Please increase image size before retrying.", "KTP_SIZE_NOTE": "File size between 110KB and 2MB", "CHECKING_COMPLETED": "Check completed", "YOUR_INFO_LOOKS_GOOD": "Your info looks great!", "FOUND_INFO_IN_GLOBAL_SANCTIONS": "Sorry, we are unable to proceed with your registration.", "PLEASE_CONTACT": "Please contact", "FOR_SUPPORTING": "for support.", "RESEND": "Resend", "VERIFIED": "Verified", "NOT_VERIFIED": "Not verified", "PLEASE_VERIFY_YOUR_EMAIL_ADDRESS": "Please verify your email address", "DO_YOU_HOLD_KTP": "Are you an Indonesian citizen? Foreigners (non-Indonesians) and Indonesian visa holders (including KITAS/KITAP holders), please select NO.", "YES": "Yes", "NO": "No", "WE_ARE_PROCESSING_YOUR_CARD": "We are processing your card info. Please wait for a moment", "VIRTUAL_BALANCE": "Virtual Balance", "TAKE_SELFIE_NOT_SUPPORT": "Your browser does not support or permission to access camera was denied. Please contact hello{'@'}goro.id.", "CLICK_TO_CONFIRM": "Click to confirm your bank account", "SETTINGS": "Settings", "DISPLAY_CURRENCY": "<PERSON><PERSON>lay Currency", "OTHER_CURRENCIES_ARE_REFERENCES_ONLY": "Equivalent to {value}. Conversions to other currencies are for reference only. Amounts are rounded down to 2 decimal places.", "ARE_REFERENCES_ONLY": "{value} are references only. Amount is rounded down to 2 decimal places.", "MY_PORTFOLIO": "My Portfolio", "UPLOAD_IDENTITY_CARD": "Upload Identity\nCard", "IDENTITY_CONFIRMATION": "Identity\nConfirmation", "VERIFY_INFORMATION": "Identity\nVerification", "ARE_YOU_AN_INDONESIAN": "Are you an Indonesian Citizen (WNI)?", "CLICK_TO_UPLOAD_KTP": "Click To Upload KTP (Resident Identity Card)", "CLICK_TO_UPLOAD_PASSPORT": "Click To Upload Passport", "VERIFYING_YOUR_IDENTITY": "Verifying Your Identity", "PLEASE_CHECK_YOUR_VERIFICATION_STATUS": "Please check periodically regarding\nyour verification status", "YOUR_IDENTITY_HAS_VERIFIED": "Your Identity \n Has Been Verified", "PLEASE_PROCEED_TO_NEXT_STAGE": "Please proceed to the next stage", "SELFIE_NOTE": "Make sure your camera is active and\nyour face is clearly visible,\nnot covered by a mask", "HAPPY_INVESTING": "Welcome to GORO!", "FINISH": "Finish", "VERIFICATION_FAILED": "Sorry, Your Identity\nVerification Failed", "KYC_FIELDS": {"CITY": "City", "NAME": "Name", "RTRW": "RT/RW", "GENDER": "Gender", "ADDRESS": "Address", "VILLAGE": "Village", "DISTRICT": "District", "IDNUMBER": "Id Number", "PROVINCE": "Province", "RELIGION": "Religion", "BLOODTYPE": "Blood Type", "EXPIRYDATE": "Expiry Date", "OCCUPATION": "Occupation", "NATIONALITY": "Nationality", "MARITALSTATUS": "Marital Status", "BIRTHPLACEBIRTHDAY": "Birth Place Birthday", "DATEOFBIRTH": "Date of birth", "ZIPPOSTAL": "Postal code"}, "COULD_NOT_DETECT_SOME_INFO": "We could not detect some informations: {value}. Please try to upload again!", "CONTINUE_TO_PAY": "Continue to Pay", "VIEW_PHOTO": "View Photo", "TAKE_A_PHOTO": "Take a Photo", "CHOOSE_A_PHOTO": "Choose a Photo", "PHOTO_CROPPER": "Photo Cropper", "KYC_CORRECT_EXAMPLE": "Correct Example", "KYC_WRONG_EXAMPLE": "Wrong Example", "KYC_ID_CARD_CORRECT_NOTE": "The identity card is clear and legible", "KYC_ID_CARD_WRONG_NOTE": "Identification card is illegible (blurred, too far away, or has light reflections)", "PASSPORT_CORRECT_NOTE": "Passport is clear and legible", "PASSPORT_WRONG_NOTE": "Passport is illegible (blurry, too far away, or has light reflections)", "IDENTITY_CONFIRMATION_TITLE": "Identity Confirmation", "KTP_MAKE_SURE_INFO_CORRECT": "Make sure the information on the side matches your KTP.", "PASSPORT_MAKE_SURE_INFO_CORRECT": "Make sure the Information on the side matches your Passport.", "SELFIE_GUIDE": "Selfie Guide", "SELFIE_GUIDE_NOTE": "Make sure the selfie you take comply\nwith the following conditions", "SELFIE_CORRECT_NOTE": "Make sure the face is clearly visible with sufficient lighting", "SELFIE_WRONG_NOTE_1": "DO NOT wear glasses", "SELFIE_WRONG_NOTE_2": "DO NOT cover your face", "SELFIE_WRONG_NOTE_3": "DO NOT face up or down", "ID_NUMBER": "Id Number", "GENDER": "Gender", "BLOOD_TYPE": "Blood type", "DISTRICT": "District", "VILLAGE": "Village", "PROVINCE": "Province", "ADDRESS": "Address", "RELIIGION": "Religion", "EXPIRY_DATE": "Expiry Date", "OCCUPATION": "Occupation", "NATIONALITY": "Nationality", "MARITAL_STATUS": "Marital status", "DATE_OF_BIRTH": "Date of birth", "BIRTH_PLACE": "Birth place", "PASSPORT_NUMBER": "Passport Number", "SURNAME": "Surname", "GIVEN_NAME": "Given name", "PLACE_OF_BIRTH": "Place of birth", "COUNTRY": "Country", "DATE_OF_ISSUE": "Date of issue", "DATE_OF_EXPIRY": "Date of expiry", "ISSUING_AUTHORITY": "Issuing authority", "SELECT_GENDER": "Select gender", "SELECT_BLOOD_TYPE": "Select blood type", "IDENTITY_CARD_PHOTO_GUIDE": "Identity Card\nPhoto Guide", "IDENTITY_CARD_PHOTO_GUIDE_DESC": "Make sure the photos you take comply\nwith the following guidelines", "PASSPORT_PHOTO_GUIDE": "Passport Photo Guide", "PASSPORT_PHOTO_GUIDE_DESC": "Make sure the photos you take comply\nwith the following guidelines", "LIST_PROPERTIES": "Property List", "CURRENCY": {"NOTE_LABEL": "Note", "NOTES": "other currencies are references only. Amount is rounded down to 2 decimal places."}, "USERNAME": "Username", "KYC": "KYC", "FOR_CUSTOMER_SUPPORTING": "For customer support, please contact GORO via Whatsapp at <a href='{whatsapp}' target='_blank'>{whatsapp}</a> or contact <a href='{contactMailTo}'>{contact}</a>", "WARNING_REVIEW_KYC_INFO": "Please review before proceeding and ensure all information is correct.", "WARNING_NAME_MATCH_CARD": "Make sure your name matches the one on your ID card.", "WARNING_FORMAT_OF_DOB": "Format DD-MM-YYYY", "KYC_TITLE": "KYC Verification", "WAIL_PROCESS_KYC_TEXT": "Please wait while we process your KYC verification.", "KYC_STATUS": {"HEADLINE": "KYC Status", "VERIFICATION_PROCESS_HEADLINE": "KYC Data Verification Process", "VERIFICATION_PROCESS_TEXT": "We are currently carrying out the KYC data verification process, please wait 1x24 hours", "VERIFICATION_SUCCESSFUL_HEADLINE": "Successful KYC Verification", "VERIFICATION_FAILED_HEADLINE": "KYC Data Verification Failed", "VERIFICATION_FAILED_TEXT": "Your data has not been successfully verified, please contact our Customer Service team on {phone}", "REFRESH": "Refresh", "DONE": "Done"}}, "PAYMENT": {"EACH": "token", "TOKEN": "Token", "TOKENS": "Tokens", "CURRENCY": "IDR", "INVESTMENT_SUMMARY": "Token Purchase", "TOKEN_QUANTITY": "Token Quantity", "ORDER_SUMMARY": "Order Summary", "ORDER_TOTAL": "Order Total", "USE_BALANCE": "Use Balance", "TOTAL_PAYMENT": "Total Payment", "CONTINUE_TO_PAYMENT": "Continue to Payment", "PAYMENT_CONFIRMATION": "Purchase Confirmation", "PAY_NOW": "Pay Now", "CHECKOUT": "Checkout", "SUCCESS": "Success!", "SUCCESSFULLY_PURCHASED": "Successfully purchased <b>{value}</b> {value2}", "YOU_ARE_NOW_OWNER_OF": "You now officially own a total of <b>{numOfTokens}</b> {tokenLabel} of {propertyName}!", "YOU_ARE_NOW_OWNER_OF_SWAP": "You now officially own a total of <b>{numOfTokens}</b> {tokenLabel} in {propertyName}!", "BACK_TO_PROPERTY": "Back to property", "PAYMENT_FAILED": "Your payment failed", "PAYMENT_FAILED_DETAIL": "Your payment for", "PAYMENT_FAILED_DETAIL_2": "was unsuccessful", "PAYMENT_FAILED_GUIDE": "Please check your payment information and try again or email <b>hello{'@'}goro.id</b> for assistance.", "PAYMENT_PROCESSING_FEE": "Payment Processing Fee", "NEXT_EXPECTED_PAYOUT_DATE": "The next rent distribution is no later than", "NEXT_EXPECTED_PAYOUT_DATE_TITLE": "IMPORTANT!", "NEXT_EXPECTED_PAYOUT_DATE_DESCRIPTION": "For the {currentRent} rent distribution, which will be paid by {nextPayoutDate}, you will only receive rent distribution for {totalDaysReceiveRent} {totalDaysReceiveRentLabel} ({receiveRentRangeDate})", "NEXT_EXPECTED_PAYOUT_DATE_DESCRIPTION_UNDERLINE": "if you do not sell tokens in {currentMonth}.", "USE_VIRTUAL_BALANCE": "Use Virtual Balance", "PAY_NOW_PROCESSING_FEE": "PayNow processing fee", "CREDIT_CARD": "Credit Card", "BANK_TRANSFER": "Bank Transfer", "BANK_TRANSFER_PROCESSING_FEE": "Bank transfer processing fee", "PLEASE_COMPLETE_PROFILE": "GORO requires KYC for users who own greater than {value} tokens, you will be redirected to the KYC page.  You may proceed with the transaction after the KYC process has been fully completed.", "CONTINUE_TO_KYC": "Continue to KYC", "I_HAVE_READ_AND_AGREE": "By checking the box, I acknowledge that I have read and understood the", "TOKEN_TRANSACTION_AGREEMENT": "Fractional Property (Token) Transaction Agreement", "I_AGREE_TO_BE_BOUND": "(\"Agreement\"). I agree to be bound by its terms and my failure to read the Agreement does not excuse non-compliance. I waive any claim that the Agreement is unenforceable due to my failure to read or understand it.", "PLEASE_AGREE_TO_AGREEMENT": "Please open and read the Fractional Property (Token) Transaction Agreement and tick the checkbox before you proceed with the payment.", "PLEASE_FILL_THE_BALANCE_OR": "Please enter the balance amount or turn off use balance to continue", "PAYMENT_METHOD": "Payment Method", "SUCCESSFULLY_PURCHASED_TITLE": "Congratulations!", "TOKENS_BOUGHT": "Token(s) bought"}, "ORDER": {"SUCCESSFUL": "Order Successful!", "SUCCESSFUL_DETAIL": "Your order is successful. You are now officially an owner of", "REJECTED": "Order Rejected!", "REJECTED_DETAIL": "Your order to buy {numOfTokens} token of property {propertyInfo} has been rejected.", "REJECTED_REASON": "Reason: {reason}", "PLACED": "Your Order Has Been Placed!", "INFO": "Order Info", "UUID": "<PERSON><PERSON>", "REFERENCE_ID": "Transaction Reference ID", "TRANSACTION_ID": "Transaction ID", "PROPERTY_NAME": "Property Name", "REPLACE_PROPERTY_UUID": "Penggantian Properti Uuid", "NUM_OF_TOKENS": "Number Of Tokens", "PRICE": "Price", "FEE": "Fee", "BALANCE_USED": "Balance Used", "REMAINING_AMOUNT_TO_TRANSFER": "Remaining Amount to Transfer", "TOKEN_NOTE": "The tokens may take up to 3 business days to be credited to your account.", "NOTE": "All relevant transaction fees shall be borne by the customer.", "TRANSACTION_NOTE": "Transaction will be made in Indonesian Rupiah (IDR)", "CONTACT_INFO": "If you have any questions, please feel free to contact hello{'@'}goro.id", "BANK_TRANSFER_INTRO": "You can transfer funds to GORO Aspire account from any bank account using <b>SWIFT/Telegraphic transfer</b>. GORO account details are below:", "PAY_NOW_ON_SGQR": "Pay Now on SGQR", "SCAN_TO_PAY": "SCAN TO PAY", "BANK": "Bank:", "BANK_NAME": "Bank Name:", "BANK_CODE": "Bank Code:", "BRANCH_CODE": "Branch code:", "BANK_ADDRESS": "Bank address:", "BANK_COUNTRY": "Bank country:", "ACCOUNT_HOLDER_NAME": "Account name:", "SGD_GORO_BANK_DETAILS": "Singapore dollar bank account details:", "SGD_IF_FAST_ACT_GIRO": "• If you are sending through FAST/ACT/GIRO:", "SGD_IF_FAST_ACT_GIRO_VALUE": "GORO PROPERTY PTE. LTD.", "SGD_IF_FAST_MEPS_LOCAL_TT": "• If you are sending through MEPS/Local TT:", "SGD_IF_FAST_MEPS_LOCAL_TT_VALUE": "Aspire FT Pte. Ltd. on behalf of GORO PROPERTY PTE. LTD.", "SGD_ACCOUNT_NUMBER": "Account number:", "SGD_ACCOUNT_NUMBER_VALUE": "8852-1592-5748", "SGD_BANK_NAME_VALUE": "DBS Bank Ltd", "SGD_BANK_CODE_VALUE": "7171", "SGD_BRANCH_CODE_VALUE": "001", "USD_GORO_BANK_DETAILS": "US dollar bank account details:", "USD_ACCOUNT_NUMBER": "Account number/IBAN:", "USD_BANK_SWIFT_BIC": "Bank SWIFT/BIC", "USD_ACCOUNT_HOLDER_NAME_VALUE": "GORO PROPERTY PTE. LTD.", "USD_ACCOUNT_NUMBER_VALUE": "**********************", "USD_BANK_NAME_VALUE": "The Currency Cloud Limited", "USD_BANK_SWIFT_BIC_VALUE": "TCCLGB3L", "USD_BANK_ADDRESS_VALUE": "12 Steward Street, The Steward Building, London, E1 6FQ, GB", "USD_BANK_COUNTRY_VALUE": "GB", "TRANSFER_CONTENT": "Transfer Description:", "TRANSFER_CONTENT_VALUE": "{value}", "TRANSFER_AMOUNT": "Transfer Amount:", "EURO_GORO_BANK_DETAILS": "Euro bank account details:", "EURO_ACCOUNT_HOLDER_NAME_VALUE": "GORO PROPERTY PTE. LTD.", "EURO_ACCOUNT_NUMBER": "Account number/IBAN:", "EURO_ACCOUNT_NUMBER_VALUE": "BE05 9677 2837 4275", "EURO_BANK_NAME_VALUE": "WISE EUROPE S.A", "EURO_BANK_SWIFT_BIC": "Bank SWIFT/BIC:", "EURO_BANK_SWIFT_BIC_VALUE": "TRWIBEB1XXX", "EURO_BANK_ADDRESS_VALUE": "Rue du Trône 100, 3rd floor, Brussels, 1050, Belgium", "EURO_BANK_COUNTRY_VALUE": "Belgium", "AUD_GORO_BANK_DETAILS": "Australian dollar bank account details:", "AUD_ACCOUNT_HOLDER_NAME_VALUE": "GORO PROPERTY PTE. LTD.", "AUD_ACCOUNT_NUMBER": "Account number:", "AUD_ACCOUNT_NUMBER_VALUE": "*********", "AUD_BSB_NUMBER": "BSB code:", "AUD_BSB_NUMBER_VALUE": "774001", "AUD_BANK_ADDRESS": "<PERSON>’s address:", "AUD_BANK_ADDRESS_VALUE": "Suite 1. Level 11,66 Goulburn Street, Sydney, 2000, Australia", "AUD_BANK_COUNTRY_VALUE": "Australia", "UK_GORO_BANK_DETAILS": "British pound bank account details:", "UK_ACCOUNT_HOLDER_NAME_VALUE": "GORO PROPERTY PTE. LTD.", "UK_ACCOUNT_NUMBER": "Account number:", "UK_ACCOUNT_NUMBER_VALUE": "********", "UK_SORT_CODE": "Sort Code:", "UK_SORT_CODE_VALUE": "23-14-70", "UK_IBAN": "IBAN:", "UK_IBAN_VALUE": "GB86 TRWI 2314 7087 3663 59", "UK_BANK_NAME_VALUE": "<PERSON>", "UK_BANK_ADDRESS_VALUE": "56 Shoreditch High Street, London, E16JJ, United Kingdom", "UK_BANK_COUNTRY_VALUE": "United Kingdom", "IMPORTANT_UPDATE": "Important Update:", "BANK_ACCOUNT_DETAILS_CHANGED": "Bank Account Details Changed", "IMPORTANT_UPDATE_CONTENT": "We have updated our <b>Singapore Dollar (SGD)</b> and <b>US Dollar (USD)</b> bank account information. <b>Please check the updated details below carefully</b> and <b>do not transfer to the old account</b> to avoid failed transactions."}, "SELL_TOKEN": {"UUID": "<PERSON><PERSON>", "ADMIN_REQUEST_ID": "Admin Request Id", "NAME": "Name", "EMAIL": "Email", "PROPERTY_NAME": "Property Name", "NUM_OF_TOKENS": "Num Of Tokens", "AMOUNT": "Amount", "AMOUNT_FROM_BALANCE": "Amount From Balance", "RECEIVED_AMOUNT": "Received Amount", "FEE": "Fee", "FEE_PERCENT": "<PERSON><PERSON>", "DESCRIPTION": "Description", "REASON": "Reason", "STATUS": "Status", "SELL_TOKEN_HISTORY": "<PERSON><PERSON>", "SELL_TOKEN_REQUEST_HISTORY": "<PERSON><PERSON>ken Request History", "THERE_ARE_NO_SELL_TOKEN_REQUEST_HISTORY": "There are no sell token request history", "CANCEL_REQUEST": "Cancel Request", "SELLABLE_TOKENS": "Sellable Tokens", "NOT_HAVE_SELLABLE_TOKENS": "You do not have any sellable tokens for this property. (Click for details)", "REQUEST_SELL_TOKEN": "Request Sell <PERSON>", "SUMMARY": "Summary", "TOKEN_QUANTITY": "Token Quantity", "PRICE_PER_TOKEN": "Price per Token", "SUBTOTAL": "Subtotal", "PROCESSING_FEE": "Processing fee", "TOTAL_RECEIVE": "Total Receive", "CONFIRM_SELL_REQUEST": "Next", "SUBMIT_SELL_REQUEST": "Proceed", "SELL_REQUEST_CONFIRMATION": "<PERSON>ll <PERSON>ken Request Confirmation", "SELL_REQUEST_SUBMITTED": "Your request to sell {tokenValue} of {propertyValue} is {status}.", "PENDING_APPROVAL": "being processed and will be completed within a maximum of 72 hours", "VIEW_MY_TRANSACTIONS": "View My Transactions", "YOUR_STATUS_ON": "Status for {propertyName}", "AVAILABLE_TOKENS_TO_SELL": "Total tokens available for sale: {value}", "OWNING_TOKENS": "Tokens owned: {value}", "LOCKED_TOKENS": "Tokens locked: {value}", "PENDING_TOKENS": "Tokens requested for sale: {value}", "OWNING_TOKENS_LABEL": "Token owned", "LOCKED_TOKENS_LABEL": "{value} Locked tokens", "TOKEN_SOLD": "Token(s) sold"}, "SELL_TOKEN_TABLE_HEADER": {"STATUS": "Status", "PROPERTY": "Property", "NO_OF_TOKENS": "No Of Tokens", "RECEIVED_AMOUNT": "Received Amount", "FEE": "Fee", "NOTE": "Note", "DATE": "Date"}, "SELL_TOKEN_STATUSES": {"PENDING": "Pending", "APPROVED": "Approved", "REJECTED": "Rejected", "CANCELLED": "Cancelled"}, "SWAP_TOKEN": {"SWAP_TOKEN": "<PERSON><PERSON>p <PERSON>", "SWAP_TOKEN_SUMMARY": "Swap Token Summary", "AVAILABLE_TOKENS_TO_SWAP": "Total tokens available for swap: {value}", "NOT_HAVE_SWAPPABLE_TOKENS": "You do not have any swappable tokens for this property. (Click for details)", "CURRENT_PROPERTY": "Current Property", "NEW_PROPERTY": "New Property", "YOUR_TOKEN": "Your Token", "PROPERTY_TOKEN": "Property Token", "TOKEN_TO_SWAP": "Token to Swap", "TOKEN_TO_RECEIVE": "Token to Receive", "TOKEN_AVAILABLE": "{value} {token_label} Available", "TOTAL_TOKEN": "Total Swap Token", "NUMBER_OF_TOKEN_MUST_BE_POSITIVE": "The number of tokens must be positive. Please review before moving on to the next step.", "NUMBER_OF_TOKEN_MUST_BE_POSITIVE_FEE_APPLIED": "Fees are applied to this transaction. Please ensure the amount is greater than {value} tokens. ", "PLEASE_READ_AND_AGREE_TO_AGREEMENT_OF_CURRENT_PROPERTY": "Please read and agree to the <b>Fractional Property (Token) Transaction Agreement</b> of current property before continue.", "CONTINUE_TO_SWAP": "Continue to Swap", "SWAP_NOW": "Swap Now", "SUCCESSFULLY_SWAPPED": "Successfully swapped <b>{numOfToken}</b> token{plural} of {currentPropertyName} to {newPropertyName}", "CURRENT_EXPECTED_PAYOUT_DESCRIPTION": "Your rental income from {currentPropertyName} until {yesterday} will be distributed at the next rental distribution.", "PRESALE_EXPECTED_PAYOUT_DESCRIPTION": "The {propertyName} is still being finalized and thus does not produce any rental income yet. It is expected to start generating income from: {firstLiveOn}.", "NEXT_EXPECTED_PAYOUT_DESCRIPTION": "For {newPropertyName}, the next rental income will be distributed before {nextPayoutDate}. Your rental income will be pro-rated since {receiveRentFrom} to {receiveRentTo} ({totalDaysReceiveRent} {totalDaysReceiveRentLabel}).", "NEXT_EXPECTED_PAYOUT_DESCRIPTION_UNDERLINE": "This only applies if you don’t sell or swap your tokens before the end of {currentMonth}.", "SWAP_DETAILS": "Swap Details", "YOUR_BALANCE": "Your GORO balance", "CREDITED_TO_BALANCE": "Credited to Balance", "BALANCE_AFTER_SWAP": "Balance after <PERSON><PERSON><PERSON>", "DEDUCTED_METHOD": "Deduction Fee Method", "DEDUCTED_FROM_BALANCE": "Deducted from GORO Balance", "DEDUCTED_FROM_PROCEEDS": "Deducted from Proceeds", "TOTAL_RECEIVED_TOKEN": "Total Received Token", "TOKENS_DEDUCTED_FOR_FEE": "Tokens Deducted for Fee", "SWAP_TOKEN_WARNING": "Fees are applied to this transaction. Please ensure the amount is greater than {token} {token_label} "}, "TRANSACTION": {"TRANSACTION_DETAILS": "Transaction Details", "TRX_ID": "Transaction ID", "TYPE": "Type", "TOKENS": "Tokens", "NUM_OF_TOKENS": "Number of tokens", "VALUE": "Value", "DESCRIPTION": "Description", "DATE": "Date", "EXTERNAL_ID": "Transaction ID", "BLOCKCHAIN_TRANSACTION_ID": "Blockchain Transaction ID", "ORDER_UUID": "Order Uuid", "SELL_REQUEST_UUID": "Sell Request Uuid", "REFERENCE_ID": "Transaction Reference ID", "PRICE_PER_TOKEN": "Price per token", "STATUS": "Status", "PAYMENT_METHOD": "Payment Method"}, "ASSETS": {"VIEW_MY_ASSETS": "View My Assets", "MY_BALANCE": "My Balance", "WITHDRAW": "Withdraw", "CURRENT_ACCOUNT_VALUE": "Current Account Value", "TOTAL_PROPERTY_VALUE": "Total Property Value", "LAST_RENT_EARNED": "Last Rent Earned", "TOTAL_RENT_EARNED": "Total Rent Earned", "PROPERTIES_OWNED": "Properties Owned", "CURRENT_VALUE": "Current Value", "YOUR_ARY_RETURN": "Your ARY Return", "NO_PROPERTY": "Start earning rental yield by purchasing your first property token.", "VIEW_PROPERTIES": "Invest Now", "ANNUALIZED_PERCENT": "(annualized %)", "TOTAL_VALUE": "Total Value", "LOCKED_TOKENS": "Locked tokens", "CLICK_TO_SEE_DETAILS": "Click to see details.", "LOCKED_TOKENS_OF": "Locked tokens of {propertyName}", "LOCKED_UNTIL": "Locked until", "THIS_PROPERTY_HAS_NO_LOCKED_TOKENS": "This property has no locked tokens", "TOKEN_AVAILABLE": "Tokens available", "SWAP": "<PERSON><PERSON><PERSON>", "SELL": "<PERSON>ll", "CONTRACT": "Contract"}, "REFERRAL": {"REFERRALS": "Referrals", "REFERRAL_CODE": "Referral Code (optional)", "REFERRAL_JUST_GIFTED": "has given you priority access", "REFERRAL_SIGN_UP_TO_INVEST": "Join today and start investing in property.", "PLEASE_INPUT_REFERRAL_CODE": "Please enter a referral code to activate your account", "USE_THIS_REFERRAL_CODE_FROM": "Use this referral code from {value} to activate your account", "COPY_REFERRAL_LINK": "Copy Referral Link", "JOIN_GORO_TO_START_INVESTING": "Join GORO now to earn monthly passive income from our selected properties", "REFERRAL_OWN_PROPERTIES": "{name} owns these stunning properties. Join {name} now on GORO by registering an account and {bonus_text} for your first purchase{bonus_required_text}.", "REFERRAL_OWN_PROPERTIES_CASHBACK": "earn {cashback_percent} cashback", "REFERRAL_OWN_PROPERTIES_FREE_TOKEN": "earn {token_bonus} {token_label} worth {token_value}", "REFERRAL_OWN_PROPERTIES_FREE_TOKEN_REQUIRED": " of minimum {token_minimum_required} {token_label}", "REFERRAL_BONUS_INFO": "Refer a friend and earn {percent}% CASHBACK after they have made their first purchase on GORO", "REFERRAL_BONUS_TOKEN_INFO": "Refer a friend and earn {token_bonus} {token_label} worth {token_value} after they complete their first purchase{token_bonus_required} on GORO.", "REFERRAL_BONUS_TOKEN_REQUIRED_INFO": " of minimum {token_minimum_required} {token_label}", "REFERRAL_BONUS_AMOUNT_FOR_REFEREE_INFO": "Hurry! You may still be able to earn <b>{percent}</b>% cashback by making your first transaction on GORO!", "REFERRAL_BONUS_TOKEN_FOR_REFEREE_INFO": "Hurry! You may still be able to earn {token_bonus} {token_label} worth {token_value} when you make your first purchase{token_bonus_required} on GORO!", "REFERRAL_BONUS_TOKEN_FOR_REFEREE_REQUIRED_INFO": " of at least {token_minimum_required} {token_label}", "REFERRAL_BONUS_TOKEN_FOR_REFEREE_REMIND_INFO": "Don’t miss out! Earn a bonus of {token_bonus} {token_label} worth {token_value} when you purchase at least {token_minimum_required} {token_minimum_required_label} for your first transaction. Increase your token count now to claim this special reward!", "EARN_CASHBACK_FOR_EVERY_REFERRAL": "Earn <b>{percent}</b>% cashback for every referral", "EARN_FREE_TOKEN_FOR_EVERY_REFERRAL": "Earn <b>{token}</b> {token_label} worth {token_value} for every referral{token_bonus_required}.", "EARN_FREE_TOKEN_FOR_EVERY_REFERRAL_REQUIRED": " (minimum first purchase {token_minimum_required} {token_label})", "BUY_TOKENS_TO_EARN_MORE_INVITES": "Buy tokens to earn more invites!", "HOW_IT_WORK": "How It Works", "INVITED_USERS": "Invited Users", "HOW_IT_WORK_ITEMS": {"INVITE_YOUR_FRIEND": {"HEADING": "Invite Your Friend", "DESCRIPTION": "Send your unique link to your friends inviting them to use GORO"}, "MAKE_FIRST_PURCHASE": {"HEADING": "Make First Purchase", "DESCRIPTION": "When your friend sign up to GORO and make their first purchase you both get rewards!{token_bonus_required}", "DESCRIPTION_REQUIRED": " *minimum purchase {token_minimum_required} {token_label}"}, "GET_REWARDS": {"HEADING": "Get Rewarded", "DESCRIPTION": "*You will earn {referrer_bonus} and your friend will earn {referee_bonus} for free!", "DESCRIPTION_CASHBACK": "You will earn {referrer_bonus} cashback and your friend will earn {referee_bonus} cashback!"}}, "TOP_REFERRER": "Top Referrer", "WEEKLY": "Weekly", "MONTHLY": "Monthly", "ALL_TIME": "All Time", "RANK": "Rank", "INVITED_USER": "Invited Users", "NAME": "Username", "GORO_ID": "ID", "NO_RECORD_TITLE": "No Ranking at the moment!", "NO_RECORD_CONTENT": "Here's your chance to be the first! Invite as many friends as you can and reach the top of the leaderboard."}, "WITHDRAWALS": {"WITHDRAWALS": "<PERSON><PERSON><PERSON><PERSON>", "BANK_ACCOUNT": "Bank Account", "HISTORY": "History", "WITHDRAWAL_HISTORY": "Withdrawal History", "MAKE_YOUR_FIRST_TRANSACTION_TO_WITHDRAWAL_MONEY": "Make your first property investment before you can withdraw money.", "ADD_BANK_ACCOUNT": "Add Bank Account", "SELECT_BANK_NAME": "Select Bank name", "BANK_ACCOUNT_HOLDER": "Account name", "BANK_ACCOUNT_NUMBER": "Account Number", "CURRENT_BANK_ACCOUNT": "Current Bank Account", "CHANGE_BANK_ACCOUNT": "Change Bank Account", "CONFIRM_BANK_ACCOUNT": "Confirm Bank Account", "CONFIRM_BANK_ACCOUNT_SUCCESS": "You have successfully updated your Bank account.", "REDIRECT_IN": "Redirect in {value} seconds", "CREATE_WITHDRAWAL": "C<PERSON> <PERSON><PERSON><PERSON>", "CURRENT_BALANCE": "Current Balance", "INPUT_AMOUNT_TO_WITHDRAWAL": "Input withdrawal amount", "MINIMUM_WITHDRAWAL_AMOUNT": "Minimum withdrawal amount", "WITHDRAWAL_AMOUNT": "Withdrawal amount", "FEE": "Fee", "TOTAL": "Total", "ACCOUNT_BALANCE_LEFT": "Account balance after withdrawal", "WITHDRAWAL_VOUCHER": "Voucher (optional)", "WITHDRAWAL_DESCRIPTION": "Description", "MAKE_WITHDRAWAL": "Make a withdrawal", "THERE_ARE_NO_CHANGES_HISTORY_YET": "There is no change history yet", "THERE_ARE_NO_WITHDRAWAL_HISTORY": "There is no withdrawal history", "WITHDRAWAL_DETAILS": "Withdrawal details", "AMOUNT": "Amount", "BALANCE_DEDUCTED": "Balance Deducted", "BANK_NAME": "Bank Name", "ACCOUNT_HOLDER": "Account Name", "ACCOUNT_NUMBER": "Account Number", "NOTE": "Note", "DATE": "Date", "CANCEL_REQUEST": "Cancel Request", "NEXT": "Next", "CONFIRM": "Confirm", "BACK": "Back", "WITHDRAWAL_FEE": "Withdrawal fee", "REASON": "Reason", "STATUS": "Status", "CLICK_TO_VERIFY": "Validate account name to activate this bank account", "TAP_HERE": "tap here", "VERIFY": "Verify", "LAST_CHANGED": "Last changed", "CANCEL": "Cancel", "BANK_ACCOUNT_NOT_CONFIGURED": "The bank account is not configured yet", "DETAILS": "Details", "ACCOUNT_NUMBER_MUST_AT_LEAST_8_DIGITS": "Account number must be at least 8 digits", "HAVE_PENDING_BANK_ACCOUNT": "You have a new bank account details to confirm, please check your email.", "HAVE_PENDING_BANK_ACCOUNT_NAME": "You have a new bank account details to confirm.", "CONFIRM_NOW": "Confirm Now", "MESSAGE_CAN_NOT_ADD_BANK_ACCOUNT": "Sorry, you can only access this page when you own at least 1 token. Please purchase a property first.", "BANK_WARNING_MESSAGE": "Please ensure that you do not enter virtual account numbers, phone numbers, and atm card numbers.", "PAYPAL_ACCOUNT_EMAIL": "PayPal Account Email", "YOUR_PAYPAL_ACCOUNT_EMAIL": "Your PayPal account email", "MANDATORY": "* Required", "WITHDRAWAL_FORM": "Withdrawal Form", "MESSAGE_CONFIRM_BANK_ACCOUNT": "Are you sure this is the correct bank account?", "EXCHANGE_RATES": "Exchange Rates", "BANK_ACCOUNT_HOLDER_NAME": "Account Name", "BANK_ADDRESS": "Bank Address", "BANK_COUNTRY": "Bank Country", "NOTES": "Notes", "PLEASE_MAKE_SURE_BANK_CORRECT": "Please make sure the bank account information you have entered is correct.", "YOUR_BANK_ACCOUNT_IS_BEING_VERIFIED": "Your bank account is being verified", "PLEASE_WAIT_FOR_A_MAXIMUM": "Please wait for a maximum of 72 hours", "EXCHANGE_RATES_NOTE": "The exchange rate quoted is for indication only and is subject to bank transfer fee.", "NOTICE_CHANGE_BANK_ACCOUNT": "You can only change your bank account once a month. Make sure you choose the right bank account.", "NOTICE_WITHDRAW_TAKE_TIME": "Withdrawal to be settled in {take_time} hours", "RECEIVE_AMOUNT": "You will receive", "WANT_TO_ENJOY_RETURNS": "<PERSON><PERSON><PERSON> due to paying for vacation tickets.", "WITHDRAWAL_SUMMARY": "<PERSON><PERSON><PERSON>", "CONTINUE": "Continue", "WITHDRAWAL_CONFIRMATION": "<PERSON><PERSON><PERSON> Confirmation", "PLEASE_ENSURE_YOUR_BANK_CORRECT": "Please ensure your bank account information is correct", "WITHDRAWAL_DESCRIPTION_TOOLTIP": "Please provide a description of your withdrawal for your record.", "DISCOUNT_APPLIED_LABEL": "Discount Applied", "AMOUNT_RECEIVED": "Amount Received"}, "RENTAL_INCOME": {"RENTAL_INCOME_REPORTS": "Rental Income Reports", "RENTAL_INCOME_DETAIL": "Rental Income Detail", "DETAILS": "Details", "SUMMARY_REPORT": "Summary report", "RENTAL_DISTRIBUTION_REPORT": "Rental distribution report of {propertyName} for {monthAndYear}", "RENTAL_INCOME_NET": "Net Rental Income ({value} days)", "RENTAL_INCOME_GROSS": "Gross Rental Income", "TOTAL_TOKENS": "Total Tokens", "EARNINGS_PER_TOKEN_PER_DAY": "Earnings per Token per Day", "RENTAL_DEDUCTIONS": "Rental Deductions", "THERE_ARE_NO_RENTAL_INCOME": "You have no rental income yet. If you have purchased tokens in {currentMonth}, then you will receive the first rental income by {nextPayoutDate}.", "BREAK_DOWN_REPORT": "Break down report", "RENTAL_MONTH": "Rental Month", "VIRTUAL_RENTAL_INCOME_REPORTS": "Virtual Rental Income Reports", "RECEIVED": "RECEIVED", "NON_IDR_REFERENCE_ONLY": "* any non-IDR amount is for reference only", "NOTES": "Notes", "DEDUCTION": "Deductions", "SUMMARY": "Summary", "DISTRIBUTION_DATE": "Distribution Date"}, "ABOUT_US": {"VIEW_PROPERTIES": "View Properties", "VIEW_OPEN_POSITIONS": "View open positions", "WE_ARE_ON_MISSION": "Unlocking Property Investment for Everyone", "THROUGH_FRACTIONAL_INVESTING": "We believe property investment should be easy, inclusive, and built for everyone, not just the privileged few.", "OUR_INVESTORS": "Our Investors", "OUR_ANGLES": "Our Angels & Advisors Come From", "JOIN_GORO": "Join <PERSON>", "ARE_YOU_PASSIONATE": "Are you passionate about property and want to be part of shaping the future of property investing? Come join GORO!"}, "VIRTUAL_BALANCE": {"VIEW_OUR_PROPERIES": "View Our Properties", "CONTINUE_TO_BUY": "Continue to Buy", "YOUR_VIRTUAL_BALANCE": "Your Virtual Balance", "ORDER_CONFIRMATION": "Order Confirmation", "BUY_NOW": "Invest Now", "TOOLTIP_MESSAGE": "This virtual balance cannot be withdrawn, but any rental yield acquired will be yours to keep. After a certain amount of time, this virtual balance will expire.", "EXPIRES_ON": "Expires on:", "EXPIRED_ON": "Expired on:", "VIRTUAL_BALANCE": "Virtual Balance:", "VIRTUAL_BALANCE_EARN": "Earn {value} days worth of REAL rental income using your virtual balance.", "REGISTER_TO_BRING": "Register to earn REAL rental income using your virtual balance", "YOU_ARE_NOW_OWNED_TOKENS": "You now own {value} {value2} of {value3}!", "EXPIRED": "Expired:", "VIRTUAL_ASSETS": "Virtual Assets ({value1} <PERSON><PERSON>{value2}, {value3} Token{value4}, {value5})", "TOKEN_EXPIRED_ON": "{token} token{value} will be expired on {date}"}, "BANK_STATUS": {"PENDING": "Pending", "FAILED": "Failed", "NEED_CONFIRM_NAME": "Need confirm name", "NEED_CONFIRM_EMAIL": "Need confirm email", "CONFIRMED": "Confirmed", "DEACTIVATED": "Deactivated"}, "BANK_STATUS_NOTE": {"PENDING": "Pending checking", "NEED_CONFIRM_NAME": "Please confirm your account holder name", "NEED_CONFIRM_EMAIL": "Check email to confirm your bank account", "CONFIRMED": "The bank account is successfully confirmed", "DEACTIVATED": "The bank account is deactivated"}, "BANK_ACCOUNT_HISTORY_TABLE_HEADER": {"STATUS": "Status", "BANK": "Bank", "ACCOUNT_NUMBER": "Account Number", "ACCOUNT_HOLDER": "Account Name", "DATE": "Date & Time"}, "WITHDRAWAL_HISTORY_TABLE_HEADER": {"STATUS": "Status", "AMOUNT": "Amount", "FEE": "Fee", "BANK": "Bank", "ACCOUNT_NUMBER": "Account Number", "NOTE": "Description", "DATE": "Date & Time"}, "WITHDRAWAL_STATUSES": {"PENDING": "Pending", "APPROVED": "Approved", "REJECTED": "Rejected", "CANCELLED": "Cancelled", "COMPLETED": "Completed", "FAILED": "Failed", "PENDING_PROCESS": "Pending Process", "PROCESSING": "Processing"}, "RENTAL_INCOME_TABLE_HEADER": {"TRANSACTION_ID": "Transaction ID", "PROPERTY": "Property", "VALUE": "Value", "STATUS": "Status", "RENTAL_MONTH": "Rental Month"}, "TRANSACTIONS_TABLE_HEADER": {"PROPERTY": "Property", "TYPE": "Transaction Type", "TOKEN": "Token", "VALUE": "Value", "STATUS": "Status", "DESCRIPTION": "Description", "DATE": "Date & Time"}, "TRANSACTIONS_TYPES": {"ALL": "All", "BUY_TOKEN": "Buy token", "ORDER_TOKEN": "Order token", "SELL_TOKEN": "Sell token", "REFERRAL_BONUS": "Referral bonus", "REFERRAL_BONUS_TOKEN": "Referral bonus token", "WITHDRAWAL": "<PERSON><PERSON><PERSON>", "RENTAL_DISTRIBUTION": "Rental distribution", "ADMIN_ADD_BALANCE": "Admin add balance", "BUY_TOKEN_VIRTUAL_BALANCE": "Buy token using virtual balance", "VIRTUAL_RENTAL_DISTRIBUTION": "Virtual balance rental distribution", "ADMIN_DEDUCT_BALANCE": "Admin deduct balance", "SYSTEM_ADD_BALANCE": "Deposited Balance"}, "TRANSACTIONS_STATUS": {"RECEIVED": "Received", "FAILED": "Failed", "PAID": "Paid", "EXPIRED": "Expired", "COMPLETED": "Completed", "PENDING": "Pending", "REJECTED": "Rejected", "APPROVED": "Approved", "CANCELLED": "Cancelled", "SWAPPED": "Swapped", "REFUNDED": "Deposited", "UNCLAIM": "Unclaim", "PENDING_PROCESS": "Pending Process", "PROCESSING": "Processing"}, "RENTAL_INCOME_SUMMARY_HEADER": {"NUM_OF_TOKENS": "Number Of Tokens", "OWNERSHIP_DAYS": "Ownership Days", "SUB_TOTAL": "Subtotal"}, "RENTAL_INCOME_DEDUCTION_HEADER": {"DEDUCTION_TYPE": "Deduction Type", "DESCRIPTION": "Description", "AMOUNT": "Amount"}, "CONTRACT": {"YOUR_CONTRACT_ON_THIS_ASSET": "Your contract on this asset", "YOUR_CONTRACT_DOCUMENTS_OF_PROPERTY": "Your contract documents of the {value} property", "DOCUMENT_IS_BEING_PROCESSED": "(Document is being processed...)"}, "PENDING_TASKS": {"TABLE_HEADER_TASK": "Action", "TABLE_HEADER_TYPE": "Type", "TABLE_HEADER_STATUS": "Status", "TABLE_HEADER_EXPIRES_ON": "Expires On", "TABLE_HEADER_ACTION": "", "TABLE_HEADER_DATE": "Date", "PENDING_TASKS": "Pending Actions", "THERE_ARE_NO_PENDING_TASKS": "There are no pending actions.", "COMPLETE_PENDING_TASK": "Complete pending actions", "PLEASE_READ_AND_AGREE_TO_AGREEMENT": "Please read and agree to the <b>Fractional Property (Token) Transaction Agreement</b> before continue.", "READ_AND_AGREE_TOKEN_TRANSACTION_AGREEMENT": "Read and agree to the <b>Fractional Property (Token) Transaction Agreement</b> of property: <b>{value}</b>", "ACTION_SIGN_NOW": "Sign Now", "CLAIM_RENTAL_INCOME": "Claim <b>{amount}</b> from the <b>Rental Income</b> of property: <b>{value}</b> on <b>{month}</b>", "CLAIM": "<PERSON><PERSON><PERSON>", "CLAIM_REFERRAL_BONUS": "Claim <b>{amount}</b> bonus from GORO referral program"}, "COMPLETED_TASKS": {"CASHBACK": "You have claimed <b>{amount}</b> from the referral program.", "CONTRACT": "You have signed a contract for <b>{property_name}</b>", "THERE_ARE_NO_COMPLETED_TASKS": "There are no completed actions.", "EXPIRED": "Bonus <b>{amount}</b> has expired and can no longer be claimed.", "STATUS_COMPLETED": "Completed", "STATUS_EXPIRED": "Expired"}, "PROPERTY_STATUS": {"COMING_SOON": "Coming Soon", "SOLD_OUT": "Sold Out", "PROMO": "Promo", "PRESALE": "Presale", "AVAILABLE": "Available"}, "PIN_SECURITY": {"PIN": "PIN", "CHANGE_PIN": "Change PIN", "CREATE_NEW_PIN": "Create New PIN", "AVOID_USING": "Avoid using PIN that contains repeating\nand consecutive numbers, or your date of birth", "RE_ENTER_PIN": "Re-Enter PIN", "RE_ENTER_6_DIGITS": "Re-enter the 6 digit PIN you just created", "PIN_DOES_NOT_MATCH": "PIN does not match. Please check again", "PIN_CREATED_SUCCESSFULLY": "PIN Created Successfully", "PIN_UPDATED_SUCCESSFULLY": "PIN Successfully Changed", "OKAY": "Okay", "ENTER_YOUR_CURRENT_PIN": "Enter Your Current PIN", "ENTER_6_DIGITS": "Enter your 6 digit PIN", "FORGOT_PIN": "Forgot PIN?", "ACCOUNT_BLOCKED": "Account Blocked", "ACCOUNT_BLOCKED_MESSAGE": "Your account has been blocked\nbecause you have entered wrong PIN 5 times", "PLEASE_CONTACT": "Please contact {email} or WhatsApp\n{phone} to unblock your account", "SEND_EMAIL": "Send email", "WE_WILL_SEND_EMAIL_TO": "We will send an email to", "CLICK_THE_LINK_TO_CHANGE_YOUR_PIN": "Click the link given on the email to change your PIN", "EMAIL_SENT": "Email <PERSON>!", "PLEASE_CHECK_THE_EMAIL_WE_SENT_TO": "Please check the email we sent to", "TO_CHANGE_YOUR_PIN": "to change your PIN", "WRONG_PIN_ATTEMPTS": "You entered an incorrect PIN {value} times.\nIf you entered wrong PIN 5 times,\nyour account will be blocked"}, "CHANGE_PASSWORD": {"HEADER": "Change password", "CHANGE_PASSWORD": "Change password", "NEW_PASSWORD": "New password", "CONFIRM_PASSWORD": "Confirm password", "SUCCESSFULLY": "Password changed successfully, please log in again with your new password.", "CHECKLISTS": {"LEAST1CHAR": "At least 8 characters", "LEAST1NUMBER": "At least 1 number", "LEAST1CHARCASE": "At least 1 lowercase and 1 upper case letter", "LEAST1SPECIAL": "At least 1 special character"}}, "ACCOUNT_SECURITY": {"TITLE": "Security", "MODIFY": "Modify", "PASSWORD": {"TITLE": "Password", "DESCRIPTION": "Used to log in to your account.", "WARNING": {"TITLE": "Essential Password Security Measures", "WARNING_1": "Update your password at least once every three months.", "WARNING_2": "Avoid reusing passwords.", "WARNING_3": "Keep your password confidential."}}}, "TEXT_BANNER": {"TEXT": "GORO Becomes First Participant in Indonesian Financial Services Authority's (OJK) Regulatory Sandbox for Property Tokenization"}, "SHARING": {"GORO_ID": "GORO ID: {value}", "MEMBER_SINCE": "Member since", "TOTAL_PROPERTIES": "Total properties", "TOTAL_EARNINGS": "Total earnings (Annualized %)", "FAVORITE_PROPERTIES": "Favorite properties", "JOIN_WITH_REFERRAL_CODE": "Use this invitation code and start investing in properties with GORO now:", "INVITE_MESSAGE": "Join me in investing in property from $1 with GORO! I am earning up to 10% net rental yield per year, which I get paid monthly! Check out My property portfolio here:"}, "GORO_OFFICE": {"GORO_BALI_OFFICE": {"NAME": "GORO Bali Office", "ADDRESS": "Jalan Canggu Shortcut No.7, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ka<PERSON><PERSON><PERSON>, Bali 80351"}, "GORO_JAKARTA_OFFICE": {"NAME": "GORO Jakarta Office", "ADDRESS": "RDTX SQUARE Lt. 9, Jl. Prof. DR. Satrio No.164, <PERSON><PERSON>, Setiabudi, South Jakarta City, Jakarta 12930"}}, "TWO_FA": {"TWO_FACTOR_AUTHENTICATION": "Two-Factor Authentication", "RECOVERY_CODES": "Recovery Codes", "ADD_EXTRA_SECURITY_TO_YOUR_ACCOUNT": "Add extra security to your account", "DESCRIPTION": "Two-factor authentication protects your account by requiring an additional code when you login to your account", "AUTHENTICATION_APP": "Authentication app", "AUTHENTICATION_APP_DESCRIPTION": "We will recommend an app to download if you don't have one. It will generate a code you will enter when you login to your account.", "INSTRUCTION_FOR_SETUP": "Instruction for setup", "INSTRUCTION_STEP_1": "Step 1. Download an authentication app", "INSTRUCTION_STEP_1_DESC": "We recommend downloading Twi<PERSON> if you don't have one installed. You can download the Twilio <PERSON>thy app through this link.", "INSTRUCTION_STEP_2": "Step 2. Scan this QR code or copy the key", "INSTRUCTION_STEP_2_DESC": "Scan this QR code in the authentication app or copy the key and paste it in the authentication app.", "INSTRUCTION_STEP_3": "Step 3. Copy and enter 6-digit code", "INSTRUCTION_STEP_3_DESC": "After the QR code is scanned or the key is entered, your authentication app will generate a 6-digit code. Copy the code and then come back to GORO to enter it.", "COPY_KEY": "Copy key", "GET_YOUR_CODE": "Get your code from your authentication app", "ENTER_THE_6_DIGITS": "Enter the 6-digit code generate by your authentication app", "ENTER_CODE": "Enter Code", "TWO_FA_IS_ON": "Two-factor authentication is on", "TWO_FA_IS_ON_DESC": "We will now ask for a login code anytime you login.", "DOWNLOAD_AS_TXT": "Download as .txt", "COPY_CODES": "Copy codes", "GET_NEW_CODES": "Get new codes", "RECOVERY_CODES_DESC": "You can use each backup code one time. Save your backup codes in a safe place. Without these, you may not be able to login to your account if you lose access to your phone or can't login using your two-factor authentication.", "ADDITIONAL_METHODS": "Additional Methods", "ADDITIONAL_METHODS_DESC": "If your other security methods are not available, you can still login with these options", "BACKUP_CODES": "Backup Codes", "BACKUP_CODES_DESC": "Use these when you don’t have access to your phone.", "TURN_OFF": "Turn Off", "TURN_ON": "Turn On", "ENABLE_RECOVERY_CODES_DESC": "Backup codes Use a backup code to login if you lose access to your phone or can’t login using your two-factor authentication", "TURN_OFF_BACKUP_CODES": "Turn off backup codes?", "TURN_OFF_BACKUP_CODES_DESC": "If you turn off backup codes, you will no longer be able to use them as a backup method to confirm a login to your account. Your other security methods will stay turned on for two-factor authentication.", "VERIFY_TWO_FA_DESC": "Open your two-factor authenticator app or browser extension to view your authentication code", "HAVING_PROBLEMS": "Having problems?", "USE_RECOVERY_CODE": "Use Backup Code", "USE_AUTHENTICATION_CODE": "Use Authentication Code", "LOGIN_TO_GORO": "Login to GORO", "ENTER_CODE_AUTHENTICATION_APP": "Enter the 6-digit code for this account from the two-factor authentication app you have set up.", "ENTER_RECOVERY_CODE": "Enter one of your one-time use backup codes", "REMEMBER_THIS_DEVICE": "Remember this device", "TURN_OFF_AUTHENTICATION_APP": "Turn off authentication app?", "TURN_OFF_AUTHENTICATION_APP_DESC": "If you turn off your authentication app, the security of your GORO account will be reduced.", "CONTACT_US": "Contact us", "IF_YOU_ARE_LOCKED_OUT": "if you are locked out", "CLICK_HERE": "click here!"}, "TAB_CONTENT_HEADER": {"PENDING": "Pending", "DONE": "Done"}, "MODALS": {"COMMON": {"CANCEL": "Cancel", "CONTINUE": "Continue"}, "SELL_SOLD_OUT": {"HEADER": "Are you sure you want to sell this property?", "CONTENT": "This property is in high demand and is sold out. If you decide to sell this property, you may not be able to purchase it again in the future."}}, "MAINTENANCE": {"THIS_SITE_UNDER_MAINTENANCE": "This site is currently under maintenance", "WE_WILL_BACK_LATER": "We’ll be back later!"}, "PROOF_NOTIFICATION": {"MESSAGE": "A {award_label} has just bought {token} {token_label} of", "MESSAGE_TOTAL_ALL_PURCHASE": "purchase in the last {hour} hours", "MESSAGE_TOTAL_PURCHASE": "purchase in the last {hour} hours", "GROWER": "GROWER", "COLLECTOR": "COLLECTOR", "CHAMPION": "CHAMPION"}, "USERNAME": {"TERMS_OF_CHANGE": "Terms of username change:", "TERM_1": "You must own at least 10 tokens to be able to change your username.", "TERM_2": "Username needs to be a minimum of 6 characters and a maximum of 14 characters.", "TERM_3": "Username may only contain letters and numbers.", "TERM_4": "Username may only be changed once.", "TERM_5": "Username must not contain the words \"GORO\" and \"Admin\".", "TERM_6": "Username must not contain any inappropriate words.", "TERM_7": "Your username will revert, if your total assets are less than 10 tokens. You may lose your username to other users permanently.", "TERM_8": "GORO reserves the rights to disable the username feature and reset your username to the original username, if you violate the terms.", "USERNAME_CAN_BE_USE": "Username {value} can be used", "ARE_YOU_SURE_TO_USE": "Are you sure you are using this username?", "USERNAME_CHANGE_NOTE": "You need to own at least 10 tokens to change your username once. Please purchase more token to change your username.", "CHANGE_USERNAME": "Change Username", "ARE_YOU_SURE_TO_LEAVE_THIS_PAGE": "Are you sure you want to leave this page?", "THE_CHANGES_NOT_BE_SAVED": "The changes you have made will not be saved"}, "BALANCE_HISTORY": {"TITLE": "Balance History", "ALL_DATES": "All Dates", "ALL_TYPES": "All Types", "THERE_ARE_NO_HISTORIES": "There are no histories", "STATUS": "Status", "SALES_DETAILS": "Sales Details", "TOTAL_WITHDRAW": "Total Withdraw", "DEPOSITED_DETAILS": "Deposited Details", "TOTAL_DEPOSITED": "Total Deposited", "DEDUCTED_DETAILS": "Deducted Details", "TOTAL_DEDUCTED": "Total Deducted", "TYPE_MAP": {"SELL": "<PERSON><PERSON>", "BUY": "Buy Token", "WITHDRAW": "Withdraw Balance", "ADD": "Balance Deposit", "DEDUCT": "Balance Deduction", "SWAP": "<PERSON><PERSON>p <PERSON>"}, "GORO_BALANCE": "GORO Balance", "PAYMENT_DETAILS": "Payment Details", "PAYMENT_METHOD": "Payment Method", "TODAY": "Today", "LAST_X_DAYS": "Last {value} Days", "WITHDRAW_DETAILS": "Withdraw details", "PICK_DATE": "Pick Date", "APPLY": "Apply", "SELECT_DATES_UP_TO": "You can select dates up to the last 30 days", "SELECT_EXCEED_30_DAYS": "Selected date is more than 30 days, please select again."}, "BANK_ACCOUNT_HISTORY": {"TITLE": "Bank Account Details", "CONFIRM_NAME_NOTE": "Please fill in the fields above according to the name on your bank account to ensure you are the account holder."}, "NAVBAR": {"ACTIVITY_CENTER": "Activity Centre", "WITHDRAWLS": "<PERSON><PERSON><PERSON><PERSON>", "SECURITY": "Security"}, "ONBOARDING": {"QUIZ_HEADING": "GORO Quiz", "WELCOME_MESSAGE": "Hello, <PERSON><PERSON><PERSON>! Welcome to the GORO Quiz. This quiz was designed to help you better understand how GORO works.", "CORRECT": "Correct!", "INCORRECT": "Incorrect!", "GREAT_JOB": "Great job!", "NOT_QUITE": "Not quite!", "START": "Start", "NEXT": "Next", "TRY_AGAIN": "Try Again", "STEP_QUESTION": "Question", "FINISHED": "Finish", "FINISHED_MESSAGE": "You have successfully completed the GORO Quiz. Let's join the GORO community group on Telegram with over {members}k members, so you can be notified when a sold-out property becomes available and chat directly with GORO founders:", "CONGRATULATIONS": "Congratulations!", "CLICK_HERE": "click here!"}, "BLOCKCHAIN": {"TITLE": "Blockchain", "CONTRACT_ADDRESS": "Smart Contract Address", "NETWORK": "Blockchain Network", "TOKEN_ID": "Token ID", "WALLET": "Wallet", "WALLET_ADDRESS": "Wallet Address", "WALLET_WARNING_1": "Do not send any crypto assets to this wallet address. We are not responsible for any loss of assets sent to this wallet address.", "WALLET_WARNING_2": "Do not share your wallet address, because all your assets and transactions will be visible to others."}, "PARTNER": {"LOGIN_HEADER": "PARTNER LOGIN", "LOGIN_DESCRIPTION": "Please enter your login and password!", "PROPERTY": {"TOKEN_HOLDERS": "<PERSON><PERSON> Holders"}, "PICK_DATE": "Pick Date", "SELECT_DATES_UP_TO": "You can select dates up to the last {limit} days", "SELECT_EXCEED_LIMIT_DAYS": "Selected date is more than {limit} days, please select again.", "APPLY": "Apply", "USER": {"VIEW_USER_ASSETS": "View Assets", "ASSETS_LIST": "Assets List"}, "TRANSACTION": {"DETAIL": "Detail", "UUID": "<PERSON><PERSON>", "TRANSACTION_DETAILS": "Transaction Details", "EXTERNAL_ID": "Transaction ID", "BLOCKCHAIN_TRANSACTION_ID": "Transaction Blockchain ID", "TOKENS": "Tokens", "VALUE": "Value", "DESCRIPTION": "Description", "STATUS": "Status"}}, "SANDBOX": {"TITLE": "GORO has been accepted into the Indonesian Financial Services Authority’s Regulatory Sandbox.", "HEADER_FOOTER": "GORO is a participant of the Indonesian Financial Services Authority's (OJK) Regulatory Sandbox based on OJK Letter no. S-548/IK.01/2024 and S-549/IK.01/2024", "BODY_CONTENT_1": "We are thrilled that GORO has created history by becoming the first Indonesian company accepted into the Indonesian Financial Regulatory Sandbox for Property Tokenization. The <strong>Indonesian Financial Services Authority's (OJK)</strong> accepted GORO based on letters <strong>S-548/IK.01/2024</strong> and <strong>S-549/IK.01/2024</strong> dated November 7, 2024. This milestone highlights our commitment to deliver safe and trustworthy innovation in property investment.", "BODY_CONTENT_2": "As Indonesia’s largest property tokenization platform, GORO empowers over <strong>{number}</strong> users with an affordable and accessible way to invest in high-yield Indonesian properties, starting from just <strong>IDR 10,000</strong>.", "DO_NOT_SHOW_IN_DAYS": "Don’t show again for 30 days", "CLOSE": "Close"}, "GO_LEARN": {"GO_LEARN": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "EPS": "Eps.", "EPISODE": "{total} Episode", "SEE_MORE": "See more", "SEE_LESS": "See less", "COMING_TITLE": "<PERSON><PERSON><PERSON><PERSON> is almost here", "COMING_MESSAGE": "Discover a fresh new way to learn and grow. Stay tuned for exciting updates."}, "ATTESTATION_REPORT": {"HEADING": "Attestation Report", "QUARTER_SYMBOL": "Q"}, "EVENT": {"EVENTS": "Events", "STATUS_UPCOMING": "Upcoming", "STATUS_ONGOING": "Ongoing", "STATUS_ENDED": "Ended", "RULES": "Rules", "JOIN_LEADERBOARD": "You are not on the leaderboard yet. Start investing, climb your way up and be the winner!", "ADD_TOKEN_TO_JOIN_LEADERBOARD": "Your are {token} {token_label} away from joining the leaderboard!", "ADD_TOKEN_TO_NEXT_RANK": "Buy {token} more {token_label} to get to the next rank", "MAINTAIN_YOUR_LEAD": "Congratulations! You are already at #1\nKeep it up!", "MAINTAIN_YOUR_TOP": "Awesome! You're in the Top {top_scorer}.\nKeep investing to stay on top!", "STARTING_SOON": "The event is starting soon! Get ready to climb the leaderboard!", "STAY_TUNE": "Stay tune!", "EVENT_ENDED": "The event has ended! Thank you for participating.", "READ_FOR_NEXT_CHALLENGE": "Ready for the next challenge?", "RANKING": "Rank", "PARTICIPANT": "Participant", "YOU_ARE_ON_TEAM": "You are on the {team} team", "JURAGAN": "Juragan", "SULTAN": "<PERSON>", "LEADERBOARD_REFRESH_NOTE": "Leaderboard last refreshed at: {last_updated}\nLeaderboard refreshes automatically every 1 minute."}, "VALIDATION": {"THE_DOB_IS_REQUIRED": "The Date of birth is required", "THE_DATE_OF_ISSUE_IS_REQUIRED": "The Date of issue is required", "THE_DATE_OF_EXPIRY_IS_REQUIRED": "The Date of expiry is required"}, "RECAPTCHA": {"REQUIRED": "CAPTCHA verification required.", "EXPIRED": "CAPTCHA expired. Please try again.", "ERROR": "Captcha not available. Refresh the page or try again later."}, "PROCESSING_FEE": {"TITLE": "Processing Fee", "DESCRIPTION_BALANCE": "Fee {processing_fee}", "DESCRIPTION_PERCENTAGE": "Fee {processing_fee_percent}%", "DESCRIPTION_PERCENTAGE_WITH_MAX": "Fee {processing_fee_percent}% with maximum of {max_processing_fee}"}, "TRANSACTION_FEE": {"TITLE": "Transaction Fee", "DESCRIPTION_BALANCE": "Fee {transaction_fee}", "DESCRIPTION_PERCENTAGE": "Fee {transaction_fee_percent}%", "DESCRIPTION_PERCENTAGE_WITH_MAX": "Fee {transaction_fee_percent}% with maximum of {max_transaction_fee}", "NOTE_FOR_SWAP": "Fee are deducted from your balance when available, or from the swapped tokens if your balance is insufficient."}, "VOUCHER": {"PROMO_CODE": "Voucher Code", "PROMO_DISCOUNT": "Voucher Discount", "INPUT_PROMO_CODE": "Input voucher code", "APPLIED": "Applied", "DISCOUNT_BALANCE": "Discount {amount}{discount_on}{required_note}", "DISCOUNT_PERCENTAGE": "Discount {percent}%{discount_on}{required_note}", "DISCOUNT_PERCENTAGE_WITH_MAX": "Discount {percent}% with maximum of {max_amount}{discount_on}{required_note}", "ON_TRANSACTION_FEE": " on transaction fee", "ON_PROCESSING_FEE": " on processing fee", "ON_FEES": " on fees", "REQUIRED_MIN_PURCHASE_NOTE": " (requires a minimum purchase of {required_transaction_amount})", "REQUIRED_MIN_SALE_NOTE": " (requires a minimum sale of {required_transaction_amount})", "REQUIRED_MIN_SWAP_NOTE": " (requires a minimum swap of {required_transaction_amount})", "REQUIRED_MIN_WITHDRAWAL_NOTE": " (requires a minimum withdrawal of {required_transaction_amount})", "WARNING_NO_DISCOUNT": "Your voucher code is valid, but it didn’t apply any discount. Please review the voucher conditions."}}