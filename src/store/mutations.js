import { KYC_CARD_TYPES } from "../constants/constants";
import { USER_STATUSES } from "../constants/userStatus";

export default {
  setGeoLocation (state, payload) {
    state.geoLocation = payload
  },
  setStayAtCurrentLocation (state, stayAtCurrentLocation) {
    state.stayAtCurrentLocation = stayAtCurrentLocation
  },
  setSwitchToNewLocation (state, switchToNewLocation) {
    state.switchToNewLocation = switchToNewLocation
  },
  setUserProfile(state, payload) {
    if (payload) {
      const { id_card, selfie } = payload

      const hasNotKyc = !id_card || !id_card.is_valid
      const compledUpload = id_card
        && id_card.is_valid;

      const completedIDVerfification = id_card
        && id_card.is_valid
        && id_card.adjusted_card_data !== null;

      const isUserActiveFully = !!(payload.status === USER_STATUSES.FullyActive)
      const isTypePassport = id_card && (id_card.card_type === KYC_CARD_TYPES.PASSPORT)
      const completedSelfie = selfie && selfie.is_valid
      const completedVerification = completedSelfie && (selfie.matched_ktp || isTypePassport) && (id_card && id_card.cft_pep_valid);
      const beingReviewed = id_card && selfie && (!id_card.cft_pep_valid || !selfie.matched_ktp) && id_card.reason != "REJECTED_BY_ADMIN" && selfie.reason != "REJECTED_BY_ADMIN"
      const failedVerification = !completedVerification && !beingReviewed
      
      payload = {
        ...payload,
        kyc_status: {
          hasNotKyc: hasNotKyc,
          compledUpload: !!compledUpload,
          completedIDVerfification: !!completedIDVerfification,
          completedSelfie: !!completedSelfie,
          completedVerification: (!!completedVerification || isUserActiveFully),
          beingReviewed: !!beingReviewed,
          failedVerification: !!failedVerification,
        }
      }
    }

    state.userProfile = payload;
  },
  setIsLoading(state, loading) {
    state.isLoading = loading;
  },
  setConfigs(state, payload) {
    state.configs = payload;
  },
  setUnreadCount(state, payload) {
    state.unread = payload;
  },
  setAvailableBanks(state, payload) {
    state.availableBanks = payload;
  },
  setReferralCode(state, payload) {
    state.referralCode = payload;
  },
  setAssetsCount(state, payload) {
    state.assetsCount = payload
  },
  setPendingTasksCount(state, payload) {
    state.pendingTasksCount = payload
  },
  setInitialBalance(state, payload) {
    state.initialBalance = payload
  },
  setExchangeRates(state, payload) {
    state.exchangeRates = payload
  },
  setSocialLoginData(state, payload) {
    state.socialLoginData = payload
  },
  setAdminPermissions(state, payload) {
    state.adminPermissions = payload
  },
  setRequired2FA(state, payload) {
    state.required2FA = payload
  },
  setPartnerPermissions(state, payload) {
    state.partnerPermissions = payload
  },
  setGoLearnPlaylists(state, payload) {
    state.goLearnPlaylists = payload
  },
  setDocumentVersioning(state, payload) {
    state.documentVersioning = payload
  },
  setInstalledPin(state, payload) {
    state.installedPin = payload
  },
  setShowRegistrationComplete(state, payload) {
    state.showRegistrationComplete = payload
  },
  setPreRegistered(state, payload) {
    state.preRegistered = payload;
  },
  setPreRegisteredOtpMethod(state, payload) {
    state.preRegisteredOtpMethod = payload;
  },
};
