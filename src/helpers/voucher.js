import {VOUCHER} from "@/constants/constants";

export function calculateVoucherRewardAmount(voucherCode, baseAmount, usedRewardAmount = 0) {
    try {
        const { voucher = {} } = voucherCode || {}
        const {
            reward_amount: configRewardAmount = 0,
            reward_amount_unit: configRewardAmountUnit,
            reward_max_amount: configRewardMaxAmount = 0
        } = voucher

        let rewardAmount = 0

        if (configRewardAmountUnit === VOUCHER.REWARD_AMOUNT_UNIT_BALANCE) {
            rewardAmount = Math.max(0, configRewardAmount - usedRewardAmount)
        } else if (configRewardAmountUnit === VOUCHER.REWARD_AMOUNT_UNIT_PERCENTAGE) {
            rewardAmount = Math.floor(baseAmount * (configRewardAmount / 100))
        }

        if (configRewardMaxAmount > 0) {
            const remainingRewardMaxAmount = Math.max(0, configRewardMaxAmount - usedRewardAmount)
            rewardAmount = Math.min(rewardAmount, remainingRewardMaxAmount)
        }

        return rewardAmount
    } catch {
        return 0
    }
}

export function getVoucherRewardNote (voucherCode, action, t, exchangeValue) {
    const voucher = voucherCode?.voucher
    if (!voucher) return ''

    const rewardAmount = voucher.reward_amount
    const rewardMaxAmount = voucher.reward_max_amount
    const rewardAmountUnit = voucher.reward_amount_unit

    const key = rewardAmountUnit === VOUCHER.REWARD_AMOUNT_UNIT_BALANCE
        ? 'VOUCHER.DISCOUNT_BALANCE'
        : (rewardMaxAmount > 0
            ? 'VOUCHER.DISCOUNT_PERCENTAGE_WITH_MAX'
            : 'VOUCHER.DISCOUNT_PERCENTAGE')
    const voucherRewardType = voucher.reward_type
    let discountOn = ''
    if (voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_BUY
        || voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_SELL
        || voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_SWAP
        // || voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_WITHDRAWAL
    ) {
        discountOn = t('VOUCHER.ON_TRANSACTION_FEE')
    } else if (voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_FEE_BUY
        || voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_FEE_WITHDRAWAL) {
        discountOn = t('VOUCHER.ON_PROCESSING_FEE')
    } else if (voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES || voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES_BUY) {
        discountOn = t('VOUCHER.ON_FEES')
    }
    let requireNote = ''
    if (voucher.required_transaction_amount > 0) {
        if (action === VOUCHER.REDEEM_ACTION_PAYMENT || action === VOUCHER.REDEEM_ACTION_ORDER) {
            requireNote = t('VOUCHER.REQUIRED_MIN_PURCHASE_NOTE', { required_transaction_amount: exchangeValue(voucher.required_transaction_amount) })
        } else if (action === VOUCHER.REDEEM_ACTION_SELL) {
            requireNote = t('VOUCHER.REQUIRED_MIN_SALE_NOTE', { required_transaction_amount: exchangeValue(voucher.required_transaction_amount) })
        } else if (action === VOUCHER.REDEEM_ACTION_SWAP) {
            requireNote = t('VOUCHER.REQUIRED_MIN_SWAP_NOTE', { required_transaction_amount: exchangeValue(voucher.required_transaction_amount) })
        } else if (action === VOUCHER.REDEEM_ACTION_WITHDRAWAL) {
            requireNote = t('VOUCHER.REQUIRED_MIN_WITHDRAWAL_NOTE', { required_transaction_amount: exchangeValue(voucher.required_transaction_amount) })
        }
    }

    return t(key, {
        amount: exchangeValue(rewardAmount),
        percent: rewardAmount,
        max_amount: exchangeValue(rewardMaxAmount),
        discount_on: discountOn,
        required_note: requireNote,
    })
}
