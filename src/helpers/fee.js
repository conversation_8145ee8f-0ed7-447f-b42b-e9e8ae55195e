import {CONFIG} from "@/constants/constants";

function parseFeeConfig(transactionFeeConfig) {
    const config = transactionFeeConfig || {}
    const type = config.type || ""
    const amount = parseFloat(config.amount) || 0
    const maxAmount = parseFloat(config.max_amount) || 0
    return { type: type, amount: amount, maxAmount: maxAmount }
}

function calculateFee(feeConfig, baseAmount) {
    if (baseAmount <= 0) {
        return 0
    }

    let fee = 0
    const { type, amount, maxAmount } = parseFeeConfig(feeConfig)
    if (type === CONFIG.FEE_TYPE_BALANCE) {
        fee = amount
    } else if (type === CONFIG.FEE_TYPE_PERCENTAGE) {
        fee = amount > 0 ? Math.round(baseAmount * (amount / 100)) : 0
    }

    if (maxAmount > 0) {
        fee = Math.min(fee, maxAmount)
    }

    return Math.round(fee)
}

function getTransactionFeeNote(displayTransactionFees, transactionFeeConfig, t, exchangeValue) {
    if (displayTransactionFees) {
        const { type, amount, maxAmount } = parseFeeConfig(transactionFeeConfig)
        if (amount > 0) {
            if (type === CONFIG.FEE_TYPE_BALANCE) {
                return t("TRANSACTION_FEE.DESCRIPTION_BALANCE", { transaction_fee: exchangeValue(amount) });
            } else if (type === CONFIG.FEE_TYPE_PERCENTAGE) {
                if (maxAmount > 0) {
                    return t("TRANSACTION_FEE.DESCRIPTION_PERCENTAGE_WITH_MAX", {
                        transaction_fee_percent: amount,
                        max_transaction_fee: exchangeValue(maxAmount),
                    });
                } else if (maxAmount === 0) {
                    return t("TRANSACTION_FEE.DESCRIPTION_PERCENTAGE", {
                        transaction_fee_percent: amount
                    });
                }
            }
        }
    }
    return "";
}

function getProcessingFeeNote(feeConfig, t, exchangeValue,) {
    const { type, amount, maxAmount } = parseFeeConfig(feeConfig)
    if (amount > 0) {
        if (type === CONFIG.FEE_TYPE_BALANCE) {
            return t("PROCESSING_FEE.DESCRIPTION_BALANCE", { processing_fee: exchangeValue(amount) });
        } else if (type === CONFIG.FEE_TYPE_PERCENTAGE) {
            if (maxAmount > 0) {
                return t("PROCESSING_FEE.DESCRIPTION_PERCENTAGE_WITH_MAX", {
                    processing_fee_percent: amount,
                    max_processing_fee: exchangeValue(maxAmount),
                });
            } else if (maxAmount === 0) {
                return t("PROCESSING_FEE.DESCRIPTION_PERCENTAGE", {
                    processing_fee_percent: amount
                });
            }
        }
    }
    return "";
}

export {calculateFee, getTransactionFeeNote, getProcessingFeeNote};
