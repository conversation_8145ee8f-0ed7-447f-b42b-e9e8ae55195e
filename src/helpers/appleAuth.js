const authUrl = () => {
    const url = process.env.VUE_APP_APPLE_AUTH_URL
    const params = {
        client_id: process.env.VUE_APP_APPLE_CLIENT_ID,
        redirect_uri: `${process.env.VUE_APP_API_ENDPOINT}/apple/signin-callback-web`,
        scope: 'email+name',
        response_type: 'code+id_token',
        response_mode: 'form_post',
    }
    const queryParams = Object.keys(params).map(key => `${key}=${params[key]}`).join("&")
    return `${url}?${queryParams}`
}

let eventListeners = [];

const requestSignIn = (callback) => {
    eventListeners.forEach(cb => {
        window.removeEventListener('message', cb);
    })
    eventListeners = [];

    const width = 500;
    const height = 680;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;
    window.open(authUrl(), 'Sign in with <PERSON>', `width=${width},height=${height},top=${top},left=${left}`);

    window.addEventListener('message', callback, false);
    eventListeners.push(callback)
}

export {
    authUrl,
    requestSignIn,
}