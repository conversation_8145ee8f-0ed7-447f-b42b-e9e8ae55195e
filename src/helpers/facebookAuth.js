class FacebookAuth {
  // Load the Facebook JavaScript SDK and Init
  init () {
    return new Promise((resolve) => {
      window.fbAsyncInit = function () {
        const FB = window.FB
        FB.init({
          appId: process.env.VUE_APP_FACEBOOK_APP_ID,
          cookie: true,
          xfbml: true,
          version: "v21.0"
        })
        resolve()
      };
      (function (d, s, id) {
        let js, fjs = d.getElementsByTagName(s)[0]
        if (d.getElementById(id)) return
        js = d.createElement(s)
        js.id = id
        js.src = "https://connect.facebook.net/en_US/all.js"
        fjs.parentNode.insertBefore(js, fjs)
      }(document, "script", "facebook-jssdk"))
    })
  }

  // Make an API call to the Graph API
  api (...params) {
    return new Promise((resolve) => {
      const callback = (response) => {
        //console.log("FB.api(", params, ") = ", response)
        resolve(response)
      }
      params.push(callback)
      window.FB.api(...params)
    })
  }

  // Trigger different forms of Facebook created UI dialogs,
  // such as the Feed dialog, or the Requests dialog.
  ui (params) {
    return new Promise((resolve) => {
      window.FB.ui(params, (response) => {
        //console.log("FB.ui(", params, ") = ", response)
        resolve(response)
      })
    })
  }

  // Returns the Facebook Login status of a user.
  getLoginStatus () {
    return new Promise((resolve) => {
      window.FB.getLoginStatus((response) => {
        //console.log("FB.getLoginStatus() = ", response)
        resolve(response)
      })
    })
  }

  // Prompts a user to login to your app using the Login dialog in a popup.
  login (params = { scope: "public_profile,email" }) {
    return new Promise((resolve) => {
      window.FB.login((response) => {
        //console.log("FB.login(", params, ") = ", response)
        resolve(response)
      }, params)
    })
  }

  // Logout the current user both from your site or app and from Facebook.com.
  logout () {
    return new Promise((resolve) => {
      window.FB.logout((response) => {
        //console.log("FB.logout() = ", response)
        resolve(response)
      })
    })
  }

  // Return the authResponse object without the overhead of an asynchronous call
  getAuthResponse () {
    //console.log("FB.getAuthResponse()")
    return window.FB.getAuthResponse()
  }
}

export const facebookAuth = new FacebookAuth();
