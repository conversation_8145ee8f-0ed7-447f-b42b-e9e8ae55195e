:root {
  --primary-lighter-color: #008e8e;
  --primary-color: #006666;
  --primary-darker-color: #005151;
  --primary-hover-color: #013D3D;
  --primary-background-color: #e7f6f6;
  --primary-background-darker-color: #cbecee;
  --primary-background-hover-color: #D6E7E8;
  --primary-background-darker-hover-color: #a9dfe3;
  --vs-selected-color: #006666 !important;
  --primary-light-color: #fff;
  --color-in-progress: #d9d9d9;
  --layout2-primary-color: #006867;
  --layout2-color-light: #C9F4F8;
  --layout2-primary-color-text: #00918E;
  --layout2-primary-color-light: #00B7B6;
  --layout2-background-color: #EAFDFF;
  --layout2-background-color-light: #F8FFFF;
  --footer-text: #DEE2E6;
  --hr-normal: #D5D5D5;
  --primary-menu-color: #3F3F3F;
  --primary-menu-active-color: #006867;
  --text-color: #3F3F3F;
  --text-color-darker: #121212;
  --text-color-secondary: #7E7E7E;
  --text-color-tertiary: #AAAAAA;
  --text-color-warning: #FF7D05;
  --text-color-error: #FF0000;
  --bg-gray: #DEE2E6;
  --green-dark-color: #005455;
}

@font-face {
  font-family: "Figtree";
  src: local("Figtree"),
    url(../fonts/figtree/fonts/ttf/Figtree-Regular.ttf) format("truetype");
  font-display: swap;
}

@font-face {
  font-family: "Figtree-Bold";
  src: local("Figtree-Bold"),
    url(../fonts/figtree/fonts/ttf/Figtree-Bold.ttf) format("truetype");
  font-display: swap;
}

@font-face {
  font-family: 'AcuminVariableConcept';
  src: url('../fonts/AcuminVariableConcept/Acumin-Variable-Concept.ttf') format('truetype'),
    url('../fonts/AcuminVariableConcept/Acumin-Variable-Concept.otf') format('opentype');
  font-style: normal;
}

/* This font face is the same with the original AcuminVariableConcept but fixed the space below */
@font-face {
  font-family: 'NewAcuminVariableConcept';
  src: url('../fonts/AcuminVariableConcept/Acumin-Variable-Concept.ttf') format('truetype'),
      url('../fonts/AcuminVariableConcept/Acumin-Variable-Concept.otf') format('opentype');
  font-style: normal;
  ascent-override: 86%;
  descent-override: 14%;
  line-gap-override: 0%;
}

@font-face {
  font-family: 'PressStart2P';
  src: url('../fonts/PressStart2P/PressStart2P-Regular.ttf') format('truetype');
}

body {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  font-style: normal;
}

body.modal-open {
  overflow: hidden;
}

p {
  margin: 0;
  padding: 0;
}

.font-normal {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  font-style: normal !important;
}

.font-medium {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  font-weight: 500 !important;
}

.font-semibold {
  font-family: "AcuminVariableConcept", Helvetica, sans-serif;
  font-weight: 600 !important;
}

.font-bold {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  font-weight: bold !important;
}

.font-extra-bold {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  font-weight: 800 !important;
}

.font-black {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  font-weight: 900 !important;
}

.site-header {
  height: auto;
  width: 100%;
  position: fixed;
  background-color: #ffffff;
}

@media (min-width: 992px) and (max-width: 1245px) {
  .site-header {
    height: auto;
    width: 100%;
    position: fixed;
    background-color: #ffffff;
  }
}

.bg-main-color {
  background-color: var(--primary-color);
}

.main-color {
  color: var(--primary-color);
}

.color-white {
  color: #ffffff;
}

.color-cyan {
  color: #00B7B6 !important;
}

.color-gray {
  color: #6c757d !important;
}

.color-gray-6d {
  color: #6D6D6D !important;
}

.color-black {
  color: #000000;
}

.color-orangered {
  color: orangered;
}

.color-main {
  color: var(--primary-color);
}

.color-red {
  color: red;
}

.color-error {
  color: #DC3445;
}

.text-color {
  color: #3F3F3F !important;
}

.text-color-secondary {
  color: #7E7E7E;
}

.text-color-tertiary {
  color: #AAAAAA;
}

.font-80 {
  font-size: 80px;
}

.font-72 {
  font-size: 72px;
}

.font-70 {
  font-size: 70px;
}

.font-64 {
  font-size: 64px;
}

.font-54 {
  font-size: 54px;
}

.font-52 {
  font-size: 52px;
}

.font-48 {
  font-size: 48px;
}

.font-46 {
  font-size: 46px;
}

.font-44 {
  font-size: 44px;
}

.font-42 {
  font-size: 42px;
}

.font-40 {
  font-size: 40px;
}

.font-38 {
  font-size: 38px;
}

.font-36 {
  font-size: 36px !important;
}

.font-34 {
  font-size: 34px !important;
}

.font-33 {
  font-size: 33px !important;
}

.font-32 {
  font-size: 32px !important;
}

.font-30 {
  font-size: 30px !important;
}

.font-28 {
  font-size: 28px !important;
}

.font-26 {
  font-size: 26px !important;
}

.font-24 {
  font-size: 24px !important;
}

.font-23 {
  font-size: 23px !important;
}

.font-22 {
  font-size: 22px !important;
}

.font-21 {
  font-size: 21px !important;
}

.font-20 {
  font-size: 20px !important;
}

.font-18 {
  font-size: 18px;
}

.font-17 {
  font-size: 17px;
}

.font-16 {
  font-size: 16px;
}

.font-14 {
  font-size: 14px !important;
}

.font-13 {
  font-size: 13px !important;
}

.font-12 {
  font-size: 12px !important;
}

.font-11 {
  font-size: 11px !important;
}

.line-height-70 {
  line-height: 70px;
}

.menu-mobile .vue-simple-drawer {
  width: 100% !important;
  background-color: #ffffff;
  color: #000000;
}

.menu-mobile .vue-simple-drawer .close-btn {
  top: 40px;
  right: 15px;
}

.menu-mobile ul li {
  list-style: none;
  font-size: 20px;
}

.menu-mobile ul li a {
  color: var(--primary-color);
  font-weight: 600;
}

.menu-mobile ul li a:hover {
  color: var(--primary-darker-color);
}

.menu-mobile .menu-content {
  padding-top: 15vh;
}

.menu-mobile .site-navigation .locale-switcher {
  padding: 0.5rem 1rem;
  margin-top: 0;
  float: none;
}

.menu-mobile .site-navigation .locale-switcher select {
  font-size: 20px;
  font-weight: bold;
}

.menu-mobile .site-navigation .user-avatar {
  margin: 15px auto;
  float: none;
}

.bm-burger-button {
  width: 26px;
  height: 24px;
  left: 26px;
  top: 26px;
}

.bm-burger-bars {
  background: #373a47;
  height: 20%;
  left: 0;
  right: 0;
  position: absolute;
}

.logo-img {
  width: 110px;
}

.img {
  width: 100%;
}

.click-able:hover {
  cursor: pointer;
}

.text-transparent {
  color: transparent;
}

.bg-clip-text {
  -webkit-background-clip: text;
}

.to-lofty-light {
  --tw-gradient-to: #9383ff;
}

.from-lofty-dark {
  --tw-gradient-from: #08165d;
  --tw-gradient-stops: var(--tw-gradient-from),
    var(--tw-gradient-to, rgba(8, 22, 93, 0));
}

.bg-gradient-to-tl {
  background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}

.dropdown-menu {
  position: absolute;
  text-align: left;
  -webkit-box-shadow: 0 2px 10px -2px rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px -2px rgba(0, 0, 0, 0.1);
  padding: 5px 0;
  margin-left: 0;
  background: #fff;
  -webkit-transition: 0.2s 0s;
  -o-transition: 0.2s 0s;
  transition: 0.2s 0s;
  border-radius: 4px;
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: none;
  font-size: 14px;
}

.dropdown-menu li > a {
  padding: 12px 16px;
}

.dropdown-menu > li > a:hover {
  background-color: var(--primary-color);
  color: white;
}

.dropdown-menu > li > a:hover svg {
  fill: white;
  stroke: white;
}

.site-navigation .user-avatar {
  width: 44px;
  float: right;
  margin-top: 12px;
  margin-right: 12px;
}

.site-navigation .locale-switcher {
  float: none;
  padding: 10px 3px;
}

.site-navigation .locale-switcher .dropdown-toggle-custom {
  border: none;
  background-color: transparent;
}

.site-navigation .locale-switcher .icon {
  color: #6c757d;
}

.site-navigation .locale-switcher .icon:hover {
  color: var(--primary-color);
}

.site-navigation .locale-switcher .icon-item {
  float: right;
  color: var(--primary-color);
}

.site-navigation .locale-switcher img {
  width: 24px;
  height: 24px;
}

.user-avatar button {
  padding: 0;
}

.user-avatar .show > .btn.dropdown-toggle {
  background-color: transparent;
}

.home-image2 {
  text-align: right;
}

.marketplace-select {
  height: 44px;
}

.marketplace .status {
  width: 100%;
}

.page-item.active .page-link {
  background-color: var(--primary-darker-color);
  border-color: var(--primary-darker-color);
}

/*.marketplace .radio-group-custom {*/
/*  width: 100%;*/
/*}*/

.marketplace .radio-group-custom .btn-outline-secondary {
  line-height: 56px;
  padding: 0 38px;
}

.card-image {
  width: 100%;
  height: 235px;
}

.bg-linear {
  background: linear-gradient(
    90.54deg,
    rgba(255, 255, 255, 0.75) 0.4%,
    rgba(255, 255, 255, 0) 74.7%
  );
}

.goro-select .vs__dropdown-toggle {
  background-color: white;
  color: var(--primary-color);
  padding: 0 8px 3px;
  height: 100%;
  border-radius: 8px;
}

.goro-select .vs__open-indicator {
  margin-right: 3px;
  fill: var(--primary-color);
}

.goro-select:focus {
  border-color: #ced4da;
  box-shadow: none;
}

.goro-select:after {
  position: absolute;
  content: "";
  top: 14px;
  right: 10px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  /* border-color: #fff transparent transparent transparent; */
}

.goro-select.select-arrow-active:after {
  border-color: transparent transparent #fff transparent;
  top: 7px;
}

.goro-select .vs__dropdown-option--selected {
  background-repeat: no-repeat;
  background-position: 95% 10px;
  background-image: url("../../assets/img/check-svgrepo-com.svg");
}

.goro-select .vs__dropdown-option--highlight {
  background-color: var(--primary-darker-color);
}

.goro-select .vs__dropdown-option--highlight.vs__dropdown-option--selected {
  background-image: url("../../assets/img/check_option.png");
  background-repeat: no-repeat;
  background-position: 95% 10px;
}

.goro-select .vs__dropdown-option {
  padding: 12px 10px;
}

button.bg-main-color {
  font-weight: 600;
}

button.bg-main-color:hover {
  background-color: var(--primary-darker-color);
  color: white;
}

#modal-scrollable___BV_modal_outer_ {
  z-index: 10001 !important;
}

#full-screen-modal___BV_modal_outer_ {
  z-index: 10001 !important;
}

.viewer-container {
  z-index: 10001 !important;
}

.fit-content {
  width: fit-content;
}

#modal-prevent-closing___BV_modal_outer_ {
  z-index: 10001 !important;
}

.vld-overlay {
  z-index: 10002 !important;
}

.mx-datepicker-main.mx-datepicker-popup {
  z-index: 10003 !important;
}

.timeline-item .title-item {
  font-weight: bold !important;
  font-size: 17px !important;
}

.timeline .year {
  display: none !important;
}

.vue-notification-group.notifications-group {
  z-index: 10004 !important;
}

@media (min-width: 992px) {
  .modal .modal-huge {
    max-width: 100% !important;
    width: 100% !important;
    height: 100vh;
    max-height: 100vh;
    min-height: 100vh;
    margin: 0;
    background: white;
  }
}

.modal-content-full {
  width: 100%;
  height: 100vh;
  min-height: 100vh;
}

.modal-body-full {
  height: 100%;
  padding: 2px !important;
  margin: 2px !important;
  border: none;
  border-radius: 0;
  background: white;
}

.modal-header {
  top: 0;
  width: 100%;
  background-color: white;
}

.modal-header-fixed {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: white;
}

.modal-footer {
  bottom: 0;
  width: 100%;
  background-color: transparent;
}

.modal-footer-fixed {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: white;
}

.dp__input {
  padding: 6px 30px 6px 12px !important;
  padding-left: 35px !important;
}

.hyperlink {
  color: var(--primary-color) !important;
  text-decoration: underline;
  text-decoration-color: var(--primary-color) !important;
  cursor: pointer;
}

.hyperlink-disabled {
  color: #6D6D6D !important;
  text-decoration: underline;
  text-decoration-color: #6D6D6D !important;
  cursor: pointer;
}

.otp-input {
  width: 50px;
  height: 60px;
  padding: 5px;
  margin: 0 4px;
  font-size: 50px;
  border-radius: 10px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  text-align: center;
}

/* Background colour of an input field with value */
.otp-input.is-complete {
  background-color: white;
}

.otp-input::-webkit-inner-spin-button,
.otp-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.otp-status-queued {
  color: white;
  background-color: #D98000;
  padding: 4px 15px 6px !important;
  border-radius: 50px;
}

.otp-status-sent {
  color: white;
  background-color: green;
  padding: 4px 15px 6px !important;
  border-radius: 50px;
}

.otp-status-error {
  color: white;
  background-color: red;
  padding: 4px 15px 6px !important;
  border-radius: 50px;
}

.otp-status-undelivered {
  color: white;
  background-color: red;
  padding: 4px 15px 6px !important;
  border-radius: 50px;
}

.otp-notice-background {
  width: auto;
  height: auto;
  margin-left: 0;
  margin-right: 0;
  display: flex;
  padding: 40px 70px 30px 125px; /*top right bottom left*/
  background-image: url('../../assets/img/otp_notice_background.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  color: white;
}

.goro-checkbox .custom-control-input:empty ~ .custom-control-label::before {
  border-color: var(--primary-color);
}

.goro-checkbox .custom-control-input ~ .custom-control-label::before {
  color: white;
  border-color: var(--primary-color);
  box-shadow: none;
}

.goro-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  box-shadow: 0 0 3px var(--primary-color);
  outline-color: var(--primary-color);
  background-color: var(--primary-color);
}

.single-line {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inherit;
}

.off-screen {
  position: absolute;
  left: -9999px;
  top: -9999px;
}

.text-clickable:hover {
  color: var(--primary-darker-color);
}

.proof-notification-container .award-label{
  font-family: "AcuminVariableConcept", Helvetica, sans-serif;
  line-height: 13.2px;
  color: #fff !important;
  font-weight: 700;
}

.color-green-dark{
  color: var(--green-dark-color);
}

.warning-message {
  color: var(--text-color-warning);
  background-color: #FFF6EC;
  border: 1px solid var(--text-color-warning);
  border-radius: 8px;
  padding: 15px 20px !important;
}

.tooltip {
  z-index: 11050 !important;
}

.tooltip .tooltip-inner {
  background-color: gray;
}