.v-sidebar-menu {
  background-color: #cdd7e0;
  box-shadow: 0 0px 8px 0 rgba(0, 0, 0, 0.2);
  transition: 0.2s;
}

.v-sidebar-menu.vsm_expanded .vsm--link_level-1.vsm--link_open{
  background-color: rgba(7, 55, 99, 0.9);
}

@media (max-width: 991px) {
  .v-sidebar-menu.vsm_collapsed {
    visibility: hidden;
    max-width: 0px !important;
  }
}

.v-sidebar-menu.vsm_rtl {
}

.v-sidebar-menu .vsm--item {
}

.v-sidebar-menu .vsm--item.vsm--item_open {
}

.v-sidebar-menu .vsm--link {
  color: white;
}

.v-sidebar-menu .vsm--link.vsm--link_active {
  /* background-color: rgba(7, 55, 99, 0.2); */
  background-color: rgba(7, 55, 99, 0.9);
}

.v-sidebar-menu .vsm--link.vsm--link_level-2.vsm--link {
  background-color: transparent;
}

.v-sidebar-menu .vsm--link.vsm--link_exact-active {
  background-color: rgba(7, 55, 99, 0.9);
}

.v-sidebar-menu .vsm--link_open {
  background-color: rgba(7, 55, 99, 0.9);
}

.v-sidebar-menu .vsm--link.vsm--link_mobile-item {
}

.v-sidebar-menu .vsm--item.vsm--item_open .vsm--link.vsm--link_level-1 {
  background-color: rgba(7, 55, 99, 0.9);
}

.v-sidebar-menu .vsm--item.vsm--item_open .vsm--dropdown {
    background-color: rgba(7, 55, 99, 0.7);
  }

  .v-sidebar-menu .vsm--dropdown {
    background-color: #2E4257;
  }

.v-sidebar-menu .vsm--link.vsm--link_disabled {
}

.v-sidebar-menu .vsm--title {
  color: var(--primary-color);
}

.v-sidebar-menu .vsm--link_active .vsm--title {
  color: white;
}

.v-sidebar-menu .vsm--link_level-1 .vsm--icon {
  background-color: transparent;
  color: var(--primary-color);
}
.v-sidebar-menu.vsm_expanded .vsm--link_level-1.vsm--link_open .vsm--icon{
  background-color: transparent;
  color: var(--primary-color);
}

.v-sidebar-menu .vsm--link_level-1.vsm--link_active .vsm--icon {
  color: white;
}

.v-sidebar-menu .vsm--link_level-2.vsm--link_active .vsm--icon {
  color: white;
}

.v-sidebar-menu .vsm--link.vsm--link_level-1.vsm--link_hover {
  background-color: rgba(7, 55, 99, 0.3);
}

.v-sidebar-menu .vsm--link.vsm--link_level-1.vsm--link_hover .vsm--icon {
  color: white;
}

.v-sidebar-menu .vsm--link.vsm--link_level-1.vsm--link_hover .vsm--title {
  color: white;
}

.v-sidebar-menu
  .vsm--item.vsm--item_open
  .vsm--link.vsm--link_level-1
  .vsm--icon {
  background-color: transparent;
  color: white;
}

.v-sidebar-menu
  .vsm--item.vsm--item_open
  .vsm--link.vsm--link_level-1
  .vsm--title {
  color: white;
}

.v-sidebar-menu
  .vsm--item.vsm--item_open
  .vsm--link.vsm--link_level-2
  .vsm--icon {
  background-color: transparent;
  color: white;
}

.v-sidebar-menu
  .vsm--item
  .vsm--link.vsm--link_level-2
  .vsm--title {
  color: white;
}

.v-sidebar-menu .vsm--arrow {
  color: var(--primary-color);
}

.v-sidebar-menu .vsm--arrow.vsm--arrow_open {
  color: white;
}

.v-sidebar-menu .vsm--badge {
  color: white;
  background-color: var(--primary-lighter-color);
}

.v-sidebar-menu .vsm--badge--empty {
  color: transparent;
  background-color: transparent;
}

.v-sidebar-menu .vsm--header {
}

.v-sidebar-menu .vsm--list {
}

.v-sidebar-menu .vsm--dropdown > .vsm--list {
  background-color: rgba(0, 0, 0, 0.4);
}

.v-sidebar-menu .vsm--mobile-item {
}

.v-sidebar-menu .vsm--mobile-bg {
}

.v-sidebar-menu .vsm--toggle-btn {
  background-color: rgba(7, 55, 99, 0.5);
  height: 50px;
}
