// .was-validated :invalid~.invalid-feedback,
// .was-validated :invalid~.invalid-tooltip,
// .is-invalid~.invalid-feedback,
// .is-invalid~.invalid-tooltip{
//   display: block
// }

.cls-custom-form-group-field {
  input:-webkit-autofill {
    background-color: white !important;
    color: black !important;
    transition: background-color 5000s ease-in-out 0s;
  }

  textarea:-webkit-autofill,
  select:-webkit-autofill {
    background-color: white !important;
    color: black !important;
  }

  .form-group-parent {
    margin-bottom: 1rem;
    .form-group {
      margin-bottom: 5px;
      &:has(.invalid),
      &:has(.is-invalid),
      &:has(.is-valid) {
        ~ .invalid-feedback,
        ~ .invalid-tooltip {
          display: block;
        }
      }
    }
  }
  .form-group {
    .btn-group{
      &.cls-custom-button-group{
        .btn{
          border: 1px solid #385c65;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 382;
          font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
          color: #6D6D6D;
          background-color: #fff;
          &:hover,
          &:active,
          &:focus{
            border: 1px solid #28A745;
            background-color: #fff;
            box-shadow: none !important;
          }
          &.active{
            border: 1px solid #28A745;
            box-shadow: none !important;
          }
        }
      }
    }
    .custom-label {
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
      color: #6D6D6D;
      font-size: 12px;
      font-weight: 500;
      line-height: 16.84px;
      margin-bottom: 0 !important;
      line-height: 18px !important;
      padding: 0.375rem 0 !important;
    }
    input,
    select {
      border-radius: 8px !important;
      box-shadow: none !important;
      color: #6D6D6D;
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
      font-size: 16px;
      font-weight: 500;
      height: 32px;
      line-height: 32px;
    }
    select.custom-select {
      line-height: 19px;
    }

    .invalid-feedback,
    .invalid {
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
      font-size: 12px;
      padding: 0 !important;
    }
    &.custom-form-group{
      border: 1px solid #CFD3D4;
      border-radius: 8px;

      .custom-label {
        font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
        color: #6D6D6D;
        font-size: 12px;
        font-weight: 500;
        line-height: 16.84px;
        margin-bottom: 0 !important;
        line-height: 18px !important;
        padding: 0.375rem 0.75rem !important;
        padding-bottom: 0 !important;
      }

      input,
      select {
        border-radius: 8px !important;
        border: 0 !important;
        box-shadow: none !important;
        color: #6D6D6D;
        font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
        font-size: 16px;
        font-weight: 500;
        height: 32px;
        line-height: 32px;
      }
      select.custom-select {
        line-height: 19px;
      }

      .invalid-feedback,
      .invalid {
        font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
        font-size: 12px;
        padding: 0.375rem 0.75rem !important;
        padding-top: 0 !important;
      }
    }
    &.custom-form-group-passord {
      position: relative;
      .cls-icon-lock {
        position: absolute;
        left: 10px;
        top: 20px;
      }

      .custom-label {
        padding-left: 45px !important;
      }

      input {
        padding-left: 45px;
        background-color: transparent;
      }
    }
  }

  .cls-warning-field-text {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    font-size: 12px;
    color: #FF8A00;
    line-height: 16.84px;
    margin-bottom: 1rem;
  }
  &.extend-invalid{
    .form-group {
      &:has(.is-invalid) {
        .custom-label {
          color: #DC3545;
        }
      
        .cls-custom-button-group {
          .btn {
            border: 1px solid #DC3545;
            box-shadow: 0px 0px 6px 0px #FF0000 !important;
          }
        }
      }
      
      &.custom-form-group{
        &:has(.is-invalid) {
          border: 1px solid #DC3545;
          box-shadow: 0px 0px 6px 0px #FF0000;
          .custom-label {
            color: #DC3545;
          }
          .cls-custom-button-group{
            .btn{
              border: 1px solid #DC3545;
              box-shadow: 0px 0px 6px 0px #FF0000 !important;
            }
          }
        }
        &:has(.is-valid) {
          border: 1px solid #28A745;
          .custom-label {
            color: #28A745;
          }
        }
        .invalid-feedback,
        .invalid {
          padding-left: 0 !important;
          padding-top: 5px !important;
        }
        &:focus-within{
          box-shadow: 0px 0px 8px 0px #0066FF;
          border: 1px solid #66AFE9;
          .custom-label {
            color: #66AFE9;
          }
        }
        .vue-tel-input{
          border: 0 !important;
          &:focus-within{
            box-shadow: none !important;
          }
          .vti__dropdown{
            border-radius: 0 0 0 8px;
            &:hover{
              background-color: transparent !important;
            }
          }
        }
      }
    }
  }
}