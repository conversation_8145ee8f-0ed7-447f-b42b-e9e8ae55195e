.cls-goro-custom-modal {
  * {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  }

  .modal-dialog {
    .modal-content {
      border-radius: 10px;

      header.modal-header {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;

        &.no-border {
          border-bottom: 0 !important;
        }

        .header-title {
          line-height: 21.55px;
          color: #000 !important;
        }

        button.btn-close {
          background-color: #EAEAEA;
          width: 32px;
          height: 32px;
          border: 0 !important;
          line-height: 17px;
          padding: 0 !important;
          border-radius: 100%;

          &:hover {
            opacity: .8;
          }

          svg {
            color: #C1C1C1 !important;
            width: 10px;
          }
        }
      }
    }
  }

  &.cls-pickdate-modal {
    .modal-dialog {
      width: 100% !important;
      max-width: 400px !important;

      @media screen and (max-width: 992px) {
        max-width: 100% !important;
        width: auto !important;
      }
    }
  }
}

.cls-custom-transaction-detail-modal {
  .modal-dialog {
    max-width: 650px !important;
  }
}

.cls-custom-popup-container{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: black;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
  display: block;
  z-index: 1071;
  .popup-dialog{
    position: relative;
    width: auto;
    margin: .5rem;
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
    .popup-content {
      position: relative;
      background-color: #fff;
      border-radius: 20px;
      text-align: center;
      width: 98%;
      height: 98%;
      margin: auto;
      overflow: hidden;
    }
  }
}

.cls-golearn-modal-panel{
  .modal-dialog{
    @media screen and (max-width: 620px) {
    }
    .modal-content {
      border-radius: 20px !important;
      overflow: hidden;
      .modal-body{
        padding: 0 !important;
      }
    }
  }
  
  .cls-playlist {
    .title {
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }

    .episode {
      font-weight: 500;
      line-height: 19.2px;
      color: #9D9D9D;
    }
  }
}

.cls-custom-modal-confirming{
  .modal-header{
    justify-content: center;
    background-color: #fff !important;
    border-bottom: 0 !important;
    padding: 30px;
    padding-bottom: 0;
    .modal-header-title{
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
      font-weight: 700;
      line-height: 28.8px;
      text-align: center;
      color: #212529;
    }
  }
  .modal-body{
    padding: 30px;
    .cls-content{
      *{
        font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
        color: #212529;
      }
    }
  }
  .modal-footer{
    justify-content: center;
    background-color: #fff !important;
    border-top: 0 !important;
    padding: 30px;
    padding-top: 0;
    button{
      flex: 1;
    }
  }
}
