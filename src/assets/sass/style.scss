.font-acumin {
  font-family: "AcuminVariableConcept", Helvetica, sans-serif;

  *,
  &::before,
  &::after {
    font-family: "AcuminVariableConcept", Helvetica, sans-serif;
  }
}

.font-new-acumin {
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif;

  *,
  &::before,
  &::after {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif;
  }
}

.btn-main {
  background-color: var(--primary-color);
  border-color: var(--primary-darker-color);
  color: white;
  font-weight: 600;
  border-radius: 10px;
  transition: 0.3s;

  &:hover {
    color: var(--primary-background-color);
    background-color: var(--primary-hover-color);
  }

  &:active,
  &:focus {
    box-shadow: none !important;
    color: var(--primary-background-color) !important;
    background-color: var(--primary-hover-color) !important;
  }

  &:disabled {
    background-color: gray;
    border-color: gray;
    color: lightgrey;
  }

  &.white{
    background-color: #fff;
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-size: 200% auto;

    &:hover {
      background-position: right center;
      background-image: linear-gradient(90deg, rgba(192, 234, 236, 0) 3.28%, rgba(192, 234, 236, 0) 23.47%, rgba(160, 213, 214, 0.08) 25.69%, rgba(112, 181, 181, 0.23) 29.52%, rgb(1 142 142 / 20%) 33.34%, rgb(1 142 142 / 30%) 37.06%, rgb(1 142 142 / 40%) 40.66%, rgb(1 142 142 / 60%) 44.08%, rgb(1 142 142 / 80%) 47.15%, rgb(1 142 142) 63.96%);
      color: #fff;
    }
  }

  &.btn-medium{
    height: 50px;
  }

  &.white-normal{
    background-color: #fff;
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-size: 200% auto;

    &:hover {
      background-color: var(--primary-color);
      color: #fff;
    }
  }
  &.white-reset{
    background-color: #fff;
    border-color: #dedede;
    color: #303030;
    background-size: 200% auto;

    &:hover,
    &:active,
    &:focus {
      background-color: #dedede !important;
      color: #303030 !important;
    }
  }
  &.btn-modal{
    padding: 5px 25px 7px 25px;
    &.fixed-width{
      width: 101px !important;
      padding: 5px 0 7px 0;
    }
    &.btn-outline{
      color: var(--primary-color);
      border: 1px solid var(--primary-color);
      background-color: #fff;
      &:hover {
        background-color: var(--primary-background-darker-color);
      }
      &:active,
      &:focus {
        box-shadow: none !important;
        color: var(--primary-color) !important;
        background-color: var(--primary-background-darker-color) !important;
      }
    }
    &.btn-danger{
      color: #FF0000;
      border: 1px solid #FF0000;
      background-color: #FFF4F4;
      &:hover {
        color: #fff;
        background-color: #FF0000;
      }
      &:active,
      &:focus {
        box-shadow: none !important;
        color: #fff;
        background-color: #FF0000;
      }
    }
  }
  &.btn-social{
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    padding: 9px;
    img.icon{
      margin-right: 10px;
    }
    span{
      font-weight: 600;
      line-height: 18px;
      text-align: center;
      color: #fff;
      margin-top: 3px;
    }
    &:hover,
    &:active,
    &:focus{
      opacity: .9;
    }
    &.social-whatsapp{
      border-color: #00AC00;
      background-color: #00AC00;
      &:hover,
      &:active,
      &:focus{
        background-color: #00AC00 !important;
      }
    }
    &.social-sms{
      border-color: #8E8E8E;
      background-color: #8E8E8E;
      &:hover,
      &:active,
      &:focus{
        background-color: #8E8E8E !important;
      }
    }
    &.social-x{
      border-color: #212121;
      background-color: #212121;
      &:hover,
      &:active,
      &:focus{
        background-color: #212121 !important;
      }
    }
    &.social-facebook{
      border-color: #315A94;
      background-color: #315A94;
      &:hover,
      &:active,
      &:focus{
        background-color: #315A94 !important;
      }
    }
  }
}

.btn-outline-main {
  background-color: var(--primary-background-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
  border-radius: 10px;

  &:hover {
    background-color: var(--primary-background-hover-color);
    border-color: var(--primary-hover-color);
    color: var(--primary-color);
  }

  &:active {
    filter: brightness(120%);
    box-shadow: none;
  }

  &:focus {
    filter: brightness(120%);
    box-shadow: none;
  }

  &:disabled {
    background-color: gray;
    border-color: gray;
    color: lightgrey;
  }
}

.btn-outline-main-darker {
  background-color: var(--primary-background-darker-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
  border-radius: 10px;

  &:hover {
    background-color: var(--primary-background-darker-hover-color);
    border-color: var(--primary-hover-color);
    color: var(--primary-color);
  }

  &:active {
    filter: brightness(120%);
    box-shadow: none;
  }

  &:focus {
    filter: brightness(120%);
    box-shadow: none;
  }

  &:disabled {
    background-color: gray;
    border-color: gray;
    color: lightgrey;
  }
}

.btn-outline-white {
  background-color: white;
  border-color: var(--primary-color);
  color: var(--primary-color);
  border-radius: 10px;

  &:hover {
    background-color: var(--primary-background-color);
    border-color: var(--primary-hover-color);
    color: var(--primary-color);
  }

  &:active,
  &:focus {
    // filter: brightness(120%);
    box-shadow: none !important;
    background-color: white !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
  }

  &:disabled {
    background-color: #ffffff;
    border-color: gray;
    color: gray;
  }
}

.btn-outline-cancel {
  background-color: rgb(247, 247, 247);
  border-color: rgb(233, 77, 77);
  color: rgb(233, 77, 77);
  border-radius: 10px;

  &:hover {
    background-color: rgb(255, 224, 224);
    border-color: rgb(246, 34, 34);
    color: rgb(233, 77, 77);
  }

  &:active {
    filter: brightness(120%);
    box-shadow: none;
  }

  &:focus {
    filter: brightness(120%);
    box-shadow: none;
  }

  &:disabled {
    background-color: gray;
    border-color: gray;
    color: lightgrey;
  }
}

.btn-outline-orange {
  background-color: rgb(255, 251, 245);
  border-color: darkorange;
  color: darkorange;
  border-radius: 10px;

  &:hover {
    background-color: rgb(255, 244, 230);
    border-color: darkorange;
    color: darkorange;
  }

  &:active {
    filter: brightness(120%);
    box-shadow: none;
  }

  &:focus {
    filter: brightness(120%);
    box-shadow: none;
  }

  &:disabled {
    background-color: gray;
    border-color: gray;
    color: lightgrey;
  }
}

.goro-password-strength-checklist{
  padding: 0;
  list-style-type: none;
  margin-top: 8px;
  margin-left: 2px;
  margin-bottom: 0;
  li{
    font-size: 13px;
    display: flex;
    align-items: center;
    vertical-align: middle;
    color: rgb(112, 122, 138);
    svg{
      width: 14px;
      margin-right: 5px;
      color: rgb(112, 122, 138);
    }
    &.active{
      svg{
        color: rgb(46, 189, 133);
      }
    }
  }
}

.goro-verification-code-block{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  @media screen and (max-width: 992px) {
    flex-direction: column;
    justify-content: center;
  }
  .otp-input{
    border: 1px solid rgba(0, 0, 0, 0.1);
    height: 50px;
    &:focus{
      border-color: var(--primary-color);
    }

    @media screen and (max-width: 768px) {
      height: 45px;
      width: 40px;
    }
    @media screen and (max-width: 320px) {
      height: 35px;
      width: 30px;
    }
  }
  .btn-send-code{
    min-width: 120px;
    padding: 10px 15px;
    margin-left: 13px;
    @media screen and (max-width: 992px) {
      margin-left: 0 !important;
      width: 50%;
      max-width: 150px;
      margin-top: 15px;
    }
  }
  .text-verified-code{
    padding: 13px;
    color: var(--primary-lighter-color);
  }
}

.show-desktop{
  display: block;
  @media screen and (max-width: 992px) {
    display: none !important;
  }
}

.show-mobile{
  display: none !important;
  @media screen and (max-width: 992px) {
    display: block !important;
  }
}

.btn-numberic-custom{
  &.full-width{
    flex: 1;
  }
  .vue-numeric-input{
    margin: 0;
    flex: 1;
    button{
      &.input-btn{
        width: 21px;
        height: 21px;
        background-color: #E7F6F6;
        border-radius: 100%;
        box-shadow: none !important;
        border: 1px solid #E7F6F6;
        top: 4px;
        &:hover{
          background-color: #d1f7f7;
          border: 1px solid #d1f7f7;
        }
        .btn-icon{
          display: flex;
          justify-content: center;
          align-items: center;
          &:before{
            width: 13px;
            height: 13px;
          }
        }
        &.btn-decrement{
          left: 5px;
          .btn-icon{
            &:before{
              background-image: url('~@/assets/img/icons/icon-decrement.svg');
            }
          }
        }
        &.btn-increment{
          right: 5px;
          .btn-icon{
            &:before{
              background-image: url('~@/assets/img/icons/icon-increment.svg');
            }
          }
        }
      }
    }
  }
  &.has-max-button{
    .vue-numeric-input{
      button{
        &.input-btn{
          &.btn-decrement{
            left: 15px;
          }
          &.btn-increment{
            right: 15px;
          }
        }
      }
    }
  }
  &.has-border{
    input{
      border: 0 !important;
      border-radius: 0 !important;
    }
    border: 1px solid var(--primary-color);
    display: flex;
    justify-content: stretch;
    padding: 2px;
    border-radius: 7px;
    align-items: center;
    background-color: #fff;
    .btn-max{

    }
  }
}

.goro-text-banner-section {
  height: auto;
  width: 100%;
  background-color: #F38300;
  text-align: center;
  position: relative;
  padding: 10px 0;
  top: 0;
  color: var(--primary-light-color);

  .content-banner {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;

    .text {
      color: var(--primary-light-color);
      margin: 0;
      padding: 0 20px;
      font-family: "AcuminVariableConcept", Helvetica, sans-serif;
      font-weight: 600;
      padding: 4px 20px 0;

      a {
        font-family: "AcuminVariableConcept", Helvetica, sans-serif;
        text-decoration: underline;
        font-weight: 700;
        color: var(--primary-light-color);

        &:hover,
        &:focus,
        &:active{
          color: var(--primary-color);
        }
        &.cls-text-link{
          color: #fff;
          text-decoration: underline;
          &:hover,
          &:focus,
          &:active{
            color: #fff;
            opacity: .8;
          }
        }
      }
    }
  }
}

.common-contain-tooltips{
  display: flex;
  align-items: center;
  .common-tooltips{
    margin-left: 10px;
    line-height: 0;
    img.tooltip-icon{
      width: 24px;
      height: 24px;
    }
  }
}

#id-goro-sell-sold-out-modal___BV_modal_outer_{
  z-index: 10001 !important;
}
#id-goro-onboarding-quiz-modal___BV_modal_outer_{
  z-index: 10001 !important;
}
.modal{
  &.goro-general-fe-side-modal{
    z-index: 10001 !important;
  
    .modal-dialog{
      @media (min-width: 576px) {
        max-width: 551px;
      }
    }
    
    .modal-content{
      border: none !important;
      border-radius: 20px !important;
      background-color: #fff;
      header.modal-header{
        background-color: #fff;
        position: relative;
        text-align: center;
        border-bottom: none !important;
        padding: 20px !important;
        padding-top: 30px !important;
        padding-bottom: 5px !important;
        border-top-left-radius: 20px !important;
        border-top-right-radius: 20px !important;
        .icon-header{
          display: none;
          position: absolute;
          width: calc(100% - 40px);
          top: -120px;
          img{
            width: 210px;
            height: 210px;
          }
          &.warning{
            img{
              width: 210px;
              height: 210px;
            }
          }
          &.info{
            img{
              width: 210px;
              height: 154px;
            }
          }
          &.presale{
            top: -95px;
          }
        }
        &.has-icon{
          .icon-header{
            display: block;
          }
        }
        .modal-header-title{
          width: 100%;
          text-align: center;
          color: var(--primary-color);
          &.red{
            color: #FF0000;
          }
        }
        &.has-icon{
          .modal-header-title{
            margin-top: 40px;
          }
        }
        button{
          &.close{
            position: absolute;
            right: 15px;
            top: 15px;
          }
        }
      }
      .modal-body{
        background-color: #fff;
        padding: 5px 20px 10px 20px !important;
        color: var(--primary-color);
        text-align: center;
      }
      footer.modal-footer{
        background-color: #fff;
        justify-content: center;
        border-top: none !important;
        padding: 20px !important;
        padding-bottom: 30px !important;
        padding-top: 10px !important;
        border-bottom-left-radius: 20px !important;
        border-bottom-right-radius: 20px !important;
      }
    }
  }

  footer.footer-notifications{
    justify-content: center !important;
    min-height: 70px;
    button{
      &.btn-absolute{
        position: absolute;
        right: 0;
        @media screen and (max-width: 992px) {
          position: unset !important;
        }
      }
    }
    @media screen and (max-width: 992px) {
      flex-direction: row !important;
    }
  }
}

.notice-important-block {
  padding: 5px 6px;
  color: white;
  background: darkorange;
  border-radius: 16px;
  font-size: 13px;
  font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
  text-align: center;
  &.medium-notice{
    padding: 20px;
    h3{
      margin-top: 0;
      color: white;
    }
    p{
      .underline{
        text-decoration: underline;
        text-underline-offset: 3px;
      }
    }
  }
}

.vti__dropdown-list{
  .vti__search_box_container{
    display: flex;
    .vti__input{
      &.vti__search_box{
        flex: 1;
      }
    }
  }
}

.goro-site-nav-custom {
  &.site-navigation-left,
  &.site-navigation-right{
    height: 80px;
    display: flex;
    align-items: center;
  }
  &.border-bottom{
    border-bottom: 1px solid white !important;
  }
  .site-menu-custom {
    padding: 0;
    margin-bottom: 0;
    a {
      text-decoration: none !important;
      display: inline-block;
      padding-left: 5px;
    }
    > li {
      display: inline-block;
      > a {
        padding: 20px 12px;
        color: var(--primary-menu-color);
        display: inline-block;
        text-decoration: none !important;
        font-size: 16px;
        font-weight: 500;
        line-height: 19.2px;
        text-align: left;

        &.has-child-menu{
          position: relative;
          margin-right: 20px;
          padding-right: 6px !important;
          &:before{
            content: "";
            width: 20px;
            height: 20px;
            background-image: url('~@/assets/img/icons/arrow_down.svg');
            display: block;
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            right: -20px;
            top: calc(50% - 9px);
          }
          &.active,
          &:hover{
            &:before{
              background-image: url('~@/assets/img/icons/arrow_up_active.svg') !important;
            }
          }
        }
        &:hover {
          color: var(--primary-menu-active-color);
        }
      }
    }
    .active {
      color: var(--primary-menu-active-color);
      font-weight: 600;
    }
    .has-children {
      position: relative;
      > a {
        position: relative;
      }
      &:hover,
      &:focus,
      &:active{
        > a{
          color: var(--primary-menu-active-color);
          cursor: pointer;
        }
        .dropdown {
          -webkit-transition-delay: 0s;
          -o-transition-delay: 0s;
          transition-delay: 0s;
          margin-top: 0;
          visibility: visible;
          opacity: 1;
        }
      }
      .dropdown {
        visibility: hidden;
        opacity: 0;
        top: 100%;
        position: absolute;
        text-align: left;
        border-top: 2px solid #143863;
        -webkit-box-shadow: 0 2px 10px -2px rgba(0, 0, 0, 0.1);
        box-shadow: 0 2px 10px -2px rgba(0, 0, 0, 0.1);
        padding: 0 0;
        margin-top: 20px;
        margin-left: 0;
        background: #fff;
        -webkit-transition: 0.2s 0s;
        -o-transition: 0.2s 0s;
        transition: 0.2s 0s;
        &.arrow-top {
          position: absolute;
          &:before {
            display: none;
            bottom: 100%;
            left: 20%;
            border: solid transparent;
            content: " ";
            height: 0;
            width: 0;
            position: absolute;
            pointer-events: none;
            border-color: rgba(136, 183, 213, 0);
            border-bottom-color: #fff;
            border-width: 10px;
            margin-left: -10px;
          }
        }
        a {
          text-transform: none;
          letter-spacing: normal;
          -webkit-transition: 0s all;
          -o-transition: 0s all;
          transition: 0s all;
          color: var(--primary-color) !important;
          &.active {
            color: var(--primary-darker-color);
            background: #f8f9fa;
          }
        }
        > li {
          list-style: none;
          padding: 0;
          margin: 0;
          min-width: 200px;
          > a {
            padding: 9px 20px;
            display: block;
            &:hover {
              background: #f8f9fa;
              color: var(--primary-color);
            }
          }
          &.has-children{
            > .dropdown,
            > ul {
              left: 100%;
              top: 0;
            }
            &:hover > a,
            &:active > a,
            :focus > a{
              background: #f8f9fa;
              color: var(--primary-color);
            }
          }
        }
      }
    }
    .nav-item{
      position: relative;
      // margin-right: 20px;
      .sub-menu,
      .dropdown-menu {
        position: absolute;
        top: 100%!important;
        left: 12px !important;
        background-color: #fff;
        list-style-type: none;
        padding: 15px;
        margin: 0;
        min-width: 190px;
        width: max-content;
        max-width: 300px;
        display: none;
        border: 1px solid #CCCCCC;
        border-radius: 18px;
        &:before {
          content: "";
          position: absolute;
          top: -18px;
          left: 25px;
          width: 0;
          height: 0;
          border-left: 12px solid transparent;
          border-right: 12px solid transparent;
          border-bottom: 18px solid #CCCCCC;
        }

        &:after {
          content: "";
          position: absolute;
          top: -16px;
          left: 25px;
          width: 0;
          height: 0;
          border-left: 12px solid transparent;
          border-right: 12px solid transparent;
          border-bottom: 18px solid #fff;
        }
        .sub-item{
          a{
            display: block;
            font-size: 13px;
            font-weight: 500;
            line-height: 15.6px;
            text-align: left;
            color: #3F3F3F;
            background-color: #fff !important;
            &.active,
            &:hover {
              color: var(--primary-menu-active-color);
            }
          }
          &.active{
            a{
              color: var(--primary-menu-active-color);
            }
          }
        }
      }
      .dropdown-menu{
        -webkit-transform: translate3d(0px, 0px, 0px);
        transform: translate3d(0px, 0px, 0px);
        -webkit-transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s;
        transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s;
      }
      &:hover{
        .sub-menu,
        .dropdown-menu {
          display: block;
        }
      }
    }
    .sub-menu-al-right{
      .dropdown-menu{
        left: auto !important;
        right: 0 !important;
        &:before {
          left: auto !important;
          right: 25px;
        }
        &:after {
          left: auto !important;
          right: 25px;
        }
      }
    }
  }
}

.goro-dropdown-site-menu-custom{
  position: relative;
  // margin-right: 20px;
  .dropdown-toggle{
    box-shadow: none !important;
  }
  .sub-menu,
  .dropdown-menu {
    position: absolute;
    top: calc(100% + 10px)!important;
    left: 12px !important;
    background-color: #fff;
    list-style-type: none;
    padding: 15px;
    margin: 0;
    min-width: 190px;
    width: max-content;
    max-width: 300px;
    // display: none;
    border: 1px solid #CCCCCC;
    border-radius: 18px;
    &:before {
      content: "";
      position: absolute;
      top: -18px;
      left: 25px;
      width: 0;
      height: 0;
      border-left: 12px solid transparent;
      border-right: 12px solid transparent;
      border-bottom: 18px solid #CCCCCC;
    }

    &:after {
      content: "";
      position: absolute;
      top: -16px;
      left: 25px;
      width: 0;
      height: 0;
      border-left: 12px solid transparent;
      border-right: 12px solid transparent;
      border-bottom: 18px solid #fff;
    }
    .sub-item{
      a{
        display: block;
        font-size: 13px;
        font-weight: 500;
        line-height: 15.6px;
        text-align: left;
        color: #3F3F3F;
        background-color: #fff !important;
        &.active,
        &:hover {
          color: var(--primary-menu-active-color);
        }
      }
      &.active{
        a{
          color: var(--primary-menu-active-color);
        }
      }
    }
  }
  .dropdown-menu{
    -webkit-transform: translate3d(0px, 0px, 0px) !important;
    transform: translate3d(0px, 0px, 0px) !important;
    -webkit-transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s !important;
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s !important;
  }
  // &:hover{
  //   .sub-menu,
  //   .dropdown-menu {
  //     display: block;
  //   }
  // }
  &.sub-menu-al-right{
    .dropdown-menu{
      left: auto !important;
      right: 0 !important;
      &:before {
        left: auto !important;
        right: 25px;
      }
      &:after {
        left: auto !important;
        right: 25px;
      }
    }
  }
  &.goro-dropdown-user-avatar{
    .dropdown-menu {
      @media(max-width: 991px) {
        right: 10px !important;
      }
    }
  }
}

.new-site-header-layout{
  .bm-burger-button{
    .bm-burger-bars{
      background-color: var(--primary-darker-color);
    }
  }
}

.goro-site-navigation-account{
	*{
		font-family: "AcuminVariableConcept", Helvetica, sans-serif;
	}
  &.v-sidebar-menu{
    box-shadow: 3px 0px 18.5px 0px #********;
    background-color: var(--primary-light-color);
    .vsm--toggle-btn{
      background-color: var(--primary-menu-active-color);
    }
    .vsm--link{
      box-shadow: none !important;
      &.vsm--link_hover{
        background-color: #******** !important;
      }
      &.vsm--link_active{
        background-color: var(--primary-menu-active-color) !important;
        .vsm--arrow{
          color: #fff;
        }
      }
    }
    .vsm--dropdown{
      background-color: #********;//var(--primary-menu-active-color);
    }
    &.vsm_expanded{
      .vsm--link_level-1{
        &.vsm--link_hover,
        &.vsm--link_open{
          background-color: #********;//var(--primary-menu-active-color);
          .vsm--icon{
            background-color: transparent !important;
            color: var(--primary-color) !important;
          }
        }
      }
    }
    .vsm--mobile-bg{
      // background-color: #********;//var(--primary-menu-active-color);
    }
    .vsm--link_level-1{
      .vsm--icon{
        background-color: transparent !important;
        color: var(--primary-color) !important;
      }
    }
    .vsm--badge{
      color: #fff !important;
      background-color: var(--primary-lighter-color) !important;
    }
  }
}

.table-wrapper-fixed-header {
  table {
    table-layout: fixed;
    width: 100%;
    thead {
      position: sticky;
      top: 0;
      z-index: 1;
      background-color: white;
    }
    tbody {
      display: block;
      max-height: 450px;
      overflow-y: scroll;
    }
    thead,tbody {
      tr{
        display: table;
        width: 100%;
        table-layout: fixed;
      }
    }
    thead,tbody {
      td, th{
        width: 100%;
        box-sizing: border-box;
      }
    }
  }
}

.cls-goro-custom-table{
  &.table-striped{
    tbody > tr:nth-of-type(2n){
      background-color: #FAFAFA !important;
    }
  }
  &.table-borderless{
    th,
    td,
    thead th,
    tbody + tbody{
      border: 0 !important;
    }
  }
  &.table-hover{
    tbody tr:hover {
      background-color: rgba(0, 0, 0, 0.075) !important;
    }
  }
}

.cls-goro-warning-message{
  border: 1.27px solid #FFA705;
  background-color: #FFEED9;
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  padding: 10px 20px;
  border-radius: 10px;
  .message{
    font-weight: 500;
    line-height: 13.67px;
    letter-spacing: -0.03em;
    text-align: left;
  }
}

.cls-custom-filters-applied{
  color: #3F3F3F;
  font-size: 16px;
  .cls-applied-label{
    font-size: 15px;
    color: #3F3F3F;
    &.no-filtered{
      font-size: 15px;
      color: #7C7C7C;
      margin-left: 7px;
    }
  }
  .cls-applied-item{
    font-size: 14px;
    border: 1px solid #cfcfcf;
    background-color: #dedede;
    border-radius: 5px;
    padding: 2px 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
    .btn-remove-filter{
      border: none;
      overflow: hidden;
      padding: 0;
      padding: 3px;
      margin: 0;
      line-height: 3px;
      margin-left: 8px;
      background-color: transparent;
      svg{
        font-size: 7px !important;
      }
      &:hover{
        svg {
          color: #000 !important;
        }
      }
    }
  }
}

.cls-custom-container-fluid{
  max-width: 80%;
  @media(max-width: 1366px) {
    max-width: 100%;
  }
}

.cls-glossary-text{
  cursor: pointer;
  font-weight: 700;
}

.goro-select{
  &.vs--single {
    &:not(.vs--open) .vs__selected+.vs__search {
      width: 0;
      padding: 0;
      margin: 0;
      border: none;
      height: 0;
    }

    .vs__selected-options {
      width: 0;
    }

    .vs__selected {
      display: block;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
      overflow: hidden;
      padding: 8px 5px;
    }
  }

  .vs__dropdown-menu {
    max-width: 100%;
    /* Keep the dropdown within the box's width */
    word-wrap: break-word;
    /* Break long words into the next line */
    white-space: normal;
    /* Allow wrapping of text */
  }

  .vs__dropdown-option {
    white-space: normal;
    /* Ensure options wrap onto multiple lines */
    word-break: break-word;
    /* Break words that are too long */
    padding: 8px 12px;
    /* Add spacing for better readability */
  }
}

.cls-warning-priority-text{
  border: 1px solid #F20011;
  border-radius: 10px;
  background-color: #FFEFEF;
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  *{
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  }
  &.wd-md{
    max-width: 440px;
  }
  .cls-icon{
    width: 30px;
    height: 30px;
  }
  .cls-text{
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: -3%;
    color: #F20011;
  }
}

.cls-custom-form-kyc-group-field{
  .form-group{
    border: 1px solid #CFD3D4;
    border-radius: 8px;
    .custom-label{
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
      color: #6D6D6D;
      font-size: 12px;
      font-weight: 500;
      line-height: 16.84px;
      margin-bottom: 0 !important;
      line-height: 18px !important;
      padding: 0.375rem 0.75rem !important;
      padding-bottom: 0 !important;
    }
    input,
    select{
      border-radius: 8px !important;
      border: 0 !important;
      box-shadow: none !important;
      color: #6D6D6D;
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
      font-size: 16px;
      font-weight: 500;
      line-height: calc(1.5em + 0.75rem + 2px);
    }
    .invalid-feedback,
    .invalid{
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
      font-size: 12px;
      padding: 0.375rem 0.75rem !important;
      padding-top: 0 !important;
    }
  }
  .cls-warning-field-text{
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    font-size: 12px;
    color: #FF8A00;
    line-height: 16.84px;
    margin-bottom: 1rem;
  }
}

.cls-goro-custom-tabpanel {
  .card-header {
    background-color: transparent;
    border-bottom: 0 !important;

    .nav {
      .nav-item {
        .nav-link {
          font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
          font-size: 14px;
          line-height: 19.64px;
          color: #8B8B8B;
          padding: 4px 20px !important;
          font-weight: 382;
          @media (max-width: 991px) {
            padding: 4px 10px !important;
          }

          &:hover {
            color: #464646;
          }

          &.active {
            color: #464646;
            font-weight: 600;
            background-color: #E8ECEF;
            border-radius: 10px;
          }
        }
      }
    }
  }

  .tab-content {
    .tab-footer {
      border-top: 1px solid #dee2e6;
      padding: 20px 20px 0 20px;
      margin-top: 30px !important;
      margin-bottom: 0 !important;
      button{
        min-width: 100px;
      }
    }
  }
}

.cls-kyc-form-container{
  .cls-info-block {
    .title {
      font-weight: 600;
      line-height: 20px;
      text-align: center;
      vertical-align: middle;
      color: #3E3E3E;
      margin-top: 25px;
      margin-bottom: 10px;
    }

    .description {
      color: #828282;
      font-weight: 500;
      line-height: 20px;
      text-align: center;
    }
  }

  .cls-sub-info-block {
    .sub-title {
      color: #000000;
      font-weight: 500;
    }

    .sub-description {
      color: #828282;
      font-weight: 500;
    }
  }
}

