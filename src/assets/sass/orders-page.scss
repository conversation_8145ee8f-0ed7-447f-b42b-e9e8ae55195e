.cls-single-page-container{
  .cls-page-content-wrapper{
    background-color: white;
    border-radius: 16px;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 5px;
    padding-right: 5px;
    margin-top: 15px;
    border: 1px solid #E9E9E9;
    &.has-shadow{
      box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    }
    &.no-boder{
      border: none !important;
    }
  }
  .cls-page-header-wrapper{
    .cls-heading{
      font-weight: 600;
      line-height: 33.6px;
      text-align: center;
      color: #161616;
      @media(max-width: 992px) {
        width: 100% !important;
      }
    }
  }
}

.cls-order-form-page{
  box-shadow: none !important;
  border: 0 !important;
  padding: 40px 0;
  @media(max-width: 992px) {
    margin-top: 20px !important;
    padding: 10px 0 !important;
  }
  *{
    font-family: "AcuminVariableConcept", Helvetica, sans-serif !important;
  }
  .error-text{
    white-space: break-spaces !important;
    text-align: left !important;
  }
  .content-right{
    box-shadow: 0px 0px 26.4px 0px #0000000D !important;
    border: 1px solid #E9E9E9 !important;
    border-radius: 13px !important;
    padding: 30px !important;
    @media(max-width: 992px) {
      padding: 20px !important;
    }
  }
  .page-heading{
    margin-top: 0px;
    color: #3F3F3F;
  }
  .cls-block-icon{
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    img.cls-icon{
      width: 24px;
      margin-right: 15px;
    }
    .cls-inner-block{
      flex: 1;
      max-width: calc(100% - 24px - 15px);
      width: calc(100% - 24px - 15px);
    }
  }
  .cls-token-order-wrapper{
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 16.9px 0px rgba(0, 0, 0, 0.1);
    border-radius: 13px;
    overflow: hidden;
  }
  .cls-property-order-item{
    &.image-container {
      aspect-ratio: 1.7;
      width: 100%;
      max-height: 300px;
      position: relative;
      padding: 0px;
      border-radius: 13px;
      overflow: hidden;
      @media(max-width: 400px) {
        border-radius: 10px;
      }
      .img-property {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .bottom-info {
        min-height: 71px;
        display: flex;
        align-items: center;
        padding: 10px 15px;
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        font-weight: bold;
        background: linear-gradient(90deg, #0E4A4A 0%, rgba(13, 105, 105, 0.88) 23%, rgba(114, 148, 148, 0.55) 58%, rgba(252, 252, 252, 0.01) 99%, rgba(255, 255, 255, 0) 100%);
        color: #fff;
        @media(max-width: 400px) {
          min-height: 40px;
          padding-left: 15px;
          .priority-name{
            font-size: 16px;
          }
        }
        
        .priority-name{
          color: #fff;
          font-weight: 600;
          line-height: 19.2px;
          letter-spacing: -0.03em;
          text-align: left;
        }
        .priority-address{
          color: #DCE6E7;
          font-weight: normal;
          margin-top: 5px;
        }
      }

      .address-container {
        position: absolute;
        top: 25px;
        right: 25px;
        padding: 6px 20px 6px 20px;
        background-color: #fff;
        color: var(--primary-color);
        font-size: 12px;
        border-radius: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        img {
          width: 14px;
          height: auto;
          margin-right: 6px;
          object-fit: contain;
          margin-top: 2px;
        }
        @media(max-width: 400px) {
          top: 15px;
          right: 15px;
        }
      }
    }
  }
  .order-token-quantity-row{
    padding: 10px 15px;
    .cls-token-available-block{
      .token-available-text{
        font-weight: 500;
        line-height: 14.4px;
        letter-spacing: -0.03em;
        text-align: left;
        color: var(--layout2-primary-color-text);
      }
    }
  }

  .cls-order-form-container{
    .order-label-main-1{
      font-size: 16px;
      font-weight: 600;
      line-height: 19.2px;
      letter-spacing: -0.03em;
      text-align: left;
      color: #484848;
    }
    .order-label-sub-1{
      font-size: 15px;
      font-weight: 382;
      line-height: 15.6px;
      letter-spacing: -0.03em;
      text-align: left;
      color: #484848;
      margin-top: 5px;
    }

    .order-sub-heading-level-1{
      line-height: 24px;
      color: #484848;
      margin-top: 0px;
    }

    .order-sub-heading-level-2{
      line-height: 24px;
      color: #484848;
      margin-top: 0px;
    }
    .order-payment-methods{
      .method-item{
        font-size: 16px;
        font-weight: 500;
        line-height: 19.2px;
        text-align: left;
        color: #6D6D6D;
      }
    }
    .order-summary-items{
      .order-summary-item{
        padding: 8px 0;
        .order-label,
        .order-label *{
          font-size: 16px;
          font-weight: 500;
          line-height: 19.2px;
          text-align: left;
          color: #7E7E7E;
        }
        .order-value,
        .order-value *{
          font-size: 16px;
          font-weight: 500;
          line-height: 19.2px;
          text-align: left;
          color: #484848;
        }
        .sub-text{
          font-size: 14px;
          font-weight: 382;
          line-height: 16.8px;
          text-align: left;
          color: #A2A2A2;
          margin-top: 3px;
        }
      }
    }

    .custom-switch{
      &.custom-checkbox-control{
        .custom-control-label{
          &::before{
            width: 48px !important;
            height: 23px;
            border-radius: 15px !important;
            background-color: #B5B5B5;
            box-shadow: none !important;
            left: -60px !important;
          }
          &::after{
            width: 19px !important;
            height: 19px !important;
            border-radius: 100%;
            background-color: #fff;
            box-shadow: none !important;
            left: calc(-60px + 2px) !important;
          }
        }
        .custom-control-input:checked ~ .custom-control-label::before{
          background-color: #00B7B6 !important;
          border-color: #00B7B6 !important;
        }

        .custom-control-input:checked ~ .custom-control-label::after{
          transform: translateX(24px);
        }
      }
    }

    .cls-total-payment-value{
      font-weight: 600;
      line-height: 28.8px;
      text-align: left;
      color: #484848;
    }
  }

  .btn-process-payment{
    height: 45px;
  }

  .cls-use-balance-block{
    padding-left: 30px;
    padding-right: 30px;
    @media(max-width: 768px) {
      padding-left: 10px;
      padding-right: 10px;
    }
  }

  .token-transaction {
    color: #00B7B6;
  }

  .cls-referral-bonus-message{
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.02em;
    text-align: left;
    color: var(--layout2-primary-color-light);
  }
  .cls-order-form-container{
    .cls-order-form-content{
      @media(max-width: 400px) {
        padding: 10px;
        .content-left{
          .page-heading{
            font-size: 20px !important;
          }
        }
      }
      hr {
        margin-top: 20px;
        margin-bottom: 20px;
        height: 0.5px;
        color: var(--hr-normal);
        background-color: var(--hr-normal);
        border-top: 0 !important;
      }

      // .order-summary-block{
      //   .order-label{
      //     font-family: "AcuminVariableConcept", Helvetica, sans-serif;
      //     font-size: 14px;
      //     font-weight: 600;
      //     line-height: 17px;
      //     p{
      //       font-family: "AcuminVariableConcept", Helvetica, sans-serif;
      //       font-size: 14px;
      //       font-weight: 600;
      //       line-height: 17px;
      //     }
      //     @media(max-width: 400px) {
      //       font-size: 13px;
      //       p{
      //         font-size: 13px;
      //       }
      //     }
      //   }
      //   .order-value{
      //     font-family: "AcuminVariableConcept", Helvetica, sans-serif;
      //     font-size: 15px;
      //     font-weight: 500;
      //     line-height: 17px;
      //     p{
      //       font-family: "AcuminVariableConcept", Helvetica, sans-serif;
      //       font-size: 15px;
      //       font-weight: 500;
      //       line-height: 17px;
      //     }
      //     @media(max-width: 400px) {
      //       font-size: 14px;
      //       p{
      //         font-size: 14px;
      //       }
      //     }
      //   }
      // }

      .custom-checkbox-control{
        .custom-control-label{
          padding: 5px 0;
        }
      }
      .order-payment-methods{
        padding-left: 15px;
        .method-item{
          line-height: 18px;
          padding-left: 22px;
        }
      }
    }
  }
}
