<svg width="75" height="90" viewBox="0 0 75 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_6627_9140)">
<path d="M63.9968 27.2047L31.2819 3.30121C28.7266 1.4368 25.7032 0.313833 22.5467 0.0567892C19.3902 -0.200255 16.2239 0.418663 13.3987 1.84494C10.5736 3.27121 8.20003 5.44914 6.54108 8.13729C4.88213 10.8255 4.00261 13.9188 4.00002 17.0746V64.9054C3.99509 68.0643 4.87001 71.1625 6.52738 73.8552C8.18475 76.548 10.5596 78.7296 13.3874 80.1573C16.2153 81.585 19.3854 82.2027 22.5447 81.9416C25.704 81.6806 28.7286 80.5511 31.2819 78.6788L63.9968 54.7753C66.1675 53.188 67.9327 51.1136 69.1493 48.7202C70.366 46.3268 71 43.6815 71 40.9986C71 38.3156 70.366 35.6703 69.1493 33.2769C67.9327 30.8835 66.1675 28.8091 63.9968 27.2218V27.2047Z" fill="white" fill-opacity="0.7" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_6627_9140" x="0" y="0" width="75" height="90" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6627_9140"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6627_9140" result="shape"/>
</filter>
</defs>
</svg>
