<template>
  <div class="blockchain-card">
    <p class="title font-semibold ml-1">{{ $t('BLOCKCHAIN.TITLE') }} </p>
    <div v-if="innerWidth > 560">
      <div class="address-row">
        <label class="font-semibold mr-2" style="font-size: 18px">
          <img src="~@/assets/img/blockchain/smart_contract.svg" alt=""/>
          {{ $t('BLOCKCHAIN.CONTRACT_ADDRESS') }}
        </label>
        <a class="font-semibold text-gray" :href="contractAddressUrl" target="_blank">{{ truncateContractAddress }}</a>
      </div>
      <div class="address-row">
        <label class="font-semibold mr-2" style="font-size: 18px">
          <img class="mr-1" src="~@/assets/img/blockchain/network.svg" alt=""/>
          {{ $t('BLOCKCHAIN.NETWORK') }}
        </label>
        <label class="font-semibold text-gray">
          <img src="~@/assets/img/blockchain/polygon.svg" height="30" alt=""/>{{ blockchainNetwork }}
        </label>
      </div>
      <div class="address-row">
        <label class="font-semibold mr-2" style="font-size: 18px">
          <img class="mr-1" src="~@/assets/img/blockchain/token_id.svg" alt=""/>
          {{ $t('BLOCKCHAIN.TOKEN_ID') }}
        </label>
        <label class="font-semibold text-gray">{{ nftTokenId }}</label>
      </div>
    </div>

    <div v-if="innerWidth <= 560">
      <div class="address-row">
        <img class="mr-2" src="~@/assets/img/blockchain/smart_contract.svg" height="60" alt=""/>
        <div>
          <p class="font-semibold" style="font-size: 18px">{{ $t('BLOCKCHAIN.CONTRACT_ADDRESS') }}</p>
          <a class="font-semibold text-gray" :href="contractAddressUrl" target="_blank">
            {{ truncateContractAddress }}
          </a>
        </div>
      </div>
      <div class="address-row">
        <img class="mr-2" src="~@/assets/img/blockchain/network.svg" height="60" alt=""/>
        <div>
          <p class="font-semibold" style="font-size: 18px">{{ $t('BLOCKCHAIN.NETWORK') }}</p>
          <label class="font-semibold text-gray">
            <img src="~@/assets/img/blockchain/polygon.svg" alt="" height="30"
                 style="margin-left: -5px"/>{{ blockchainNetwork }}
          </label>
        </div>
      </div>
      <div class="address-row">
        <img class="mr-2" src="~@/assets/img/blockchain/token_id.svg" height="60" alt=""/>
        <div>
          <p class="font-semibold" style="font-size: 18px">{{ $t('BLOCKCHAIN.TOKEN_ID') }}</p>
          <label class="font-semibold text-gray">{{ nftTokenId }}</label>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {BLOCKCHAIN} from "../constants/constants";
import {obfuscateBlockchainInfo} from "../helpers/common";

export default {
  props: {
    nftTokenId: {
      type: String,
      default: '',
    }
  },

  data() {
    return {
      innerWidth: 0,
      blockchainNetwork: BLOCKCHAIN.POLYGON_NETWORK,
      contractAddress: process.env.VUE_APP_GORO_CONTRACT_ADDRESS,
      contractAddressUrl: BLOCKCHAIN.POLYGON_SCAN_ADDRESS_URL + process.env.VUE_APP_GORO_CONTRACT_ADDRESS,
    }
  },

  mounted() {
    this.handleWindowResize()
    window.addEventListener("resize", this.handleWindowResize)
  },

  async beforeDestroy() {
    window.removeEventListener("resize", this.handleWindowResize)
  },

  methods: {
    handleWindowResize() {
      this.innerWidth = window.innerWidth
    },
  },
  computed: {
    truncateContractAddress() {
      if (this.contractAddress) {
        return obfuscateBlockchainInfo(this.contractAddress) || '-'
      }
      return '-'
    }
  }
}
</script>

<style lang="scss" scoped>
.blockchain-card {
  width: auto;
  padding: 25px 20px 25px 15px;
  margin-top: 30px;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.24) 0 0.5px 2px;

  .title {
    color: var(--primary-color);
    font-size: 21px;
  }

  .address-row {
    display: flex;
    margin-top: 10px;
    justify-content: space-between;
    align-items: center;

    label {
      margin-top: 0;
      margin-bottom: 0;
      color: var(--primary-color);
      white-space: nowrap;
    }

    a {
      color: var(--primary-color);
      text-decoration: underline;
      overflow-wrap: break-word;
      word-break: break-all;
    }

    .text-gray {
      color: #484848
    }
  }

  .divider {
    width: auto;
    margin-top: 18px;
    margin-bottom: 25px;
    height: 0.8px;
    background-color: #000000;
    opacity: 0.1;
  }

  @media (max-width: 560px) {
    .address-row {
      margin-top: 25px;
      justify-content: start;
      align-items: center;
    }

    p {
      margin-bottom: -5px;
    }

    .divider {
      width: auto;
      margin-top: 20px;
      margin-bottom: 20px;
      height: 0.8px;
      background-color: #000000;
      opacity: 0.1;
    }
  }
}
</style>
