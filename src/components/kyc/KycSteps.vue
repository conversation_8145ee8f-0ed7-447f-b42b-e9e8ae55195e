<template>
    <b-container class="kyc-steps-container" :class="{'ktp-verification-container': typeSelected === 'YES'}">
        <b-row align-h="center">
            <b-col cols="12" xl="8" lg="9" class="d-flex flex-column align-items-center">
                <b-row align-v="center" class="step-number-container">
                    <div class="step-number d-flex align-items-center justify-content-center"
                        :class="{ active: currentStep === 1 }">1
                        <img v-if="currentStep > 1" src="@/assets/img/check-completed.png" alt="" class="check-img">
                    </div>
                    <b-col class="dashed-line" :class="{ active: currentStep > 1 }"></b-col>

                    <div class="step-number d-flex align-items-center justify-content-center"
                        :class="{ active: currentStep === 2 }">2
                        <img v-if="currentStep > 2" src="@/assets/img/check-completed.png" alt="" class="check-img">
                    </div>
                    <b-col class="dashed-line" :class="{ active: currentStep > 2 }"></b-col>

                    <div class="step-number d-flex align-items-center justify-content-center"
                        :class="{ active: currentStep === 3 }">3
                        <img v-if="currentStep > 3" src="@/assets/img/check-completed.png" alt="" class="check-img">
                    </div>
                </b-row>
                <b-row class="mt-2 w-100">
                    <p class="text-center text-step-info" :class="{ active: currentStep === 1 }">{{
                            $t('account.UPLOAD_IDENTITY_CARD') }}</p>
                    <p class="text-center text-step-info" :class="{ active: currentStep === 2 }">{{
                            $t('account.IDENTITY_CONFIRMATION') }}</p>
                    <p class="text-center text-step-info" :class="{ active: currentStep === 3 }">{{
                            $t('account.SELFIE') }}</p>
                </b-row>
            </b-col>
        </b-row>
    </b-container>
</template>

<script>
export default {
    props: {
        currentStep: {
            type: Number,
            default: 1,
        },

        successCftPep: {
            type: Boolean,
            default: false,
        },

        failedCftPep: {
            type: Boolean,
            default: false,
        },
        typeSelected: {
            type: String,
            default: 'YES',
        },
    },
}
</script>

<style lang="scss">
.kyc-steps-container {

    .step-number-container {
        width: 70%;
        @media screen and (max-width: 460px) {
            width: 86%;
        }
    }

    &.ktp-verification-container{
        .step-number-container {
            
        }
    }

    .step-number {
        width: 26px;
        height: 26px;
        border-radius: 50%;
        font-size: 16px;
        color: rgb(121, 121, 121);
        border: 1px solid rgb(121, 121, 121);

        @media screen and (max-width: 460px) {
            width: 20px;
            height: 20px;
            font-size: 12px;
        }

        @media screen and (max-width: 380px) {
            width: 16px;
            height: 16px;
            font-size: 11px;
        }

        &.active {
            background-color: var(--primary-color);
            color: white;
            border: 1px solid var(--primary-color);
        }

        &.scanning {
            background-color: #E39635;
            color: white;
            border: 1px solid #E39635;
        }

        &.failed {
            background-color: red;
            color: white;
            border: 1px solid red;
        }
    }

    .dashed-line {
        width: 100%;
        height: 1px;
        background: repeating-linear-gradient(to right,
                transparent,
                transparent 8px,
                rgb(121, 121, 121) 10px,
                rgb(121, 121, 121) 18px,
            );

        &.active {
            height: 2px;
            background: none;
            background-color: var(--primary-color);
        }
    }

    .text-step-info {
        color: rgb(121, 121, 121);
        font-size: 15px;
        white-space: pre;
        flex: 1;

        &.active {
            color: black;
        }
    }

    @media screen and (max-width: 460px) {
        .text-step-info {
            font-size: 10px;
        }
    }

    .text-spacing {
        flex: 1;
        margin-left: -20px;
    }

    .second-spacing {
        flex: 1;
    }

    .third-spacing {
        flex: 1;
    }


    @media screen and (max-width: 460px) {
        .text-spacing {
            flex: 0;
            margin-left: 0px;
        }

        .second-spacing {
            flex: 0;
            margin-left: 20px;
        }
    }

    @media screen and (max-width: 380px) {
        .second-spacing {
            flex: 0;
            margin-left: 5px;
        }
    }

    .check-img {
        position: absolute;
        width: 30px;
        height: 30px;
        object-fit: fill;

        @media screen and (max-width: 460px) {
            width: 24px;
            height: 24px;
        }

        @media screen and (max-width: 380px) {
            width: 20px;
            height: 20px;
        }
    }
}
</style>