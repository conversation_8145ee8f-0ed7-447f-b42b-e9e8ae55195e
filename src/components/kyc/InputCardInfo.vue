<template>
    <b-container class="input-card-container cls-kyc-form-container">
        <div class="d-flex align-items-center justify-content-center">
            <div class="cls-warning-priority-text d-flex align-items-center justify-content-start p-3 mb-4 wd-md">
                <img width="30" src="@/assets/img/icons/warning-priority-circle.svg" alt="" class="cls-icon mr-3">
                <p class="cls-text font-12">
                    {{$t('account.WARNING_REVIEW_KYC_INFO')}}
                </p>
            </div>
        </div>
        <Form ref="observer" v-slot="{ handleSubmit }">
            <form ref="form" @submit.prevent="handleSubmit(onSubmit)" class="cls-custom-form-kyc-group-field">
                <b-row align-h="center">
                    <b-col cols="12" lg="11">
                        <div class="d-flex flex-column flex-lg-row">
                            <b-col cols="12" lg="6" class="text-center w-100 mt-2 mt-lg-5 cls-info-block">
                                <img src="@/assets/img/new-info-person.png" alt="" class="icon-img">
                                <p class="font-28 font-weight-bold title">{{ $t('account.IDENTITY_CONFIRMATION_TITLE') }}
                                </p>
                                <p class="font-18 complete-info content-text description">{{ isKTP ?
            $t('account.KTP_MAKE_SURE_INFO_CORRECT') :
            $t('account.PASSPORT_MAKE_SURE_INFO_CORRECT')
                                    }}
                                </p>
                            </b-col>
                            <b-col v-if="isKTP" cols="12" lg="6">
                                <Field :key="$i18n.locale" :model-value="ktpForm.id_number" @update:modelValue="ktpForm.id_number = $event"
                                    :name="$t('account.KYC_FIELDS.IDNUMBER')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.IDNUMBER')" label-for="account_IDNUMBER" label-class="custom-label">
                                        <b-form-input id="account_IDNUMBER" v-bind="validationContext.field"
                                            aria-describedby="input-id-number-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-id-number-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.name" @update:modelValue="ktpForm.name = $event"
                                    :name="$t('account.KYC_FIELDS.NAME')" :rules="{ required: true }" v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.NAME')" label-for="account_NAME" label-class="custom-label" class="mb-0">
                                        <b-form-input id="account_NAME" v-bind="validationContext.field"
                                            aria-describedby="input-name-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-name-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                    <p class="cls-warning-field-text mt-1">
                                        {{$t('account.WARNING_NAME_MATCH_CARD')}}
                                    </p>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.dob" @update:modelValue="ktpForm.dob = $event"
                                    :name="$t('account.KYC_FIELDS.DATEOFBIRTH')" :rules="{ required: true } "
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.DATEOFBIRTH')" label-for="account_DATEOFBIRTH" label-class="custom-label" class="mb-0">
                                        <VueDatePicker id="account_DATEOFBIRTH" v-model="ktpForm.dob" format="dd-MM-yyyy"
                                            :state="getValidationState(validationContext.meta)" />
                                        <p class="invalid mt-1">
                                            {{ getValidationState(validationContext.meta) == false ?
            $t("VALIDATION.THE_DOB_IS_REQUIRED") : '' }}
                                        </p>
                                    </b-form-group>
                                    <p class="cls-warning-field-text mt-1">
                                        {{$t('account.WARNING_FORMAT_OF_DOB')}}
                                    </p>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.birth_place"
                                    @update:modelValue="ktpForm.birth_place = $event" 
                                    :name="$t('account.KYC_FIELDS.BIRTHPLACEBIRTHDAY')"
                                    :rules="{ required: true }" v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.BIRTHPLACEBIRTHDAY')" label-for="account_BIRTHPLACEBIRTHDAY" label-class="custom-label">
                                        <b-form-input id="account_BIRTHPLACEBIRTHDAY" v-bind="validationContext.field"
                                            aria-describedby="input-birth-place-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-birth-place-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.gender"
                                    @update:modelValue="ktpForm.gender = $event" 
                                    :name="$t('account.KYC_FIELDS.GENDER')"
                                    :rules="{ required: true }" v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.GENDER')" label-for="account_GENDER" label-class="custom-label">
                                        <b-form-select id="account_GENDER" :value="ktpForm.gender" v-bind="validationContext.field"
                                            aria-describedby="input-gender-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                            <option :value="null" disabled>{{ $t('account.SELECT_GENDER') }}
                                            </option>
                                            <option v-for="gender in idGenderOptions" :value="gender.value">{{
    gender.text }}</option>
                                        </b-form-select>
                                        <b-form-invalid-feedback id="input-gender-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.religion" @update:modelValue="ktpForm.religion = $event"
                                    :name="$t('account.KYC_FIELDS.RELIGION')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.RELIGION')" label-for="account_RELIGION" label-class="custom-label">
                                        <b-form-input id="account_RELIGION" v-bind="validationContext.field"
                                            aria-describedby="input-religion-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-religion-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.blood_type"
                                    @update:modelValue="ktpForm.blood_type = $event"
                                    :name="$t('account.KYC_FIELDS.BLOODTYPE')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.BLOODTYPE')" label-for="account_BLOODTYPE" label-class="custom-label">
                                        <b-form-select id="account_BLOODTYPE" v-bind="validationContext.field"
                                            aria-describedby="input-blood-type-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                            <option :value="null" disabled>{{ $t('account.SELECT_BLOOD_TYPE') }}
                                            </option>
                                            <option v-for="bloodType in bloodTypeOptions"
                                                :value="bloodType.value">{{ bloodType.text }}</option>
                                        </b-form-select>
                                        <b-form-invalid-feedback id="input-blood-type-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.marital_status"
                                    @update:modelValue="ktpForm.marital_status = $event"
                                    :name="$t('account.KYC_FIELDS.MARITALSTATUS')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.MARITALSTATUS')"  label-for="account_MARITALSTATUS" label-class="custom-label">
                                        <b-form-input id="account_MARITALSTATUS" v-bind="validationContext.field"
                                            aria-describedby="input-marital-status-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-marital-status-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.address" @update:modelValue="ktpForm.address = $event"
                                    :name="$t('account.KYC_FIELDS.ADDRESS')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.ADDRESS')" label-for="account_ADDRESS" label-class="custom-label">
                                        <b-form-input id="account_ADDRESS" v-bind="validationContext.field"
                                            aria-describedby="input-address-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-address-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.village" @update:modelValue="ktpForm.village = $event"
                                    :name="$t('account.KYC_FIELDS.VILLAGE')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.VILLAGE')" label-for="account_ADDRESS" label-class="custom-label">
                                        <b-form-input v-bind="validationContext.field"
                                            aria-describedby="input-village-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-village-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.city" @update:modelValue="ktpForm.city = $event"
                                    :name="$t('account.KYC_FIELDS.CITY')" :rules="{ required: true }" v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.CITY')" label-for="account_CITY" label-class="custom-label">
                                        <b-form-input id="account_CITY" v-bind="validationContext.field"
                                            aria-describedby="input-city-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-city-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.district" @update:modelValue="ktpForm.district = $event"
                                    :name="$t('account.KYC_FIELDS.DISTRICT')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.DISTRICT')" label-for="account_DISTRICT" label-class="custom-label">
                                        <b-form-input id="account_DISTRICT" v-bind="validationContext.field"
                                            aria-describedby="input-district-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-district-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="ktpForm.province" @update:modelValue="ktpForm.province = $event"
                                    :name="$t('account.KYC_FIELDS.PROVINCE')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.KYC_FIELDS.PROVINCE')" label-for="account_PROVINCE" label-class="custom-label">
                                        <b-form-input id="account_PROVINCE" v-bind="validationContext.field"
                                            aria-describedby="input-province-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-province-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :name="$t('account.KYC_FIELDS.ZIPPOSTAL')" rules="required" v-slot="validationContext"
                                        :model-value="ktpForm.zipCode" @update:modelValue="ktpForm.zipCode = $event">
                                    <b-form-group class="pr-xl-2" :label="$t('account.KYC_FIELDS.ZIPPOSTAL')" label-for="account_ZIPPOSTAL" label-class="custom-label">
                                        <b-form-input id="account_ZIPPOSTAL" v-bind="validationContext.field" placeholder=""
                                            aria-describedby="input-zip-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>

                                        <b-form-invalid-feedback id="input-zip-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>
                                
                            </b-col>
                            <b-col v-else cols="12" lg="6">
                                <Field :key="$i18n.locale" :model-value="passportForm.passport_number"
                                    @update:modelValue="passportForm.passport_number = $event"
                                    :name="$t('account.PASSPORT_NUMBER')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.PASSPORT_NUMBER')" label-for="account_PASSPORT_NUMBER" label-class="custom-label">
                                        <b-form-input id="account_PASSPORT_NUMBER" v-bind="validationContext.field"
                                            aria-describedby="input-passport-number-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-passport-number-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.surname"
                                    @update:modelValue="passportForm.surname = $event" :name="$t('account.SURNAME')"
                                    :rules="{ required: true }" v-slot="validationContext">
                                    <b-form-group :label="$t('account.SURNAME')" label-for="account_SURNAME" label-class="custom-label">
                                        <b-form-input id="account_SURNAME" v-bind="validationContext.field"
                                            aria-describedby="input-surname-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-surname-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.given_name"
                                    @update:modelValue="passportForm.given_name = $event"
                                    :name="$t('account.GIVEN_NAME')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.GIVEN_NAME')" label-for="account_GIVEN_NAME" label-class="custom-label">
                                        <b-form-input id="account_GIVEN_NAME" v-bind="validationContext.field"
                                            aria-describedby="input-given-name-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-given-name-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.full_name"
                                    @update:modelValue="passportForm.full_name = $event" :name="$t('AUTH.FULL_NAME')"
                                    :rules="{ required: true }" v-slot="validationContext">
                                    <b-form-group :label="$t('AUTH.FULL_NAME')" label-for="account_FULL_NAME" label-class="custom-label">
                                        <b-form-input id="account_FULL_NAME" v-bind="validationContext.field"
                                            aria-describedby="input-full-name-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-full-name-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.gender"
                                    @update:modelValue="passportForm.gender = $event" :name="$t('account.GENDER')"
                                    :rules="{ required: true }" v-slot="validationContext">
                                    <b-form-group :label="$t('account.GENDER')" label-for="account_GENDER" label-class="custom-label">
                                        <b-form-select id="account_GENDER" :value="passportForm.gender" v-bind="validationContext.field"
                                            aria-describedby="input-gender-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                            <option :value="null" disabled>{{ $t('account.SELECT_GENDER') }}</option>
                                            <option v-for="gender in genderOptions" :value="gender.value">{{
            gender.text }}</option>
                                        </b-form-select>
                                        <b-form-invalid-feedback id="input-gender-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.date_of_birth"
                                    @update:modelValue="passportForm.date_of_birth = $event"
                                    :name="$t('account.DATE_OF_BIRTH')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.DATE_OF_BIRTH')" label-for="account_DATE_OF_BIRTH" label-class="custom-label">
                                        <VueDatePicker id="account_DATE_OF_BIRTH" v-model="passportForm.date_of_birth" format="dd-MM-yyyy"
                                            :state="getValidationState(validationContext.meta)" />
                                        <p class="invalid mt-1">
                                            {{ getValidationState(validationContext.meta) == false ?
            $t('VALIDATION.THE_DOB_IS_REQUIRED') : '' }}
                                        </p>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.place_of_birth"
                                    @update:modelValue="passportForm.place_of_birth = $event"
                                    :name="$t('account.PLACE_OF_BIRTH')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.PLACE_OF_BIRTH')" label-for="account_PLACE_OF_BIRTH" label-class="custom-label">
                                        <b-form-input id="account_PLACE_OF_BIRTH" v-bind="validationContext.field"
                                            aria-describedby="input-place-of-birth-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-place-of-birth-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.country"
                                    @update:modelValue="passportForm.country = $event" :name="$t('account.COUNTRY')"
                                    :rules="{ required: true }" v-slot="validationContext">
                                    <b-form-group :label="$t('account.COUNTRY')" label-for="account_COUNTRY" label-class="custom-label">
                                        <b-form-input id="account_COUNTRY" v-bind="validationContext.field"
                                            aria-describedby="input-country-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-countryfeedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.date_of_issue"
                                    @update:modelValue="passportForm.date_of_issue = $event"
                                    :name="$t('account.DATE_OF_ISSUE')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.DATE_OF_ISSUE')" label-for="account_DATE_OF_ISSUE" label-class="custom-label">
                                        <VueDatePicker id="account_DATE_OF_ISSUE" v-model="passportForm.date_of_issue" format="dd-MM-yyyy"
                                            :state="getValidationState(validationContext.meta)" />
                                        <p class="invalid mt-1">{{ getValidationState(validationContext.meta) == false ?
            $t('VALIDATION.THE_DATE_OF_ISSUE_IS_REQUIRED') : '' }}</p>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.date_of_expiry"
                                    @update:modelValue="passportForm.date_of_expiry = $event"
                                    :name="$t('account.DATE_OF_EXPIRY')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.DATE_OF_EXPIRY')" label-for="account_DATE_OF_EXPIRY" label-class="custom-label">
                                        <VueDatePicker id="account_DATE_OF_EXPIRY" v-model="passportForm.date_of_expiry" format="dd-MM-yyyy"
                                            :state="getValidationState(validationContext.meta)" />
                                        <p class="invalid mt-1">{{ getValidationState(validationContext.meta) == false ?
            $t('VALIDATION.THE_DATE_OF_EXPIRY_IS_REQUIRED') : '' }}</p>
                                    </b-form-group>
                                </Field>

                                <Field :key="$i18n.locale" :model-value="passportForm.issuing_authority"
                                    @update:modelValue="passportForm.issuing_authority = $event"
                                    :name="$t('account.ISSUING_AUTHORITY')" :rules="{ required: true }"
                                    v-slot="validationContext">
                                    <b-form-group :label="$t('account.ISSUING_AUTHORITY')" label-for="account_ISSUING_AUTHORITY" label-class="custom-label">
                                        <b-form-input id="account_ISSUING_AUTHORITY" v-bind="validationContext.field"
                                            aria-describedby="input-issuing-authority-feedback"
                                            :state="getValidationState(validationContext.meta)">
                                        </b-form-input>
                                        <b-form-invalid-feedback id="input-issuing-authority-feedback">
                                            {{ validationContext.errors[0] }}
                                        </b-form-invalid-feedback>
                                    </b-form-group>
                                </Field>

                            </b-col>
                        </div>
                    </b-col>
                </b-row>
                <div class="d-flex flex-row justify-content-end mt-4">
                    <b-button id="btn_inputCardInfo_Next" class="btn-main px-4" type="submit" variant="none">{{
                        $t('common.NEXT') }}
                    </b-button>
                </div>
            </form>
        </Form>
    </b-container>
</template>

<script>
import { configure, defineRule, Field, Form } from "vee-validate"
import moment from "moment"
import { localize, setLocale } from "@vee-validate/i18n"
import { required } from "@vee-validate/rules"
import accountService from "../../services/account.service"

defineRule("required", required)
configure({
  generateMessage: localize({
    en: {
      messages: {
        required: "The {field} is required",
      },
    },
    id: {
      messages: {
        required: "Kolom {field} wajib diisi",
      },
    },
  }),
})

export default {
    components: {
        Field,
        Form,
        defineRule,
    },
    emits: ['next-step'],
    data() {
        return {
            user: this.$store.state.userProfile,
            ktpForm: {},
            passportForm: {},
            bloodTypeOptions: [
                {
                    value: 'O',
                    text: 'O',
                },
                {
                    value: 'A',
                    text: 'A'
                },
                {
                    value: 'B',
                    text: 'B'
                },
                {
                    value: 'AB',
                    text: 'AB'
                },
            ],
        }
    },

    mounted() {
        if (this.idCardData) {
            if (this.isKTP) {
                const birthPlaceBirthday = this.idCardData.birthPlaceBirthday.split(',').map(part => part.trim());
                this.ktpForm = {
                    id_number: this.idCardData.idNumber,
                    name: this.idCardData.name,
                    gender: this.idCardData.gender,
                    blood_type: this.idCardData.bloodType,
                    city: this.idCardData.city,
                    district: this.idCardData.district,
                    village: this.idCardData.village,
                    province: this.idCardData.province,
                    address: this.idCardData.address,
                    religion: this.idCardData.religion,
                    expiry_date: this.idCardData.expiryDate,
                    occupation: this.idCardData.occupation,
                    nationality: this.idCardData.nationality,
                    marital_status: this.idCardData.maritalStatus,
                    dob: birthPlaceBirthday.length > 1 ? moment(birthPlaceBirthday[1], 'DD-MM-YYYY').toDate() : '',
                    birth_place: birthPlaceBirthday.length > 0 ? birthPlaceBirthday[0] : '',
                    rtrw: this.idCardData.rtrw,
                    zipCode: this.idCardData.zipCode,
                }
            } else {
                this.passportForm = {
                    passport_number: this.idCardData.passport_number,
                    surname: this.idCardData.surname,
                    given_name: this.idCardData.given_name,
                    full_name: this.idCardData.full_name,
                    gender: this.idCardData.gender,
                    date_of_birth: this.idCardData.date_of_birth,
                    place_of_birth: this.idCardData.place_of_birth,
                    date_of_issue: this.idCardData.date_of_issue,
                    date_of_expiry: this.idCardData.date_of_expiry,
                    issuing_authority: this.idCardData.issuing_authority,
                    country: this.idCardData.country,
                }
            }
        }
    },

    methods: {
        getValidationState({ dirty, validated, valid = null }) {
            return dirty || validated ? valid : null
        },
        async onSubmit() {
            let cardData;
            if (this.isKTP) {
                const birthday = moment(this.ktpForm.dob).format('DD-MM-YYYY')
                cardData = {
                    city: this.ktpForm.city,
                    name: this.ktpForm.name,
                    rtrw: this.ktpForm.rtrw,
                    gender: this.ktpForm.gender,
                    address: this.ktpForm.address,
                    village: this.ktpForm.village,
                    district: this.ktpForm.district,
                    idNumber: this.ktpForm.id_number,
                    province: this.ktpForm.province,
                    religion: this.ktpForm.religion,
                    bloodType: this.ktpForm.blood_type,
                    expiryDate: this.ktpForm.expiry_date,
                    occupation: this.ktpForm.occupation,
                    nationality: this.ktpForm.nationality,
                    maritalStatus: this.ktpForm.marital_status,
                    birthPlaceBirthday: `${this.ktpForm.birth_place}, ${birthday}`,
                    zipCode: this.ktpForm.zipCode,
                }
            } else {
                this.passportForm.date_of_birth = moment(this.passportForm.date_of_birth).toISOString()
                this.passportForm.date_of_expiry = moment(this.passportForm.date_of_expiry).toISOString()
                this.passportForm.date_of_issue = moment(this.passportForm.date_of_issue).toISOString()
                cardData = {
                    ...this.passportForm,
                    is_tampered: this.idCardData.is_tampered,
                }
            }
            await accountService.updateCard({
                card_data: cardData
            });
            this.$emit('next-step');
        },
    },

    computed: {
        isKTP() {
            if (this.user && this.user.id_card) {
                return this.user.id_card.card_type === 'indonesia_ktp'
            }
            return false;
        },

        idCardData() {
            return this.$store.state.userProfile && this.$store.state.userProfile.id_card && this.$store.state.userProfile.id_card.card_data
        },

        idGenderOptions() {
            return [
                {
                    value: 'LAKI-LAKI',
                    text: this.$t('common.MALE'),
                },
                {
                    value: 'PEREMPUAN',
                    text: this.$t('common.FEMALE')
                }
            ]
        },

        genderOptions() {
            return [
                {
                    value: 'Male',
                    text: this.$t('common.MALE'),
                },
                {
                    value: 'Female',
                    text: this.$t('common.FEMALE')
                },
            ]
        },
    },
    watch: {
        "$i18n.locale": {
            handler(newLocale) {
                setLocale(newLocale);
                this.$nextTick(() => {
                    this.$refs.observer.validate();
                });
            },
            immediate: true,
        },
    }
}
</script>

<style lang="scss" scoped>
.input-card-container {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    *{
        font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    }
    .icon-img {
        width: 180px;
        height: 100px;
        object-fit: contain;
    }

    .invalid {
        font-size: 14px;
        color: #dc3545;
        font-weight: 500;
    }
}
</style>