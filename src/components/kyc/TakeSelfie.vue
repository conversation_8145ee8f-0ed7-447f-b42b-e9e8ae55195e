<template>
    <b-container class="selfie-container cls-kyc-form-container">
        <b-row align-h="center">
            <b-col cols="12" lg="11">
                <div class="d-flex flex-column flex-lg-row">
                    <b-col cols="12" lg="6" class="text-center w-100 mt-2 mt-lg-5 cls-info-block">
                        <img src="@/assets/img/new-camera.png" alt="" class="icon-img">
                        <p class="font-28 font-weight-bold title">{{ step === 1 ? $t('account.SELFIE_GUIDE') :
                            $t('account.SELFIE') }}</p>
                        <p class="font-16 complete-info content-text description">{{ step === 1 ? $t('account.SELFIE_GUIDE_NOTE') :
                            $t('account.SELFIE_NOTE') }}</p>
                    </b-col>
                    <b-col v-if="step === 1" cols="12" lg="6" class="mt-3 w-100 d-flex flex-column">
                        <b-row align-v="center">
                            <b-col cols="4">
                                <img src="@/assets/img/kyc/correct-selfie.png" alt="" class="w-100">
                            </b-col>
                            <b-col cols="7">
                                <p class="text-center color-gray font-14">{{ $t('account.SELFIE_CORRECT_NOTE') }}</p>
                            </b-col>
                        </b-row>
                        <div class="divider my-4"></div>
                        <b-row>
                            <b-col cols="4" class="d-flex flex-column align-items-center">
                                <img src="@/assets/img/kyc/wrong-selfie-1.png" alt="" class="w-75">
                                <p class="text-center color-gray font-14 mt-2">{{ $t('account.SELFIE_WRONG_NOTE_1') }}</p>
                            </b-col>
                            <b-col cols="4" class="d-flex flex-column align-items-center">
                                <img src="@/assets/img/kyc/wrong-selfie-2.png" alt="" class="w-75">
                                <p class="text-center color-gray font-14 mt-2">{{ $t('account.SELFIE_WRONG_NOTE_2') }}</p>
                            </b-col>
                            <b-col cols="4" class="d-flex flex-column align-items-center">
                                <img src="@/assets/img/kyc/wrong-selfie-3.png" alt="" class="w-75">
                                <p class="text-center color-gray font-14 mt-2">{{ $t('account.SELFIE_WRONG_NOTE_3') }}</p>
                            </b-col>
                        </b-row>
                    </b-col>
                    <b-col v-else cols="12" lg="6" class="mt-3 w-100 d-flex flex-row justify-content-center">
                        <div class="d-flex flex-column align-items-center w-100">
                            <div v-if="isCameraOpen" v-show="!isLoading" class="camera-box"
                                :class="{ 'flash': isShotPhoto }">
                                <div class="camera-shutter" :class="{ 'flash': isShotPhoto }"></div>
                                <video v-show="!isPhotoTaken" ref="camera" autoplay></video>
                                <canvas v-show="isPhotoTaken" :width="cameraOptions.width"
                                    :height="cameraOptions.height" id="photoTaken" ref="canvas"></canvas>
                                <div v-if="isCameraOpen && !isLoading && !isUploading && !isPhotoTaken"
                                    class="camera-shoot d-flex flex-row justify-content-center">
                                    <img src="@/assets/img/take-photo.png" @click="takePhoto">
                                </div>
                                <div v-if="isUploading"
                                    class="overlay d-flex justify-content-center align-items-center">
                                    <b-spinner variant="success" class="spinner" type="grow"
                                        label="Spinning"></b-spinner>
                                </div>
                                <img v-if="isPhotoTaken && !isUploading && !success" @click="retake"
                                    src="@/assets/img/close.png" class="img-close" alt="">
                            </div>
                            <p v-if="message" class="note" :class="{ error: hasError }">{{ message }}</p>
                        </div>
                    </b-col>
                </div>
            </b-col>
        </b-row>
        <div class="d-flex flex-row justify-content-end mt-3 btn-next">
            <b-button id="btn_takeSelfie_PreviousStep" v-if="step > 1" class="btn-outline-main px-4 mr-3" :disabled="isUploading" @click="previousStep" variant="none">{{ $t('common.PREVIOUS') }}</b-button>
            <b-button id="btn_takeSelfie_Next" class="btn-main px-4" :disabled="!success && step >= 2" @click="nextStep" variant="none">{{
                            step < 2 ? $t('common.NEXT') : $t('account.FINISH') }} </b-button>
        </div>
    </b-container>
</template>
<script>

import { resizeBase64Img } from "../../helpers/common";
import accountService from "../../services/account.service";
import { Buffer } from 'buffer';

export default {
    emits: ['next-step'],

    data() {
        return {
            isCameraOpen: false,
            isPhotoTaken: false,
            isShotPhoto: false,
            isLoading: false,
            isUploading: false,
            message: null,
            hasError: false,
            cameraOptions: {
                width: 800,
                height: 600,
            },
            success: false,
            step: 1,
        };
    },

    mounted() {

    },

    beforeDestroy() {
        if (this.step >= 2) {
            this.stopCameraStream();
        }
    },

    methods: {

        openCamera() {
            this.isCameraOpen = true;
            this.createCameraElement();
        },

        createCameraElement() {
            this.isLoading = true;

            const constraints = (window.constraints = {
                audio: false,
                video: true
            });

            navigator.mediaDevices
                .getUserMedia(constraints)
                .then(stream => {
                    this.isLoading = false;
                    this.$refs.camera.srcObject = stream;
                })
                .catch(error => {
                    this.isLoading = false
                    this.hasError = true
                    this.message = this.$t('account.TAKE_SELFIE_NOT_SUPPORT')
                });
        },

        stopCameraStream() {
            let tracks = this.$refs.camera.srcObject.getTracks();

            tracks.forEach(track => {
                track.stop();
            });
        },

        takePhoto() {
            if (!this.isPhotoTaken) {
                this.isShotPhoto = true;

                const FLASH_TIMEOUT = 50;

                setTimeout(() => {
                    this.isShotPhoto = false;
                }, FLASH_TIMEOUT);

                this.isPhotoTaken = true;

                const context = this.$refs.canvas.getContext('2d');
                context.setTransform(-1, 0, 0, 1, this.$refs.canvas.width, 0);
                context.drawImage(this.$refs.camera, 0, 0, this.cameraOptions.width, this.cameraOptions.height);
                this.stopCameraStream();
                this.uploadSelfie();
            }
        },

        retake() {
            this.isPhotoTaken = false
            this.message = null
            this.hasError = false
            this.success = false
            this.openCamera()
        },

        async uploadSelfie() {
            let base64 = document.getElementById("photoTaken").toDataURL("image/jpeg", 1);
            let buffer = Buffer.from(base64.substring(base64.indexOf(',') + 1));
            let sizeInMB = buffer.length / (1024 * 1024);
            if (sizeInMB >= 2) {
                base64 = await resizeBase64Img(base64);
                buffer = Buffer.from(base64.substring(base64.indexOf(',') + 1));
                sizeInMB = buffer.length / (1024 * 1024);
                if (sizeInMB >= 2) {
                    this.message = this.$t('account.IMAGE_SIZE_TOO_LARGE')
                    this.hasError = true
                    return;
                }
            }
            try {
                this.isUploading = true;
                const response = await accountService.uploadSelfie({
                    image: base64,
                });
                this.isUploading = false;
                if (response.success) {
                    this.hasError = false
                    this.message = this.$t('account.SUCCESS_SELFIE_CHECKING')
                    this.success = true
                    setTimeout(() => {
                        this.$emit('next-step')
                    }, 2000)
                } else {
                    this.hasError = true
                    this.message = this.$t('account.SELFIE_IS_NOT_VALID')
                }
            } catch (e) {
                this.hasError = true
                this.isUploading = false;
                this.message = e.data ? Array.isArray(e.data) ? e.data.join(' | ') : e.data : e.error ? e.error : e.toString()
            }
        },

        previousStep() {
            this.step = 1
            this.stopCameraStream()
        },

        nextStep() {
            if (this.step < 2) {
                this.step++
                this.openCamera();
            } else {
                this.$emit('next-step')
            }
        }
    },
}
</script>

<style lang="scss">
.selfie-container {
    color: black;

    .camera-box {
        position: relative;
        width: 100%;
        aspect-ratio: 4 / 3;
        border-radius: 10px;
        overflow: hidden;
        background-color: #e6e6e6b6;

        video {
            transform: rotateY(180deg);
            -webkit-transform: rotateY(180deg);
            -moz-transform: rotateY(180deg);
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        canvas {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .camera-shutter {
            opacity: 0;
            background-color: #fff;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;

            &.flash {
                opacity: 1;
            }
        }

        .img-close {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 35px;
            height: 35px;
            cursor: pointer;
        }

        .overlay {
            position: absolute;
            background-color: rgba(120, 111, 111, 0.432);
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;

            .spinner {
                width: 3rem;
                height: 3rem;
            }
        }
    }

    .note {
        font-size: 15px;
        color: black;
        margin-top: 10px;
        text-align: center;

        &.error {
            color: red;
        }
    }

    .camera-shoot {
        position: absolute;
        bottom: 5px;
        left: 0;
        right: 0;

        img {
            width: 50px;
            height: 50px;
            object-fit: fill;
            cursor: pointer;
        }
    }

    .content-text {
        color: #666;
    }

    .complete-info {
        white-space: pre;
    }

    .icon-img {
        width: 180px;
        height: 100px;
        object-fit: contain;
    }

    @media screen and (max-width: 450px) {
        .btn-next {
            margin-right: -25px;
        }
    }

    .img-correct {
        width: 120px;
        height: 120px;
        object-fit: contain;
    }

    .divider {
        width: 100%;
        border-bottom: solid 1px rgb(172, 172, 172);
    }
}
</style>