<template>
    <div v-if="annualizedReturns.length" style="min-height: 350px; max-height: 500px;">
        <LineChartGenerator :options="chartOptions" :data="chartData" :chart-id="chartId" :dataset-id-key="datasetIdKey"
            :plugins="plugins" :css-classes="cssClasses" :styles="styles" :width="width" :height="height" />
    </div>
    <!-- <div style="position: relative">
        <img :src="chartUrl" style="width:100%" />
        <div style="width:100px;height:40px;background-color:white;position:absolute;top:0;right:0;"></div>
    </div> -->
</template>

<script>
import { Line as LineChartGenerator } from 'vue-chartjs'
import moment from 'moment'

import {
    Chart as ChartJS,
    Title,
    Tooltip,
    Legend,
    LineElement,
    LinearScale,
    CategoryScale,
    PointElement,
    Filler
} from 'chart.js'

ChartJS.register(
    Title,
    Tooltip,
    Legend,
    LineElement,
    LinearScale,
    CategoryScale,
    PointElement,
    Filler
)

export default {
    name: 'AnnualizedReturnsChart',
    components: {
        LineChartGenerator
    },
    props: {
        chartId: {
            type: String,
            default: 'line-chart'
        },
        datasetIdKey: {
            type: String,
            default: 'label'
        },
        width: {
            type: Number,
            default: 400
        },
        height: {
            type: Number,
            default: 350
        },
        cssClasses: {
            default: '',
            type: String
        },
        styles: {
            type: Object,
            default: () => { }
        },
        plugins: {
            type: Array,
            default: () => []
        },
        annualizedReturns: [],
    },
    data() {
        return {
            chartData: this.getChartData(),
            chartOptions: this.getChartOptions(),
            chartUrl: this.getChartUrl(),
        }
    },

    watch: {
        '$i18n.locale'(newVal, oldVal) {
            this.chartData = this.getChartData()
            this.chartOptions = this.getChartOptions()
            this.chartUrl = this.getChartUrl()
        },
        annualizedReturns: {
            handler() {
                this.chartData = this.getChartData()
                this.chartOptions = this.getChartOptions()
                this.chartUrl = this.getChartUrl()
            },
            deep: true
        }
    },

    methods: {
        getChartData() {
            return {
                labels: this.annualizedReturns.map(e => e.formatted_date),
                datasets: [
                    {
                        label: this.$t('MARKETPLACE.ANNUALIZED_RETURNS'),
                        backgroundColor: '#006666',
                        data: this.annualizedReturns.map(e => e.annualized_percent),
                        borderColor: '#006666',
                        borderWidth: 1,
                        fill: {
                            target: 'origin',
                            above: 'rgba(0, 102, 102, 0.2)',
                        },
                    },
                    {
                        label: this.$t('MARKETPLACE.AVERAGE'),
                        backgroundColor: 'transparent',
                        data: this.annualizedReturns.map(e => e.avg),
                        borderColor: '#006666',
                        borderWidth: 1,
                        borderDash: [5],
                    }
                ],
            }
        },

        getChartOptions() {
            return {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '(%)',
                        },
                        suggestedMin: 0,
                        suggestedMax: this.chartMaxY,
                    },
                },
                plugins: {
                    title: {
                        display: true,
                        text: this.$t('MARKETPLACE.MONTHLY_ANNUALIZED_RETURNS')
                    }
                },
            }
        },

        getChartUrl() {
            const params = {
                type: 'line',
                data: {
                    labels: this.annualizedReturns.map(e => e.formatted_date),
                    datasets: [
                        {
                            label: this.$t('MARKETPLACE.ANNUALIZED_RETURNS'),
                            backgroundColor: 'rgba(0, 102, 102, 0.2)',
                            borderColor: 'rgba(0, 102, 102, 1)',
                            borderWidth: 1,
                            data: this.annualizedReturns.map(e => e.annualized_percent),
                        }
                    ],
                },
                options: {
                    responsive: true,
                    title: {
                        display: true,
                        text: this.$t('MARKETPLACE.MONTHLY_ANNUALIZED_RETURNS'),
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: '(%)',
                            }
                        },
                        yAxes: [
                            {
                                display: true,
                                ticks: {
                                    suggestedMax: 30,
                                    beginAtZero: true,
                                },
                            }
                        ],
                    },
                },
            }
            return `https://image-charts.com/chart.js/2.8.0?bkg=white&c=${JSON.stringify(params)}`
        }
    },

    computed: {
        chartMaxY() {
            if (this.annualizedReturns.length) {
                const values = this.annualizedReturns.map(e => Math.ceil(e.annualized_percent))
                const maxValue = Math.max(...values)
                if (maxValue <= 20) {
                    return 20
                }
                return maxValue + 2
            }
            return 20
        }
    },
}
</script>