<template>
    <div class="popup" v-if="isOpen" style="z-index: 10001;">
        <div class="popup-content d-flex flex-column">
            <div class="d-flex justify-content-between">
                <p class="font-bold font-22">{{ $t('RENTAL_INCOME.RENTAL_INCOME_DETAIL') }}</p>
                <div class="btn-close" @click="close()">
                    <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
                </div>
            </div>
            <div class="content-container">
                <div class="top-container mt-4">
                    <div class="d-flex flex-column flex-lg-row">
                        <div class="image-container col-12 col-lg-6">
                            <b-img :src="getAvatar()" blank-src="property image" class="click-able img-property"
                                @click="openPropertyDetail()" />
                            <div class="bottom-info text-left">{{ property.name }}</div>
                            <div class="address-container" @click="openGoogleMap()">
                                <img src="@/assets/img/location.svg" alt="" />
                                <span>{{ property.metadata.address }}</span>
                            </div>
                        </div>
                        <div class="info-container col-12 col-lg-6">
                            <div class="ts-item d-flex flex-row justify-content-between">
                                <p class="ts-title">{{ $t("TRANSACTION.TRX_ID") }}</p>
                                <p class="ts-value">{{ trxId }}</p>
                            </div>
                            <div class="ts-item d-flex flex-row justify-content-between">
                                <p class="ts-title">{{ $t("TRANSACTION.VALUE") }}</p>
                                <p class="ts-value" id="price-value">{{ exchangeValue(value) }}</p>
                                <CurrencyTooltip tooltipId="price-value" :value="value"></CurrencyTooltip>
                            </div>
                            <div class="ts-item d-flex flex-row justify-content-between">
                                <p class="ts-title">{{ $t("TRANSACTION.STATUS") }}</p>
                                <p class="ts-value">{{ status }}</p>
                            </div>
                            <div class="ts-item d-flex flex-row justify-content-between">
                                <p class="ts-title">{{ $t("RENTAL_INCOME.RENTAL_MONTH") }}</p>
                                <p class="ts-value">{{ monthAndYear }}</p>
                            </div>
                            <div class="ts-item d-flex flex-row justify-content-between">
                                <p class="ts-title">{{ $t("RENTAL_INCOME.DISTRIBUTION_DATE") }}</p>
                                <p class="ts-value">{{ date }}</p>
                            </div>
                            <div class="ts-item d-flex flex-row justify-content-between">
                                <p class="ts-title">{{ $t("TRANSACTION.DESCRIPTION") }}</p>
                                <p class="ts-value">{{ note }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <b-tabs content-class="mt-3" active-nav-item-class="font-16 color-main"
                        active-tab-class="font-weight-bold font-16 color-black">
                        <b-tab :title="$t('RENTAL_INCOME.DETAILS')" active>
                            <div class="d-flex flex-column align-items-center tab-content">
                                <div class="property-info col-12 mb-2">
                                    <p class="font-weight-bold font-24 text-center">{{ monthAndYearTitle }}</p>
                                </div>
                                <div class="table-container">
                                    <table class="calendar-table">
                                        <thead>
                                            <tr>
                                                <th class="calendar-title">SUN</th>
                                                <th class="calendar-title">MON</th>
                                                <th class="calendar-title">TUE</th>
                                                <th class="calendar-title">WED</th>
                                                <th class="calendar-title">THU</th>
                                                <th class="calendar-title">FRI</th>
                                                <th class="calendar-title">SAT</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="weekItem in rentalIncomeDetails">
                                                <td class="bordered" v-for="dayItem in weekItem.weekly_details">
                                                    <div class="calendar-cell-day">{{ dayItem.date_in_month }}</div>
                                                    <div class="text-center pb-1">
                                                        <label v-if="dayItem.earned_amount"
                                                            class="calendar-cell-earned-amount">
                                                            {{ exchangeValue(dayItem.earned_amount, true) }}
                                                        </label>
                                                        <p v-if="dayItem.owned_tokens"
                                                            class="calendar-cell-owned_tokens">
                                                            {{ formatNumberIntl(dayItem.owned_tokens) }} {{ (dayItem.owned_tokens > 1) ? $t('common.TOKENS') : $t('common.TOKEN') }}
                                                        </p>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="ml-3 d-flex flex-column align-items-start w-100 mt-2">
                                    <p class="currency-note">{{ $t('RENTAL_INCOME.NON_IDR_REFERENCE_ONLY') }}</p>
                                    <p v-if="rentalDistributionNote" class="currency-note text-left">
                                        {{ $t('RENTAL_INCOME.NOTES')}}: {{ rentalDistributionNote }}
                                    </p>
                                </div>
                            </div>
                        </b-tab>
                        <b-tab :title="$t('RENTAL_INCOME.DEDUCTION')">
                            <div class="d-flex flex-column align-items-center tab-content">
                                <div class="table-container">
                                    <table class="calendar-table">
                                        <thead>
                                            <tr>
                                                <th v-for="value in deductionFields" class="calendar-title">{{ value.label }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="rentalDeduction in rentalDeductions">
                                                <td class="deduction-row border-none color-gray">{{ rentalDeduction.deduction_type }}</td>
                                                <td class="deduction-row border-none color-gray">{{ rentalDeduction.description }}</td>
                                                <td class="deduction-row border-none color-gray"
                                                    :id="`rental-deduction-${rentalDeduction.id}`">{{ exchangeValue(rentalDeduction.amount) }}</td>
                                                <CurrencyTooltip :tooltipId="`rental-deduction-${rentalDeduction.id}`"
                                                    :value="rentalDeduction.amount">
                                                </CurrencyTooltip>
                                            </tr>
                                            <tr>
                                                <td class="deduction-row border-top"></td>
                                                <td class="deduction-row border-top font-bold">{{ $t("WITHDRAWALS.TOTAL") }}</td>
                                                <td class="deduction-row border-top font-bold"
                                                    id="total-rental-deductions-2">{{ exchangeValue(totalRentalDeductions) }}</td>
                                                <CurrencyTooltip tooltipId="total-rental-deductions-2"
                                                    :value="totalRentalDeductions">
                                                </CurrencyTooltip>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </b-tab>
                        <b-tab :title="$t('RENTAL_INCOME.SUMMARY')">
                            <div class="d-flex flex-column align-items-center tab-content">
                                <div class="table-container border-none">
                                    <table class="calendar-table">
                                        <thead>
                                            <tr>
                                                <th v-for="value in summaryReportFields" class="calendar-title gray">{{ value.label }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="summary in rentalIncomeSummaryDetails">
                                                <td class="deduction-row border-top color-gray">{{ formatNumberIntl(summary.number_of_tokens) }}</td>
                                                <td class="deduction-row border-top color-gray">{{ summary.ownership_days }}</td>
                                                <td class="deduction-row border-top color-gray" id="summary-value">{{ exchangeValue(summary.subtotal) }}</td>
                                                <CurrencyTooltip tooltipId="summary-value" :value="summary.subtotal">
                                                </CurrencyTooltip>
                                            </tr>
                                            <tr>
                                                <td class="deduction-row border-top"></td>
                                                <td class="deduction-row border-top font-bold">{{ $t("WITHDRAWALS.TOTAL") }}</td>
                                                <td class="deduction-row border-top font-bold" id="summary-subtotal">{{
                                                    exchangeValue(value) }}</td>
                                                <CurrencyTooltip tooltipId="summary-subtotal" :value="value">
                                                </CurrencyTooltip>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-12 col-md-8 mt-4 summary-container">
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start">
                                        <p class="color-gray">{{ $t("RENTAL_INCOME.RENTAL_INCOME_NET", { value: numOfDays }) }}</p>
                                        <p class="font-bold" id="net-rental-income">{{ exchangeValue(netRentalIncome) }}</p>
                                        <CurrencyTooltip tooltipId="net-rental-income" :value="netRentalIncome">
                                        </CurrencyTooltip>
                                    </div>
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start mt-1">
                                        <p class="color-gray">{{ $t("RENTAL_INCOME.TOTAL_TOKENS") }}</p>
                                        <p class="font-bold">{{ formatNumberIntl(propertyTotalTokens) }}</p>
                                    </div>
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start mt-1">
                                        <p class="color-gray">{{ $t("RENTAL_INCOME.EARNINGS_PER_TOKEN_PER_DAY") }}</p>
                                        <p class="font-bold" id="earnings-per-token-per-day">{{ earnTokenPerDayExchangeValue(earningsPerTokenPerDay) }}
                                        </p>
                                        <CurrencyTooltip tooltipId="earnings-per-token-per-day"
                                            :value="earningsPerTokenPerDay">
                                        </CurrencyTooltip>
                                    </div>
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start mt-1">
                                        <p class="color-gray">{{ $t("RENTAL_INCOME.RENTAL_INCOME_GROSS") }}</p>
                                        <p class="font-bold" id="rental-icon-gross">{{ exchangeValue(rentalIncome) }}</p>
                                        <CurrencyTooltip tooltipId="rental-icon-gross" :value="rentalIncome">
                                        </CurrencyTooltip>
                                    </div>
                                    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start mt-1">
                                        <p class="color-gray">{{ $t("RENTAL_INCOME.RENTAL_DEDUCTIONS") }}</p>
                                        <p class="font-bold" id="total-rental-deductions">{{ exchangeValue(totalRentalDeductions) }}</p>
                                        <CurrencyTooltip tooltipId="total-rental-deductions"
                                            :value="totalRentalDeductions">
                                        </CurrencyTooltip>
                                    </div>
                                </div>
                            </div>
                        </b-tab>
                    </b-tabs>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import moment from "moment"
import { exchange, urlImage, toFixedWithoutRounding, formatNumberIntl } from "@/helpers/common"
import CurrencyTooltip from "../components/CurrencyTooltip.vue"
import i18n from "../i18n"
import store from '../store/store';

export default {
    components: {
        CurrencyTooltip,
    },
    data() {
        return {
            isOpen: false,
            property: null,
            transaction: null,
            rentalIncomeDetails: [],
            rentalDistribution: null,
            deductionFields: [],
            rentalDeductions: [],
            totalDeductions: 0,
            summaryReportFields: [],
            rentalIncomeSummaryDetails: [],
            distributionDate: null,
        }
    },

    watch: {
        isOpen(open) {
            if (open) {
                document.body.classList.add("modal-open")
            } else {
                document.body.classList.remove("modal-open")
            }
        },
    },

    methods: {
      formatNumberIntl,
        openPopup(property, transaction, details, rentalDistribution, deductionFields, rentalDeductions, totalDeductions, summaryReportFields, rentalIncomeSummaryDetails, distributionDate) {
            this.property = property
            this.transaction = transaction
            this.rentalIncomeDetails = details
            this.rentalDistribution = rentalDistribution
            this.deductionFields = deductionFields
            this.rentalDeductions = rentalDeductions
            this.totalDeductions = totalDeductions
            this.summaryReportFields = summaryReportFields
            this.rentalIncomeSummaryDetails = rentalIncomeSummaryDetails
            this.distributionDate = distributionDate
            this.isOpen = true
        },
        close() {
            this.isOpen = false
        },
        getAvatar() {
            if (this.property.images && this.property.images.length) {
                return urlImage(this.property.images[0])
            }
            return ""
        },

        async openPropertyDetail() {
            if (this.property) {
                await this.$router.push({ name: "propertyDetail", params: { uuid: this.property.uuid } })
            }
        },

        openGoogleMap() {
            if (this.property.map_link) {
                window.open(this.property.map_link, "_blank")
            }
        },

        exchangeValue(value, asterisk = false) {
            return exchange(value, 1000, asterisk, null, false, this.rentalDistribution?.exchange_rates?.rates)
        },

        earnTokenPerDayExchangeValue(value, asterisk = false) {
            const locale = i18n.global.locale.value
            const user = store.state.userProfile
            let exchangedValue = this.exchangeValue(value, asterisk)
            if (locale === 'id' && user.preferred_currency === 'IDR') {
                return exchangedValue.replace('.', ',')
            }
            return exchangedValue
        },
    },

    computed: {
        trxId() {
            return this.transaction && this.transaction.secondary_id || ""
        },
        value() {
            return this.transaction && this.transaction.amount || 0
        },
        status() {
            return this.$t('RENTAL_INCOME.RECEIVED')
        },

        monthAndYear() {
            return this.transaction && moment(this.transaction.rental_distribution.date).format("MM/YYYY") || ""
        },

        monthAndYearTitle() {
            const locale = this.$i18n.locale.toLowerCase()
            moment.locale(locale)
            return this.transaction && moment(this.transaction.rental_distribution.date).format("MMMM YYYY") || ""
        },

        date() {
            return this.distributionDate && moment(this.distributionDate).format("DD/MM/YYYY") || ""
        },

        note() {
            return this.transaction && this.transaction.note || ""
        },

        rentalDistributionNote() {
            if (!this.rentalDistribution) {
                return null
            }
            return this.isIndonesian ? this.rentalDistribution.note_id_locale : this.rentalDistribution.note
        },
        totalRentalDeductions() {
            if (this.transaction) {
                return this.totalDeductions
            }
            return 0
        },
        propertyName() {
            return this.property && `${this.property.name} - ${this.property.category.name}` || ""
        },
        propertyTotalTokens() {
            return this.property && this.property.total_tokens || ""
        },
        earningsPerTokenPerDay() {
            if (this.transaction && this.rentalDistribution) {
                if (!this.rentalDistribution.num_of_days) {
                    return 0
                }
                const value = this.netRentalIncome / this.property.total_tokens / this.rentalDistribution.num_of_days
                return toFixedWithoutRounding(value)
            }
            return 0
        },
        netRentalIncome() {
            if (this.transaction && this.rentalDistribution) {
                return this.rentalDistribution.rental_income - this.totalDeductions
            }
            return 0
        },

        rentalIncome() {
            if (this.transaction && this.rentalDistribution) {
                return this.rentalDistribution.rental_income
            }
            return 0
        },

        numOfDays() {
            return this.rentalDistribution && this.rentalDistribution.num_of_days || 0
        },

        isIndonesian() {
            return this.$i18n.locale.toLowerCase() === 'id';
        },
    },
}
</script>
<style lang="scss" scoped>
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;

    .popup-content {
        position: relative;
        background-color: #fff;
        padding: 30px;
        border-radius: 30px;
        text-align: center;
        height: 95%;
        width: 95%;
        overflow: hidden;

        @media screen and (min-width: 600px) {
            width: 80%;
        }

        @media screen and (min-width: 1100px) {
            width: 60%;
        }

        .btn-close {
            background-color: rgb(221, 221, 221);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

        .content-container {
            overflow-y: auto;
            overflow-x: hidden;

            .top-container {
                border-radius: 16px;
                overflow: hidden;
                background-color: #F2F2F2;

                .image-container {
                    aspect-ratio: 1.7;
                    width: 100%;
                    position: relative;
                    padding: 0px;
                    border-radius: 16px;
                    overflow: hidden;
                    background-color: #fff;

                    .img-property {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .bottom-info {
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        right: 0;
                        padding: 10px 30px 10px 30px;
                        font-weight: bold;
                        background-image: linear-gradient(to right, #003E3E, #003e3e71);
                        color: #fff;
                    }

                    .address-container {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        padding: 4px 8px 4px 8px;
                        background-color: #fff;
                        color: var(--primary-color);
                        font-size: 12px;
                        border-radius: 16px;
                        font-weight: 600;
                        cursor: pointer;

                        img {
                            width: 14px;
                            height: 14px;
                            margin-right: 4px;
                            object-fit: contain;
                        }
                    }
                }

                .info-container {
                    padding: 24px;

                    .ts-item {
                        border-bottom: 1px solid rgb(194, 193, 193);
                        margin-top: 10px;
                        margin-bottom: 10px;

                        .ts-value {
                            font-size: 16px;
                            font-weight: 600;
                            text-align: end;
                        }

                        .ts-title {
                            font-size: 16px;
                            color: rgb(114, 112, 112);
                        }
                    }
                }
            }
        }

        .summary-container {
            padding: 20px;
            border-radius: 20px;
            background-color: rgb(242, 242, 242);
        }
    }
}

.table-container {
    width: 95%;
    border-radius: 20px;
    border: 1px solid lightgrey;

    &.border-none {
        border: none;
    }

    .calendar-table {
        width: 100%;
        border-radius: 20px;
        overflow: hidden;
        margin: 0;
        border: 1px solid lightgrey;

        thead {

            th,
            td {
                border: 1px solid transparent;
            }

            .calendar-title {
                height: 40px;
                text-align: center;
                font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
                font-weight: bold;
                background-color: var(--primary-color);
                color: white;
                font-size: 14px;

                &.gray {
                    background-color: white;
                    color: #6c757d;
                }
            }
        }

        tbody {

            .bordered {
                border: 1px solid lightgrey;
            }
            .calendar-cell-day {
                padding: 4px 8px;
                font-size: 90%;
                font-family: "Figtree", Helvetica, sans-serif, serif;
                color: gray;
                text-align: left;
                font-weight: 600;
            }

            .calendar-cell-earned-amount {
                font-size: 100%;
                font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
                color: var(--primary-color);
                display: inline;

                @media (max-width: 768px) {
                    font-size: 65%;
                }
            }

            .calendar-cell-owned_tokens {
                font-size: 80%;
                font-style: italic;
                font-family: "Figtree", Helvetica, sans-serif, serif;
                color: var(--primary-color);

                @media (max-width: 768px) {
                    font-size: 60%;
                }
            }

            .border-top {
                border-top: 1px solid lightgrey;
                border-bottom: none;
                border-left: none;
                border-right: none;
            }

            .border-none {
                border: none;
            }

            .deduction-row {
                height: 40px;
            }
        }
    }
}

.tab-content {
    color: var(--primary-color);

    .currency-note {
        font-style: italic;
        font-size: 13px;
        font-weight: 400;
    }
}

.table-row-content {
    font-size: 18px;

    @media (max-width: 768px) {
        font-size: 13px;
    }
}
</style>
