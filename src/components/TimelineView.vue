<template>
    <div class="mb-3">
        <p class="font-20 font-weight-bold title">{{ $t('propertyDetail.PROPERTY_TIMELINE') }}</p>
        <div v-for="(item, index) in rows" class="timeline-item d-flex flex-row">
            <div class="year">
                <p class="font-20 font-weight-bold text-color" :class="{ done: item.is_done }">{{ showYear(item, index) ?
                    getYear(item.date) : '' }}</p>
            </div>
            <div class="ml-2">
                <div class="circle" :class="{ done: item.is_done }"></div>
                <div class="line" :class="{ done: item.is_done }"></div>
            </div>
            <div class="calendar-container mt-1 ml-4 mr-3 text-color" :class="{ done: item.is_done }">
                <b-icon scale="2.5" icon="calendar"></b-icon>
                <p class="month-text">{{ getMonth(item.date) }}</p>
                <p class="day">{{ getDay(item.date) }}</p>
            </div>
            <div class="text-left right-content ml-3 pb-4">
                <p class="font-18 font-weight-bold headline text-color" :class="{ done: item.is_done }">{{ isID ?
                    item.headline_id_locale : item.headline }}</p>
                <p class="font-14 text-color" :class="{ done: item.is_done }">{{ isID ? item.description_id_locale :
                    item.description }}</p>
            </div>
        </div>
        <div v-if="showMore" class="view-more" @click="viewMore">{{ showAll ?
            $t('propertyDetail.VIEW_LESS') :
            $t('propertyDetail.VIEW_MORE') }}</div>
    </div>
</template>
<script>

import moment from "moment"

export default {
    props: {
        items: {
            type: Array,
            default() {
                return []
            }
        },
        totalCount: {
            type: Number,
            default: 0,
        },
        displayCount: {
            type: Number,
            default: 0,
        },
    },

    data() {
        return {
            showAll: false,
        }
    },

    methods: {
        getYear(date) {
            return moment(date).format('YYYY')
        },
        getMonth(date) {
            return moment(date).format('MMM').toUpperCase()
        },

        getDay(date) {
            return moment(date).format('DD')
        },

        viewMore() {
            this.showAll = !this.showAll
        },

        showYear(item, index) {
            const prevItem = index > 0 ? this.rows[index - 1] : null
            const nextItem = index < this.rows.length - 1 ? this.rows[index + 1] : null
            if (prevItem == null && nextItem == null) {
                return true
            }
            if (nextItem != null) {
                return moment(item.date).format('YYYY') !== moment(nextItem.date).format('YYYY')
            }
            if (nextItem == null) {
                return true
            }
            return false
        },
    },

    computed: {
        isID() {
            return this.$i18n.locale.toLowerCase() === 'id';
        },

        rows() {
            if (this.showAll) {
                return this.items
            }
            return this.items.slice(0, this.displayCount)
        },

        showMore() {
            return this.displayCount < this.totalCount
        },
    },
}
</script>
<style lang="scss" scoped>
.title {
    margin-bottom: 18px;
    color: var(--primary-color);
}

.timeline-item {
    width: 100%;

    .year {
        width: 50px;
        min-width: 50px;
        text-align: right;
        margin-top: -6px;
        margin-right: 3px;
    }

    .circle {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background-color: #a1a1a1;

        &.done {
            background-color: #006666;
        }
    }

    .line {
        height: 100%;
        width: 2px;
        background-color: #a1a1a1;
        margin-left: 6px;

        &.done {
            background-color: #006666;
        }
    }

    .headline {
        margin-top: -7px;
    }

    .right-content {
        width: 60%;
    }

    .calendar-container {
        position: relative;

        .month-text {
            position: absolute;
            top: 2px;
            left: -7px;
            font-size: 9px;
            font-weight: bold;
            text-align: center;
            width: 30px;
        }

        .day {
            position: absolute;
            top: 8px;
            left: -7px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            width: 30px;
        }
    }
}

.view-more {
    margin-left: 68px;
    margin-top: 15px;
    cursor: pointer;
    font-size: 13px;
    text-decoration: underline;
    width: fit-content;
}

.text-color {
    color: #a1a1a1;

    &.done {
        color: #006666;
    }
}
</style>
