<template>
  <b-form-select v-model="location_id" @change="onchange" :options="locations" text-field="name" value-field="id">
    <template #first>
      <b-form-select-option :value="null">Select Location</b-form-select-option>
    </template>
  </b-form-select>
</template>
<script>
  import locationsService from "../services/locations.service";

  export default {
    props: {
      location_id: {
        type: Number,
        default: null,
      }
    },
    // watch: {
    //   location_id(value) {
    //     this.$emit('location_id', value);
    //   },
    // },
    emits: ["location_id"],
    data() {
      return {
        // category_id: 1
        locations: []
      }
    },

    async mounted() {
      await this.getLocations()
    },
    methods: {
      onchange(value) {
        this.$emit('location_id', value);
      },
      async getLocations() {
        this.locations = await locationsService.getLocations();
      }
    },
  }
</script>
