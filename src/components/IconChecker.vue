<template>
  <button
    id="btn_blue_status_checker"
    @click="$emit('click-button')"
    :style="buttonStyle"
  >
    <img :src="require(`@/assets/img/icons/${iconName}`)" alt="" />
  </button>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: "blue",
    },
    color: {
      type: String,
      default: "white",
    },
    bgColor: {
      type: String,
      default: "transparent",
    },
  },
  emits: ["click-button"],
  computed: {
    buttonStyle() {
      return {
        "background-color": this.bgColor,
        color: this.color,
      };
    },
    iconName() {
      return `${this.type}-checker.svg`;
    },
  },
};
</script>

<style lang="scss" scoped>
button {
  border: none;
  font-size: 18px;
  font-weight: bold;
  padding: 0;
  border-radius: 100%;
  margin: 0;
  line-height: 0;

  img {
    width: 14px;
    height: 14px;
  }

  span {
    margin-left: 10px;
  }
}
</style>