<!-- Popup.vue -->
<template>
  <div class="popup" v-if="isOpen" style="z-index: 9999;">
    <div class="popup-content">
      <p v-if="title" class="font-24 font-bold">{{ title }}</p>
      <p v-if="message" v-html="message" :class="['font-18', 'font-normal', 'mt-3', messageClass]"></p>
      <p v-if="warningMessage" v-html="warningMessage" :class="['font-16','font-medium','mt-3', warningMessageClass]"></p>

      <div v-if="dontShowAgainKey" class="cls-check-not-show">
        <b-form-checkbox v-model="dontShowAgain" class="goro-checkbox" size="lg">
          {{
            dontShowAgainDays === 1
              ? $t("common.DO_NOT_SHOW_FOR_TODAY")
              : $t("common.DO_NOT_SHOW_IN_DAYS", { value: dontShowAgainDays })
          }}
        </b-form-checkbox>
      </div>

      <b-row class="justify-content-center mt-4">
        <b-button class="btn-main font-18 ml-2 mr-2" :class="btnClass" variant="none"
                  style="border-radius: 8px; padding-bottom: 8px; padding-top: 12px;" @click="onNegativeButtonClicked">
          {{ negativeButton ? negativeButton : $t("common.CANCEL") }}
        </b-button>
        <b-button v-if="positiveButton" class="btn-outline-white font-bold font-18 ml-2 mr-2" :class="btnClass"
                  style="border-radius: 8px; padding-bottom: 8px; padding-top: 12px;" variant="none" @click="onPositiveButtonClicked">
          {{ positiveButton }}
        </b-button>
      </b-row>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isOpen: false,
      title: null,
      message: null,
      warningMessage: null,
      dontShowAgainKey: null,
      dontShowAgainDays: 1,
      positiveButton: null,
      negativeButton: null,
      messageClass: null,
      warningMessageClass: null,
      btnClass: "col-5",
      dontShowAgain: false,
    }
  },
  methods: {
    openPopup(options) {
      const defaults = {
        title: null, message: null, warningMessage: null, dontShowAgainKey: null, dontShowAgainDays: 1,
        positiveButton: null, negativeButton: null, messageClass: null, warningMessageClass: null, btnClass: "col-5",
      }
      const o = { ...defaults, ...options }
      Object.assign(this, o, { isOpen: true, dontShowAgain: false })
    },
    handleDontShowAgain() {
      if (this.dontShowAgainKey && this.dontShowAgain) {
        const days = Math.max(1, Number(this.dontShowAgainDays) || 1);
        const now = new Date();
        const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const nextAllowed = new Date(startOfToday);
        nextAllowed.setDate(startOfToday.getDate() + days);
        localStorage.setItem(this.dontShowAgainKey, String(nextAllowed.getTime()));
      }
    },
    onPositiveButtonClicked() {
      this.handleDontShowAgain()
      this.$emit("on-positive-clicked")
      this.isOpen = false
    },
    onNegativeButtonClicked() {
      this.handleDontShowAgain()
      this.$emit("on-negative-clicked")
      this.isOpen = false
    },
  }
}
</script>

<style scoped>
.popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
}

.popup-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  max-width: 600px;
  text-align: center;
}

.cls-check-not-show {
  margin-top: 15px;
  margin-bottom: 15px;

  :deep(.custom-control-label::before),
  :deep(.custom-control-label::after) {
    top: 50%;
    transform: translateY(-55%);
  }

  :deep(.custom-control-label) {
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -0.03em;
    padding: 3px 0;
    color: var(--primary-color);

    @media (max-width: 992px) {
      font-size: 14px !important;
    }
  }
}
</style>
