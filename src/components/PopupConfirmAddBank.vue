<template>
    <div class="popup" v-if="isOpen" style="z-index: 9999;">
        <div class="popup-content d-flex flex-column align-items-center">
            <p class="font-20 font-weight-bold">{{ $t('WITHDRAWALS.MESSAGE_CONFIRM_BANK_ACCOUNT') }}</p>
            <div class="number-box mt-3 d-flex flex-column align-items-start text-left">
                <p class="font-18"><span class="font-weight-bold">{{ $t('WITHDRAWALS.BANK_NAME') }}:</span> {{ bankName }}</p>
                <p class="font-18 mt-1"><span class="font-weight-bold">{{ $t('WITHDRAWALS.ACCOUNT_NUMBER') }}:</span> {{ accountNumber }}</p>
            </div>
            <b-row class="justify-content-center mt-4 w-100">
                <b-button id="btn_confirmAddBank_Yes" class="btn-main ml-1 mr-1 col-5" variant="none" @click="onYesClicked">
                    {{ $t("account.YES") }}
                </b-button>
                <b-button id="btn_confirmAddBank_No" class="btn-outline-main col-5 ml-1 mr-1" variant="none" @click="onNoClicked">
                    {{ $t("account.NO") }}
                </b-button>
            </b-row>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            isOpen: false,
            accountNumber: '',
            bankName: '',
        }
    },
    emits: ['on-confirmed'],
    methods: {
        openPopup(accountNumber, bankName) {
            this.accountNumber = accountNumber
            this.bankName = bankName
            this.isOpen = true
        },
        onYesClicked() {
            this.$emit('on-confirmed')
            this.isOpen = false
        },
        onNoClicked() {
            this.isOpen = false
        },
    }
}
</script>

<style lang="scss" scoped>
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;

    .popup-content {
        background-color: #fff;
        padding: 30px;
        border-radius: 8px;
        max-width: 600px;
        text-align: center;

        .number-box {
            width: 100%;
            color: black;
            background-color: #e5f7f7;
            padding: 8px 20px;
            border-radius: 8px;
            border: var(--primary-color) solid 1px;
        }
    }
}
</style>
