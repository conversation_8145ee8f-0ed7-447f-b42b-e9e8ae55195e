<template>
    <button id="btn_socialClicked" @click="$emit('click-button')" :style="buttonStyle">
        <b-icon class="icon" v-if="icon" :icon="icon"></b-icon>
        <span>{{text}}</span>
    </button>
</template>

<script>
export default {
    props: {
        icon: {
            type: String,
            default: null,
        },
        text: {
            type: String,
            default: null,
        },
        color: {
            type: String,
            default: 'white',
        },
        bgColor: {
            type: String,
            default: 'blue',
        }
    },
    emits: ['click-button'],
    computed: {
        buttonStyle() {
            return {
                'background-color': this.bgColor,
                'color': this.color,
            };
        }
    }

}
</script>

<style lang="scss" scoped>
button {
    border: none;
    font-size: 18px;
    font-weight: bold;
    padding: 0.3rem 1rem 0.3rem 1rem;
    border-radius: 20px;
    margin: 5px;

    .icon {
        width: 20px;
        height: 20px;
    }

    span {
        margin-left: 10px;
    }
}
</style>