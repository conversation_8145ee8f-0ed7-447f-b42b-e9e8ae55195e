<!-- Popup.vue -->
<template>
    <div class="popup" v-if="isOpen" style="z-index: 9999;">
        <div class="popup-content">
            <p class="font-24 font-bolder mb-3">{{ $t('account.TAKE_A_PHOTO') }}</p>
            <div v-if="isCameraOpen" class="camera-box" :class="{ 'flash': isShotPhoto }">
                <div class="camera-shutter" :class="{ 'flash': isShotPhoto }"></div>
                <video v-show="!isPhotoTaken" ref="camera" autoplay></video>
                <canvas v-show="isPhotoTaken" :width="cameraOptions.width" :height="cameraOptions.height" id="photoTaken"
                    ref="canvas"></canvas>
                <div v-if="isCameraOpen && !isPhotoTaken" class="camera-shoot d-flex flex-row justify-content-center">
                    <img src="@/assets/img/take-photo.png" @click="takePhoto">
                </div>
                <img v-if="isPhotoTaken" @click="retake" src="@/assets/img/close.png" class="img-close" alt="">
            </div>
            <b-row class="justify-content-center mt-4">
                <b-button id="btn_takePhotoPopup_Done" :disabled="!isPhotoTaken" class="btn-main ml-1 mr-1 col-5" variant="none" @click="onDoneClicked">
                    {{ $t("common.DONE") }}
                </b-button>
                <b-button id="btn_takePhotoPopup_Cancel" class="btn-outline-cancel col-5 ml-1 mr-1" variant="none" @click="onCancelClicked">
                    {{ $t("common.CANCEL") }}
                </b-button>
            </b-row>
        </div>
    </div>
</template>

<script>

export default {
    data() {
        return {
            isOpen: false,
            file: null,
            isCameraOpen: false,
            isPhotoTaken: false,
            isShotPhoto: false,
            cameraOptions: {
                width: 600,
                height: 450,
            },
        }
    },

    beforeDestroy() {
        this.stopCameraStream();
    },

    methods: {
        openPopup() {
            this.isOpen = true
            this.isCameraOpen = false
            this.isPhotoTaken = false
            this.openCamera()
        },
        onDoneClicked() {
            this.$emit("on-done", this.file)
            this.isOpen = false
        },
        onCancelClicked() {
            this.$emit("on-cancel")
            this.isOpen = false
            this.stopCameraStream()
        },

        openCamera() {
            this.isCameraOpen = true;
            this.createCameraElement();
        },

        createCameraElement() {
            const constraints = (window.constraints = {
                audio: false,
                video: true
            });

            navigator.mediaDevices
                .getUserMedia(constraints)
                .then(stream => {
                    this.$refs.camera.srcObject = stream;
                })
                .catch(error => {

                });
        },

        stopCameraStream() {
            let tracks = this.$refs.camera && this.$refs.camera.srcObject.getTracks();

            if (tracks) {
                tracks.forEach(track => {
                    track.stop();
                });
            }
        },

        takePhoto() {
            if (!this.isPhotoTaken) {
                this.isShotPhoto = true;

                const FLASH_TIMEOUT = 50;

                setTimeout(() => {
                    this.isShotPhoto = false;
                }, FLASH_TIMEOUT);
                this.isPhotoTaken = true;
                const context = this.$refs.canvas.getContext('2d');
                context.setTransform(-1, 0, 0, 1, this.$refs.canvas.width, 0);
                context.drawImage(this.$refs.camera, 0, 0, this.cameraOptions.width, this.cameraOptions.height);
                this.stopCameraStream();
                this.file = document.getElementById("photoTaken").toDataURL("image/jpeg", 1);
            }
        },

        retake() {
            this.isPhotoTaken = false
            this.openCamera()
        },
    }
}
</script>

<style scoped lang="scss">
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.popup-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    max-width: 60%;
    min-width: 40%;
    text-align: center;

    @media only screen and (max-width: 650px) {
        max-width: 90%;
        min-width: 70%;
    }

    .camera-box {
        position: relative;
        width: 100%;
        aspect-ratio: 4 / 3;
        border-radius: 10px;
        overflow: hidden;
        background-color: #e6e6e6b6;

        video {
            transform: rotateY(180deg);
            -webkit-transform: rotateY(180deg);
            -moz-transform: rotateY(180deg);
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        canvas {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .camera-shutter {
            opacity: 0;
            background-color: #fff;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;

            &.flash {
                opacity: 1;
            }
        }

        .img-close {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 35px;
            height: 35px;
            cursor: pointer;
        }

        .overlay {
            position: absolute;
            background-color: rgba(120, 111, 111, 0.432);
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;

            .spinner {
                width: 3rem;
                height: 3rem;
            }
        }
    }

    .camera-shoot {
        position: absolute;
        bottom: 5px;
        left: 0;
        right: 0;

        img {
            width: 50px;
            height: 50px;
            object-fit: fill;
            cursor: pointer;
        }
    }
}
</style>
