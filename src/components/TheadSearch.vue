<template>
  <thead>

  </thead>
</template>
<script>
export default {
  name: 'p-button',
  props: {
    tag: {
      type: String,
      default: "button"
    },
    round: <PERSON>olean,
    icon: <PERSON><PERSON>an,
    outline: Boolean,
    block: Boolean,
    loading: Boolean,
    disabled: Boolean,
    type: {
      type: String,
      default: "default"
    },
    nativeType: {
      type: String,
      default: "button"
    },
    size: {
      type: String,
      default: ""
    },
    simple: <PERSON>olean
  }
};
</script>
<style>
</style>
