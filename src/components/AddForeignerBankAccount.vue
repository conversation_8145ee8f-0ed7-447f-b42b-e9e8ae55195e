<template>
    <div class="col-12 d-flex flex-column align-items-start add-fore-container">
        <div class="col-12 text-center">
            <p class="font-24 font-weight-bold text-center mb-3">{{ confirming ? $t('WITHDRAWALS.CONFIRM_BANK_ACCOUNT') :
                isAdd ? $t('WITHDRAWALS.ADD_BANK_ACCOUNT') : $t('WITHDRAWALS.CHANGE_BANK_ACCOUNT') }}</p>
        </div>
        <p v-if="!confirming" class="mandatory-note mb-3 ml-2">{{ $t('WITHDRAWALS.MANDATORY') }}</p>
        <Form v-if="!confirming" class="col-12 m-0 p-0" v-slot="{ handleSubmit }">
            <b-form class="col-12 d-flex flex-column p-0 m-0" @submit.prevent="handleSubmit(onNext)">
                <div class="d-flex flex-column flex-lg-row justify-content-between col-12">
                    <div class="col-12 col-lg-6 p-0 m-0">
                        <Field :model-value="form.bank_account_holder_name"
                            :name="$t('WITHDRAWALS.BANK_ACCOUNT_HOLDER_NAME')"
                            @update:modelValue="form.bank_account_holder_name = $event" :rules="{ required: true }"
                            v-slot="validationContext">
                            <b-form-group>
                                <p class="mb-1">{{ $t('WITHDRAWALS.BANK_ACCOUNT_HOLDER_NAME') }}<span
                                        style="color: red;">*</span>
                                </p>
                                <b-form-input :placeholder="$t('WITHDRAWALS.BANK_ACCOUNT_HOLDER_NAME')"
                                    v-bind="validationContext.field" aria-describedby="input-account-holder-feedback"
                                    :state="getValidationState(validationContext.meta)">
                                </b-form-input>
                                <b-form-invalid-feedback id="input-account-holder-feedback">
                                    {{ validationContext.errors[0] }}
                                </b-form-invalid-feedback>
                            </b-form-group>
                        </Field>
                    </div>
                    <div class="col-12 col-lg-5 p-0 m-0">
                        <Field :model-value="form.account_number" :name="$t('WITHDRAWALS.BANK_ACCOUNT_NUMBER')"
                            @update:modelValue="form.account_number = $event" :rules="{ required: true }"
                            v-slot="validationContext">
                            <b-form-group>
                                <p class="mb-1 capitalize">{{ $t('WITHDRAWALS.BANK_ACCOUNT_NUMBER') }}<span
                                        style="color: red;">*</span></p>
                                <b-form-input :placeholder="$t('WITHDRAWALS.BANK_ACCOUNT_NUMBER')"
                                    v-bind="validationContext.field" :state="getValidationState(validationContext.meta)"
                                    aria-describedby="input-account-number-feedback"></b-form-input>
                                <b-form-invalid-feedback id="input-account-number-feedback">
                                    {{ validationContext.errors[0] }}
                                </b-form-invalid-feedback>
                            </b-form-group>
                        </Field>
                    </div>
                </div>
                <div class="d-flex flex-column flex-lg-row justify-content-between mt-3 col-12">
                    <div class="col-12 col-lg-6 p-0 m-0">
                        <Field :model-value="form.bank_name" :name="$t('WITHDRAWALS.BANK_NAME')"
                            @update:modelValue="form.bank_name = $event" :rules="{ required: true }"
                            v-slot="validationContext">
                            <b-form-group>
                                <p class="mb-1 capitalize">{{ $t('WITHDRAWALS.BANK_NAME') }}<span
                                        style="color: red;">*</span>
                                </p>
                                <b-form-input :placeholder="$t('WITHDRAWALS.BANK_NAME')" v-bind="validationContext.field"
                                    :state="getValidationState(validationContext.meta)"
                                    aria-describedby="input-bank-name-feedback"></b-form-input>
                                <b-form-invalid-feedback id="input-bank-name-feedback">
                                    {{ validationContext.errors[0] }}
                                </b-form-invalid-feedback>
                            </b-form-group>
                        </Field>
                    </div>
                    <div class="col-12 col-lg-5 p-0 m-0">
                        <Field :model-value="form.bank_country" :name="$t('WITHDRAWALS.BANK_COUNTRY')"
                            @update:modelValue="form.bank_country = $event" :rules="{ required: true }"
                            v-slot="validationContext">
                            <b-form-group>
                                <p class="mb-1">{{ $t('WITHDRAWALS.BANK_COUNTRY') }}<span style="color: red;">*</span></p>
                                <b-form-input :placeholder="$t('WITHDRAWALS.BANK_COUNTRY')" v-bind="validationContext.field"
                                    :state="getValidationState(validationContext.meta)"
                                    aria-describedby="input-bank-country-feedback"></b-form-input>
                                <b-form-invalid-feedback id="input-bank-country-feedback">
                                    {{ validationContext.errors[0] }}
                                </b-form-invalid-feedback>
                            </b-form-group>
                        </Field>
                    </div>
                </div>
                <Field :model-value="form.bank_address" :name="$t('WITHDRAWALS.BANK_ADDRESS')"
                    @update:modelValue="form.bank_address = $event" :rules="{ required: true }" v-slot="validationContext">
                    <b-form-group class="mt-3 col-12">
                        <p class="mb-1">{{ $t('WITHDRAWALS.BANK_ADDRESS') }}<span style="color: red;">*</span></p>
                        <b-form-textarea :placeholder="$t('WITHDRAWALS.BANK_ADDRESS')" v-bind="validationContext.field"
                            :state="getValidationState(validationContext.meta)"
                            aria-describedby="input-bank-address-feedback" rows="3" max-rows="6"></b-form-textarea>
                        <b-form-invalid-feedback id="input-bank-address-feedback">
                            {{ validationContext.errors[0] }}
                        </b-form-invalid-feedback>
                    </b-form-group>
                </Field>
                <Field :model-value="form.swift_code" name="SWIFT Code" @update:modelValue="form.swift_code = $event"
                    :rules="{ required: true }" v-slot="validationContext">
                    <b-form-group class="mt-3 col-12 col-lg-6">
                        <p class="mb-1">SWIFT Code<span style="color: red;">*</span></p>
                        <b-form-input placeholder="SWIFT Code" v-bind="validationContext.field"
                            :state="getValidationState(validationContext.meta)"
                            aria-describedby="input-swift-code-feedback"></b-form-input>
                        <b-form-invalid-feedback id="input-swift-code-feedback">
                            {{ validationContext.errors[0] }}
                        </b-form-invalid-feedback>
                    </b-form-group>
                </Field>
                <Field :model-value="form.note" :name="$t('WITHDRAWALS.NOTES')" @update:modelValue="form.note = $event"
                    :rules="{}" v-slot="validationContext">
                    <b-form-group class="mt-3 col-12">
                        <p class="mb-1">{{ $t('WITHDRAWALS.NOTES') }}</p>
                        <b-form-textarea :placeholder="$t('WITHDRAWALS.NOTES')" aria-describedby="input-note-feedback"
                            v-bind="validationContext.field" :state="getValidationState(validationContext.meta)" rows="3"
                            max-rows="6"></b-form-textarea>
                        <b-form-invalid-feedback id="input-note-feedback">
                            {{ validationContext.errors[0] }}
                        </b-form-invalid-feedback>
                    </b-form-group>
                </Field>
                <div class="text-center">
                    <b-button id="btn_addForeignerBankAccount_Next" class="bg-main-color color-white mt-5 col-6" type="submit" variant="light"
                        style="padding: 10px 20px 10px 20px">
                        {{ $t("common.NEXT") }}</b-button>
                </div>
            </b-form>
        </Form>
        <div v-else class="col-12">
            <p class="font-18 font-weight-bold">{{ $t('WITHDRAWALS.PLEASE_MAKE_SURE_BANK_CORRECT') }}</p>
            <div class="divider mt-3 mb-3"></div>
            <div class="d-flex flex-column flex-lg-row justify-content-between col-12 m-0 p-0">
                <div class="d-flex flex-column col-12 col-lg-7 m-0 p-0 align-items-start">
                    <p class="title">{{ $t('WITHDRAWALS.BANK_ACCOUNT_HOLDER_NAME') }}</p>
                    <p class="value mt-1">{{ form.bank_account_holder_name }}</p>
                </div>
                <div class="d-flex flex-column col-12 col-lg-5 m-0 p-0 mt-3 mt-lg-0 align-items-start">
                    <p class="title">{{ $t('WITHDRAWALS.BANK_ACCOUNT_NUMBER') }}</p>
                    <p class="value mt-1">{{ form.account_number }}</p>
                </div>
            </div>
            <div class="divider mt-3 mb-3"></div>
            <div class="d-flex flex-column flex-lg-row justify-content-between col-12 m-0 p-0">
                <div class="d-flex flex-column col-12 col-lg-7 m-0 p-0 align-items-start">
                    <p class="title">{{ $t('WITHDRAWALS.BANK_NAME') }}</p>
                    <p class="value mt-1">{{ form.bank_name }}</p>
                </div>
                <div class="d-flex flex-column col-12 col-lg-5 m-0 p-0 mt-lg-0 align-items-start">
                    <p class="title">{{ $t('WITHDRAWALS.BANK_COUNTRY') }}</p>
                    <p class="value mt-1">{{ form.bank_country }}</p>
                </div>
            </div>
            <div class="d-flex flex-column col-12 m-0 p-0 mt-3 align-items-start">
                <p class="title">{{ $t('WITHDRAWALS.BANK_ADDRESS') }}</p>
                <p class="value mt-1">{{ form.bank_address }}</p>
            </div>
            <div class="d-flex flex-column col-12 m-0 p-0 mt-3 align-items-start">
                <p class="title">SWIFT Code</p>
                <p class="value mt-1">{{ form.swift_code }}</p>
            </div>
            <div class="d-flex flex-column col-12 m-0 p-0 mt-3 align-items-start">
                <p class="title">{{ $t('WITHDRAWALS.NOTES') }}</p>
                <p class="value mt-1">{{ form.note }}</p>
            </div>
            <div class="text-center">
                <b-button id="btn_addForeignerBankAccount_Submit" class="bg-main-color color-white mt-5 col-6" @click="addbankAccount" variant="light"
                    style="padding: 10px 20px 10px 20px">
                    {{ $t("common.SUBMIT") }}</b-button>
            </div>
        </div>
    </div>
</template>

<script>

import { configure, Field, Form } from "vee-validate"
import { localize } from "@vee-validate/i18n"
import withdrawalsService from "../services/withdrawals.service"
import { gtmTrackEvent } from "../helpers/gtm"
import { GTM_EVENT_NAMES } from "../constants/gtm"

configure({
    generateMessage: localize({
        en: {
            messages: {
                required: "The {field} is required",
            },
        },
        id: {
            messages: {
                required: "Kolom {field} wajib diisi",
            },
        },
    }),
})

export default {
    components: {
        Field,
        Form,
    },

    emits: ['on-success'],

    data() {
        return {
            form: {
                bank_name: '',
                account_number: '',
                bank_account_holder_name: '',
                bank_country: '',
                bank_address: '',
                swift_code: '',
                note: '',
            },
            confirming: false,
        }
    },

    props: {
        isAdd: {
            type: Boolean,
            default: true,
        },
    },

    methods: {
        getValidationState({ dirty, validated, valid = null }) {
            return dirty || validated ? valid : null
        },

        onNext() {
            this.confirming = true
        },

        async addbankAccount() {
            if (this.isAdd) {
                const res = await withdrawalsService.addBankAccount(this.form);
                if (res) {
                    await this.$store.dispatch('refreshUserProfile');
                    await this.$router.push({ name: 'bankAccountHistory' })
                    this.$emit('on-success')
                }
                gtmTrackEvent({
                    event: GTM_EVENT_NAMES.ADD_BANK_ACCOUNT
                })
            } else {
                const res = await withdrawalsService.changeBankAccount(this.form);
                if (res && !res.error) {
                    await this.$store.dispatch('refreshUserProfile');
                    this.$emit('on-success')
                }
                gtmTrackEvent({
                    event: GTM_EVENT_NAMES.CHANGE_BANK_ACCOUNT
                })
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.add-fore-container {
    .mandatory-note {
        color: red;
        font-size: 14px;
    }

    .capitalize {
        text-transform: capitalize;
    }

    .title {
        color: black;
        font-size: 16px;
    }

    .value {
        color: var(--primary-color);
        font-size: 17px;
    }

    .divider {
        background-color: rgb(169, 169, 169);
        width: 100%;
        height: 1px;
    }
}
</style>