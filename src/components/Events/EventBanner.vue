<template>
  <router-link v-if="eventData" :to="{ name: 'eventDetail', params: { uuid: eventData.uuid } }">
    <div class="mt-4 event-container" ref="containerRef" :style="{ aspectRatio: computedAspectRatio }">
      <img v-if="isWideContainer" class="banner-image"
           :src="getImageUrl(eventData.image_wide)" alt=""/>
      <img v-else class="banner-image"
           :src="getImageUrl(eventData.image)" alt=""/>
      <div v-if="!isSultanJuraganEvent"
           :style="isWideContainer
            ? { bottom: '0', left: '50%', position: 'absolute', transform: 'translateX(-50%) scale(' + topRankingScale + ')', transformOrigin: 'bottom center'}
            : { bottom: '0', right: '2%', position: 'absolute', transform: 'scale(' + topRankingScale + ')', transformOrigin: 'bottom right' }">
        <EventTopRanking
          :rank1User="rank1User"
          :rank2User="rank2User"
          :rank3User="rank3User"
          :isWideContainer="isWideContainer"
          :parentWidth="containerWidth"/>
      </div>
      <div v-if="isSultanJuraganEvent">
        <div :style="{ bottom: '0', left: isWideContainer ? '20%' : '3.3%', position: 'absolute',
         transform: 'scale(' + topRankingScaleJuraganSultan + ')',transformOrigin: 'bottom left' }">
          <EventTopRanking
            :rank1User="rank1UserJuragan"
            :rank2User="rank2UserJuragan"
            :rank3User="rank3UserJuragan"
            :isWideContainer="isWideContainer"
            :parentWidth="containerWidth"
          />
        </div>
        <div :style="{ bottom: '0', right: isWideContainer ? '20%' : '3.3%', position: 'absolute',
         transform: 'scale(' + topRankingScaleJuraganSultan + ')',transformOrigin: 'bottom right' }">
          <EventTopRanking
            :rank1User="rank1UserSultan"
            :rank2User="rank2UserSultan"
            :rank3User="rank3UserSultan"
            :isWideContainer="isWideContainer"
            :parentWidth="containerWidth"
          />
        </div>
      </div>
    </div>
  </router-link>
</template>

<script>
import { urlImage } from '@/helpers/common'
import eventsService from '@/services/events.service'
import EventTopRanking from '@/components/Events/EventTopRanking.vue'
import { EVENT_TYPES } from "@/constants/constants";

const CACHE_KEY = 'active_event_v1'
const CACHE_TTL_MS = 60_000 // 1 minute

export default {
  name: 'EventBanner',
  components: { EventTopRanking },

  data() {
    return {
      eventData: null,
      intervalId: null,
      containerWidth: 0,
      breakpointPx: 800,
      aspectRatio: 3.2,
      wideAspectRatio: 7.2
    }
  },

  computed: {
    isWideContainer() {
      return this.containerWidth >= this.breakpointPx
    },
    computedAspectRatio() {
      const ratio = this.isWideContainer
        ? this.wideAspectRatio
        : this.aspectRatio
      return `${ratio} / 1`
    },
    topRankingScale() {
      let scale = 1
      if (this.containerWidth <= 480) {
        scale = 0.9
      }
      const baseline = this.isWideContainer ? 1150 : 600
      const f = this.containerWidth / baseline
      return (f < 1 ? f : 1) * scale
    },
    topRankingScaleJuraganSultan() {
      const scale = this.isWideContainer ? 0.8 : 0.72
      const baseline = this.isWideContainer ? 1150 : 600
      const f = this.containerWidth / baseline
      return (f < 1 ? f : 1) * scale
    },
    rank1User() {
      return this.eventData?.ranks?.[0] || null
    },
    rank2User() {
      return this.eventData?.ranks?.[1] || null
    },
    rank3User() {
      return this.eventData?.ranks?.[2] || null
    },
    isSultanJuraganEvent() {
      return this.eventData?.type === EVENT_TYPES.TYPE_SULTAN_JURAGAN
    },
    rank1UserJuragan() {
      return this.eventData?.juragan_ranks?.[0] || null
    },
    rank2UserJuragan() {
      return this.eventData?.juragan_ranks?.[1] || null
    },
    rank3UserJuragan() {
      return this.eventData?.juragan_ranks?.[2] || null
    },
    rank1UserSultan() {
      return this.eventData?.sultan_ranks?.[0] || null
    },
    rank2UserSultan() {
      return this.eventData?.sultan_ranks?.[1] || null
    },
    rank3UserSultan() {
      return this.eventData?.sultan_ranks?.[2] || null
    }
  },

  async mounted() {
    // 1) fetch event data & start polling
    await this.fetchActiveEvent()
    this.startPolling()

    // 2) measure banner size after render
    this.$nextTick(() => {
      const el = this.$refs.containerRef
      if (!el) return

      // Set initial width/height
      this.containerWidth = el.offsetWidth

      // Observe future resizes
      this.resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          const newWidth = entry.contentRect.width;
          window.requestAnimationFrame(() => {
            if (newWidth !== this.containerWidth) {
              this.containerWidth = newWidth;
            }
          });
        }
      })
      this.resizeObserver.observe(el)
    })
  },

  beforeDestroy() {
    if (this.intervalId) clearInterval(this.intervalId)
    if (this.resizeObserver) this.resizeObserver.disconnect()
  },

  methods: {
    async fetchActiveEvent() {
      try {
        const raw = localStorage.getItem(CACHE_KEY)
        if (raw) {
          const parsed = JSON.parse(raw)
          const updatedAt = Number(parsed?.updatedAt) || 0
          const isFresh = Date.now() - updatedAt < CACHE_TTL_MS
          if (isFresh && parsed?.data) {
            this.eventData = parsed.data
            this.$emit('loaded')
            return
          }
        }
        // Fallback to API
        const res = await eventsService.getActiveEvent()
        if (res?.data) {
          this.eventData = res.data
          this.$emit('loaded')
          localStorage.setItem(CACHE_KEY, JSON.stringify({
            data: res.data,
            updatedAt: Date.now()
          }))
        } else {
          localStorage.removeItem(CACHE_KEY)
        }
      } catch (_) {
      }
    },

    startPolling() {
      if (this.intervalId) clearInterval(this.intervalId)
      const intervalTime = this.eventData ? CACHE_TTL_MS : 5 * CACHE_TTL_MS
      this.intervalId = setInterval(async () => {
        await this.fetchActiveEvent()
      }, intervalTime)
    },

    getImageUrl(path) {
      return urlImage({ image: path })
    }
  }
}
</script>

<style scoped>
.event-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 1rem;
  /* aspect-ratio is provided dynamically by :style above */
}

.banner-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
