<template>
  <carousel v-if="events.length"
            ref="eventCarousel"
            class="mt-4 event-carousel"
            :perPage="1"
            :spacePadding="0"
            :scrollPerPage="true"
            :paginationEnabled="true"
            :autoplay="true"
            :autoplayTimeout="8000"
            :loop="true">
    <slide v-for="(slideEvent) in events" :key="slideEvent.uuid" @slide-click="goToEvent(slideEvent.uuid)">
      <div class="event-container" :style="{ aspectRatio: computedAspectRatio }">
        <img v-if="isWideContainer" class="banner-image" :src="getImageUrl(slideEvent.image_wide)" alt=""/>
        <img v-else class="banner-image" :src="getImageUrl(slideEvent.image)" alt=""/>

        <!-- standard event -->
        <div v-if="slideEvent.type === EVENT_TYPES.TYPE_STANDARD"
             :style="isWideContainer
                 ? { bottom: '0', left: '50%', position: 'absolute', transform: 'translateX(-50%) scale(' + topRankingScale + ')', transformOrigin: 'bottom center'}
                 : { bottom: '0', right: '2%', position: 'absolute', transform: 'scale(' + topRankingScale + ')', transformOrigin: 'bottom right' }">
          <EventTopRanking
            :eventType="slideEvent.type"
            :rank1User="slideEvent.ranks?.[0]"
            :rank2User="slideEvent.ranks?.[1]"
            :rank3User="slideEvent.ranks?.[2]"
            :isWideContainer="isWideContainer"
            :parentWidth="containerWidth"/>
        </div>

        <!-- referral event -->
        <div v-else-if="slideEvent.type === EVENT_TYPES.TYPE_REFERRAL"
             :style="isWideContainer
                 ? { bottom: '0', left: '50%', position: 'absolute', transform: 'translateX(-50%) scale(' + topRankingReferralScale + ')', transformOrigin: 'bottom center'}
                 : { bottom: '0', right: '2%', position: 'absolute', transform: 'scale(' + topRankingReferralScale + ')', transformOrigin: 'bottom right' }">
          <EventTopRanking
            :eventType="slideEvent.type"
            :rank1User="slideEvent.ranks?.[0]"
            :rank2User="slideEvent.ranks?.[1]"
            :rank3User="slideEvent.ranks?.[2]"
            :isWideContainer="isWideContainer"
            :parentWidth="containerWidth"/>
        </div>

        <!-- sultan/juragan event -->
        <div v-else>
          <div :style="{ bottom: '0', left: isWideContainer ? '20%' : '3.3%', position: 'absolute',
              transform: 'scale(' + topRankingScaleJuraganSultan + ')', transformOrigin: 'bottom left' }">
            <EventTopRanking
              :eventType="slideEvent.type"
              :rank1User="slideEvent.juragan_ranks?.[0]"
              :rank2User="slideEvent.juragan_ranks?.[1]"
              :rank3User="slideEvent.juragan_ranks?.[2]"
              :isWideContainer="isWideContainer"
              :parentWidth="containerWidth"
            />
          </div>
          <div :style="{ bottom: '0', right: isWideContainer ? '20%' : '3.3%', position: 'absolute',
              transform: 'scale(' + topRankingScaleJuraganSultan + ')', transformOrigin: 'bottom right' }">
            <EventTopRanking
              :eventType="slideEvent.type"
              :rank1User="slideEvent.sultan_ranks?.[0]"
              :rank2User="slideEvent.sultan_ranks?.[1]"
              :rank3User="slideEvent.sultan_ranks?.[2]"
              :isWideContainer="isWideContainer"
              :parentWidth="containerWidth"
            />
          </div>
        </div>
      </div>
    </slide>
  </carousel>
</template>

<script>
import { Carousel, Slide } from '@jambonn/vue-concise-carousel'
import '@jambonn/vue-concise-carousel/lib/vue-concise-carousel.css'
import { urlImage } from '@/helpers/common'
import { EVENT_TYPES, STORAGE_KEYS } from "@/constants/constants";
import EventTopRanking from '@/components/Events/EventTopRanking.vue'
import eventsService from '@/services/events.service'

const CACHE_TTL_MS = 60_000

export default {
  name: 'EventBanner',
  components: { Carousel, Slide, EventTopRanking },

  data() {
    return {
      events: [],
      intervalId: null,
      containerWidth: 0,
      breakpointPx: 800,
      aspectRatio: 3.2,
      wideAspectRatio: 7.2,
    }
  },

  computed: {
    EVENT_TYPES() {
      return EVENT_TYPES
    },
    isWideContainer() {
      return this.containerWidth >= this.breakpointPx
    },
    computedAspectRatio() {
      return `${this.isWideContainer ? this.wideAspectRatio : this.aspectRatio} / 1`
    },
    topRankingScale() {
      let scale = 1
      if (this.containerWidth <= 480) scale = scale * 0.9
      const baseline = this.isWideContainer ? 1150 : 600
      const f = this.containerWidth / baseline
      return (f < 1 ? f : 1) * scale
    },
    topRankingReferralScale() {
      let scale = this.isWideContainer ? 0.8 : 0.93
      const baseline = this.isWideContainer ? 1150 : 600
      const f = this.containerWidth / baseline
      return (f < 1 ? f : 1) * scale
    },
    topRankingScaleJuraganSultan() {
      const scale = this.isWideContainer ? 0.8 : 0.72
      const baseline = this.isWideContainer ? 1150 : 600
      const f = this.containerWidth / baseline
      return (f < 1 ? f : 1) * scale
    },
  },

  async mounted() {
    await this.fetchActiveEvents()
    this.startPolling()
    await this.$nextTick(() => {
      const el = this.$refs.eventCarousel?.$el || this.$refs.eventCarousel
      if (!el || !(el instanceof Element)) return
      this.containerWidth = el.offsetWidth
      this.resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          const newWidth = entry.contentRect.width
          window.requestAnimationFrame(() => {
            if (newWidth !== this.containerWidth) this.containerWidth = newWidth
          })
        }
      })
      this.resizeObserver.observe(el)
    })
  },

  beforeDestroy() {
    if (this.intervalId) clearInterval(this.intervalId)
    if (this.resizeObserver) this.resizeObserver.disconnect()
  },

  methods: {
    async fetchActiveEvents() {
      try {
        const raw = localStorage.getItem(STORAGE_KEYS.EVENT_ACTIVES.key)
        if (raw) {
          const parsed = JSON.parse(raw)
          const updatedAt = Number(parsed?.updatedAt) || 0
          const fresh = Date.now() - updatedAt < CACHE_TTL_MS
          const cached = Array.isArray(parsed?.data) ? parsed.data : []

          if (fresh && cached.length) {
            this.events = cached
            this.$emit('loaded')
            return
          }
        }
      } catch {
        localStorage.removeItem(STORAGE_KEYS.EVENT_ACTIVES.key)
      }

      // Fallback to API
      try {
        const res = await eventsService.getActiveEvents()
        this.events = res?.data || []
        this.$emit('loaded')

        localStorage.setItem(STORAGE_KEYS.EVENT_ACTIVES.key, JSON.stringify({
          data: this.events,
          updatedAt: Date.now()
        }))
      } catch (_) {
      }
    },
    startPolling() {
      if (this.intervalId) clearInterval(this.intervalId)
      this.intervalId = setInterval(this.fetchActiveEvents, CACHE_TTL_MS)
    },
    getImageUrl(path) {
      return urlImage({ image: path })
    },
    goToEvent(uuid, e) {
      if (this.carouselDragMoved) {
        e.preventDefault()
        e.stopPropagation()
        this.carouselDragMoved = false
        return
      }
      this.$router.push({ name: 'eventDetail', params: { uuid } })
    },
  }
}
</script>
<style scoped>
.event-carousel {
  position: relative;
}

.event-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 1rem;
  /* aspect-ratio is provided dynamically by :style above */
}

.banner-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/*noinspection CssUnusedSymbol*/
:deep(.VueCarousel) {
  padding-bottom: 0 !important; /* remove extra bottom padding */
}

/*noinspection CssUnusedSymbol*/
:deep(.VueCarousel-pagination) {
  position: absolute !important; /* overlay on the banner */
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  gap: 8px;
  pointer-events: none;
  z-index: 2;
}

/*noinspection CssUnusedSymbol*/
:deep(.VueCarousel-dot) {
  pointer-events: auto;
  border-radius: 50%;
  background-color: transparent !important;
  border: 0.1rem solid var(--primary-light-color);
  width: 6px !important;
  height: 6px !important;
  padding: 0 !important;
  margin: 0 3px;
}

/*noinspection CssUnusedSymbol*/
:deep(.VueCarousel-dot--active) {
  background-color: var(--primary-color) !important;
}
</style>