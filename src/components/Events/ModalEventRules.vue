<template>
    <div
        v-if="isEnabledShowModal && event"
        id="id-goro-user-consent-modal"
        class="cls-custom-popup-container"
    >
        <div class="popup-dialog popup-dialog-centered">
            <div class="popup-content">
                <div class="popup-body text-center">
                    <div class="body-content">
                        <div class="heading-content">
                            <h2 class="heading font-24 font-bold mt-2 mb-3">
                                {{ $t("EVENT.RULES") }}
                            </h2>
                        </div>
                        <div class="content text-left">
                            <div class="content-consent">
                                <article>
                                    <iframe
                                        ref="editorFrame"
                                        class="editor-frame"
                                        :srcdoc="iframeContent"
                                    ></iframe>
                                </article>
                            </div>
                        </div>
                        <div
                            class="footer-content mt-4 d-flex flex-column align-items-center justify-content-between"
                        >
                            <b-button
                                @click="closePopup"
                                class="btn btn-none btn-main font-18 font-medium btn-close"
                                style="padding: 6px 25px 7px 25px;"
                            >
                                {{ $t("common.CLOSE") }}
                            </b-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        event: {
            type: Object,
            default: null,
        },
    },
    emits: ["on-close"],
    data () {
        return {
            isEnabledShowModal: false,
            scrollOptions: {
                suppressScrollX: true,
            },
        };
    },
    watch: {
        show (value) {
            this.isEnabledShowModal = value;
        },
    },
    computed: {
        bodyContent () {
            if (!this.event || !this.event.rules) {
                return "";
            }
            return this.event.rules;
        },
        iframeContent () {
            return `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            body {
              font-family: "Figtree", Helvetica, sans-serif, serif;
              font-size: 16px;
              line-height: 1.3;
              color: #333333;
              margin: 0;
              padding: 10px;
              box-sizing: border-box;
              padding-right: 15px;
            }
          </style>
        </head>
        <body contenteditable="false">
          ${this.bodyContent}
        </body>
      </html>
    `;
        },
    },
    methods: {
        onClose () {
            this.isEnabledShowModal = false;
        },
        async closePopup () {
            this.onClose();
            this.$emit("on-close");
        },
    },
};
</script>

<style lang="scss" scoped>
/* ──────────────────────────────────────────────────────────────────────────
   Overlay container (semi‐transparent background)
   ────────────────────────────────────────────────────────────────────────── */
.cls-custom-popup-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    color: black;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1071;

    /* Center everything, both horizontally & vertically */
    display: flex;
    align-items: center;
    justify-content: center;

    /* ─────────────────────────────────────────────────────────────────────
       Make .popup-dialog scale its width by percentage and set height to 85%
       of the viewport (so the modal fills ~85% of the vertical space).
       ───────────────────────────────────────────────────────────────────── */
    .popup-dialog {
        position: relative;
        width: 100%; /* fallback on very small screens (<400px) */
        height: 85%; /* fill 85% of the parent (100% viewport) */
        max-height: calc(100vh - 1rem); /* ensure a 0.5rem margin top/bottom */
        margin: 0.5rem auto;
        display: flex;
        align-items: center;
        justify-content: center;

        /* At each breakpoint, set width so it “feels” like ~480px wide: */
        @media (min-width: 400px) {
            /* 480 / 400 = 120% → clamp down to 98% so it never exceeds the viewport */
            width: 98%;
        }
        @media (min-width: 550px) {
            /* 480 / 550 ≈ 87.27% → round to 87% */
            width: 87%;
        }
        @media (min-width: 700px) {
            /* 480 / 700 ≈ 68.57% → round to 69% */
            width: 69%;
        }
        @media (min-width: 950px) {
            /* 480 / 950 ≈ 50.53% → round to 51% */
            width: 51%;
        }
        @media (min-width: 1100px) {
            /* 480 / 1100 ≈ 43.64% → round to 44% */
            width: 44%;
        }
        @media (min-width: 1250px) {
            /* 480 / 1250 = 38.40% → round to 38% */
            width: 38%;
        }
        @media (min-width: 1400px) {
            /* 480 / 1400 ≈ 34.29% → round to 34% */
            width: 34%;
        }
        @media (min-width: 1550px) {
            /* 480 / 1550 ≈ 30.97% → round to 31% */
            width: 31%;
        }
        @media (min-width: 1700px) {
            /* 480 / 1700 ≈ 28.24% → round to 28.5% */
            width: 28.5%;
        }
    }

    /* ─────────────────────────────────────────────────────────────────────
       Have .popup-content stretch to fill the full width & height of .popup-dialog.
       Then limit internal height so the iframe/content scrolls if it’s too tall.
       ───────────────────────────────────────────────────────────────────── */
    .popup-content {
        position: relative;
        background-color: #fff;
        border-radius: 20px;

        /* Fill the .popup-dialog’s width & height */
        width: 100%;
        height: 100%;
        max-height: 100%;

        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 4px solid #00b7b6;
        padding: 12px;

        @media (max-width: 992px) {
            padding: 10px;
        }
        @media (max-width: 768px) {
            /* width handled by .popup-dialog; no change here */
        }
        @media (max-width: 576px) {
            padding: 8px;
            border: 3px solid #00b7b6;
        }

        /* ─────────────────────────────────────────────────────────────────
           Inside the white box: stack header + scrollable iframe area + footer
           ───────────────────────────────────────────────────────────────── */
        .popup-body {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .body-content {
                flex: 1 1 auto;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                .heading-content {
                    h2 {
                        line-height: 28px;
                        text-align: center;
                        color: #333333;

                        @media (max-width: 992px) {
                            line-height: 20px;
                        }
                    }
                }

                /* ───────────────────────────────────────────────────────────
                   The scrollable “rules” area (keeps the iframe from overflowing)
                   ─────────────────────────────────────────────────────────── */
                .content {
                    flex: 1 1 auto;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    margin-top: 8px;

                    .content-consent {
                        flex: 1 1 auto;
                        display: flex;
                        flex-direction: column;
                        overflow: hidden;
                        padding: 0 8px;

                        article {
                            flex: 1 1 auto;
                            display: flex;
                            flex-direction: column;
                            overflow: hidden;

                            .editor-frame {
                                flex: 1 1 auto;
                                width: 100%;
                                border: 0 !important;
                            }
                        }

                        @media (max-width: 992px) {
                            padding: 0 10px 0 0;
                        }
                    }
                }

                /* ───────────────────────────────────────────────────────────
                   Footer area with “Close” button
                   ─────────────────────────────────────────────────────────── */
                .footer-content {
                    flex: 0 0 auto;
                    display: flex;
                    justify-content: center;
                    margin-top: 12px;

                    .btn-close {
                        min-width: 140px;
                        padding: 8px 16px;
                        font-size: 0.9rem;
                    }
                }
            }
        }
    }
}
</style>