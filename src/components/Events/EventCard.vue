<template>
  <div class="event-container">
    <div class="banner-container" ref="containerRef" :style="{ aspectRatio: aspectRatio }">
      <img class="banner-image" :src="getImageUrl(eventData.image)" alt=""/>
      <div v-if="!isSultanJuraganEvent"
           :style="{ bottom: '0', right: '2%', position: 'absolute', transform: 'scale(' + topRankingScale + ')', transformOrigin: 'bottom right' }">
        <EventTopRanking
          :eventType="eventData.type"
          :rank1User="rank1User"
          :rank2User="rank2User"
          :rank3User="rank3User"
          :isWideContainer="false"
          :parentWidth="containerWidth"
        />
      </div>
      <div v-if="isSultanJuraganEvent" style="background: red">
        <div :style="{ bottom: '0', left: '3%', position: 'absolute',
         transform: 'scale(' + topRankingScaleJuraganSultan + ')', transformOrigin: 'bottom left' }">
          <EventTopRanking
            :eventType="eventData.type"
            :rank1User="rank1UserJuragan"
            :rank2User="rank2UserJuragan"
            :rank3User="rank3UserJuragan"
            :isWideContainer="false"
            :parentWidth="containerWidth"
          />
        </div>
        <div :style="{ bottom: '0', right: '3%', position: 'absolute',
         transform: 'scale(' + topRankingScaleJuraganSultan + ')', transformOrigin: 'bottom right' }">
          <EventTopRanking
            :eventType="eventData.type"
            :rank1User="rank1UserSultan"
            :rank2User="rank2UserSultan"
            :rank3User="rank3UserSultan"
            :isWideContainer="false"
            :parentWidth="containerWidth"
          />
        </div>
      </div>
      <div class="event-status">
        <p class="status-badge status-upcoming" v-if="isUpcoming">{{ $t('EVENT.STATUS_UPCOMING') }}</p>
        <p class="status-badge status-ongoing" v-else-if="isOngoing">{{ $t('EVENT.STATUS_ONGOING') }}</p>
        <p class="status-badge status-ended" v-if="isEnded">{{ $t('EVENT.STATUS_ENDED') }}</p>
      </div>
    </div>
    <div class="event-card-info">
      <div>
        <div class="event-card-info-title">{{ eventData.name }}</div>
        <div class="event-card-info-period">
          <img src="../../assets/img/events/calendar.svg" class="event-calendar-icon" alt=""/>
          {{ formatPeriod(eventData.start_time, eventData.end_time) }}
        </div>
      </div>
      <router-link :to="{ name: 'eventDetail', params: { uuid: eventData.uuid } }" class="view-detail-btn" :class="{ disabled: isEnded }">
        {{ $t('common.VIEW_DETAILS') }}
      </router-link>
    </div>
  </div>
</template>


<script>
import {urlImage} from '@/helpers/common'
import EventTopRanking from '@/components/Events/EventTopRanking.vue'
import { EVENT_STATUS, EVENT_TYPES } from "@/constants/constants";

export default {
  name: 'EventBanner',
  components: { EventTopRanking },

  props: {
    eventData: {},
  },

  data () {
    return {
      containerWidth: 0,
      aspectRatio: 3.2,
    }
  },

  computed: {
    isUpcoming () {
      return this.eventData && this.eventData.status === EVENT_STATUS.UPCOMING
    },
    isOngoing () {
      return this.eventData && this.eventData.status === EVENT_STATUS.ONGOING
    },
    isEnded () {
      return this.eventData && this.eventData.status === EVENT_STATUS.ENDED
    },
    topRankingScale() {
      let scale = 1
      if (this.containerWidth <= 660) {
        scale = 0.9
      } else if (this.containerWidth < 550) {
        scale = 0.8
      }
      const baseline = 600
      const f = this.containerWidth / baseline
      return (f < 1 ? f : 1) * scale
    },
    topRankingScaleJuraganSultan() {
      let scale = 0.72
      if (this.containerWidth > 751) {
        scale = 0.9
      } else if (this.containerWidth >= 660) {
        scale = 0.78
      }
      const baseline = 600
      const f = this.containerWidth / baseline
      return (f < 1 ? f : 1) * scale
    },
    // Top-3 users from API
    rank1User () {
      return this.eventData?.ranks?.[0] || null
    },
    rank2User () {
      return this.eventData?.ranks?.[1] || null
    },
    rank3User () {
      return this.eventData?.ranks?.[2] || null
    },
    isSultanJuraganEvent() {
      return this.eventData && this.eventData.type === EVENT_TYPES.TYPE_SULTAN_JURAGAN
    },
    rank1UserJuragan() {
      return this.eventData?.juragan_ranks?.[0] || null
    },
    rank2UserJuragan() {
      return this.eventData?.juragan_ranks?.[1] || null
    },
    rank3UserJuragan() {
      return this.eventData?.juragan_ranks?.[2] || null
    },
    rank1UserSultan() {
      return this.eventData?.sultan_ranks?.[0] || null
    },
    rank2UserSultan() {
      return this.eventData?.sultan_ranks?.[1] || null
    },
    rank3UserSultan() {
      return this.eventData?.sultan_ranks?.[2] || null
    }
  },

  async mounted () {
    this.$nextTick(() => {
      const el = this.$refs.containerRef
      if (!el) return

      // Set initial width/height
      this.containerWidth = el.offsetWidth

      // Observe future resizes
      this.resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          const newWidth = entry.contentRect.width;
          window.requestAnimationFrame(() => {
            if (newWidth !== this.containerWidth) {
              this.containerWidth = newWidth;
            }
          });
        }
      })
      this.resizeObserver.observe(el)
    })
  },

  beforeDestroy () {
    if (this.resizeObserver) this.resizeObserver.disconnect()
  },

  methods: {
    getImageUrl (path) {
      return urlImage({ image: path })
    },

    formatPeriod (start, end) {
      const startDate = new Date(start);
      const endDate = new Date(end);

      // Month names for English and Indonesian
      const months = {
        en: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        id: ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember']
      };

      // Determine current language ('en' or 'id')
      const locale = (this.$i18n && this.$i18n.locale) ? this.$i18n.locale : 'en';
      const lang = locale.startsWith('id') ? 'id' : 'en';

      // Build period string based on the range
      if (
        startDate.getMonth() === endDate.getMonth() &&
        startDate.getFullYear() === endDate.getFullYear()
      ) {
        // Same month and year
        if (lang === 'id') {
          return `Periode ${startDate.getDate()} - ${endDate.getDate()} ${months[lang][endDate.getMonth()]} ${endDate.getFullYear()}`;
        } else {
          return `Period ${startDate.getDate()} - ${endDate.getDate()} ${months[lang][endDate.getMonth()]} ${endDate.getFullYear()}`;
        }
      } else if (startDate.getFullYear() === endDate.getFullYear()) {
        // Same year, different month
        if (lang === 'id') {
          return `Periode ${startDate.getDate()} ${months[lang][startDate.getMonth()]} - ${endDate.getDate()} ${months[lang][endDate.getMonth()]} ${endDate.getFullYear()}`;
        } else {
          return `Period ${startDate.getDate()} ${months[lang][startDate.getMonth()]} - ${endDate.getDate()} ${months[lang][endDate.getMonth()]} ${endDate.getFullYear()}`;
        }
      } else {
        // Different years
        if (lang === 'id') {
          return `Periode ${startDate.getDate()} ${months[lang][startDate.getMonth()]} ${startDate.getFullYear()} - ${endDate.getDate()} ${months[lang][endDate.getMonth()]} ${endDate.getFullYear()}`;
        } else {
          return `Period ${startDate.getDate()} ${months[lang][startDate.getMonth()]} ${startDate.getFullYear()} - ${endDate.getDate()} ${months[lang][endDate.getMonth()]} ${endDate.getFullYear()}`;
        }
      }
    }
  }
}
</script>

<style scoped>
.event-container {
  background: #fff;
  border-radius: 1.2rem;
  overflow: hidden;
  box-shadow: 0 10px 48px 0 rgba(23, 40, 80, 0.22);
  margin-bottom: 48px;
  padding: 0;
}

.banner-container {
  position: relative;
  width: 100%;
  aspect-ratio: 3.2;
  overflow: hidden;

  .banner-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .event-status {
    position: absolute;
    display: flex;
    top: 15px;
    right: 20px;
    flex-direction: row;

    .status-badge {
      font-size: 1.1rem;
      border-radius: 8px;
      box-shadow: 0 4px 4px 0 #00000080;
      padding: 4px 15px;
      font-weight: bold;
      background: #e6e6e6;
      color: #3d3d3d;
      margin-left: 5px;
    }

    .status-upcoming {
      background-color: #B8D8FF;
      color: #0954AF;
    }

    .status-ongoing {
      background-color: #B8FFC3;
      color: #09AF5B;
    }

    .status-ended {
      background-color: #DDDDDD;
      color: #3D3D3D;
    }
  }
}

.event-card-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.6rem 1.6rem;

  .event-card-info-title {
    color: #000000;
    margin-bottom: 2px;
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    font-weight: bold;
    font-size: 22px;
  }

  .event-card-info-period {
    color: #616161;
    display: flex;
    align-items: center;
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    font-weight: 550;
    font-size: 16px;

    .event-calendar-icon {
      width: 22px;
      height: 22px;
      margin-right: 8px;
    }
  }

  .view-detail-btn {
    display: inline-block;
    background: #01756a;
    color: #fff !important;
    font-size: 18px;
    font-weight: 600;
    border-radius: 0.6rem;
    padding: 12px 52px;
    transition: background 0.2s;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
  }

  .view-detail-btn.disabled {
    background: #FFFFFF !important;
    border: #616161 solid 2px;
    color: #616161 !important;
  }

  .view-detail-btn:hover {
    background: #08574d;
  }
}

@media (max-width: 768px) {
  .event-container {
    border-radius: 0.8rem;
    margin-bottom: 25px;
  }

  .banner-container {
    .event-status {
      top: 10px;
      right: 12px;

      .status-badge {
        font-size: 0.8rem;
        padding: 3px 10px;
        border-radius: 5px;
      }
    }
  }

  .event-card-info {
    flex-direction: column;
    align-items: stretch;
    padding: 0.8rem 0.8rem;

    .event-card-info-title {
      margin-top: -0.5rem;
      margin-bottom: 0;
      font-size: 16px;
    }

    .event-card-info-period {
      font-size: 13px;

      .event-calendar-icon {
        width: 16px;
        height: 16px;
        margin-right: 6px;
      }
    }

    .view-detail-btn {
      width: 100%;
      margin: 10px 0 10px 0;
      padding: 10px 0;
      font-size: 14px;
      border-radius: 0.5rem;
    }
  }
}
</style>
