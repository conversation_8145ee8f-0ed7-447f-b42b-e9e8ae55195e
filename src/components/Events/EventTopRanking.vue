<template>
  <div ref="rankContainerRef" class="rank-container d-flex flex-row align-items-end" :style="rankContainerStyle">

    <!-- Second place -->
    <div class="d-flex flex-column align-items-center" :style="rank2ContainerStyle">
      <img src="../../assets/img/events/rank_2.svg" :style="rank2BadgeStyle" class="rank-img" alt=""/>
      <!-- Avatar or placeholder -->
      <template v-if="rank2User">
        <img v-if="rank2User.avatar_url" :src="getImageUrl(rank2User.avatar_url)"
             class="rank2-avatar" :style="rank2AvatarStyle" alt=""/>
        <img v-else src="@/assets/img/logo.png" class="rank2-avatar" :style="rank2AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <template v-else>
        <img src="@/assets/img/logo.png" class="rank2-avatar" :style="rank2AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <div :style="userInfoContainerStyle">
        <!-- Username or placeholder dash -->
        <p v-if="rank2User" class="username" :style="usernameStyle">
          {{ rank2User.username }}
        </p>
        <p v-else class="placeholder-text username" :style="usernameStyle">—</p>
        <!-- ID or placeholder dash -->
        <p v-if="rank2User" class="user-id" :style="userIdStyle">
          ID: {{ rank2User.uuid }}
        </p>
        <p v-else class="placeholder-text user-id" :style="userIdStyle">—</p>
      </div>
      <!-- Bottom podium bar -->
      <div class="rank2-bottom mt-1" :style="rank2PodiumStyle"/>
    </div>

    <!-- First place -->
    <div class="d-flex flex-column align-items-center" :style="rank1ContainerStyle">
      <img src="../../assets/img/events/rank_1.svg" :style="rank1BadgeStyle" class="rank-img" alt=""/>
      <!-- Avatar or placeholder -->
      <template v-if="rank1User">
        <img v-if="rank1User.avatar_url" :src="getImageUrl(rank1User.avatar_url)"
             class="rank1-avatar" :style="rank1AvatarStyle" alt=""/>
        <img v-else src="@/assets/img/logo.png" class="rank1-avatar"
             :style="rank1AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <template v-else>
        <img src="@/assets/img/logo.png" class="rank1-avatar" :style="rank1AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <div :style="userInfoContainerStyle">
        <!-- Username or placeholder -->
        <p v-if="rank1User" class="username" :style="usernameStyle">
          {{ rank1User.username }}
        </p>
        <p v-else class="placeholder-text username" :style="usernameStyle">—</p>
        <!-- ID or placeholder -->
        <p v-if="rank1User" class="user-id" :style="userIdStyle">
          ID: {{ rank1User.uuid }}
        </p>
        <p v-else class="placeholder-text user-id" :style="userIdStyle">—</p>
      </div>
      <!-- Bottom podium bar -->
      <div class="rank1-bottom mt-1" :style="rank1PodiumStyle"/>
    </div>

    <!-- Third place -->
    <div class="d-flex flex-column align-items-center" :style="rank3ContainerStyle">
      <!-- Badge icon always visible -->
      <img src="../../assets/img/events/rank_3.svg" :style="rank3BadgeStyle" class="rank-img" alt=""/>
      <!-- Avatar or placeholder -->
      <template v-if="rank3User">
        <img v-if="rank3User.avatar_url" :src="getImageUrl(rank3User.avatar_url)"
             class="rank3-avatar" :style="rank3AvatarStyle" alt=""/>
        <img v-else src="@/assets/img/logo.png" class="rank3-avatar"
             :style="rank3AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <template v-else>
        <img src="@/assets/img/logo.png" class="rank3-avatar" :style="rank3AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <div :style="userInfoContainerStyle">
        <!-- Username or placeholder -->
        <p v-if="rank3User" class="username" :style="usernameStyle">
          {{ rank3User.username }}
        </p>
        <p v-else class="placeholder-text username" :style="usernameStyle">—</p>
        <!-- ID or placeholder -->
        <p v-if="rank3User" class="user-id" :style="userIdStyle">
          ID: {{ rank3User.uuid }}
        </p>
        <p v-else class="placeholder-text user-id" :style="userIdStyle">—</p>
      </div>
      <!-- Bottom podium bar -->
      <div class="rank3-bottom mt-1" :style="rank3PodiumStyle"/>
    </div>
  </div>
</template>

<script>
import { urlImage } from '@/helpers/common';
import { EVENT_TYPES } from "@/constants/constants";

export default {
  name: 'EventTopRanking',
  props: {
    eventType: {
      type: String,
      required: true
    },
    rank1User: {
      type: Object,
      default: null
    },
    rank2User: {
      type: Object,
      default: null
    },
    rank3User: {
      type: Object,
      default: null
    },
    isWideContainer: {
      type: Boolean,
      default: false
    },
    parentWidth: {
      type: Number,
      required: true
    },
    fillParent: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isReferralEvent() {
      return this.eventType === EVENT_TYPES.TYPE_REFERRAL
    },
    rankContainerStyle() {
      let gap = this.fillParent ? 25 : 15
      if (this.isReferralEvent) {
        gap = gap * 0.9
      }
      let backgroundImage = null
      if (this.isReferralEvent) {
        backgroundImage = this.fillParent
          ? `url(${new URL('@/assets/img/events/podium_referral_detail.png', import.meta.url)})`
          : `url(${new URL('@/assets/img/events/podium_referral.png', import.meta.url)})`
      }
      return {
        gap: `${gap}px`,
        padding: this.isReferralEvent ? `0 8px` : `0`,
        backgroundImage: backgroundImage,
        backgroundRepeat: this.isReferralEvent ? "no-repeat" : null,
        backgroundPosition: this.isReferralEvent ? "center bottom" : null,
        backgroundSize: this.isReferralEvent ? (this.fillParent ? "95% auto" : "86% auto") : null
      };
    },
    userInfoContainerStyle() {
      let width = this.fillParent ? this.parentWidth / 4.2 : 80
      let marginTop = this.isReferralEvent ? 5 : 3
      let paddingLeftRight = this.isReferralEvent ? 4 : 1
      let paddingTopBottom = this.isReferralEvent ? 2 : 1
      if (this.fillParent) {
        marginTop = marginTop * 3
        paddingLeftRight = paddingLeftRight * 1.2
        paddingTopBottom = paddingTopBottom * 2.5
      }
      return {
        'width': `${width}px`,
        'margin-top': `${marginTop}px`,
        'padding': `${paddingTopBottom}px ${paddingLeftRight}px`,
        'background-color': this.isReferralEvent ? '#FFD5F9' : null,
        'border-radius': this.isReferralEvent ? '3px' : null,
      };
    },
    usernameStyle() {
      let maxWidth = this.fillParent ? this.parentWidth / 4.1 : 76
      let fontSize = this.isReferralEvent ? 9.6 : 10.6
      if (this.fillParent) {
        fontSize = fontSize + (this.parentWidth * 0.005)
      }
      let textColor = this.isReferralEvent ? '#991788' : '#ECECEC'
      return {
        'max-width': `${maxWidth}px`,
        'font-size': `${fontSize}px`,
        'color': `${textColor}`
      };
    },
    userIdStyle() {
      let fontSize = this.isReferralEvent ? 8.3 : 9.3
      if (this.fillParent) {
        fontSize = fontSize + (this.parentWidth * 0.005)
      }
      let textColor = this.isReferralEvent ? '#991788' : '#ECECEC'
      return {
        'color': `${textColor}`,
        'font-size': `${fontSize}px`
      };
    },
    rank2ContainerStyle() {
      if (this.fillParent) {
        return { width: `${this.parentWidth / 4.2}px` };
      }
      return { width: '73px' };
    },
    rank2BadgeStyle() {
      const size = this.fillParent ? 36 : 22;
      const marginBottom = this.fillParent ? -18 : -11;
      return {
        width: `${size}px`,
        height: `${size}px`,
        'margin-bottom': `${marginBottom}px`
      };
    },
    rank2AvatarStyle() {
      let size = 73
      if (this.fillParent) {
        size = this.parentWidth / 4.2 * 0.9
      }
      if (this.isReferralEvent) {
        //size = size * 0.9
      }
      return {
        width: `${size}px`,
        height: `${size}px`,
      };
    },
    rank2PodiumStyle() {
      let height, backgroundColor, borderRadius
      if (this.isReferralEvent) {
        height = this.fillParent ? 55 + (this.parentWidth * 0.21) : 55
        backgroundColor = 'transparent'
        borderRadius = '0'
      } else {
        height = this.fillParent ? 35 : 14.5
        backgroundColor = '#d5e4ff'
        borderRadius = this.fillParent ? 12 : 5
      }
      return {
        'height': `${height}px`,
        'background-color': `${backgroundColor}`,
        'borderRadius': `${borderRadius}px ${borderRadius}px 0 0`
      };
    },
    rank1ContainerStyle() {
      if (this.fillParent) {
        return { width: `${this.parentWidth / 3.6}px` };
      }
      return { width: '86px' };
    },
    rank1BadgeStyle() {
      const size = this.fillParent ? 42 : 26;
      const marginBottom = this.fillParent ? -21 : -13;
      return {
        width: `${size}px`,
        height: `${size}px`,
        'margin-bottom': `${marginBottom}px`
      };
    },
    rank1AvatarStyle() {
      let size = 86;
      if (this.fillParent) {
        size = (this.parentWidth / 3.6) * 0.9;
      } else {
        size = 86 * 0.9;
      }
      if (this.isReferralEvent) {
        //size = size * 0.9
      }
      return {
        width: `${size}px`,
        height: `${size}px`,
      };
    },
    rank1PodiumStyle() {
      let height, backgroundColor, borderRadius
      if (this.isReferralEvent) {
        height = this.fillParent ? 68 + (this.parentWidth * 0.3) : 68
        backgroundColor = 'transparent'
        borderRadius = '0'
      } else {
        height = this.fillParent ? 68 : 25
        backgroundColor = '#ffea95'
        borderRadius = this.fillParent ? 12 : 5
      }
      return {
        'height': `${height}px`,
        'background-color': `${backgroundColor}`,
        'borderRadius': `${borderRadius}px ${borderRadius}px 0 0`
      };
    },
    rank3ContainerStyle() {
      if (this.fillParent) {
        return { width: `${this.parentWidth / 4.2}px` };
      }
      return { width: '73px' };
    },
    rank3BadgeStyle() {
      const size = this.fillParent ? 36 : 22;
      const marginBottom = this.fillParent ? -18 : -11;
      return {
        width: `${size}px`,
        height: `${size}px`,
        'margin-bottom': `${marginBottom}px`
      };
    },
    rank3AvatarStyle() {
      let size = 66;
      if (this.fillParent) {
        size = (this.parentWidth / 4.2) * 0.9;
      }
      if (this.isReferralEvent) {
        //size = size * 0.9
      }
      return {
        width: `${size}px`,
        height: `${size}px`,
      };
    },
    rank3PodiumStyle() {
      let height, backgroundColor, borderRadius
      if (this.isReferralEvent) {
        height = this.fillParent ? 48 + (this.parentWidth * 0.16) : 48
        backgroundColor = 'transparent'
        borderRadius = '0'
      } else {
        height = this.fillParent ? 19 : 8
        backgroundColor = '#ffbe8e'
        borderRadius = this.fillParent ? 12 : 5
      }
      return {
        'height': `${height}px`,
        'background-color': `${backgroundColor}`,
        'borderRadius': `${borderRadius}px ${borderRadius}px 0 0`
      };
    }
  },
  methods: {
    getImageUrl(image) {
      return urlImage({ image });
    }
  }
};
</script>

<style scoped lang="scss">
.rank-container {
  /* Username styling */
  .username {
    font-weight: bold;
    text-align: center;
    /* enforce single line with “…” if it’s too long */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* User ID styling */
  .user-id {
    margin-top: -2px;
    font-weight: normal;
    text-align: center;
    /* enforce single line with “…” if it’s too long */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .placeholder-text {
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
  }

  .rank-img {
    z-index: 10;
  }

  .rank1-avatar,
  .rank2-avatar,
  .rank3-avatar {
    background-color: #E7F6F6;
    border-radius: 50%;
    display: block;
  }

  .rank1-avatar {
    border: 3px solid #ffcf13;
  }

  .rank2-avatar {
    border: 3px solid #9ea8ba;
  }

  .rank3-avatar {
    border: 3px solid #dd722a;
  }

  .rank1-bottom,
  .rank2-bottom,
  .rank3-bottom {
    width: 100%;
  }
}
</style>
