<template>
  <div ref="rankContainerRef" class="rank-container d-flex flex-row align-items-end" :style="fillParent ? 'gap: 25px ': 'gap: 15px'"
       style="background: none">

    <!-- Second place -->
    <div class="d-flex flex-column align-items-center" :style="rank2ContainerStyle">
      <img src="../../assets/img/events/rank_2.svg" :style="rank2BadgeStyle" class="rank-img" alt=""/>
      <!-- Avatar or placeholder -->
      <template v-if="rank2User">
        <img v-if="rank2User.avatar_url" :src="getImageUrl(rank2User.avatar_url)"
             class="rank2-avatar" :style="rank2AvatarStyle" alt=""/>
        <img v-else src="@/assets/img/logo.png" class="rank2-avatar" :style="rank2AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <template v-else>
        <img src="@/assets/img/logo.png" class="rank2-avatar" :style="rank2AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>

      <!-- Username or placeholder dash -->
      <p v-if="rank2User" class="username text-white" :style="rank2UsernameStyle">
        {{ rank2User.username }}
      </p>
      <p v-else class="placeholder-text username" :style="rank2UsernameStyle">—</p>

      <!-- ID or placeholder dash -->
      <p v-if="rank2User" class="user-id text-white">
        ID: {{ rank2User.uuid }}
      </p>
      <p v-else class="placeholder-text user-id">—</p>

      <!-- Bottom podium bar -->
      <div class="rank2-bottom mt-1" :style="fillParent ? 'height: 2.2rem; border-radius: 12px 12px 0 0' : 'height: 0.9rem; border-radius: 5px 5px 0 0'"/>
    </div>

    <!-- First place -->
    <div class="d-flex flex-column align-items-center" :style="rank1ContainerStyle">
      <img src="../../assets/img/events/rank_1.svg" :style="rank1BadgeStyle" class="rank-img" alt=""/>

      <!-- Avatar or placeholder -->
      <template v-if="rank1User">
        <img v-if="rank1User.avatar_url" :src="getImageUrl(rank1User.avatar_url)"
             class="rank1-avatar" :style="rank1AvatarStyle" alt=""/>
        <img v-else src="@/assets/img/logo.png" class="rank1-avatar"
             :style="rank1AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <template v-else>
        <img src="@/assets/img/logo.png" class="rank1-avatar" :style="rank1AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>

      <!-- Username or placeholder -->
      <p v-if="rank1User" class="username text-white" :style="rank1UsernameStyle">
        {{ rank1User.username }}
      </p>
      <p v-else class="placeholder-text username" :style="rank1UsernameStyle">—</p>

      <!-- ID or placeholder -->
      <p v-if="rank1User" class="user-id text-white">
        ID: {{ rank1User.uuid }}
      </p>
      <p v-else class="placeholder-text user-id">—</p>

      <!-- Bottom podium bar -->
      <div class="rank1-bottom mt-1" :style="fillParent ? 'height: 4.2rem; border-radius: 12px 12px 0 0' : 'height: 1.6rem; border-radius: 5px 5px 0 0'"/>
    </div>

    <!-- Third place -->
    <div class="d-flex flex-column align-items-center" :style="rank3ContainerStyle">
      <!-- Badge icon always visible -->
      <img src="../../assets/img/events/rank_3.svg" :style="rank3BadgeStyle" class="rank-img" alt=""/>

      <!-- Avatar or placeholder -->
      <template v-if="rank3User">
        <img v-if="rank3User.avatar_url" :src="getImageUrl(rank3User.avatar_url)"
             class="rank3-avatar" :style="rank3AvatarStyle" alt=""/>
        <img v-else src="@/assets/img/logo.png" class="rank3-avatar"
             :style="rank3AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>
      <template v-else>
        <img src="@/assets/img/logo.png" class="rank3-avatar" :style="rank3AvatarStyle" :class="fillParent ? 'p-3' : 'p-2'" alt=""/>
      </template>

      <!-- Username or placeholder -->
      <p v-if="rank3User" class="username text-white" :style="rank3UsernameStyle">
        {{ rank3User.username }}
      </p>
      <p v-else class="placeholder-text username" :style="rank3UsernameStyle">—</p>

      <!-- ID or placeholder -->
      <p v-if="rank3User" class="user-id text-white">
        ID: {{ rank3User.uuid }}
      </p>
      <p v-else class="placeholder-text user-id">—</p>

      <!-- Bottom podium bar -->
      <div class="rank3-bottom mt-1" :style="fillParent ? 'height: 1.2rem; border-radius: 12px 12px 0 0' : 'height: 0.5rem; border-radius: 5px 5px 0 0'"/>
    </div>
  </div>
</template>

<script>
import {urlImage} from '@/helpers/common';

export default {
    name: 'EventTopRanking',
    props: {
        rank1User: {
            type: Object,
            default: null
        },
        rank2User: {
            type: Object,
            default: null
        },
        rank3User: {
            type: Object,
            default: null
        },
        /**
         * If true, the entire rank-container is centered horizontally.
         * Otherwise, it’s aligned to the right.
         */
        isWideContainer: {
            type: Boolean,
            default: false
        },
        /**
         * The banner’s current width in pixels (passed down from EventBanner.vue).
         * When < 1280, we shrink proportionally. When ≥ 1280, we use scaleFactor = 1.
         */
        parentWidth: {
            type: Number,
            required: true
        },
        fillParent: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        rank2ContainerStyle () {
            if (this.fillParent) {
                return { width: `${this.parentWidth / 4.2}px` };
            }
            return { width: '73px' };
        },
        rank2BadgeStyle () {
            const size = this.fillParent ? 36 : 22;
            const marginBottom = this.fillParent ? -18 : -11;
            return {
                width: `${size}px`,
                height: `${size}px`,
                'margin-bottom': `${marginBottom}px`
            };
        },
        rank2AvatarStyle () {
            let size = 73
            if (this.fillParent) {
                size = this.parentWidth / 4.2 * 0.9
            }
            return {
                width: `${size}px`,
                height: `${size}px`,
            };
        },
        rank2UsernameStyle () {
            if (this.fillParent) {
                return { 'max-width': `${this.parentWidth / 4.1}px` };
            }
            return { 'max-width': '76px' };
        },
        rank1ContainerStyle() {
            if (this.fillParent) {
                return { width: `${this.parentWidth / 3.6}px` };
            }
            return { width: '86px' };
        },
        rank1BadgeStyle() {
            const size = this.fillParent ? 42 : 26;
            const marginBottom = this.fillParent ? -21 : -13;
            return {
                width: `${size}px`,
                height: `${size}px`,
                'margin-bottom': `${marginBottom}px`
            };
        },
        rank1AvatarStyle() {
            let container = 86;
            if (this.fillParent) {
                container = (this.parentWidth / 3.6) * 0.9; // 90% of (parentWidth/3.6)
            } else {
                container = 86 * 0.9; // 90% of 86px = 77.4px
            }
            return {
                width: `${container}px`,
                height: `${container}px`,
                'margin-bottom': '0.25rem'
            };
        },
        rank1UsernameStyle() {
            if (this.fillParent) {
                return { 'max-width': `${this.parentWidth / 3.5}px` };
            }
            return { 'max-width': '90px' };
        },
        rank3ContainerStyle() {
            if (this.fillParent) {
                return { width: `${this.parentWidth / 4.2}px` };
            }
            return { width: '73px' };
        },
        rank3BadgeStyle() {
            const size = this.fillParent ? 36 : 22;
            const marginBottom = this.fillParent ? -18 : -11;
            return {
                width: `${size}px`,
                height: `${size}px`,
                'margin-bottom': `${marginBottom}px`
            };
        },
        rank3AvatarStyle() {
            let container = 73 * 0.9; // 90% of 73px = 65.7px
            if (this.fillParent) {
                container = (this.parentWidth / 4.2) * 0.9; // 90% of (parentWidth/4.2)
            }
            return {
                width: `${container}px`,
                height: `${container}px`,
                'margin-bottom': '0.25rem'
            };
        },
        rank3UsernameStyle() {
            if (this.fillParent) {
                return { 'max-width': `${this.parentWidth / 4.1}px` };
            }
            return { 'max-width': '76px' };
        }
    },
    methods: {
        getImageUrl (image) {
            return urlImage({ image });
        }
    }
};
</script>

<style scoped lang="scss">
.rank-container {
    /* Username styling */
    .username {
        font-size: 0.75rem;
        font-weight: bold;
        margin: 0;
        padding: 0;
        text-align: center;
        /* enforce single line with “…” if it’s too long */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* User ID styling */
    .user-id {
        font-size: 0.6rem;
        font-weight: normal;
        margin: 0;
        padding: 0;
        text-align: center;
        color: #ececec;
        /* enforce single line with “…” if it’s too long */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .placeholder-text {
        color: rgba(255, 255, 255, 0.5); /* lighter color for placeholder */
        font-style: italic;
    }

    .rank-img {
        z-index: 10;
    }

    .rank1-avatar,
    .rank2-avatar,
    .rank3-avatar {
        background-color: #E7F6F6;
        border-radius: 50%;
        display: block;
    }

    .rank1-avatar {
        border: 3px solid #ffcf13;
    }

    .rank2-avatar {
        border: 3px solid #9ea8ba;
    }

    .rank3-avatar {
        border: 3px solid #dd722a;
    }

    .rank1-bottom,
    .rank2-bottom,
    .rank3-bottom {
        width: 100%;
        border-radius: 5px 5px 0 0;
    }

    .rank1-bottom {
        background-color: #ffea95;
    }

    .rank2-bottom {
        background-color: #d5e4ff;
    }

    .rank3-bottom {
        background-color: #ffbe8e;
    }
}
</style>
