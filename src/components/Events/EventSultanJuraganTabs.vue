<template>
  <div class="juragan-sultan-tabs">
    <div
      v-for="tab in EVENT_SULTAN_JURAGAN_TABS"
      :key="tab.key"
      class="tab-item"
      :class="{ active: selected === tab.key }"
      :style="getTabBackground(tab.key)"
      @click="selected = tab.key">
    </div>
  </div>
</template>

<script>
import tabJuragan from '@/assets/img/events/tab_juragan.svg'
import tabJuraganSelected from '@/assets/img/events/tab_juragan_selected.svg'
import tabTopScorer from '@/assets/img/events/tab_top_scorer.svg'
import tabTopScorerSelected from '@/assets/img/events/tab_top_scorer_selected.svg'
import tabSultan from '@/assets/img/events/tab_sultan.svg'
import tabSultanSelected from '@/assets/img/events/tab_sultan_selected.svg'

export const EVENT_SULTAN_JURAGAN_KEYS = {
  JURAGAN: 'juragan',
  TOP_SCORER: 'top_scorer',
  SULTAN: 'sultan',
}

export const EVENT_SULTAN_JURAGAN_TABS = [
  { key: EVENT_SULTAN_JURAGAN_KEYS.JURAGAN },
  { key: EVENT_SULTAN_JURAGAN_KEYS.TOP_SCORER },
  { key: EVENT_SULTAN_JURAGAN_KEYS.SULTAN },
]

const EVENT_SULTAN_JURAGAN_TAB_BACKGROUNDS = {
  juragan: {
    default: tabJuragan,
    selected: tabJuraganSelected,
  },
  top_scorer: {
    default: tabTopScorer,
    selected: tabTopScorerSelected,
  },
  sultan: {
    default: tabSultan,
    selected: tabSultanSelected,
  }
}

export default {
  props: {
    modelValue: {
      type: String,
      required: true
    }
  },
  emits: ['update:modelValue'],
  mounted() {
    // Preload images
    Object.values(EVENT_SULTAN_JURAGAN_TAB_BACKGROUNDS).forEach(bg => {
      new Image().src = bg.default
      new Image().src = bg.selected
    })
  },
  data() {
    return {
      EVENT_SULTAN_JURAGAN_TABS
    }
  },
  computed: {
    selected: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val)
      }
    }
  },
  methods: {
    getTabBackground(key) {
      const bg = this.selected === key
        ? EVENT_SULTAN_JURAGAN_TAB_BACKGROUNDS[key].selected
        : EVENT_SULTAN_JURAGAN_TAB_BACKGROUNDS[key].default

      return {
        backgroundImage: `url('${bg}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    }
  },
}
</script>

<style scoped>
.juragan-sultan-tabs {
  display: flex;
  margin: 0 auto;
  border-radius: 10px;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  aspect-ratio: 116 / 26;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}

.tab-item.active {
  filter: brightness(1.05);
}
</style>
