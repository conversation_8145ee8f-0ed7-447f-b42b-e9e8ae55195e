<template>
  <div class="popup" v-if="isOpen" style="z-index: 10001;">
    <div class="popup-content">
      <div class="d-flex justify-content-between">
        <p class="font-24 font-bold">{{ $t('USERNAME.CHANGE_USERNAME') }}</p>
        <div class="btn-close" @click="close()">
          <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
        </div>
      </div>
      <div class="d-flex align-items-stretch mt-5">
        <div class="d-flex flex-column align-items-start flex-grow-1">
          <input class="username-input" v-model.trim="username" @keydown.space.prevent/>
        </div>
        <b-button id="btn_saveChangeUsername" @click="onClickSave" :disabled="!enableSave" class="bg-main-color color-white btn-edit ml-3"
                  style="padding: 4px 30px;">
          {{ $t("common.SAVE") }}
        </b-button>
      </div>
      <p class="d-flex align-items-center" v-if="error">
        <p class="username-note capitalize ml-1">{{ error }}</p>
      </p>
      <div class="d-flex flex-column align-items-start mt-5">
        <p class="font-semibold term-title">{{ $t('USERNAME.TERMS_OF_CHANGE') }}</p>
        <ul class="mt-2">
          <li>{{ $t('USERNAME.TERM_1') }}</li>
          <li>{{ $t('USERNAME.TERM_2') }}</li>
          <li>{{ $t('USERNAME.TERM_3') }}</li>
          <li>{{ $t('USERNAME.TERM_4') }}</li>
          <li>{{ $t('USERNAME.TERM_5') }}</li>
          <li>{{ $t('USERNAME.TERM_6') }}</li>
          <li>{{ $t('USERNAME.TERM_7') }}</li>
          <li>{{ $t('USERNAME.TERM_8') }}</li>
        </ul>
      </div>
    </div>
    <popup ref="popupConfirmChangeUsername" @on-positive-clicked="confirmedSave"></popup>
    <popup ref="popupConfirmExit" @on-positive-clicked="isOpen = false"></popup>
  </div>
</template>

<script>
import accountService from '../services/account.service';
import Popup from "./Popup.vue";

export default {
  components: {
    Popup,
  },
  data() {
    return {
      isOpen: false,
      username: '',
      error: null,
    }
  },

  emits: ['on-success'],

  methods: {
    openPopup(username) {
      this.isOpen = true
      this.username = username
      this.error = null
    },

    async onClickSave() {
      try {
        await accountService.canUseUsername({ username: this.username })
        this.$refs.popupConfirmChangeUsername.openPopup({
            title: this.$t('USERNAME.USERNAME_CAN_BE_USE', { value: this.username }),
            message: this.$t('USERNAME.ARE_YOU_SURE_TO_USE'),
            positiveButton: this.$t('account.YES'),
            negativeButton: this.$t('account.NO'),
            btnClass: "col-3",
          }
        );
        this.error = null
      } catch (e) {
        if (e.data && e.data.username && e.data.username.length) {
          this.error = e.data.username.join(', ')
        } else if (e.message) {
          this.error = e.message
        } else {
          this.error = e.toString()
        }
      }
    },

    async confirmedSave() {
      try {
        const res = await accountService.changeUsername({ username: this.username })
        if (res.data) {
          await this.$store.dispatch('refreshUserProfile');
          this.isOpen = false
          this.$emit("on-success")
        }
      } catch (e) {
        if (e.data && e.data.username && e.data.username.length) {
          this.error = e.data.username.join(', ')
        } else if (e.message) {
          this.error = e.message
        } else {
          this.error = e.toString()
        }
      }
    },

    close() {
      if (this.changedUsername) {
        this.$refs.popupConfirmExit.openPopup({
          title: this.$t('USERNAME.ARE_YOU_SURE_TO_LEAVE_THIS_PAGE'),
          message: this.$t('USERNAME.THE_CHANGES_NOT_BE_SAVED'),
          positiveButton: this.$t('account.YES'),
          negativeButton: this.$t('account.NO'),
          messageClass: "color-error",
          btnClass: "col-3",
        });
      } else {
        this.isOpen = false
      }
    },
  },

  computed: {
    userInfo() {
      return this.$store.getters.userProfile
    },

    changedUsername() {
      return this.username !== this.userInfo.username
    },

    enableSave() {
      return /(?=[a-zA-Z0-9])(^(?!.*admin|.*goro).*)$/i.test(this.username) && this.username.length >= 6 && this.username.length <= 14 && this.username !== this.userInfo.username
    },
  },
}
</script>

<style lang="scss" scoped>
.popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #006867;

  .popup-content {
    position: relative;
    background-color: #fff;
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    width: 95%;
    height: fit-content;
    margin: auto;

    @media screen and (min-width: 600px) {
      width: 70%;
    }

    @media screen and (min-width: 1100px) {
      width: 40%;
    }

    .term-title {
      font-size: 18px;
    }

    .username-input {
      width: 100%;
      background-color: #ebecf0;
      border: none;
      border-radius: 6px;
      padding: 8px 16px;
      color: var(--primary-color);
      min-height: 34px;
      font-size: 20px;
      font-weight: 700;
    }

    .username-note {
      color: #CD1400;
      font-weight: 600;
      font-size: 14px;
    }

    .exclamation-icon {
      color: #CD1400;
    }

    ul {
      list-style-type: square;
      font-size: 15px;
      text-align: left;
    }

    .btn-close {
      background-color: rgb(221, 221, 221);
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
}
</style>