<template>
    <div class="vb-invest-container" :class="{ 'show': showInvest }">
        <b-button id="btn_virtualBalanceInvest_Close" v-if="showInvest" class="close-btn" @click="onClose">x</b-button>
        <b-container v-if="!confirmBuy">
            <b-row align-h="center" align-v="center">
                <b-col cols="12" lg="3" xl="3" class="content-left text-center">
                    <p class="font-18">{{ $t("PAYMENT.TOKEN_QUANTITY") }}</p>
                    <b-row class="mt-1 mb-2" align-h="center">
                        <VueNumericInput class="custom" @change="onTokenQuantityChanged" :value="quantity"
                            :min="property.min_purchase_token >= 1 ? property.min_purchase_token : 1" :max="availableTokens"
                            :step="1" align="center" size="normal" />
                    </b-row>
                    <p>
                        {{ $t("PAYMENT.CURRENCY") }}{{ getFormattedCurrency(property.price_per_token) }} / {{
                            $t("PAYMENT.TOKEN")
                        }}
                    </p>
                    <p class="font-14 color-gray">{{ formatNumberIntl(availableTokens) }} {{ $t("common.AVAILABLE") }}</p>
                </b-col>
                <b-col cols="12" lg="4" xl="4" class="content-right d-flex flex-column justify-content-between">
                    <div>
                        <p class="font-24 font-weight-bold">{{ $t("PAYMENT.ORDER_SUMMARY") }}</p>
                        <hr class="solid">
                        <b-row align-h="between">
                            <b-col cols="6">
                                <p>{{ $t("VIRTUAL_BALANCE.YOUR_VIRTUAL_BALANCE") }}</p>
                            </b-col>
                            <b-col cols="6">
                                <p class="text-right">{{ $t("PAYMENT.CURRENCY") }}{{ getFormattedCurrency(initialBalance)
                                }}</p>
                            </b-col>
                        </b-row>
                        <hr class="solid">
                        <b-row align-h="between">
                            <b-col cols="6">
                                <p>{{ $t("PAYMENT.ORDER_TOTAL") }}</p>
                            </b-col>
                            <b-col cols="6">
                                <p class="text-right">{{ $t("PAYMENT.CURRENCY") }}{{ getFormattedCurrency(orderTotal)  }}
                                </p>
                            </b-col>
                        </b-row>
                        <hr v-if="!isPresale" class="solid">
                        <p v-if="!isPresale" class="next-expected-payout-date mt-4">
                          {{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE") }} {{nextExpectedPayoutDate }}
                        </p>
                    </div>
                    <div>
                        <b-button id="btn_virtualBalanceInvest_ContinueToBy" :disabled="orderTotal > initialBalance" class="btn-main col-12 mt-2 mb-1" variant="none"
                            @click="continueToPayment">
                            {{ $t("VIRTUAL_BALANCE.CONTINUE_TO_BUY") }}
                        </b-button>
                    </div>
                </b-col>
            </b-row>
        </b-container>
        <b-container v-if="confirmBuy">
            <b-row align-h="center">
                <b-col cols="12" lg="7" xl="7">
                    <b-row align-content="center" class="mb-3">
                        <b-button id="btn_virtualBalanceInvest_Back" style="border: none; background-color: transparent; color: black"
                            @click.prevent="confirmBuy = false">
                            <b-icon icon="arrow-left" class="pr-1 pt-1" style="color: black;" scale="0.8"></b-icon>
                            {{ $t("common.BACK") }}
                        </b-button>
                        <p class="font-24 font-weight-bold text-center" style="width:80%;">{{
                            $t("VIRTUAL_BALANCE.ORDER_CONFIRMATION")
                        }}</p>
                    </b-row>
                    <hr class="solid">
                    <b-row align-h="between">
                        <b-col cols="6">
                            <p class="font-18 font-weight-bold">{{ property.name }}</p>
                            <p>{{ formatNumberIntl(quantity) }} {{ quantity > 1 ? $t("PAYMENT.TOKENS") : $t("PAYMENT.TOKEN") }}</p>
                        </b-col>
                        <b-col cols="5">
                            <p class="text-right">
                                {{ $t("PAYMENT.CURRENCY") }}{{ getFormattedCurrency(property.price_per_token) }}/{{
                                    $t("PAYMENT.EACH")
                                }}
                            </p>
                        </b-col>
                    </b-row>
                    <hr class="solid">
                    <b-row align-h="between">
                        <b-col cols="6">
                            <p class="font-16">{{ $t("PAYMENT.ORDER_TOTAL") }}</p>
                        </b-col>
                        <b-col cols="4">
                            <p class="text-right font-16">
                                {{ $t("PAYMENT.CURRENCY") }}{{ getFormattedCurrency(orderTotal) }}
                            </p>
                        </b-col>
                    </b-row>
                    <hr class="solid">
                    <b-row align-h="between" class="mb-4">
                        <b-col cols="6">
                            <p class="font-16">{{ $t("VIRTUAL_BALANCE.YOUR_VIRTUAL_BALANCE") }}</p>
                        </b-col>
                        <b-col cols="4">
                            <p class="text-right font-16">
                                {{ $t("PAYMENT.CURRENCY") }}{{ getFormattedCurrency(initialBalanceLeft) }}
                            </p>
                        </b-col>
                    </b-row>
                    <b-row align-h="center">
                        <b-col cols="6">
                            <b-button id="btn_virtualBalanceInvest_BuyNow" class="btn-main col-12 mt-2" variant="none" @click.prevent="buyNow">
                                {{ $t("VIRTUAL_BALANCE.BUY_NOW") }}
                            </b-button>
                        </b-col>
                    </b-row>
                </b-col>
            </b-row>
        </b-container>
    </div>
</template>
<script>

import VueNumericInput from "./VueNumericInput"
import { formatNumberIntl, getDeviceUUID, urlImage } from "@/helpers/common"
import guestService from "@/services/guest.service"
import moment from "moment"

export default {
    components: {
        VueNumericInput,
    },

    props: {
        show: {
            type: Boolean,
            default: false,
        },
        property: {
            type: Object,
            default() {
                return null
            },
        },
    },

    emits: ['on-close'],

    data() {
        return {
            showInvest: false,
            quantity: 1,
            confirmBuy: false,
        }
    },

    methods: {
      formatNumberIntl,
        onClose() {
            this.showInvest = false
            this.confirmBuy = false
            this.quantity = 1
            this.$emit('on-close')
        },
        getAvatar(images) {
            if (images && images.length) {
                return urlImage(images[0])
            }
            return ""
        },
        onTokenQuantityChanged(newValue) {
            this.quantity = newValue;
        },
        continueToPayment() {
            this.confirmBuy = true
        },
        getFormattedCurrency(value) {
            return formatNumberIntl(value)
        },
        async buyNow() {
            const res = await guestService.buyTokens({
                uuid: getDeviceUUID(),
                num_of_tokens: this.quantity,
                property_id: this.property.id,
            })
            this.onClose()
            await store.dispatch('getInitialBalance');
            if (res && res.data) {
                await this.$router.push({ name: "buySuccess", query: { trx_id: res.data.secondary_id } })
            }
        },
    },

    watch: {
        show(value) {
            this.showInvest = value
        },
    },

    computed: {
        availableTokens() {
            return this.property.total_tokens - this.property.display_sold_tokens
        },

        orderTotal() {
            return this.property.price_per_token * this.quantity
        },

        initialBalance() {
            return store.getters.initialBalance || 0
        },

        initialBalanceLeft() {
            return this.initialBalance - this.orderTotal
        },
        nextExpectedPayoutDate() {
            const expectedPayoutDate = process.env.VUE_APP_EXPECTED_PAYOUT_DATE
            return moment().set("date", expectedPayoutDate).add(1, "M").format("MMM DD, YYYY")
        },
        isPresale() {
            return this.property && this.property.status === 'presale'
        }
    },
}
</script>
<style lang="scss">
.vb-invest-container {
    position: fixed;
    bottom: -100%;
    width: 100%;
    z-index: 1001;
    background-color: white;
    border-top: 2px solid var(--primary-color);
    transition: 0.3s ease;
    display: none;

    &.show {
        bottom: 0;
        display: block;
    }

    .close-btn {
        position: absolute;
        right: 0;
        top: -35px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 10px 10px 0 0;
        padding: 5px 20px 5px 20px;
        border: none;

        &:hover {
            background-color: var(--primary-darker-color);
        }
    }

    .content-left {
        padding: 20px;
    }

    .content-right {
        background-color: rgba(7, 55, 99, 0.16);
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 8px;
        padding: 20px;
    }

    .solid {
        height: 1px;
        width: 100%;
        background-color: var(--primary-color);
    }

  .next-expected-payout-date {
    padding: 5px 6px;
    color: white;
    background: darkorange;
    border-radius: 16px;
    font-size: 13px;
    font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
    text-align: center;
  }
}
</style>
