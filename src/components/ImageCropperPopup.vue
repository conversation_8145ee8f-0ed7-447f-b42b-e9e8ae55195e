<!-- Popup.vue -->
<template>
    <div class="popup" v-if="isOpen" style="z-index: 9999;">
        <div class="popup-content">
            <p class="font-24 font-bolder mb-3">{{ $t('account.PHOTO_CROPPER') }}</p>
            <VuePictureCropper ref="pictureCropper" :boxStyle="boxStyle" :img="file" :options="options"
                :presetMode="resetMode" />
            <b-row class="justify-content-center mt-4">
                <b-button id="btn_uploadImage_Done" class="btn-main ml-1 mr-1 col-5" variant="none" @click="onDoneClicked">
                    {{ $t("common.UPLOAD") }}
                </b-button>
                <b-button id="btn_uploadImage_Cancel" class="btn-outline-cancel col-5 ml-1 mr-1" variant="none" @click="onCancelClicked">
                    {{ $t("common.CANCEL") }}
                </b-button>
            </b-row>
        </div>
    </div>
</template>

<script>

import VuePictureCropper, { cropper } from 'vue-picture-cropper'

export default {
    components: {
        VuePictureCropper,
    },
    data() {
        return {
            isOpen: false,
            file: null,
            boxStyle: {
                width: '100%',
                height: '100%',
                backgroundColor: '#f8f8f8',
                margin: 'auto',
            },
            options: {
                viewMode: 1,
                dragMode: 'move',
                aspectRatio: 1,
                cropBoxResizable: false,
            },
            resetMode: {
                mode: 'fixedSize',
                width: 320,
                height: 320,
            },
        }
    },
    methods: {
        openPopup(file) {
            this.isOpen = true
            this.file = file
        },
        onDoneClicked() {
            const croppedFile = cropper.getDataURL({
                width: 320,
                height: 320,
            })
            this.$emit("on-done", croppedFile)
            this.isOpen = false
        },
        onCancelClicked() {
            this.$emit("on-cancel")
            this.isOpen = false
        },
    }
}
</script>

<style scoped lang="scss">
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: scroll;
}

.popup-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    max-width: 60%;
    min-width: 40%;
    text-align: center;

    @media only screen and (max-width: 650px) {
        max-width: 90%;
        min-width: 70%;
    }
}
</style>
