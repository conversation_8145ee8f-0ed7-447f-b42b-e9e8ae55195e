<template>
    <b-card no-body class="financials p-0 pb-2">
        <b-card-header class="px-2 px-md-4">
            <h5 class="title main-color">{{ title }}</h5>
        </b-card-header>
        <b-card-body class="px-2 px-md-4">
            <b-list-group flush class="d-flex">
                <b-list-group-item v-for="(item, index) in items" v-bind:key="item.id">
                    <div class="divider"></div>
                    <b-row class="align-items-start align-items-xl-center pt-3 pb-3 px-0 px-xl-2 mx-0">
                        <b-col class="align-items-center px-0">
                            <p class="font-weight-bold title">{{ isID ? item.key_id_locale : item.key
                            }}:
                            </p>
                        </b-col>
                        <b-col class="align-items-center px-0 mr-0 mr-xl-3">
                            <div v-if="showEst" class="d-flex flex-column flex-lg-row align-items-end">
                                <div class="d-flex flex-row">
                                    <p class="value">IDR</p>
                                    <p class="mr-0 amount-block text-right value">{{ convertValue(item.subheader,
                                        item.value) }}</p>
                                </div>
                                <p class="mr-0 amount-block ml-4 text-right text-lg-left value">{{
                                    getEstValue(item.value) }}
                                </p>
                            </div>
                            <p v-else class="mr-0 ml-0 ml-lg-5 text-right text-lg-left value">{{
                                convertValue(item.subheader,
                                    item.value) }}</p>
                        </b-col>
                    </b-row>
                </b-list-group-item>
            </b-list-group>
        </b-card-body>
    </b-card>
</template>
<script>

import { FINANCIAL_SUBHEADER } from "../constants/constants"
import { numberWithCommas, exchange } from "../helpers/common"
import i18n from "../i18n"

export default {
    props: {
        items: {
            type: Array,
            default() {
                return []
            }
        },
        showEst: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        }
    },

    data() {
        return {
            showAll: false,
        }
    },

    methods: {
        convertValue(subheader, value) {
            if (subheader === FINANCIAL_SUBHEADER.ASSET_VALUE) {
                const locale = i18n.global.locale.value
                const separator = locale === 'id' ? '.' : ','
                const newValue = numberWithCommas(value, separator)
                return `${newValue}`
            }
            if (subheader === FINANCIAL_SUBHEADER.ANNUAL_RETURN) {
                return `${value}%`
            }
            return value
        },
        getEstValue(value) {
            const exchangedValue = exchange(value, 100, false, 'USD')

            return ` (${exchangedValue})`
        },
    },

    computed: {
        isID() {
            return this.$i18n.locale === 'id'
        },
    },
}
</script>
<style lang="scss" scoped>
.financials {
    .divider {
        width: auto;
        height: 0.8px;
        background-color: #000000;
        opacity: 0.1;
    }

    .card-body {
        padding: 0;
    }

    .card {
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
        border-radius: 10px;
    }

    .amount-block {
        width: 130px;
    }

    .title {
        color: #006867;
    }

    .value {
        color: #161616;
    }
}
</style>
