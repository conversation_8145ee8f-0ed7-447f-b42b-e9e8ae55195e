<template>
    <div class="popup" v-if="isOpen" style="z-index: 10001;">
        <div class="popup-content d-flex flex-column">
            <div class="d-flex justify-content-end">
                <div class="btn-close" @click="close()">
                    <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
                </div>
            </div>
            <div v-if="!isTurnOfConfirm" class="p-0 text-left flex-grow-1 d-flex flex-column">
                <p class="font-weight-bold font-18">{{ $t('TWO_FA.BACKUP_CODES') }}</p>
                <p class="mt-2">{{ enabledRecoveryCodes ? $t('TWO_FA.RECOVERY_CODES_DESC') :
        $t('TWO_FA.ENABLE_RECOVERY_CODES_DESC') }}</p>
                <p v-if="enabledRecoveryCodes" class="text-copy mt-4 p-0"><span class="text-btn" @click="downloadtxt">{{
        $t('TWO_FA.DOWNLOAD_AS_TXT') }}</span> - <span class="text-btn" @click="copyCodes">{{
        $t('TWO_FA.COPY_CODES') }}</span></p>
                <div v-if="enabledRecoveryCodes" class="d-flex mt-3">
                    <div class="d-flex flex-row flex-wrap col-12 p-0">
                        <p v-for="(code, index) in recoveryCodes" class="col-6 font-weight-bold font-22 pl-0 pr-2 mb-2">{{ getNumber(index) }}. {{ code }}</p>
                    </div>
                </div>
                <p v-if="enabledRecoveryCodes" class="text-copy text-btn mt-4" @click="generateNewCodes">{{
        $t('TWO_FA.GET_NEW_CODES') }}</p>
                <div class="flex-grow-1">
                </div>
                <!-- <b-button class="btn-main mt-5 col-12 btn-submit" variant="none" @click="turnOnOff()">
                    {{ enabledRecoveryCodes ? $t("TWO_FA.TURN_OFF") : $t("TWO_FA.TURN_ON") }}
                </b-button> -->
            </div>
            <!-- <div v-else class="p-3 text-left flex-grow-1 d-flex flex-column">
                <p class="font-weight-bold font-18">{{ $t('TWO_FA.TURN_OFF_BACKUP_CODES') }}</p>
                <p class="mt-2">{{ $t('TWO_FA.TURN_OFF_BACKUP_CODES_DESC') }}</p>
                <div class="flex-grow-1">
                </div>
                <b-button class="btn-outline-main mt-5 col-12 btn-submit" variant="none" @click="turnOff()">
                    {{ $t("TWO_FA.TURN_OFF") }}
                </b-button>
                <b-button class="btn-main mt-2 col-12 btn-submit" variant="none" @click="cancelTurnOff()">
                    {{ $t("common.CANCEL") }}
                </b-button>
            </div> -->
        </div>
    </div>
</template>

<script>

import twofaService from '../services/twofa.service'
import { notify } from "@/helpers/common"
import fileSaver from 'file-saver'

export default {
    data() {
        return {
            isOpen: false,
            recoveryCodes: [],
            isTurnOfConfirm: false,
        }
    },

    emits: ["on-success"],

    mounted() {

    },

    watch: {

    },

    methods: {
        openPopup(turnOffConfirm = false) {
            this.isOpen = true
            this.isTurnOfConfirm = turnOffConfirm
            this.getRecoveryCodes()
        },

        close() {
            this.isOpen = false
            this.isTurnOfConfirm = false
            this.$emit("on-success")
        },

        async getRecoveryCodes() {
            const res = await twofaService.getRecoveryCodes()
            if (res && res.data) {
                this.recoveryCodes = res.data
            }
        },

        async generateNewCodes() {
            const res = await twofaService.generateRecoveryCodes()
            if (res && res.data) {
                this.recoveryCodes = res.data
            }
        },

        async turnOnOff() {
            if (this.enabledRecoveryCodes) {
                this.isTurnOfConfirm = true
            } else {
                this.turnOn()
            }
        },

        async turnOn() {
            const res = await twofaService.updateStatus({ enabled: true })
            if (res && res.data) {
                this.recoveryCodes = res.data
            }
        },

        async turnOff() {
            const res = await twofaService.updateStatus({ enabled: false })
            if (res) {
                this.recoveryCodes = []
                this.isTurnOfConfirm = false
            }
        },

        copyCodes() {
            if (this.recoveryCodes.length) {
                navigator.clipboard.writeText(this.recoveryCodes.join(' '));
                notify({ text: this.$t('common.COPIED') });
            }
        },

        downloadtxt() {
            if (this.recoveryCodes.length) {
                const content = this.recoveryCodes.map((e, i) => `${this.getNumber(i)}. ${e}`).reduce(function (result, value, index, array) {
                    if (index % 2 === 0) {
                        result.push(array.slice(index, index + 2).join('\t'));
                    }
                    return result;
                }, []).join('\n')
                const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
                fileSaver.saveAs(blob, "GORO Two-Factor Authentication Recovery Codes.txt");
            }
        },

        cancelTurnOff() {
            this.isTurnOfConfirm = false
        },

        getNumber(index) {
            const numbers = [1, 6, 2, 7, 3, 8, 4, 9, 5, 10]
            return numbers[index]
        },
    },

    computed: {
        enabledRecoveryCodes() {
            return this.recoveryCodes.length
        }
    },

}
</script>

<style lang="scss" scoped>
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    color: black;
    overflow: auto;

    .popup-content {
        position: relative;
        background-color: #fff;
        padding: 30px;
        border-radius: 20px;
        text-align: center;
        min-width: 30%;
        max-width: 95%;
        height: fit-content;
        margin: auto;

        @media screen and (min-width: 600px) {
            max-width: 70%;
        }

        @media screen and (min-width: 1100px) {
            max-width: 50%;
        }

        .btn-close {
            background-color: rgb(221, 221, 221);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

        .btn-submit {
            max-height: 40px;
        }

        .text-copy {
            color: blue;
        }

        .text-btn {
            cursor: pointer;
        }
    }
}
</style>