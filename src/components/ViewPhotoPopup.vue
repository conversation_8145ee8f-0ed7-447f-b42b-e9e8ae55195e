<!-- Popup.vue -->
<template>
    <div class="popup" v-if="isOpen" style="z-index: 9999;">
        <div class="popup-content">
            <img :src="photoUrl" alt="" />
            <b-row class="justify-content-center mt-4">
                <b-button id="btn_closeViewPhotoPopup" class="btn-main ml-1 col-5" variant="none" @click="onCloseClicked">
                    {{ $t("common.CLOSE") }}
                </b-button>
            </b-row>
        </div>
    </div>
</template>

<script>

export default {
    data() {
        return {
            isOpen: false,
            photoUrl: null,
        }
    },
    methods: {
        openPopup(photoUrl) {
            this.isOpen = true
            this.photoUrl = photoUrl
        },
        onCloseClicked() {
            this.isOpen = false
        },
    }
}
</script>

<style scoped lang="scss">
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: scroll;
}

.popup-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    max-width: 60%;
    min-width: 40%;
    text-align: center;

    @media only screen and (max-width: 650px) {
        max-width: 90%;
        min-width: 70%;
    }

    img {
        width: 100%;
        height: 90%;
        object-fit: contain;
    }
}
</style>
