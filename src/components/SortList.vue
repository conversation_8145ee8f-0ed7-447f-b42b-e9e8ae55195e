<template>
    <div class="sort-container d-flex align-items-center" @click="toggleSort">
        <slot />
        <b-icon :class="isAsc ? 'icon-enabled' : 'icon-disabled'" icon="arrow-up" />
        <b-icon :class="isDesc ? 'icon-enabled' : 'icon-disabled'" icon="arrow-down" />
    </div>
</template>

<script>
export default {
    props: {
        sort: {
            type: String,
            default: null,
        },
    },

    emits: ['update:sort'],

    methods: {
        toggleSort() {
            const orderCycle = [null, 'asc', 'desc'];
            const currentIndex = orderCycle.indexOf(this.sort);
            const nextSort = orderCycle[(currentIndex + 1) % orderCycle.length];
            this.$emit('update:sort', nextSort);
        },
    },

    computed: {
        isAsc() {
            return this.sort === 'asc'
        },
        isDesc() {
            return this.sort === 'desc'
        },
    },
};
</script>

<style lang="scss" scoped>
.sort-container {
    cursor: pointer;
    height: 32px;

    b-icon {
        margin-left: 4px;
    }

    .icon-enabled {
        color: #000;
    }

    .icon-disabled {
        color: #BFC0C1;
    }
}
</style>
