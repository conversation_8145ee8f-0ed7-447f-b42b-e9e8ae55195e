<template>
  <router-link disabled
    :to="{ name: isFromAccount ? 'accountPropertyDetail' : 'propertyDetail', params: { uuid: property.uuid } }">
    <div class="property-card shadow">
      <img :class="{ darken: isSoldOut || isComingSoon }" :src="getAvatar(property.images)" alt="Property image">
      <div class="card-status">
        <p class="sold-out coming-soon" v-if="isComingSoon">{{ $t('PROPERTY_STATUS.COMING_SOON') }}</p>
        <p class="sold-out" v-else-if="isSoldOut">{{ $t('PROPERTY_STATUS.SOLD_OUT') }}</p>
        <p class="promo" v-if="property.status === 'promo' && !isSoldOut">{{ $t('PROPERTY_STATUS.PROMO') }}</p>
        <p class="timer" v-if="property.status === 'promo' && showPromo && !isSoldOut">{{ promoTime }}</p>
        <p class="presale" :class="{ 'presale-full': !showPresale }" v-if="property.status === 'presale' && !isSoldOut">{{
          $t('PROPERTY_STATUS.PRESALE') }}</p>
        <p class="timer" v-if="property.status === 'presale' && showPresale && !isSoldOut">{{ presaleTime }}</p>
      </div>
      <div class="card-info" :sold-out="isSoldOut || null">
        <p class="font-18 font-weight-bold m-0" :class="nameClass">{{ property.name }}</p>
        <span class="font-16" :class="descriptionClass">{{ property.metadata.address }}</span>
        <p class="card-separator font-14 pt-2 pb-1" :class="descriptionClass">
          <span class="space">{{ property.num_bed }} {{ $t("Bedrooms") }}</span>
          <span class="space">{{ property.num_bath }} {{ $t("Bathrooms") }}</span>
          <span>{{ property.sqm }} sqm</span>
        </p>
        <div class="card-separator d-flex flex-row">
          <div class="d-flex flex-row" :id="'tooltip-target-irr-card' + property.id">
            <div>
              <span class="font-22 font-weight-bold" :class="nameClass">{{ (property.ery || 0) + (property.eca || 0) }}%
                <span class="font-weight-bold m-0">{{ $t("propertyDetail.IRR") }}</span></span>
            </div>
            <div><img class="img-info" src="@/assets/img/info-circle-fill.png" alt=""></div>
          </div>
          <b-tooltip variant="secondary" :target="'tool' +
            'tip-target-irr-card' + property.id" triggers="hover" placement="top">
            {{ $t("propertyDetail.IRR_TOOLTIP") }}
          </b-tooltip>

          <div class="d-flex flex-row ml-5" :id="'tooltip-target-ery-card' + property.id">
            <div>
              <span class="font-22 font-weight-bold" :class="nameClass">{{ property.ery || 0 }}% <span
                  class="font-weight-bold m-0">{{ $t("propertyDetail.ERY") }}</span></span>
            </div>
            <div><img class="img-info" src="@/assets/img/info-circle-fill.png" alt=""></div>
          </div>
          <b-tooltip variant="secondary" :target="'tooltip-target-ery-card' + property.id" triggers="hover"
            placement="top">
            {{ $t("propertyDetail.ERY_TOOLTIP") }}
          </b-tooltip>
        </div>
        <div class="p-progress pt-2">
          <b-progress class="bg-color-progress mb-1" :max="100" :min="0" :precision="2" height="4px">
            <b-progress-bar :class="progressClass" :value="getProgress" />
          </b-progress>
          <span class="pb-2 font-weight-bold" :class="nameClass">{{ getProgress }}%</span>
          <span class="pb-2 font-weight-bold float-right" :class="nameClass">
            {{ formatNumberIntl(property.total_tokens - property.display_sold_tokens) }} {{ $t("propertyDetail.token left") }}
          </span>
        </div>
      </div>
      <div :class="{ disabled: isSoldOut || isComingSoon }"
        class="invest-now d-flex justify-content-center align-items-center">
        <p class="font-weight-bold">{{ $t("INVEST_NOW") }}</p>
      </div>
    </div>
  </router-link>
</template>

<script>
import { formatNumberIntl, formattedDuration, urlImage } from "@/helpers/common"
import moment from "moment"

export default {
  props: {
    property: {},
    isFromAccount: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      promoTime: '',
      presaleTime: '',
    };
  },
  mounted() {
    if ((this.property.status === 'presale' || this.property.status === 'promo') && (this.showPresale || this.showPromo)) {
      const self = this;
      setInterval(() => {
        self.updateTimer();
      }, 1000);
    }
  },
  methods: {
    formatNumberIntl,
    getAvatar(images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ''
    },
    updateTimer() {
      if (this.property.status === 'presale') {
        const presaleEndsAt = this.property.presale_ends_at;
        const currentDate = new Date();
        const presaleDate = new Date(presaleEndsAt);
        if (currentDate >= presaleDate) {
          this.presaleTime = this.$t('propertyDetail.ENDED');
        } else {
          const diffTime = presaleDate.getTime() - currentDate.getTime();
          this.presaleTime = this.$t('propertyDetail.END_IN', { value: formattedDuration(diffTime) });
        }
      } else if (this.property.status === 'promo') {
        const promoEndsAt = this.property.promo_ends_at;
        const currentDate = new Date();
        const promoDate = new Date(promoEndsAt);
        if (currentDate >= promoDate) {
          this.promoTime = this.$t('propertyDetail.ENDED');
        } else {
          const diffTime = promoDate.getTime() - currentDate.getTime();
          this.promoTime = this.$t('propertyDetail.END_IN', { value: formattedDuration(diffTime) });
        }
      }
    }
  },
  computed: {
    getProgress() {
      if (!this.property.total_tokens) {
        return 0;
      }
      return Math.floor(this.property.display_sold_tokens * 100 / this.property.total_tokens);
    },
    isSoldOut() {
      return this.property.status === "sold" || this.property.display_sold_tokens === this.property.total_tokens
    },
    isComingSoon() {
      return this.property.status === 'coming_soon'
    },
    showPresale() {
      const presaleEndsAt = this.property.presale_ends_at;
      const currentDate = moment(new Date()).add(1, 'days');
      const presaleDate = new Date(presaleEndsAt);
      return currentDate > presaleDate;
    },
    showPromo() {
      const promoEndsAt = this.property.promo_ends_at;
      const currentDate = moment(new Date()).add(1, 'days');
      const promoDate = new Date(promoEndsAt);
      return currentDate > promoDate;
    },
    nameClass() {
      return {
        'color-white': !this.isSoldOut,
        'color-sold-out': this.isSoldOut,
      };
    },
    descriptionClass() {
      return {
        'color-description': !this.isSoldOut,
        'color-description-sold-out': this.isSoldOut,
      };
    },
    progressClass() {
      return {
        'bg-color-white': !this.isSoldOut,
        'bg-color-sold-out': this.isSoldOut,
      };
    },
  }
};
</script>
<style lang="scss" scoped>
.property-card {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  height: 465px;
  word-wrap: break-word;
  background-clip: border-box;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: .8s;
  background: var(--primary-color);

  &:hover {
    background: var(--primary-darker-color);
  }

  &:hover .card-info {
    background: var(--primary-darker-color);
  }

  img {
    width: 98%;
    height: 56%;
    margin-left: 1%;
    margin-right: 1%;
    margin-top: 1%;
    object-fit: cover;
    position: absolute;
    border: 0 transparent;
    border-radius: 14px;
    background-color: var(--primary-background-color);
  }

  .darken {
    filter: brightness(70%);
  }

  .card-separator {
    .space {
      margin-right: 10px;
    }

    .img-info {
      width: 14px;
      height: 14px;
      background-color: transparent;
      z-index: 1;
    }
  }

  .card-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    transition: .8s;
    background: var(--primary-color);
    padding: 0.5rem 1.25rem 2.7rem 1.25rem;

    .color-white {
      color: white;
    }

    .color-description {
      color: white;
    }

    .color-sold-out {
      color: #BFC0C1;
    }

    .color-description-sold-out {
      color: #A9ACAF;
    }

    .bg-color-white {
      background-color: #ffffff;
    }

    .bg-color-sold-out {
      background-color: #BFC0C1;
    }

    .bg-color-progress {
      background-color: var(--primary-lighter-color);
    }
  }

  .invest-now {
    position: absolute;
    bottom: 3px;
    left: 3px;
    right: 3px;
    height: 35px;
    border-radius: 0 0 12px 12px;
    color: var(--primary-darker-color);
    background-color: white;
    text-align: center;
    vertical-align: middle;
    transition: .8s;

    &.disabled {
      background-color: #769B9D;
    }

    &:hover {
      background-color: var(--primary-background-hover-color);
    }
  }

  h4 {
    margin-top: 0;
    margin-bottom: 0.75rem;
  }

  .card-status {
    position: absolute;
    display: -ms-flexbox;
    display: flex;
    top: 4%;
    left: 4%;
    -ms-flex-direction: row;
    flex-direction: row;

    p {
      padding: 0.5rem 1rem 0.5rem 1rem;
      font-weight: 600;
    }

    .sold-out {
      background-color: var(--primary-color);
      color: white;
      font-size: 13px;
      border-radius: 18px;
      border: white solid 2px;
      padding: 4px 15px 4px 15px;
    }

    .coming-soon {
      background-color: var(--primary-lighter-color);
    }

    .promo {
      background-color: #DC3545;
      color: #ffffff;
      font-size: 13px;
      border-radius: 18px;
      border: white solid 2px;
      padding: 4px 15px 4px 15px;
    }

    .presale {
      background-color: #F7B733;
      color: #ffffff;
      border-radius: 18px 0 0 18px;
      font-size: 13px;
      border: white solid 2px;
      padding: 4px 15px 4px 15px;
    }

    .presale-full {
      border-radius: 18px;
    }

    .timer {
      background-color: #ffffff;
      color: #54595E;
      border-radius: 0 18px 18px 0;
      min-width: 150px;
      font-size: 13px;
      text-align: center;
      padding: 6px 0 4px 0;
    }
  }
}
</style>
