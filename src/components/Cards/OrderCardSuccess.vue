<template>
  <div class="cls-order-card-wrapper">
    <div class="cls-order-card d-flex flex-column justify-content-start align-items-center">
      <div class="cls-thumb">
        <b-img :src="getAvatar(property.images)" fluid blank-src="property image" width="100%" />
      </div>
    </div>
  </div>
</template>
<script>
import { urlImage } from "@/helpers/common"

export default {
  name: "OrderCard",
  props: {
    property: {},
    payment: {},
    transaction: null
  },
  methods: {
    getAvatar(images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    openGoogleMap(property) {
      if (property.map_link) {
        window.open(property.map_link, "_blank")
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.cls-order-card-wrapper{
  background-color: #fff;
  border-radius: 13px;
  padding: 20px;
  @media(max-width: 992px) {
    padding: 15px;
  }
  .cls-thumb{
    border-radius: 7px;
    overflow: hidden;
    width: 100%;
    @media(max-width: 768px) {
      margin-right: 0;
      margin-bottom: 15px;
    }
    img{
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }
  .cls-purchased-info{
    flex-grow: 1;
    text-align: left;
    .content-item{
      padding-bottom: 8px;
      label{
        font-size: 14px;
        font-weight: 382;
        line-height: 16.8px;
        text-align: left;
        color: #A2A2A2;
        margin-bottom: 2px;
      }
      p{
        font-size: 16px;
        font-weight: 600;
        line-height: 19.2px;
        text-align: left;
        color: #6D6D6D;
      }
    }
    
  }
}
</style>
