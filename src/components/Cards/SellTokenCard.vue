<template>
  <div class="cls-order-card-wrapper">
    <div class="cls-order-card d-flex flex-column flex-lg-row justify-content-start align-items-center">
      <div class="cls-thumb">
        <b-img :src="getAvatar(property.images)" fluid blank-src="property image" width="120" height="120" />
      </div>
      <div class="cls-purchased-info">
        <b-row>
          <b-col v-if="sellTokenRequest" cols="12" class="content-item">
            <label>
              {{ $t("TRANSACTION.EXTERNAL_ID") }}
            </label>
            <p>
              {{ sellTokenRequest.uuid }}
            </p>
          </b-col>
        </b-row>
        <b-row>
          <b-col cols="12" lg="4" class="content-item">
            <label>
              {{ $t("ORDER.PROPERTY_NAME") }}
            </label>
            <p>
              {{ property.name }}
            </p>
          </b-col>
          <b-col cols="6" lg="4" class="content-item">
            <label>
              {{ $t("Location") }}
            </label>
            <p @click="openGoogleMap(property)">
              {{ property.metadata.address }}
            </p>
          </b-col>
          <b-col cols="6" lg="4" class="content-item">
            <label>
              {{ $t("SELL_TOKEN.TOKEN_SOLD") }}
            </label>
            <p>
              {{ formatNumberIntl(sellTokenRequest.num_of_tokens) }}
            </p>
          </b-col>
        </b-row>
      </div>
    </div>
  </div>
</template>
<script>
import { formatNumberIntl, urlImage } from "@/helpers/common"

export default {
  name: "OrderCard",
  props: {
    property: {},
    sellTokenRequest: null
  },
  methods: {
    formatNumberIntl,
    getAvatar(images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    openGoogleMap(property) {
      if (property.map_link) {
        window.open(property.map_link, "_blank")
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.cls-order-card-wrapper{
  background-color: #fff;
  border-radius: 13px;
  padding: 20px;
  .cls-thumb{
    width: 105px;
    height: 105px;
    border-radius: 7px;
    overflow: hidden;
    margin-right: 20px;
    @media(max-width: 768px) {
      width: 100%;
      height: 150px;
      margin-right: 0;
      margin-bottom: 15px;
    }
    img{
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }
  .cls-purchased-info{
    flex-grow: 1;
    text-align: left;
    .content-item{
      padding-bottom: 8px;
      label{
        font-size: 14px;
        font-weight: 382;
        line-height: 16.8px;
        text-align: left;
        color: #A2A2A2;
        margin-bottom: 2px;
      }
      p{
        font-size: 16px;
        font-weight: 600;
        line-height: 19.2px;
        text-align: left;
        color: #6D6D6D;
      }
    }
    
  }
}
</style>
