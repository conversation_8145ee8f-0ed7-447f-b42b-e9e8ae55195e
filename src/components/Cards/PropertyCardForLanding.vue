<template>
  <div class="property-card shadow" @click="onClicked">
    <img :src="getAvatar(property.images)" alt="Property image">
    <div class="card-info">
      <p class="font-18 font-weight-bold m-0" :class="nameClass">{{ property.name }}</p>
      <span class="font-16" :class="descriptionClass">{{ property.metadata.address }}</span>
      <p class="card-separator font-14 pt-2 pb-1" :class="descriptionClass">
        <span class="space">{{ property.num_bed }} {{ $t("Bedrooms") }}</span>
        <span class="space">{{ property.num_bath }} {{ $t("Bathrooms") }}</span>
        <span>{{ property.sqm }} sqm</span>
      </p>
      <div v-if="showIrrEry" class="card-separator d-flex flex-row">
        <div class="d-flex flex-row" :id="'tooltip-target-irr-card' + property.id">
          <div>
                    <span class="font-20 font-weight-bold"
                          :class="nameClass">{{ (property.ery || 0) + (property.eca || 0) }}%
                      <span class="font-weight-bold m-0">{{ $t("propertyDetail.IRR") }}</span></span>
          </div>
          <div><img class="img-info" src="@/assets/img/info-circle-fill.png" alt=""></div>
        </div>
        <b-tooltip variant="secondary" :target="'tool' +
                  'tip-target-irr-card' + property.id" triggers="hover" placement="top">
          {{ $t("propertyDetail.IRR_TOOLTIP") }}
        </b-tooltip>

        <div class="d-flex flex-row ml-5" :id="'tooltip-target-ery-card' + property.id">
          <div>
                    <span class="font-20 font-weight-bold" :class="nameClass">{{ property.ery || 0 }}% <span
                      class="font-weight-bold m-0">{{ $t("propertyDetail.ERY") }}</span></span>
          </div>
          <div><img class="img-info" src="@/assets/img/info-circle-fill.png" alt=""></div>
        </div>
        <b-tooltip variant="secondary" :target="'tooltip-target-ery-card' + property.id" triggers="hover"
                   placement="top">
          {{ $t("propertyDetail.ERY_TOOLTIP") }}
        </b-tooltip>
      </div>
      <div class="p-progress pt-2">
        <b-progress class="bg-color-progress mb-1" :max="100" :min="0" :precision="2" height="4px">
          <b-progress-bar :class="progressClass" :value="getProgress"/>
        </b-progress>
        <span class="pb-2 font-weight-bold" :class="nameClass">{{ getProgress }}%</span>
        <span class="pb-2 font-weight-bold float-right" :class="nameClass">
            {{ formatNumberIntl(property.total_tokens - property.display_sold_tokens) }} {{ $t("propertyDetail.token left") }}
          </span>
      </div>
    </div>
    <div class="invest-now d-flex justify-content-center align-items-center">
      <p class="font-weight-bold">{{ bottomButtonText }}</p>
    </div>
  </div>
</template>

<script>
import { formatNumberIntl, urlImage } from "@/helpers/common"

export default {
  props: {
    property: {},
    showIrrEry: false,
    bottomButtonText: '',
  },
  emits: ["on-clicked"],
  methods: {
    formatNumberIntl,
    getAvatar (images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    onClicked () {
      this.$emit("on-clicked")
    },
  },
  computed: {
    getProgress () {
      if (!this.property.total_tokens) {
        return 0
      }
      return Math.floor(this.property.display_sold_tokens * 100 / this.property.total_tokens)
    },
    nameClass () {
      return "color-white"
    },
    descriptionClass () {
      return "color-description"
    },
    progressClass () {
      return "bg-color-white"
    },
  }
}
</script>
<style lang="scss" scoped>
.property-card {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  height: 435px;
  word-wrap: break-word;
  background-clip: border-box;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: .8s;
  background: var(--primary-color);

  &:hover {
    background: var(--primary-darker-color);
  }

  &:hover .card-info {
    background: var(--primary-darker-color);
  }

  img {
    width: 98%;
    height: 56%;
    margin-left: 1%;
    margin-right: 1%;
    margin-top: 1%;
    object-fit: cover;
    position: absolute;
    border: 0 transparent;
    border-radius: 14px 14px 0 0;
    background-color: var(--primary-background-color);
  }

  .darken {
    filter: brightness(70%);
  }

  .card-separator {
    .space {
      margin-right: 10px;
    }

    .img-info {
      width: 14px;
      height: 14px;
      background-color: transparent;
      z-index: 1;
    }
  }

  .card-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    transition: .8s;
    background: var(--primary-color);
    padding: 0.5rem 1.25rem 2.7rem 1.25rem;

    .color-white {
      color: white;
    }

    .color-description {
      color: white;
    }

    .bg-color-white {
      background-color: #ffffff;
    }

    .bg-color-progress {
      background-color: var(--primary-lighter-color);
    }
  }

  .invest-now {
    position: absolute;
    bottom: 3px;
    left: 3px;
    right: 3px;
    height: 35px;
    border-radius: 0 0 12px 12px;
    color: var(--primary-darker-color);
    background-color: white;
    text-align: center;
    vertical-align: middle;
    transition: .8s;

    &.disabled {
      background-color: #769B9D;
    }
  }

  h4 {
    margin-top: 0;
    margin-bottom: 0.75rem;
  }
}
</style>
