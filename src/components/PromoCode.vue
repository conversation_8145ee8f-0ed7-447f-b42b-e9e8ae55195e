<template>
  <div class="d-flex align-items-center w-100 promo-code" :class="{ 'applied': voucherCode }">
    <img v-if="voucherCode" height="30" style="margin-right: 10px" :src="require(`@/assets/img/icons/discount.svg`)" alt=""/>
    <div class="d-flex flex-column align-items-start flex-grow-1">
      <p :class="{ 'font-semibold': boldTitle }" :style="{ color: voucherCode ? titleColorApplied : titleColor, fontSize: titleFontSize }">
        {{ !voucherCode ? $t("VOUCHER.PROMO_CODE") : voucherCodeInput }}
      </p>
      <div v-if="!voucherCode" class="voucher-input-wrapper mt-1 w-100">
        <input type="text"
          class="form-control code-input"
          :class="{ error, disabled }"
          :placeholder="$t('VOUCHER.INPUT_PROMO_CODE')"
          autocomplete="off"
          spellcheck="false"
          :value="voucherCodeInput"
          @input="onInput"
        />
        <button class="btn btn-apply btn-input"
          :class="{ disabled }"
          :disabled="disabled"
          @click="$emit('on-apply')"
        >
          {{ $t('common.APPLY') }}
        </button>
      </div>
      <div v-else>
        <!--<p class="saved-amount"> -{{ exchangeValue(rewardAmount) }}</p>-->
        <p class="saved-amount" style="margin-top: -5px"> {{ rewardNote }}</p>
      </div>
      <p v-if="error" class="text-error font-14 mt-1">{{ error }}</p>
    </div>
    <div v-if="voucherCode" class="align-self-center ml-1">
      <button class="btn btn-apply ml-2" :class="{ 'disabled': disabled }" :disabled="disabled">
        {{ $t('VOUCHER.APPLIED') }}
      </button>
      <!--<img class="close" @click="$emit('on-clear-voucher')" src="@/assets/img/golearn/close.svg" alt=""/>-->
    </div>
  </div>
</template>

<script>

import { exchange } from "@/helpers/common"

export default {
  props: {
    error: {
      type: String,
      default: null,
    },
    voucherCodeInput: {
      type: String,
      default: '',
    },
    voucherCode: {
      type: Object,
      default: null,
    },
    rewardNote: {
      type: String,
      default: '',
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
    titleColor: {
      type: String,
      default: '#484848',
    },
    titleColorApplied: {
      type: String,
      default: '#006666',
    },
    titleFontSize: {
      type: String,
      default: '16px',
    },
    boldTitle: {
      type: Boolean,
      default: true,
    },
  },

  emits: ['on-apply', 'on-clear-voucher', 'update:modelValue'],

  methods: {
    onInput(event) {
      const value = event.target.value
      this.$emit('update:modelValue', value)
    },

    exchangeValue(value) {
      return exchange(value)
    },
  },

  computed: {
    disabled() {
      return !this.voucherCodeInput
    },
  },
}
</script>
<style lang="scss" scoped>
.promo-code {
  background: white;
  border: solid 1.5px #CECECE;
  padding: 16px;
  border-radius: 8px;
  //box-shadow: 0 1px 6px rgba(7, 55, 99, 0.16);

  .voucher-input-wrapper {
    position: relative;

    .code-input {
      padding-right: 100px;
      border: solid 1.5px #00918E;
      border-radius: 5px;
      min-height: 48px;
      color: #000;
      //background: #fafafa;

      &::placeholder {
        color: #CECECE;
      }

      &.error {
        border-color: red;
        color: red;
      }

      &.disabled {
        border-color: #CECECE;
      }
    }

    .btn-input {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      /* match your .btn-apply height */
      height: calc(1.5em + 0.75rem);
      padding: 8px 16px;
    }
  }

  .btn-apply {
    font-family: "AcuminVariableConcept", Helvetica, sans-serif;
    font-weight: 600;
    border: none;
    color: #ffffff;
    background: var(--primary-color);
    padding: 8px 16px;
    border-radius: 5px;
    font-size: 13px;
    cursor: pointer;

    &.disabled {
      background: #CECECE;
      color: gray;
    }
  }

  .saved-amount {
    color: gray;
  }

  .close {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .text-error {
    font-size: 14px;
    color: red !important;
  }
}
</style>
