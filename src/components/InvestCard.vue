<template>
    <div class="invest shadow text-center">
        <div class="progress__bar progress_padding text-left">
            <b-progress :max="100" :min="0" :precision="2" height="4px" class="mb-1">
                <b-progress-bar class="bg-main-color" :value="getProgress(property)" />
            </b-progress>
            <span class="pb-3 font-weight-bold main-color">{{ getProgress(property) }}%</span>
            <span class="pb-3 font-weight-bold color-gray float-right">
                {{ formatNumberIntl(availableTokens) }} / {{ formatNumberIntl(property.total_tokens) }} {{ $t("propertyDetail.token left") }}
            </span>
        </div>
        <div class="content_padding">
            <invest-annual :text="$t('propertyDetail.ERY_ANNUAL')" :tooltip="$t('propertyDetail.ERY_ANNUAL_TOOLTIP')"
                :tooltipId="`tooltip-target-1${num}`" :value="property.ery || 0" :is-ery="true" />

            <invest-annual :text="$t('propertyDetail.ECA_ANNUAL')" :tooltip="$t('propertyDetail.ECA_ANNUAL_TOOLTIP')"
                :tooltipId="`tooltip-target-2${num}`" :value="property.eca || 0" />

            <invest-annual class="mb-2" :text="$t('propertyDetail.IRR_ANNUAL')"
                :tooltip="$t('propertyDetail.IRR_ANNUAL_TOOLTIP')" :tooltipId="`tooltip-target-3${num}`"
                :value="(property.ery || 0) + (property.eca || 0)" />

            <b-button id="btn_detail_buyProperty" v-if="canBuyTokens" class="btn-main w-100" variant="none" @click.prevent="onInvestClicked">
                {{ $t("BUY_PROPERTY") }}
            </b-button>
            <div v-else class="disabled-button" :id="`tooltip-target-invest-disabled-${num}`">
              {{ $t("BUY_PROPERTY") }}
            </div>
            <b-tooltip v-if="!isUserFullyActive && isUserLoggedIn" class="mb-1" variant="secondary"
                       :target="`tooltip-target-invest-disabled-${num}`" triggers="hover" placement="top">
              {{ $t("propertyDetail.ACCOUNT_NOT_ACTIVE_TOOLTIP") }}
            </b-tooltip>

            <b-row>
              <b-col cols="6" class="pr-1">
                <b-button id="btn_detail_sellProperty" v-if="canSellTokens"
                          class="btn-outline-white w-100 mt-2" variant="none" @click.prevent="onSellClicked">
                  {{ $t("SELL_PROPERTY") }}
                </b-button>
                <div v-else class="disabled-button mt-2" style="cursor: pointer"
                     :id="`tooltip-target-sell-disabled-${num}`" @click.prevent="onSellClicked">
                  {{ $t("SELL_PROPERTY") }}
                </div>
                <b-tooltip v-if="!isUserFullyActive && isUserLoggedIn" class="mb-1" variant="secondary"
                           :target="`tooltip-target-sell-disabled-${num}`" triggers="hover" placement="top">
                  {{ $t("propertyDetail.ACCOUNT_NOT_ACTIVE_TOOLTIP") }}
                </b-tooltip>
                <b-tooltip v-if="isUserFullyActive && isUserLoggedIn && availableTokensForSell <= 0" variant="secondary"
                           class="mb-1" :target="`tooltip-target-sell-disabled-${num}`" triggers="hover"
                           placement="bottom">
                  {{ $t("SELL_TOKEN.NOT_HAVE_SELLABLE_TOKENS") }}
                </b-tooltip>
              </b-col>
              <b-col cols="6" class="pl-1">
                <b-button id="btn_detail_swapProperty" v-if="canSwapTokens"
                          class="btn-outline-main-darker w-100 mt-2" variant="none" @click.prevent="onSwapClicked">
                  {{ $t("SWAP_PROPERTY") }}
                </b-button>
                <div v-else class="disabled-button mt-2" style="cursor: pointer"
                     :id="`tooltip-target-swap-disabled-${num}`" @click.prevent="onSwapClicked">
                  {{ $t("SWAP_PROPERTY") }}
                </div>
                <b-tooltip v-if="!isUserFullyActive && isUserLoggedIn" class="mb-1" variant="secondary"
                           :target="`tooltip-target-swap-disabled-${num}`" triggers="hover" placement="top">
                  {{ $t("propertyDetail.ACCOUNT_NOT_ACTIVE_TOOLTIP") }}
                </b-tooltip>
                <b-tooltip v-if="isUserFullyActive && isUserLoggedIn && availableTokensForSwap <= 0" variant="secondary"
                           class="mb-1" :target="`tooltip-target-swap-disabled-${num}`" triggers="hover"
                           placement="bottom">
                  {{ $t("SWAP_TOKEN.NOT_HAVE_SWAPPABLE_TOKENS") }}
                </b-tooltip>
              </b-col>
            </b-row>

            <p :id="`min-invest-tooltip-${num}`" class="text-center color-gray" style="margin: 10px 0 8px">{{
                exchangeValue(property.price_per_token) }}
                {{ $t("propertyDetail.minimum") }}</p>
            <CurrencyTooltip :tooltipId="`min-invest-tooltip-${num}`" :value="property.price_per_token">
            </CurrencyTooltip>
        </div>
        <b-modal v-model="showPresaleModal" id="modal-prevent-closing" modal-class="goro-general-fe-side-modal" centered header-class="has-icon">
            <template #modal-header>
              <div class="icon-header info presale">
                <img width="210" height="154" src="@/assets/img/icons/modal_presale.png"/>
              </div>
              <h5 class="modal-header-title mb-0 font-24 font-bold">
                {{ $t('propertyDetail.PROPERTY_PRESALE') }}
              </h5>
            </template>
            <p class="font-20">{{ $t('propertyDetail.PRESALE_NOTE', { value: firstLiveOn }) }}</p>
            <template #modal-footer="{ ok, cancel, hide }">
                <b-button id="btn_presale_continueBuying" class="btn-main btn-modal fixed-width mr-3" variant="primary" @click="continueBuying()">
                    {{ $t('propertyDetail.CONTINUE') }}
                </b-button>
                <b-button id="btn_presale_cancel" class="btn-main btn-modal btn-outline fixed-width" variant="primary" @click="cancel()">
                    {{ $t('MODALS.COMMON.CANCEL') }}
                </b-button>
            </template>
        </b-modal>

      <modal-sellable-status :property-name="property.name" :tokens-status="tokensStatus"
                             :show="showSellableStatus" @on-close="showSellableStatus = false"></modal-sellable-status>
      <modal-swappable-status :property-name="property.name"
                              :tokens-status="tokensStatus" :show="showSwappableStatus"
                              @on-close="showSwappableStatus = false"></modal-swappable-status>
      <modal-sell-sold-out :show="showPopupSellSoldOut" @on-close="showPopupSellSoldOut = false" @on-sell="$emit('on-sell')"></modal-sell-sold-out>
    </div>
</template>

<script>

import moment from "moment"
import InvestAnnual from "./InvestAnnual.vue"
import { isFullyActive, isLoggedIn, isInactive } from "../constants/userStatus"
import { isUser } from "../constants/roles"
import { exchange, formatNumberIntl } from "../helpers/common"
import CurrencyTooltip from "./CurrencyTooltip.vue"
import ModalSellableStatus from "../modals/ModalSellableStatus.vue"
import ModalSwappableStatus from "../modals/ModalSwappableStatus"
import ModalSellSoldOut from "../modals/ModalSellSoldOut"
import { PROPERTY_STATUSES } from "../constants/constants"

export default {
    components: {
        InvestAnnual,
        CurrencyTooltip,
        ModalSellableStatus,
        ModalSwappableStatus,
        ModalSellSoldOut
    },
    props: {
        property: {
            type: Object,
            required: true,
        },
        tokensStatus: {
          type: Object,
          required: true,
        },
        num: {
            type: Number,
            default: 0,
        },
    },

    emits: ['on-invest', 'on-sell', 'on-swap'],

    data() {
      return {
        showPresaleModal: false,
        showSellableStatus: false,
        showSwappableStatus: false,
        showPopupSellSoldOut: false
      }
    },

    methods: {
      formatNumberIntl,
        getProgress(property) {
            if (!property.total_tokens) {
                return 0;
            }
            return Math.floor(property.display_sold_tokens * 100 / property.total_tokens);
        },

        onInvestClicked() {
            if (this.property.status === 'presale') {
                this.showPresaleModal = true
            } else {
                this.$emit('on-invest');
            }
        },

        async onSellClicked () {
          if (this.canSellTokens) {
            // If sold out. Show popup
            if (this.isSoldOut) {
              this.showPopupSellSoldOut = true
            } else {
              // Then continue
              this.$emit("on-sell")
            }
          } else {
            this.showSellableStatus = true
          }
        },

        async onSwapClicked () {
          if (this.canSwapTokens) {
            this.$emit("on-swap")
          } else {
            this.showSwappableStatus = true
          }
        },

        continueBuying() {
            this.showPresaleModal = false
            this.$emit('on-invest');
        },

        exchangeValue(value) {
            return exchange(value)
        },
    },

    computed: {
        isUserFullyActive () {
          return isFullyActive()
        },
        isUserInactive () {
          return isInactive()
        },
        isUserLoggedIn () {
          return isLoggedIn()
        },
        isRoleUser() {
          return isUser();
        },
        availableTokens() {
          return this.property.total_tokens - this.property.display_sold_tokens 
        },
        hasAvailableTokens() {
          return this.property.display_sold_tokens < this.property.total_tokens
        },
        isSoldOut() {
          return this.property.status === PROPERTY_STATUSES.SOLD
        },
        isComingSoon() {
          return this.property.status === 'coming_soon'
        },
        canBuyTokens () {
          return (((this.isUserFullyActive || this.isUserInactive) && this.isRoleUser) || !this.isUserLoggedIn) && this.hasAvailableTokens && !this.isSoldOut && !this.isComingSoon
        },
        availableTokensForSell () {
          return this.tokensStatus
            && this.tokensStatus.asset_sellable_tokens - this.tokensStatus.pending_sell_tokens || 0
        },
        availableTokensForSwap () {
          return this.tokensStatus
            && this.tokensStatus.asset_sellable_tokens - this.tokensStatus.pending_sell_tokens || 0
        },
        canSellTokens () {
          return ((this.isUserFullyActive && this.isRoleUser) || !this.isUserLoggedIn) && this.availableTokensForSell > 0
        },
        canSwapTokens () {
          return ((this.isUserFullyActive && this.isRoleUser) || !this.isUserLoggedIn) && this.availableTokensForSwap > 0
        },
        firstLiveOn() {
          return moment(this.property.first_live_on).format('DD/MM/YYYY')
        },
    },
}
</script>

<style lang="scss" scoped>
.invest {
    position: sticky;
    position: -webkit-sticky;
    top: 95px;
    overflow: hidden;
    border-radius: 16px;

    @media screen and (max-width: 991.98px) {
        position: static;
        max-width: 360px;
        margin-bottom: 40px;
    }

    @media screen and (max-width: 430px) {
        position: static;
        max-width: 360px;
        margin: auto auto 40px;
    }

    .progress__bar {
        background: #E6EBEF;

        .progress {
            background: #ABB5BE;
        }
    }

    .progress_padding {
        padding: 25px 25px 10px 25px;
    }

    .content_padding {
        padding: 0 25px 15px 25px;
    }

}

.disabled-button {
    background-color: #878787;
    color: white;
    font-weight: bold;
    padding: 0.5rem 0 0.5rem 0;
    height: 39px;
    border-radius: 10px;
}
</style>
