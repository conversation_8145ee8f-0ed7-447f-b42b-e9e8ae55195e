<template>
    <div class="d-flex flex-row justify-content-between w-100" style="height:55px;">
        <div class="text-left align-self-center">
            <b-button style="background:transparent;border:none;padding:0" :id="tooltipId">
                <span class="color-gray">{{ text }}</span>
                <img class="img" src="@/assets/img/info-circle-fill.png">
            </b-button>
            <b-tooltip class="mb-1" variant="secondary" :target="tooltipId" triggers="hover" placement="left"
                @shown="onShowTooltip(text)">
                {{ tooltip }}
            </b-tooltip>
        </div>
        <div class="text-right">
            <span :class="{ 'ery-color': isEry }">{{ value }}%</span>
        </div>
    </div>
</template>

<script>
import { GTM_EVENT_NAMES } from '../constants/gtm';
import { gtmTrackEvent } from '../helpers/gtm';

export default {
    props: {
        text: {
            type: String,
            default: '',
        },
        tooltip: {
            type: String,
            default: '',
        },
        tooltipId: {
            type: String,
            default: null,
        },
        value: {
            type: Number,
            default: 0,
        },
        isEry: {
            type: Boolean,
            default: false,
        }
    },
    methods: {
        trackGtmEvent(event) {
            gtmTrackEvent({
                event: event,
            })
        },
        onShowTooltip(text) {
            switch (text) {
                case this.$t('propertyDetail.ERY_ANNUAL'):
                    this.trackGtmEvent(GTM_EVENT_NAMES.ERY_INFORMATION);
                    break;
                case this.$t('propertyDetail.ECA_ANNUAL'):
                    this.trackGtmEvent(GTM_EVENT_NAMES.ECA_INFORMATION);
                    break;
                case this.$t('propertyDetail.IRR_ANNUAL'):
                    this.trackGtmEvent(GTM_EVENT_NAMES.IRR_INFORMATION);
                    break;
                default:
                    break;
            }
        },
    }
}
</script>

<style lang="scss" scoped>
img {
    width: 16px;
    margin-left: 6px;
}

.text-right {
    span {
        font-size: 40px;
        color: var(--primary-color);
        font-weight: bold;
        font-style: normal;
    }

    span.ery-color {
        color: #6A87A1;
    }
}
</style>
