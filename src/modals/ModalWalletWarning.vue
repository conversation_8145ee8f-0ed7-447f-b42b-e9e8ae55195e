<template>
  <div>
    <b-modal v-model="showModal" modal-class="goro-general-fe-side-modal"
             centered="true" @hide="onClose" :no-close-on-backdrop="true">
      <template #modal-header>
        <h5 class="modal-header-title font-24 font-bold red mt-0 mb-2">
          {{ $t('common.WARNING') }}
        </h5>
        <button type="button" class="close font-weight-bold" style="margin-right: -8px" @click="onClose">×</button>
      </template>
      <div class="paragraph-with-dot ml-2 mr-2">
        <span class="dot">•</span>
        <p class="text-left font-semibold">{{ $t("BLOCKCHAIN.WALLET_WARNING_1") }}</p>
      </div>
      <div class="paragraph-with-dot ml-2 mr-2 mt-2">
        <span class="dot">•</span>
        <p class="text-left font-semibold">{{ $t("BLOCKCHAIN.WALLET_WARNING_2") }}</p>
      </div>
      <template #modal-footer>
        <b-button class="btn-main btn-modal mt-1 mb-0 pl-4 pr-4" variant="primary" @click="onClose(true)">
          {{ $t('MODALS.COMMON.CONTINUE') }}
        </b-button>
      </template>
    </b-modal>
  </div>
</template>

<script>

export default {
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-close"],
  data() {
    return {
      showModal: false,
    }
  },

  watch: {
    show(value) {
      this.showModal = value
    },
  },

  methods: {
    onClose(showWalletDetails = false) {
      this.$emit('on-close', showWalletDetails);
    }
  },

  computed: {},
}
</script>

<style lang="scss" scoped>
.paragraph-with-dot {
  display: flex;
  align-items: flex-start;
}

.dot {
  margin-top: -5px;
  margin-left: 10px;
  margin-right: 10px;
  font-size: 20px;
}
</style>
