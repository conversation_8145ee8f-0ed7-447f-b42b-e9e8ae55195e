<template>
    <div class="popup" v-if="isOpen" style="z-index: 10001;">
        <div class="popup-content d-flex flex-column align-items-center px-3">
            <div class="d-flex justify-content-between mb-3 w-100">
                <div style="width: 30px;"></div>
                <p class="font-22 font-weight-bold mb-2 p-0 m-0">{{ $t('BALANCE_HISTORY.PICK_DATE') }}</p>
                <div class="btn-close" @click="close()">
                    <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
                </div>
            </div>
            <div class="d-flex flex-row justify-content-center">
                <VueDatePicker v-model="selectedDates" inline auto-apply range format="yyyy-MM-dd" month-name-format="long"
                    :enable-time-picker="false" :min-date="minDate" :max-date="maxDate" @range-start="onRangeStart" @range-end="onRangeEnd"/>
            </div>
            <p v-if="diffInDays > 30" class="font-14 color-error mt-3">{{ $t("BALANCE_HISTORY.SELECT_EXCEED_30_DAYS") }}</p>
            <div class="mt-3 warning d-flex align-items-center">
                <img src="@/assets/img/info-circle.svg" alt="" width="18" height="18">
                <p class="font-12 color-black ml-2">{{ $t("BALANCE_HISTORY.SELECT_DATES_UP_TO") }}</p>
            </div>
            <div class="d-flex justify-content-end mt-3 w-100">
                <b-button @click="applyFilter" class="bg-main-color color-white mr-1" style="padding: 5px 16px;" :disabled="!enabledApply">
                    {{ $t("BALANCE_HISTORY.APPLY") }}
                </b-button>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';
export default {

    emits: ['on-close', 'on-select'],
    data() {
        return {
            isOpen: false,
            diffInDays: 0,
            finishSelect: true,
            selectedDates: [moment().subtract(30, 'days'), moment()],
        }
    },
    watch: {
        isOpen(value) {
            if (value) {
                document.body.style.position = 'fixed';
                document.body.style.top = 0;
                document.body.style.left = 0;
                document.body.style.right = 0;
            } else {
                document.body.style.position = '';
            }
        },
        selectedDates(value) {
            this.diffInDays = moment(value[1]).diff(moment(value[0]), 'days', true)
        }
    },
    methods: {
        openPopup() {
            this.isOpen = true
        },
        close() {
            this.isOpen = false
            this.$emit('on-close')
        },
        onRangeStart(value) {
             this.finishSelect = false
        },
        onRangeEnd(value) {
            this.finishSelect = true
        },
        applyFilter() {
            this.$emit('on-select', this.selectedDates)
            this.close()
        },
    },

    computed: {
        minDate() {
            return moment('2024-01-01').toDate()
        },

        maxDate() {
            return moment().toDate()
        },
        enabledApply() {
            return this.diffInDays <= 30 && this.finishSelect
        }
    }
}
</script>

<style scoped lang="scss">
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;

    .popup-content {
        position: relative;
        background-color: #fff;
        padding: 20px 30px 20px 30px;
        border-radius: 20px;
        text-align: center;
        width: fit-content;
        height: fit-content;
        margin: auto;
        overflow: auto;
        -ms-overflow-style: none;
        scrollbar-width: none;

        ::-webkit-scrollbar {
            display: none;
        }

        .warning {
            background-color: #FEEED9;
            border: #FBA707 solid 1px;
            padding: 6px 12px;
            border-radius: 8px;
        }
    }

    .btn-close {
        background-color: rgb(221, 221, 221);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }
}
</style>