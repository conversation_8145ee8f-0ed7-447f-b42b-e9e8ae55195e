<template>
    <div class="popup cls-popup-referral-leaderboard" v-if="showModal" style="z-index: 1031;">
        <div class="popup-content d-flex flex-column">
            <div class="cls-heading-wrapper d-flex justify-content-between">
                <div style="width: 30px;"></div>
                <div class="mt-2">
                    <p class="p-0 m-0 cls-popup-heading font-32">{{ $t('REFERRAL.TOP_REFERRER') }}</p>
                </div>
                <div class="btn-close" @click="close()">
                    <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
                </div>
            </div>
            <div class="cls-content-wrapper">
                <div class="cls-button-wrapper cls-button-tab-panel mt-4 mb-2">
                    <b-button-group>
                        <b-button @click="filterTopReferrer(types.WEEKLY)" class="btn btn-main white font-18" :class="currentType == types.WEEKLY ? 'active' : ''">{{ $t('REFERRAL.WEEKLY') }}</b-button>
                        <b-button @click="filterTopReferrer(types.MONTHLY)" class="btn btn-main white font-18" :class="currentType == types.MONTHLY ? 'active' : ''">{{ $t('REFERRAL.MONTHLY') }}</b-button>
                        <b-button @click="filterTopReferrer(types.ALLTIME)" class="btn btn-main white font-18" :class="currentType == types.ALLTIME ? 'active' : ''">{{ $t('REFERRAL.ALL_TIME') }}</b-button>
                    </b-button-group>
                </div>
                <div v-if="!isLoading && (!rows || rows.length == 0)" class="cls-no-record-wrapper">
                    <h3 class="cls-no-record-title font-24 mt-5 mb-1">
                        {{ $t('REFERRAL.NO_RECORD_TITLE') }}
                    </h3>
                    <p class="cls-no-record-content font-18 mt-2 mb-5 p-3">
                        {{ $t('REFERRAL.NO_RECORD_CONTENT') }}
                    </p>
                </div>
                <div v-if="!isLoading && rows && rows.length > 0" class="cls-leaderboard-wrapper d-flex flex-column align-items-center flex-grow-1">
                    <div class="d-flex flex-row justify-content-center align-items-center w-100 text-center mt-4 mb-4 pt-2">
                        <div class="top-leaderboard top-2-leaderboard d-flex flex-column align-items-center">
                            <div class="avatar-container">
                                <div class="avatar">
                                    <img v-if="top2Rank?.avatar_url" :src="getUrl(top2Rank?.avatar_url)" />
                                    <img v-else-if="top2Rank" class="default" src="@/assets/img/logo_white.png" />
                                </div>
                                <img class="top-icon" src="@/assets/img/account/referrals/top-2.png" alt="" />
                            </div>
                            <div v-if="top2Rank" class="name-container mt-3" :id="'invited-users-' + top2Rank.uuid">
                                <p class="font-16 font-weight-bold text-name d-flex align-items-center">
                                    <span>{{ top2Rank ? top2Rank.username : '' }}</span>
                                    <icon-checker v-if="top2Rank.completed_kyc" :type="'blue'" class="ml-1"/>
                                </p>
                                <p class="font-14 text-id">{{ $t('REFERRAL.GORO_ID') }}: {{ top2Rank ? top2Rank.uuid : '' }}</p>
                            </div>
                            <b-tooltip v-if="top2Rank" custom-class="cls-custom-tooltip-invited" variant="secondary" :target="'invited-users-' + top2Rank.uuid" triggers="hover" placement="bottom">
                                {{ $t('REFERRAL.INVITED_USER') }}: {{ top2Rank.invited_users }}
                            </b-tooltip>
                        </div>
                        <div class="top-leaderboard top-1-leaderboard d-flex flex-column align-items-center">
                            <div class="avatar-container">
                                <img class="top-rank-icon" src="@/assets/img/account/referrals/top-1-rank.png" alt="" />
                                <div class="avatar">
                                    <img v-if="top1Rank?.avatar_url" :src="getUrl(top1Rank?.avatar_url)" />
                                    <img v-else-if="top1Rank" class="default" src="@/assets/img/logo_white.png" />
                                </div>
                                <img class="top-icon" src="@/assets/img/account/referrals/top-1.png" alt="" />
                            </div>
                            <div v-if="top1Rank" class="name-container mt-3" :id="'invited-users-' + top1Rank.uuid">
                                <p class="font-16 font-weight-bold text-name d-flex align-items-center">
                                    <span>{{ top1Rank ? top1Rank.username : '' }}</span>
                                    <icon-checker v-if="top1Rank.completed_kyc" :type="'blue'" class="ml-1"/>
                                </p>
                                <p class="font-14 text-id">{{ $t('REFERRAL.GORO_ID') }}: {{ top1Rank ? top1Rank.uuid : '' }}</p>
                            </div>
                            <b-tooltip v-if="top1Rank" custom-class="cls-custom-tooltip-invited" variant="secondary" :target="'invited-users-' + top1Rank.uuid" triggers="hover" placement="bottom">
                                {{ $t('REFERRAL.INVITED_USER') }}: {{ top1Rank.invited_users }}
                            </b-tooltip>
                        </div>
                        <div class="top-leaderboard top-3-leaderboard d-flex flex-column align-items-center">
                            <div class="avatar-container">
                                <div class="avatar">
                                    <img v-if="top3Rank?.avatar_url" :src="getUrl(top3Rank?.avatar_url)" />
                                    <img v-else-if="top3Rank" class="default" src="@/assets/img/logo_white.png" />
                                </div>
                                <img class="top-icon" src="@/assets/img/account/referrals/top-3.png" alt="" />
                            </div>
                            <div v-if="top3Rank" class="name-container mt-3" :id="'invited-users-' + top3Rank.uuid">
                                <p class="font-16 font-weight-bold text-name d-flex align-items-center">
                                    <span>{{ top3Rank ? top3Rank.username : '' }}</span>
                                    <icon-checker v-if="top3Rank.completed_kyc" :type="'blue'" class="ml-1"/>
                                </p>
                                <p class="font-14 text-id">{{ $t('REFERRAL.GORO_ID') }}: {{ top3Rank ? top3Rank.uuid : '' }}</p>
                            </div>
                            <b-tooltip v-if="top3Rank" custom-class="cls-custom-tooltip-invited" variant="secondary" :target="'invited-users-' + top3Rank.uuid" triggers="hover" placement="bottom">
                                {{ $t('REFERRAL.INVITED_USER') }}: {{ top3Rank.invited_users }}
                            </b-tooltip>
                        </div>
                    </div>
                    <div class="other-rank-container table-wrapper-fixed-header" :class="otherRanks && otherRanks.length > 0 ? 'has-other-ranks' : ''">
                        <b-table class="trx-table" v-if="otherRanks && otherRanks.length > 0" responsive head-variant="light" id="my-table" :fields="fields"
                        :items="otherRanks" :per-page="50" :current-page="currentPage" :tbody-tr-class="rowClass" small>
                            <template v-slot:cell(rank)="data">
                                <span class="rank font-20">{{ data.value }}.</span>
                            </template>
                            <template v-slot:cell(username)="data">
                                <div class="cls-invited-user-info d-flex flex-row justify-content-start align-items-center">
                                    <div class="avatar mr-2">
                                        <img v-if="data.item.avatar_url" class="avatar" :src="getUrl(data.item.avatar_url)" />
                                        <default-avatar width="32" height="32" v-else></default-avatar>
                                    </div>
                                    <div class="cls-username d-flex flex-column justify-content-center align-items-start">
                                        <p class="font-weight-bold font-18 text-name d-flex align-items-center">
                                            <span>{{ data.item.username }}</span>
                                            <icon-checker v-if="data.item.completed_kyc" :type="'blue'" class="ml-1"/>
                                        </p>
                                        <p class="font-14 text-id">
                                            {{ $t('REFERRAL.GORO_ID') }}: {{ data.item.uuid }}
                                        </p>
                                    </div>
                                </div>
                            </template>
                            <template v-slot:cell(invited_users)="data">
                                <span class="invited-users font-20">{{ data.value }}</span>
                            </template>
                        </b-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import DefaultAvatar from "../components/DefaultAvatar.vue"
import accountService from "../services/account.service"
import { urlImage } from "../helpers/common"
import IconChecker from "../components/IconChecker.vue"

export default {
    components: {
        DefaultAvatar,
        IconChecker,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['on-close'],
    data() {
        return {
            showModal: false,
            items: null,
            totalCount: 0,
            currentPage: 1,
            stickyHeader: true,
            fields: this.getTableHeaderFields(),
            types: {
                WEEKLY: "weekly",
                MONTHLY: "monthly",
                ALLTIME: "all"
            },
            currentType: 'all',
            isLoading: false
        }
    },

    watch: {
        '$i18n.locale'(newVal, oldVal) {
            this.fields = this.getTableHeaderFields()
        },
        async show(value) {
            this.showModal = value
            if (value) {
                this.currentType = this.types.ALLTIME
                await this.getTopReferrers({
                    type: this.currentType
                })
            }
            if (value) {
                document.body.style.position = 'fixed';
                document.body.style.top = 0;
                document.body.style.left = 0;
                document.body.style.right = 0;
            } else {
                document.body.style.position = '';
            }
        }
    },

    methods: {
        async getTopReferrers(filters = {type: this.types.ALLTIME}) {
            this.isLoading = true
            const res = await accountService.getTopReferrers(filters)
            if (res && res.data) {
                this.items = res.data.map((item, index) => {
                    return {
                        rank: index + 1,
                        ...item
                    }
                }) || [];
                this.totalCount = res.total
            }
            this.isLoading = false
        },

        async filterTopReferrer(type = this.types.ALLTIME) {
            this.currentType = type
            await this.getTopReferrers({
                type: this.currentType
            })
        },

        getUrl(url) {
            return urlImage({ image: url })
        },

        close() {
            this.showModal = false
            this.$emit('on-close')
        },

        getTableHeaderFields() {
            return [
                {
                    key: 'rank',
                    label: this.$t('REFERRAL.RANK'),
                    thStyle: { width: '80px' },
                    thClass: 'th-rank-column',
                    tdClass: 'rank-column'
                },
                {
                    key: 'username',
                    label: this.$t('REFERRAL.NAME'),
                    thClass: 'th-username-column',
                    thStyle: { width: '230px' },
                    tdClass: 'username-column'
                },
                {
                    key: 'invited_users',
                    label: this.$t('REFERRAL.INVITED_USER'),
                    thStyle: { width: '165px' },
                    thClass: 'th-invited-users-column',
                    tdClass: 'invited-users-column'
                },
            ]
        },

        rowClass(item, type) {
            if (type === 'row') {
                return item.uuid === this.currentUserUuid ? 'cls-current-owner' : 'tr-normal'
            }
            return ''
        },
    },

    computed: {
        rows() {
            return this.items || []
        },

        otherRanks() {
            if (this.rows.length > 3) {
                let items = [...this.rows]
                items.splice(0, 3)
                return items
            }
            return []
        },

        top1Rank() {
            if (this.rows.length) {
                return this.rows[0]
            }
            return null
        },

        top2Rank() {
            if (this.rows.length > 1) {
                return this.rows[1]
            }
            return null
        },

        top3Rank() {
            if (this.rows.length > 2) {
                return this.rows[2]
            }
            return null
        },

        currentUserUuid() {
            return this.$store.getters.userProfile?.uuid
        }
    },
}
</script>

<style lang="scss">
.cls-custom-tooltip-invited{
    .tooltip-inner{
        font-family: "AcuminVariableConcept", Helvetica, sans-serif !important;
        font-size: 13px;
        font-weight: 700;
        line-height: 15.6px;
        text-align: center;
        color: #FFFFFF;
        border: 1.88px solid #9EA8BA !important;
        background-color: #9EA8BA !important;
        padding: 10px 15px 7px 15px;
        border-radius: 35px;
    }
    
}

.cls-custom-tooltip-invited .arrow::before {
    border-bottom-color: #9EA8BA !important;
}

.popup.cls-popup-referral-leaderboard {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    color: black;

    *{
        font-family: "AcuminVariableConcept", Helvetica, sans-serif !important;
    }

    .popup-content {
        position: relative;
        background-color: #fff;
        border-radius: 20px;
        text-align: center;
        width: 98%;
        height: 98%;
        margin: auto;
        overflow: hidden;
        max-width: 560px;

        @media screen and (min-width: 600px) {
            width: 80%;
        }

        @media screen and (min-width: 800px) {
            width: 70%;
        }

        @media screen and (min-width: 1100px) {
            width: 50%;
        }

        @media screen and (min-width: 1300px) {
            width: 40%;
        }
    }

    .btn-close {
        background-color: rgb(221, 221, 221);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }

    .cls-popup-heading{
        font-weight: 600;
        line-height: 39.95px;
        text-align: center;
        color: var(--layout2-primary-color);
    }

    .cls-no-record-wrapper{
        text-align: center;
        color: #9EA8BA;
        .cls-no-record-title{
            text-align: center;
            color: #9EA8BA;
        }
        .cls-no-record-content{
            text-align: center;
            color: #9EA8BA;
        }
    }

    .cls-button-tab-panel{
        .btn-group{
            border: 1px solid var(--layout2-primary-color);
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            max-width: 450px;
            margin: 0 auto;
            button{
                border: none !important;
                font-weight: 600;
                line-height: 25.25px;
                text-align: center;
                padding-top: 8px;
                padding-bottom: 3px;
                border-radius: 7px;
                margin: 0;
                flex: 1;
                &:hover{
                    color: var(--primary-color);
                    background-image: none;
                    background-color: transparent;
                }
                &:focus,
                &:active,
                &.active{
                    background-image: none !important;
                    background-color: var(--layout2-primary-color) !important;
                    color: #fff;
                }
                @media screen and (max-width: 768px) {
                    padding: 4px 25px 1px 25px;
                }
                @media screen and (max-width: 400px) {
                    padding: 4px 15px 1px 15px;
                }
            }
        }
        
    }

    .cls-content-wrapper{
        overflow-x: hidden;
        overflow-y: auto;
        .cls-leaderboard-wrapper{
            
        }
    }

    .top-leaderboard{
        margin-left: 15px;
        margin-right: 15px;
        margin-top: 65px;
        min-height: 150px;
        @media screen and (max-width: 768px) {
            margin-left: 8px;
            margin-right: 8px;
        }
         @media screen and (max-width: 480px) {
            margin-left: 0px;
            margin-right: 0px;
        }
        .avatar-container{
            position: relative;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                &.default {
                    object-fit: cover;
                    padding: 22px;
                    background-color: var(--primary-color);
                }
            }
            .avatar{
                width: 92px;
                height: 92px;
                border-radius: 100%;
                overflow: hidden;
                border: 2.88px solid #9EA8BA;
                box-shadow: 0px 5.75px 16.25px -4.31px #0000004D;
                @media screen and (max-width: 768px) {
                    width: 72px;
                    height: 72px;
                }
                @media screen and (max-width: 400px) {
                    width: 60px;
                    height: 60px;
                }
            }
            .top-icon{
                width: 32px;
                height: 32px;
                position: absolute;
                left: calc(50% - 16px);
                bottom: -13px;
            }
            .top-rank-icon{
                width: 44px;
                height: 38px;
                position: absolute;
                left: calc(50% - 22px);
                top: -36px;
                @media screen and (max-width: 768px) {
                    width: 36px;
                    height: 32px;
                    left: calc(50% - 18px);
                    top: -28px;
                }
                @media screen and (max-width: 400px) {
                    width: 30px;
                    height: 24px;
                    left: calc(50% - 15px);
                    top: -23px;
                }
            }
        }
        .name-container{
            width: 100%;
            p{
                font-weight: 382;
                line-height: 18.98px;
                text-align: center;
                color: #000000;
                margin-top: 3px;
                @media screen and (max-width: 400px) {
                    font-size: 10px !important;
                    margin-top: 0px;
                }
            }
            .text-name{
                font-weight: 700;
                line-height: 22.44px;
                text-align: center;
                color: #9EA8BA;
                margin-top: 3px;
                @media screen and (max-width: 400px) {
                    font-size: 10px !important;
                    line-height: 18.98px;
                }
            }
            .text-name,
            .text-name span{
                font-family: 'NewAcuminVariableConcept' !important;
            }
        }
        &.top-1-leaderboard{
            margin-top: -35px !important;
            .avatar-container{
                .avatar{
                    border: 7.19px solid #FFCF13;
                    width: 110px;
                    height: 110px;
                    @media screen and (max-width: 768px) {
                        width: 87px;
                        height: 87px;
                    }
                }
                .top-icon{
                    width: 40px;
                    height: 40px;
                    position: absolute;
                    left: calc(50% - 20px);
                    bottom: -17px;
                }
            }
            .name-container{
                .text-name{
                    color: #FFCF13 !important;
                }
            }
        }
        &.top-3-leaderboard{
            .avatar{
                border: 2.88px solid #DD722A;
            }
            .name-container{
                .text-name{
                    color: #FD902A !important;
                }
            }
        }
    }
    .cls-heading-wrapper,
    .cls-button-wrapper{
        margin-left: 30px;
        margin-right: 30px;
        @media screen and (max-width: 768px) {
            margin-left: 15px;
            margin-right: 15px;
        }
    }
    .cls-heading-wrapper{
        margin-top: 30px;
    }
    .other-rank-container {
        background-color: #fff;
        overflow: hidden;
        border-radius: 20px;
        margin-left: 30px;
        margin-right: 30px;
        margin-bottom: 30px;
        box-shadow: 0px 5.75px 16.25px -4.31px #0000004D;
        &.has-other-ranks{
            min-height: 437px;
        }
        @media screen and (max-width: 768px) {
            margin-left: 15px;
            margin-right: 15px;
            margin-bottom: 15px;
        }
        .table-responsive{
            margin-bottom: 0 !important;
            overflow-x: hidden;
        }
        table{
            
            thead{
                th{
                    // box-shadow: 0px 5.75px 16.25px -4.31px #0000004D;
                    background: #ACDDE1;
                    color: var(--layout2-primary-color);
                    font-size: 20.13px;
                    font-weight: 600;
                    line-height: 24.16px;
                    text-align: left;
                    padding: 15px 15px 8px 15px;
                    @media screen and (max-width: 768px) {
                        padding: 10px 10px 5px 10px;
                        font-size: 15px;
                        &.th-rank-column{
                            width: 75px !important;
                        }
                        &.th-username-column{
                            width: 180px !important;
                        }
                        &.th-invited-users-column{
                            width: 118px !important;
                        }
                    }
                    @media screen and (max-width: 400px) {
                        padding: 8px 5px 5px 5px;
                        &.th-rank-column{
                            width: 45px !important;
                        }
                        &.th-username-column{
                            width: 150px !important;
                        }
                        &.th-invited-users-column{
                            width: 118px !important;
                        }
                    }
                }
            }
            tbody{
                max-height: 390px;
                tr{
                    td{
                        padding: 15px 15px 8px 15px;
                        border: none !important;
                        &.rank-column{
                            width: 80px;
                            .rank{
                                font-weight: 700;
                                line-height: 24.16px;
                                text-align: left;
                                color: #000;
                            }
                        }
                        &.invited-users-column{
                            width: 155px;
                            .invited-users{
                                font-weight: 700;
                                line-height: 24.16px;
                                text-align: left;
                                color: #000;
                            }
                        }
                        .cls-invited-user-info{
                            .avatar{
                                img{
                                    width: 32px;
                                    height: 32px;
                                    border-radius: 100%;
                                    overflow: hidden;
                                    box-shadow: 0px 5.75px 16.25px -4.31px #0000004D;
                                    @media screen and (max-width: 400px) {
                                        width: 24px;
                                        height: 24px;
                                    }
                                }
                            }
                            .cls-username{
                                .text-name{
                                    font-weight: 700;
                                    line-height: 22.44px;
                                    text-align: left;
                                    color: #000000;
                                    @media screen and (max-width: 400px) {
                                        font-size: 12px !important;
                                    }
                                }
                                .text-id{
                                    font-weight: 382;
                                    line-height: 17.26px;
                                    text-align: right;
                                    color: #000000;
                                }
                                .text-name,
                                .text-name span{
                                    font-family: 'NewAcuminVariableConcept' !important;
                                }
                            }
                        }
                        
                        @media screen and (max-width: 768px) {
                            padding: 10px 10px 5px 10px;
                            &.rank-column{
                                width: 75px !important;
                            }
                            &.username-column{
                                width: 180px !important;
                            }
                            &.invited-users-column{
                                width: 118px !important;
                            }
                        }
                        @media screen and (max-width: 400px) {
                            padding: 8px 5px 5px 5px;
                            &.rank-column{
                                width: 45px !important;
                            }
                            &.username-column{
                                width: 150px !important;
                            }
                            &.invited-users-column{
                                width: 118px !important;
                            }
                        }
                    }
                    &.cls-current-owner{
                        td{
                            background-color: var(--layout2-primary-color);
                            &.rank-column{
                                border-radius: 10px 0 0 10px;
                                .rank{
                                    color: #fff;
                                }
                            }
                            &.username-column{
                                .avatar{
                                    img{
                                        border: 1px solid #9EA8BA;
                                    }
                                }
                                .cls-username{
                                    .text-name{
                                        color: #fff;
                                    }
                                    .text-id{
                                        color: #fff;
                                    }
                                }
                            }
                            &.invited-users-column{
                                border-radius: 0 10px 10px 0;
                                .invited-users{
                                    color: #fff;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
