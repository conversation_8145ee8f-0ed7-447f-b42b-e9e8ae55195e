<template>
  <div class="popup" v-if="showModal" style="z-index: 10001;">
    <div v-if="isBuyToken || isOrderToken" class="popup-content-v2 d-flex flex-column">
      <div class="btn-close" @click="close()">
        <b-icon icon="x" style="color: gray;" scale="1.6"/>
      </div>
      <div class="d-flex align-items-center">
        <b-img class="property-image" :src="propertyImage" alt=""/>
        <div class="d-flex flex-column align-items-start text-left" style="width:100%">
          <div class="d-flex align-items-center" style="width:100%;">
            <span class="value text-truncate flex-grow-1">{{ type }}</span>
            <div class="status d-flex align-items-center ml-2" :style="{ backgroundColor: statusBgColor, color: statusColor }">
              <img :src="require(`@/assets/img/icons/status_${statusIcon}.svg`)" class="mr-1" style="margin-bottom: 3px" alt=""/>
              <span>{{ status }}</span>
            </div>
          </div>
          <div class="d-flex flex-row justify-content-center mb-1">
            <img class="clock-icon" src="../assets/img/clock.png" alt=""/>
            <p class="text-color-tertiary font-normal" style="font-size: 12px">{{ createdAt }}</p>
          </div>
          <span class="header text-truncate">{{ propertyName }}</span>
        </div>
      </div>
      <div class="details">
        <p class="title">{{ $t('TRANSACTION.EXTERNAL_ID') }}</p>
        <div class="d-flex flex-row align-items-center">
          <p class="value">{{ externalID ?? '-' }}</p>
          <img v-if="externalID" class="copy-icon ml-1" src="../assets/img/copy.png" @click="copyTransactionId" alt=""/>
        </div>
        <!---->
        <p v-if="isOrderToken" class="title mt-1">{{ $t('TRANSACTION.REFERENCE_ID') }}</p>
        <div v-if="isOrderToken" class="d-flex flex-row align-items-center">
          <p class="value">{{ referenceId ?? '-' }}</p>
          <img v-if="referenceId" class="copy-icon ml-1" src="../assets/img/copy.png" @click="copyReferenceId" alt=""/>
        </div>
        <!---->
        <p class="title mt-1">{{ $t('TRANSACTION.BLOCKCHAIN_TRANSACTION_ID') }}</p>
        <a v-if="blockchainTransactionLink" class="link"
           :href="blockchainTransactionLink" target="_blank">{{ blockchainTransactionExternalId }}</a>
        <p v-else class="value">{{ blockchainTransactionExternalId }}</p>
        <!---->
        <p class="title mt-1">{{ $t('PAYMENT.PAYMENT_METHOD') }}</p>
        <div class="d-flex flex-row align-items-center">
          <span class="value">{{ paymentText }}</span>
        </div>
        <!---->
        <p class="header mt-3 mb-2">{{ $t('TRANSACTION.TRANSACTION_DETAILS') }}</p>
        <div class="d-flex flex-row justify-content-between">
          <p class="title">{{ $t('TRANSACTION.TYPE') }}</p>
          <p class="value">{{ type }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.PRICE_PER_TOKEN') }}</p>
          <p class="value" id="buy-price-per-token">{{ exchangeValue(pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="buy-price-per-token" :value="pricePerToken"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.NUM_OF_TOKENS') }}</p>
          <p class="value">{{ formatNumberIntl(numOfTokens) }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('SELL_TOKEN.SUBTOTAL') }}</p>
          <p class="value" id="buy-subtotal">{{ exchangeValue(numOfTokens * pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="buy-subtotal" :value="numOfTokens * pricePerToken"></CurrencyTooltip>
        </div>

        <hr class="divider">
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('ORDER.BALANCE_USED') }}</p>
          <p class="value" id="buy-balance-used">{{ exchangeValue(balanceUsed) }}</p>
          <CurrencyTooltip tooltipId="buy-balance-used" :value="balanceUsed"></CurrencyTooltip>
        </div>
        <div v-if="displayVoucherInfo" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('VOUCHER.PROMO_CODE') }}</p>
          <p class="value">{{ voucherCode ?? '-' }}</p>
        </div>
        <div v-if="displayVoucherInfo" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('VOUCHER.PROMO_DISCOUNT') }}</p>
          <p v-if="voucherRewardAmount" class="value" id="buy-voucher-discount">-{{ exchangeValue(voucherRewardAmount) }}</p>
          <p v-else class="value">-</p>
          <CurrencyTooltip tooltipId="buy-voucher-discount" :value="voucherRewardAmount"></CurrencyTooltip>
        </div>
        <div v-if="displayTransactionFees" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION_FEE.TITLE') }}
            <img class="compound-tooltip-icon" src="../assets/img/info-circle-fill.png" id="tooltip-transaction-fee" alt="">
          </p>
          <b-tooltip variant="secondary" target="tooltip-transaction-fee" triggers="hover" placement="top">
            {{ $t("TRANSACTION_FEE.TOOLTIP") }}
          </b-tooltip>
          <p v-if="transactionFee > 0" class="value" id="buy-transaction-fee">{{ exchangeValue(transactionFee) }}</p>
          <p v-else class="value">{{ $t('common.FREE') }}</p>
          <CurrencyTooltip tooltipId="buy-transaction-fee" :value="transactionFee"></CurrencyTooltip>
        </div>
        <div v-if="displayProcessingFees" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('PROCESSING_FEE.TITLE') }}
            <img class="compound-tooltip-icon" src="../assets/img/info-circle-fill.png" id="tooltip-processing-fee" alt="">
          </p>
          <b-tooltip variant="secondary" target="tooltip-processing-fee" triggers="hover" placement="top">
            {{ $t("PROCESSING_FEE.TOOLTIP") }}
          </b-tooltip>
          <p v-if="processingFee > 0" class="value" id="buy-processing-fee">{{ exchangeValue(processingFee) }}</p>
          <p v-else class="value">{{ $t('common.FREE') }}</p>
          <CurrencyTooltip tooltipId="buy-processing-fee" :value="processingFee"></CurrencyTooltip>
        </div>

        <hr class="divider">
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.DESCRIPTION') }}</p>
          <p class="value">{{ note }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="header">{{ $t('PAYMENT.TOTAL_PAYMENT') }}</p>
          <p class="header" id="buy-total-payment">{{ exchangeValue(totalPayment) }}</p>
          <CurrencyTooltip tooltipId="buy-total-payment" :value="totalPayment"></CurrencyTooltip>
        </div>
      </div>
    </div>

    <div v-if="isSellToken" class="popup-content-v2 d-flex flex-column">
      <div class="btn-close" @click="close()">
        <b-icon icon="x" style="color: gray;" scale="1.6"/>
      </div>
      <div class="d-flex align-items-center">
        <b-img class="property-image" :src="propertyImage" alt=""/>
        <div class="d-flex flex-column align-items-start text-left" style="width:100%">
          <div class="d-flex align-items-center" style="width:100%;">
            <span class="value text-truncate flex-grow-1">{{ type }}</span>
            <div class="status d-flex align-items-center ml-2" :style="{ backgroundColor: statusBgColor, color: statusColor }">
              <img :src="require(`@/assets/img/icons/status_${statusIcon}.svg`)" class="mr-1" alt=""/>
              <span>{{ status }}</span>
            </div>
          </div>
          <div class="d-flex flex-row justify-content-center mb-1">
            <img class="clock-icon" src="../assets/img/clock.png" alt=""/>
            <p class="text-color-tertiary font-normal" style="font-size: 12px">{{ createdAt }}</p>
          </div>
          <span class="header text-truncate">{{ propertyName }}</span>
        </div>
      </div>
      <div class="details">
        <p class="title">{{ $t('TRANSACTION.EXTERNAL_ID') }}</p>
        <div class="d-flex flex-row align-items-center">
          <p class="value">{{ externalID ?? '-' }}</p>
          <img v-if="externalID" class="copy-icon ml-1" src="../assets/img/copy.png" @click="copyTransactionId" alt=""/>
        </div>
        <p class="title mt-1">{{ $t('TRANSACTION.SELL_REQUEST_UUID') }}</p>
        <div class="d-flex flex-row align-items-center">
          <p class="value">{{ sellTokenRequestUuid ?? '-' }}</p>
          <img v-if="sellTokenRequestUuid" class="copy-icon ml-1" src="../assets/img/copy.png" @click="copySellRequestUuid" alt=""/>
        </div>
        <p class="title mt-1">{{ $t('TRANSACTION.BLOCKCHAIN_TRANSACTION_ID') }}</p>
        <a v-if="blockchainTransactionLink" class="link"
           :href="blockchainTransactionLink" target="_blank">{{ blockchainTransactionExternalId }}</a>
        <p v-else class="value">{{ blockchainTransactionExternalId }}</p>
        <p class="header mt-3 mb-2">{{ $t('TRANSACTION.TRANSACTION_DETAILS') }}</p>
        <div class="d-flex flex-row justify-content-between">
          <p class="title">{{ $t('TRANSACTION.TYPE') }}</p>
          <p class="value">{{ type }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.PRICE_PER_TOKEN') }}</p>
          <p class="value" id="sell-price-per-token">{{ exchangeValue(pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="sell-price-per-token" :value="pricePerToken"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.NUM_OF_TOKENS') }}</p>
          <p class="value">{{ formatNumberIntl(numOfTokens) }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('SELL_TOKEN.SUBTOTAL') }}</p>
          <p class="value" id="sell-subtotal">{{ exchangeValue(numOfTokens * pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="sell-subtotal" :value="numOfTokens * pricePerToken"></CurrencyTooltip>
        </div>

        <hr v-if="displayVoucherInfo || displayTransactionFees" class="divider">
        <div v-if="displayVoucherInfo" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('VOUCHER.PROMO_CODE') }}</p>
          <p class="value">{{ voucherCode ?? '-' }}</p>
        </div>
        <div v-if="displayVoucherInfo" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('VOUCHER.PROMO_DISCOUNT') }}</p>
          <p v-if="voucherRewardAmount" class="value" id="sell-voucher-discount">{{ exchangeValue(voucherRewardAmount) }}</p>
          <p v-else class="value">-</p>
          <CurrencyTooltip tooltipId="sell-voucher-discount" :value="voucherRewardAmount"></CurrencyTooltip>
        </div>
        <div v-if="displayTransactionFees" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION_FEE.TITLE') }}
            <img class="compound-tooltip-icon" src="../assets/img/info-circle-fill.png" id="tooltip-transaction-fee-2" alt="">
          </p>
          <b-tooltip variant="secondary" target="tooltip-transaction-fee-2" triggers="hover" placement="top">
            {{ $t("TRANSACTION_FEE.TOOLTIP") }}
          </b-tooltip>
          <p v-if="transactionFee > 0" class="value" id="sell-transaction-fee">-{{ exchangeValue(transactionFee) }}</p>
          <p v-else class="value">{{ $t('common.FREE') }}</p>
          <CurrencyTooltip tooltipId="sell-transaction-fee" :value="transactionFee"></CurrencyTooltip>
        </div>

        <hr class="divider">
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.DESCRIPTION') }}</p>
          <p class="value">{{ note }}</p>
        </div>
        <div v-if="rejectedReason" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('SELL_TOKEN.REASON') }}</p>
          <p class="value">{{ rejectedReason }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="header">{{ $t('SELL_TOKEN.RECEIVED_AMOUNT') }}</p>
          <p class="header" id="sell-received-amount">{{ exchangeValue(receivedAmount) }}</p>
          <CurrencyTooltip tooltipId="sell-received-amount" :value="receivedAmount"></CurrencyTooltip>
        </div>
      </div>
    </div>

    <div v-if="isSwapBuyToken" class="popup-content-v2 d-flex flex-column">
      <div class="btn-close" @click="close()">
        <b-icon icon="x" style="color: gray;" scale="1.6"/>
      </div>
      <div class="d-flex align-items-center">
        <b-img class="property-image" :src="propertyImage" alt=""/>
        <div class="d-flex flex-column align-items-start text-left" style="width:100%">
          <div class="d-flex align-items-center" style="width:100%;">
            <span class="value text-truncate flex-grow-1">{{ type }}</span>
            <div class="status d-flex align-items-center ml-2" :style="{ backgroundColor: statusBgColor, color: statusColor }">
              <img :src="require(`@/assets/img/icons/status_${statusIcon}.svg`)" class="mr-1" alt=""/>
              <span>{{ status }}</span>
            </div>
          </div>
          <div class="d-flex flex-row justify-content-center mb-1">
            <img class="clock-icon" src="../assets/img/clock.png" alt=""/>
            <p class="text-color-tertiary font-normal" style="font-size: 12px">{{ createdAt }}</p>
          </div>
          <span class="header text-truncate">{{ propertyName }}</span>
        </div>
      </div>
      <div class="details">
        <p class="title">{{ $t('TRANSACTION.EXTERNAL_ID') }}</p>
        <div class="d-flex flex-row align-items-center">
          <p class="value">{{ externalID ?? '-' }}</p>
          <img v-if="externalID" class="copy-icon ml-1" src="../assets/img/copy.png" @click="copyTransactionId" alt=""/>
        </div>
        <p class="title mt-1">{{ $t('TRANSACTION.BLOCKCHAIN_TRANSACTION_ID') }}</p>
        <a v-if="blockchainTransactionLink" class="link"
           :href="blockchainTransactionLink" target="_blank">{{ blockchainTransactionExternalId }}</a>
        <p v-else class="value">{{ blockchainTransactionExternalId }}</p>
        <p class="header mt-3 mb-2">{{ $t('TRANSACTION.TRANSACTION_DETAILS') }}</p>
        <div class="d-flex flex-row justify-content-between">
          <p class="title">{{ $t('TRANSACTION.TYPE') }}</p>
          <p class="value">{{ type }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.PRICE_PER_TOKEN') }}</p>
          <p class="value" id="swap-buy-price-per-token">{{ exchangeValue(pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="swap-buy-price-per-token" :value="pricePerToken"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.NUM_OF_TOKENS') }}</p>
          <p class="value">{{ formatNumberIntl(numOfTokens) }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('SELL_TOKEN.SUBTOTAL') }}</p>
          <p class="value" id="swap-buy-subtotal">{{ exchangeValue(numOfTokens * pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="swap-buy-subtotal" :value="numOfTokens * pricePerToken"></CurrencyTooltip>
        </div>

        <hr class="divider">
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.DESCRIPTION') }}</p>
          <p class="value">{{ note }}</p>
        </div>
      </div>
    </div>

    <div v-if="isSwapSellToken" class="popup-content-v2 d-flex flex-column">
      <div class="btn-close" @click="close()">
        <b-icon icon="x" style="color: gray;" scale="1.6"/>
      </div>
      <div class="d-flex align-items-center">
        <b-img class="property-image" :src="propertyImage" alt=""/>
        <div class="d-flex flex-column align-items-start text-left" style="width:100%">
          <div class="d-flex align-items-center" style="width:100%;">
            <span class="value text-truncate flex-grow-1">{{ type }}</span>
            <div class="status d-flex align-items-center ml-2" :style="{ backgroundColor: statusBgColor, color: statusColor }">
              <img :src="require(`@/assets/img/icons/status_${statusIcon}.svg`)" class="mr-1" alt=""/>
              <span>{{ status }}</span>
            </div>
          </div>
          <div class="d-flex flex-row justify-content-center mb-1">
            <img class="clock-icon" src="../assets/img/clock.png" alt=""/>
            <p class="text-color-tertiary font-normal" style="font-size: 12px">{{ createdAt }}</p>
          </div>
          <span class="header text-truncate">{{ propertyName }}</span>
        </div>
      </div>
      <div class="details">
        <p class="title">{{ $t('TRANSACTION.EXTERNAL_ID') }}</p>
        <div class="d-flex flex-row align-items-center">
          <p class="value">{{ externalID ?? '-' }}</p>
          <img v-if="externalID" class="copy-icon ml-1" src="../assets/img/copy.png" @click="copyTransactionId" alt=""/>
        </div>
        <p class="title mt-1">{{ $t('TRANSACTION.BLOCKCHAIN_TRANSACTION_ID') }}</p>
        <a v-if="blockchainTransactionLink" class="link"
           :href="blockchainTransactionLink" target="_blank">{{ blockchainTransactionExternalId }}</a>
        <p v-else class="value">{{ blockchainTransactionExternalId }}</p>
        <p class="header mt-3 mb-2">{{ $t('TRANSACTION.TRANSACTION_DETAILS') }}</p>
        <div class="d-flex flex-row justify-content-between">
          <p class="title">{{ $t('TRANSACTION.TYPE') }}</p>
          <p class="value">{{ type }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.PRICE_PER_TOKEN') }}</p>
          <p class="value" id="swap-sell-price-per-token">{{ exchangeValue(pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="swap-sell-price-per-token" :value="pricePerToken"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.NUM_OF_TOKENS') }}</p>
          <p class="value">{{ formatNumberIntl(numOfTokens) }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('SELL_TOKEN.SUBTOTAL') }}</p>
          <p class="value" id="swap-sell-subtotal">{{ exchangeValue(numOfTokens * pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="swap-sell-subtotal" :value="numOfTokens * pricePerToken"></CurrencyTooltip>
        </div>

        <hr class="divider">
        <div v-if="balanceUsed" class="d-flex flex-row justify-content-between transaction-info-item">
          <p class="title">{{ $t('ORDER.BALANCE_USED') }}</p>
          <p class="value" id="swap-sell-balance-used">{{ exchangeValue(balanceUsed) }}</p>
          <CurrencyTooltip tooltipId="swap-sell-balance-used" :value="balanceUsed"></CurrencyTooltip>
        </div>
        <div v-if="swapTokensForFeeCover" class="d-flex flex-row justify-content-between transaction-info-item">
          <p class="title">{{ $t('SWAP_TOKEN.TOKENS_DEDUCTED_FOR_FEE') }}
            (-{{ formatNumberIntl(swapTokensForFeeCover) }} {{ swapTokensForFeeCover > 1 ? $t("PAYMENT.TOKENS") : $t("PAYMENT.TOKEN") }})</p>
          <p class="value" id="token-for-fees-cover-amount">{{ exchangeValue(swapTokensForFeeCover * pricePerToken) }}</p>
          <CurrencyTooltip tooltipId="token-for-fees-cover-amount" :value="swapTokensForFeeCover * pricePerToken"></CurrencyTooltip>
        </div>
        <div v-if="displayVoucherInfo" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('VOUCHER.PROMO_CODE') }}</p>
          <p class="value">{{ voucherCode ?? '-' }}</p>
        </div>
        <div v-if="displayVoucherInfo" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('VOUCHER.PROMO_DISCOUNT') }}</p>
          <p v-if="voucherRewardAmount" id="swap-sell-voucher-discount" class="value">{{ exchangeValue(voucherRewardAmount) }}</p>
          <p v-else class="value">-</p>
          <CurrencyTooltip tooltipId="swap-sell-voucher-discount" :value="voucherRewardAmount"></CurrencyTooltip>
        </div>
        <div v-if="displayTransactionFees" class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION_FEE.TITLE') }}
            <img class="compound-tooltip-icon" src="../assets/img/info-circle-fill.png" id="tooltip-transaction-fee-3" alt="">
          </p>
          <b-tooltip variant="secondary" target="tooltip-transaction-fee-3" triggers="hover" placement="top">
            {{ $t("TRANSACTION_FEE.TOOLTIP") }}
          </b-tooltip>
          <p v-if="transactionFee > 0" id="swap-sell-transaction-fee" class="value">-{{ exchangeValue(transactionFee) }}</p>
          <p v-else class="value">{{ $t('common.FREE') }}</p>
          <CurrencyTooltip tooltipId="swap-sell-transaction-fee" :value="transactionFee"></CurrencyTooltip>
        </div>

        <hr class="divider">
        <div class="d-flex flex-row justify-content-between mt-1">
          <p class="title">{{ $t('TRANSACTION.DESCRIPTION') }}</p>
          <p class="value">{{ note }}</p>
        </div>
        <div v-if="swapRefundedAmount" class="d-flex flex-row justify-content-between mt-1">
          <p class="header">{{ $t('SWAP_TOKEN.CREDITED_TO_BALANCE') }}</p>
          <p class="header" id="swap-sell-refunded-amount">{{ exchangeValue(swapRefundedAmount) }}</p>
          <CurrencyTooltip tooltipId="swap-sell-refunded-amount" :value="swapRefundedAmount"></CurrencyTooltip>
        </div>
      </div>
    </div>

    <div v-if="!isBuyToken && !isOrderToken && !isSellToken && !isSwapBuyToken && !isSwapSellToken" class="popup-content d-flex flex-column">
      <div class="d-flex justify-content-between">
        <div></div>
        <div class="btn-close" style="margin-right: -8px" @click="close()">
          <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
        </div>
      </div>
      <div v-if="!hasProperty" class="transaction-icon-bg d-flex justify-content-center align-items-center">
        <img src="../assets/img/transaction.svg" alt=""/>
      </div>
      <p class="text-color font-semibold" style="font-size: 26px">{{ type }}</p>
      <div class="d-flex flex-row justify-content-center" style="margin-top: -3px">
        <img class="clock-icon" src="../assets/img/clock.png" alt=""/>
        <p class="text-color-tertiary font-normal ml-1" style="font-size: 13px">{{ createdAt }}</p>
      </div>
      <div v-if="hasProperty" class="property-info mt-3">
        <b-img class="property-image" :src="propertyImage" alt=""/>
        <p class="property-name d-flex flex-row">{{ propertyName }}</p>
        <p class="transaction-status status-position" :style="{ 'background-color': statusBackground }">{{ status }}</p>
      </div>
      <p class="text-color font-semibold align-self-start mt-4 mb-2" style="font-size: 20px">
        {{ $t('TRANSACTION.TRANSACTION_DETAILS') }}
      </p>
      <div v-if="!hasProperty" class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('TRANSACTION.STATUS') }}</p>
        <p class="transaction-status" :style="{ 'background-color': statusBackground }">{{ status }}</p>
      </div>
      <div class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('TRANSACTION.EXTERNAL_ID') }}</p>
        <div class="d-flex flex-row align-items-center">
          <p class="value">{{ externalID ?? '-' }}</p>
          <img v-if="externalID" class="copy-icon ml-1" src="../assets/img/copy.png" @click="copyTransactionId" alt=""/>
        </div>
      </div>
      <div v-if="hasBlockchainTransaction" class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('TRANSACTION.BLOCKCHAIN_TRANSACTION_ID') }}</p>
        <a v-if="blockchainTransactionLink" class="link"
           :href="blockchainTransactionLink" target="_blank">{{ blockchainTransactionExternalId }}</a>
        <p v-else class="value">{{ blockchainTransactionExternalId }}</p>
      </div>
      <div v-if="isBuyTokenUsingVirtualBalance"
           class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('TRANSACTION.TOKENS') }}</p>
        <p class="value">{{ formatNumberIntl(numOfTokens) }}</p>
      </div>
      <div class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ isWithdrawal ? $t('WITHDRAWALS.AMOUNT') : $t('TRANSACTION.VALUE') }}</p>
        <p class="value" id="price-value">{{ exchangeValue(value) }}</p>
        <CurrencyTooltip tooltipId="price-value" :value="value"></CurrencyTooltip>
      </div>
      <div v-if="balanceUsed" class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('ORDER.BALANCE_USED') }}</p>
        <p class="value" id="balance-used">{{ exchangeValue(balanceUsed) }}</p>
        <CurrencyTooltip tooltipId="balance-used" :value="balanceUsed"></CurrencyTooltip>
      </div>
      <div v-if="isWithdrawal" class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('PROCESSING_FEE.TITLE') }}</p>
        <p v-if="processingFee <= 0" class="value">{{ $t("common.FREE") }}</p>
        <p v-if="processingFee > 0" class="value red" id="processing-fee">-{{ exchangeValue(processingFee) }}</p>
        <CurrencyTooltip v-if="processingFee > 0" tooltipId="processing-fee" :value="processingFee"></CurrencyTooltip>
      </div>
      <div v-if="displayVoucherInfo" class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('VOUCHER.PROMO_DISCOUNT') }} ({{ voucherCode }})</p>
        <p class="value" id="voucher-reward-amount">{{ exchangeValue(voucherRewardAmount) }}</p>
        <CurrencyTooltip tooltipId="voucher-reward-amount" :value="voucherRewardAmount"></CurrencyTooltip>
      </div>
      <div v-if="isWithdrawal" class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('SELL_TOKEN.RECEIVED_AMOUNT') }}</p>
        <p class="value" id="receive-amount">{{ exchangeValue(receivedAmount) }}</p>
        <CurrencyTooltip tooltipId="receive-amount" :value="receivedAmount"></CurrencyTooltip>
      </div>

      <div class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ $t('TRANSACTION.DESCRIPTION') }}</p>
        <p class="value">{{ note }}</p>
      </div>
      <div v-if="isBuyTokenUsingVirtualBalance" class="d-flex flex-row justify-content-between transaction-info-item">
        <p class="title">{{ isExpired ? $t('VIRTUAL_BALANCE.EXPIRED_ON') : $t('VIRTUAL_BALANCE.EXPIRES_ON') }}</p>
        <p class="value">{{ expirationTime }}</p>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment';
import {
  exchange,
  formatNumberIntl,
  getTransactionStatus,
  getTransactionStatusBgColor,
  getTransactionStatusColor,
  getTransactionStatusIcon,
  notify,
  obfuscateBlockchainInfo,
  urlImage
} from '@/helpers/common';
import { BLOCKCHAIN, STATUS, TRANSACTION_TYPE } from "@/constants/constants";
import CurrencyTooltip from "../components/CurrencyTooltip.vue"
import store from "@/store/store";

export default {
  emits: ['on-close'],
  components: {
    CurrencyTooltip,
  },
  data() {
    return {
      showModal: false,
      transaction: null,
      property: null,
    }
  },
  watch: {
    showModal(value) {
      if (value) {
        document.body.style.position = 'fixed';
        document.body.style.top = '0px';
        document.body.style.left = '0px';
        document.body.style.right = '0px';
      } else {
        document.body.style.position = '';
      }
    },
  },
  methods: {
    formatNumberIntl,
    openPopup(transaction, property) {
      this.showModal = true
      this.transaction = transaction
      this.property = property
    },
    close() {
      this.showModal = false
      this.$emit('on-close')
    },
    exchangeValue(value) {
      return exchange(value, 100, false, null, false, this.transaction?.exchange_rates?.rates)
    },
    copyTransactionId() {
      if (this.externalID) {
        navigator.clipboard.writeText(this.externalID);
        notify({ text: this.$t('common.COPIED') });
      }
    },
    copyReferenceId() {
      if (this.referenceId) {
        navigator.clipboard.writeText(this.referenceId);
        notify({ text: this.$t('common.COPIED') });
      }
    },
    copySellRequestUuid() {
      if (this.sellTokenRequestUuid) {
        navigator.clipboard.writeText(this.sellTokenRequestUuid);
        notify({ text: this.$t('common.COPIED') });
      }
    },
  },

  computed: {
    hasProperty() {
      return this.transaction && this.transaction.property;
    },
    propertyImage() {
      if (!this.property) {
        return require("../assets/img/default_avatar_purchase.png")
      }
      return urlImage(this.property.images[0]);
    },
    propertyName() {
      return this.property && `${this.property.name} - ${this.property.category.name}` || '';
    },
    createdAt() {
      return this.transaction && moment(this.transaction.created_at).format('DD/MM/YYYY HH:mm') || '';
    },
    type() {
      const type = this.transaction && this.transaction.type || ''
      return this.$t(`TRANSACTIONS_TYPES.${type}`)
    },
    status() {
      return this.transaction && this.$t(`TRANSACTIONS_STATUS.${getTransactionStatus(this.transaction)}`) || '';
    },
    statusBackground() {
      if (this.transaction) {
        const status = getTransactionStatus(this.transaction)
        if (status === 'FAILED' || status === 'EXPIRED' || status === 'REJECTED' || status === 'CANCELLED') {
          return '#bf2300'
        } else if (status === 'PAID' || status === 'COMPLETED' || status === 'RECEIVED' || status === "SWAPPED") {
          return '#81A431'
        } else if (status === 'APPROVED') {
          if (this.transaction.type === TRANSACTION_TYPE.WITHDRAWAL) {
            return '#CE7F2D'
          } else {
            return '#81A431'
          }
        } else if (status === "REFUNDED") {
          return '#474747'
        }
      }
      return '#CE7F2D'
    },
    statusIcon() {
      if (this.transaction) {
        return getTransactionStatusIcon(this.transaction)
      }
    },
    statusColor() {
      if (this.transaction) {
        return getTransactionStatusColor(this.transaction)
      }
    },
    statusBgColor() {
      if (this.transaction) {
        return getTransactionStatusBgColor(this.transaction)
      }
    },
    referenceId() {
      return this.transaction && this.transaction.reference_id;
    },
    externalID() {
      return this.transaction && (this.transaction.external_id || (this.transaction.payment && this.transaction.payment.external_id) || this.transaction.withdrawal && this.transaction.withdrawal.external_id)
    },
    hasBlockchainTransaction() {
      return this.transaction && this.transaction.blockchain_transaction;
    },
    blockchainTransactionExternalId() {
      if (this.transaction && this.transaction.blockchain_transaction && this.transaction.blockchain_transaction.external_id) {
        return obfuscateBlockchainInfo(this.transaction.blockchain_transaction.external_id) || '-'
      }
      return '-'
    },
    blockchainTransactionLink() {
      if (this.transaction && this.transaction.blockchain_transaction && this.transaction.blockchain_transaction.external_id) {
        return BLOCKCHAIN.POLYGON_SCAN_TRANSACTION_URL + this.transaction.blockchain_transaction.external_id
      }
      return null
    },
    orderUuid() {
      return this.transaction && this.transaction.order && this.transaction.order.uuid;
    },
    sellTokenRequestUuid() {
      return this.transaction && this.transaction.sell_token_request && this.transaction.sell_token_request.uuid
    },

    /* --- Transaction Flags --- */
    isBuyTokenUsingVirtualBalance() {
      return this.transaction && this.transaction.type === TRANSACTION_TYPE.BUY_TOKEN_VIRTUAL_BALANCE;
    },
    isBuyToken() {
      return this.transaction && this.transaction.payment && this.transaction.type === TRANSACTION_TYPE.BUY_TOKEN;
    },
    isOrderToken() {
      return this.transaction && this.transaction.type === TRANSACTION_TYPE.ORDER_TOKEN;
    },
    isSellToken() {
      return this.transaction && this.transaction.sell_token_request && this.transaction.type === TRANSACTION_TYPE.SELL_TOKEN
    },
    isSwapBuyToken() {
      return this.transaction && this.transaction.swap_token_transaction && this.transaction.type === TRANSACTION_TYPE.BUY_TOKEN
    },
    isSwapSellToken() {
      return this.transaction && this.transaction.swap_token_transaction && this.transaction.type === TRANSACTION_TYPE.SELL_TOKEN
    },
    isWithdrawal() {
      return this.transaction && this.transaction.type === TRANSACTION_TYPE.WITHDRAWAL;
    },
    /* --- End Transaction Flags --- */

    numOfTokens() {
      return this.transaction && this.transaction.num_of_tokens || 0;
    },
    pricePerToken() {
      if (this.property) {
        return this.property.price_per_token;
      }
      return 10000;
    },
    baseTokenAmount() {
      return this.pricePerToken * this.numOfTokens
    },
    value() {
      if (this.isBuyToken || this.isOrderToken || this.isSellToken || this.isSwapBuyToken || this.isSwapSellToken) {
        return this.baseTokenAmount;
      } else if (this.isWithdrawal) {
        return this.transaction.withdrawal.required_amount
      }
      return this.transaction.amount + this.transaction.amount_from_balance + this.transaction.amount_from_voucher
    },
    balanceUsed() {
      if (this.isBuyToken || this.isOrderToken || this.isSwapSellToken) {
        return this.transaction.amount_from_balance + this.transaction.transaction_fee_from_balance || 0
      }
      return 0
    },
    displayTransactionFees() {
      return this.transactionFee > 0 || this.$store.getters.configs?.display_transaction_fees || false;
    },
    transactionFee() {
      return this.transaction.transaction_fee + this.transaction.transaction_fee_from_balance + this.transaction.transaction_fee_from_voucher || 0
    },
    displayProcessingFees() {
      if (this.processingFee > 0) return true;
      const payment = this.transaction?.payment;
      if (!payment || payment.is_stripe || payment.is_paynow_qr) return true;
      return this.$store.getters.configs?.display_xendit_processing_fees !== false;
    },
    processingFee() {
      return this.transaction.fee + this.transaction.fee_from_voucher || 0
    },
    displayVoucherInfo() {
      if (this.voucherCode || this.voucherRewardAmount > 0) {
        return true
      }
      return this.$store.getters.configs?.enable_voucher_code_input || false
    },
    voucherCode() {
      return this.transaction.voucher_code?.code
    },
    voucherRewardAmount() {
      return this.transaction.amount_from_voucher + this.transaction.transaction_fee_from_voucher + this.transaction.fee_from_voucher;
    },
    totalPayment() {
      if (this.isBuyToken || this.isOrderToken) {
        return this.baseTokenAmount - this.balanceUsed + this.transactionFee + this.processingFee - this.voucherRewardAmount
      }
      return 0;
    },
    receivedAmount() {
      if (this.isSellToken) {
        return this.baseTokenAmount - this.transactionFee - this.processingFee + this.voucherRewardAmount
      } else if (this.isWithdrawal) {
        return this.transaction.amount + this.transaction.amount_from_balance
      }
      return 0;
    },
    swapTokensForFeeCover() {
      return this.isSwapSellToken && this.transaction.tokens_for_fee_cover || 0
    },
    swapRefundedAmount() {
      return this.isSwapSellToken && this.transaction.refunded_amount || 0
    },
    paymentMethod() {
      return this.transaction.payment && this.transaction.payment.payment_method;
    },
    paymentMethodFormatted() {
      if (!this.paymentMethod) return '';
      return this.paymentMethod
        .toLowerCase()
        .replace(/_/g, ' ')
        .replace(/\b\w/g, c => c.toUpperCase())
        .replace(/\bQr\b/gi, 'QR')
        .replace(/\bQris\b/gi, 'QRIS')
        .replace(/\bAtm\b/gi, 'ATM');
    },
    paymentChannel() {
      return this.transaction.payment && this.transaction.payment.payment_channel;
    },
    paymentText() {
      const DASH = '–';
      let text = null;
      if (this.isOrderToken) {
        text = this.$t('PAYMENT.BANK_TRANSFER');
      } else if (this.paymentChannel && this.paymentMethod) {
        text = `${this.paymentChannel} (${this.paymentMethodFormatted})`;
      } else if (this.paymentChannel) {
        text = this.paymentChannel;
      } else if (this.paymentMethod) {
        text = this.paymentMethodFormatted;
      }
      if (this.balanceUsed) {
        const balanceLabel = this.$t('BALANCE_HISTORY.GORO_BALANCE');
        if (text) {
          text = `${text} + ${balanceLabel}`;
        } else {
          text = `${balanceLabel}`;
        }
      }

      return text || DASH;
    },
    note() {
      return this.transaction && this.transaction.note || '';
    },
    rejectedReason() {
      if (this.transaction && this.isRejected) {
        if (this.transaction.sell_token_request) {
          return this.transaction.sell_token_request.reason
        }
      }
      return null
    },
    expirationTime() {
      return this.transaction && moment(this.transaction.expiration_time).format('DD/MM/YYYY HH:mm') || '';
    },
    isRejected() {
      return getTransactionStatus(this.transaction) === STATUS.REJECTED;
    },
    isExpired() {
      return this.transaction && moment() > moment(this.transaction.expiration_time) || true;
    },
  },
}
</script>

<style lang="scss" scoped>
.popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;

  .popup-content {
    position: relative;
    background-color: #fff;
    padding: 10px 18px 15px 18px;
    border-radius: 20px;
    text-align: center;
    width: 98%;
    max-height: 98%;
    margin: auto;
    overflow: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;

    ::-webkit-scrollbar {
      display: none;
    }

    @media screen and (min-width: 600px) {
      width: 80%;
      padding: 25px 45px 30px 45px;
    }

    @media screen and (min-width: 800px) {
      width: 70%;
    }

    @media screen and (min-width: 1100px) {
      width: 50%;
    }

    @media screen and (min-width: 1300px) {
      width: 40%;
    }

    .btn-close {
      background-color: rgb(221, 221, 221);
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .transaction-icon-bg {
      width: 96px;
      height: 96px;
      margin-bottom: 12px;
      align-self: center;
      border-radius: 50%;
      background-color: var(--primary-color);

      img {
        width: 88px;
        height: 88px;
      }
    }

    .transaction-status {
      color: white;
      padding: 3px 10px 3px 10px;
      border-radius: 15px;
      font-weight: bold;
      font-size: 14px;
      text-align: center;
    }

    .property-info {
      position: relative;
      width: 380px;
      height: 200px;
      align-self: center;

      .property-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 16px;
        background: var(--primary-background-color);
      }

      .property-name {
        background-image: linear-gradient(to right, #013D3D, #00666671);
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 0 0 16px 16px;
        padding: 10px 18px 10px 18px;
        font-weight: bold;
        font-size: 14px;
        color: #fff;
      }

      .status-position {
        position: absolute;
        top: 12px;
        right: 12px;
      }
    }

    .clock-icon {
      width: 14px;
      height: 14px;
      margin-top: 2px
    }

    .transaction-info-item {
      margin-bottom: 5px;
      font-size: 16px;
      font-weight: 600;
      font-family: "AcuminVariableConcept", Helvetica, sans-serif;
      align-items: center;

      .title {
        padding-right: 8px;
        color: var(--text-color-secondary);
        white-space: nowrap;
      }

      .value {
        text-align: end;
        color: var(--text-color);

        &.red {
          color: #A41100;
        }

        &.green {
          color: #5E8502;
        }
      }

      .copy-icon {
        width: 14px;
        height: 14px;
        cursor: pointer;
        margin-bottom: 1px;
      }

      .link {
        color: var(--text-color);
        text-decoration: underline;
      }
    }
  }

  .popup-content-v2 {
    position: relative;
    background-color: #fff;
    padding: 15px;
    border-radius: 20px;
    width: 94%;
    max-height: 96%;
    margin: auto;
    overflow: visible;
    -ms-overflow-style: none;
    font-family: "AcuminVariableConcept", Helvetica, sans-serif;

    @media screen and (min-width: 600px) {
      width: 80%;
      padding: 20px;
    }

    @media screen and (min-width: 800px) {
      width: 70%;
    }

    @media screen and (min-width: 1100px) {
      width: 50%;
    }

    @media screen and (min-width: 1300px) {
      width: 40%;
    }

    .btn-close {
      position: absolute;
      top: 0;
      right: 0;
      width: 30px;
      height: 30px;
      margin-top: -10px;
      margin-right: -10px;
      background-color: rgb(221, 221, 221);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .property-image {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 16px;
      background: var(--primary-background-color);
      margin-right: 12px;
    }

    .header {
      color: var(--primary-color);
      font-size: 20px;
      font-weight: 600;
    }

    .title {
      color: var(--text-color-secondary);
      font-size: 16px;
      font-weight: 500;
    }

    .value {
      color: var(--text-color);
      font-size: 16px;
      font-weight: 600;
    }

    .status {
      position: static;
      min-width: auto;
      border-radius: 8px;
      padding: 5px 8px;
      font-size: 12px;
      font-weight: 600;
    }

    .details {
      margin-top: 20px;
      padding: 20px;
      border-radius: 20px;
      box-shadow: 0 2px 4px 0 #00000021;
      text-align: left;
      overflow-y: auto;
      -ms-overflow-style: none;
    }

    .clock-icon {
      width: 12px;
      height: 12px;
      margin-top: 2px;
      margin-right: 8px;
    }

    .copy-icon {
      width: 14px;
      height: 14px;
      cursor: pointer;
      margin-bottom: 1px;
    }

    .link {
      color: var(--text-color);
      text-decoration: underline;
    }

    .compound-tooltip-icon {
      margin-bottom: 2px;
      width: 15px;
      height: 15px;
      z-index: 1;
    }
  }
}
</style>
