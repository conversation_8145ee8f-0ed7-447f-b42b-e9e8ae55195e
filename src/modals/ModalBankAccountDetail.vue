<template>
    <div class="popup" v-if="isOpen" style="z-index: 10001;">
        <div v-if="bankAccount" class="popup-content d-flex flex-column">
            <div class="d-flex justify-content-between">
                <div></div>
                <div class="btn-close" @click="close()">
                    <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
                </div>
            </div>
            <p class="font-26 font-weight-bold">{{ $t('BANK_ACCOUNT_HISTORY.TITLE') }}</p>
            <p v-if="note" class="mt-1">{{ note }}</p>
            <div class="d-flex flex-row align-items-center justify-content-center mt-2">
                <img class="img-clock" src="@/assets/img/clock.png" alt="" />
                <p class="color-gray font-13 ml-2">{{ time }}</p>
            </div>
            <div class="mt-3">
                <div class="d-flex flex-row justify-content-between my-3">
                    <p class="title">{{ $t('WITHDRAWALS.STATUS') }}</p>
                    <p class="value">{{ status }}</p>
                </div>
                <div class="d-flex flex-row justify-content-between my-3">
                    <p class="title">{{ $t('WITHDRAWALS.BANK_NAME') }}</p>
                    <p class="value">{{ bankName }}</p>
                </div>
                <div class="d-flex flex-row justify-content-between my-3">
                    <p class="title">{{ $t('WITHDRAWALS.ACCOUNT_NUMBER') }}</p>
                    <p class="value">{{ bankAccountNumber }}</p>
                </div>
                <div class="d-flex flex-row justify-content-between my-3">
                    <p class="title">{{ $t('WITHDRAWALS.ACCOUNT_HOLDER') }}</p>
                    <p class="value">{{ bankAccountHolder }}</p>
                </div>
                <input v-if="showInput" class="input-account-name" v-model="accountHolderName"
                    :placeholder="bankAccountHolder" />
                <div v-if="isForeinger" class="d-flex flex-row justify-content-between my-3">
                    <p class="title">{{ $t('WITHDRAWALS.BANK_COUNTRY') }}</p>
                    <p class="value">{{ bankCountry }}</p>
                </div>
                <div v-if="isForeinger" class="d-flex flex-row justify-content-between my-3">
                    <p class="title">{{ $t('WITHDRAWALS.BANK_ADDRESS') }}</p>
                    <p class="value">{{ bankAddress }}</p>
                </div>
                <div v-if="isForeinger" class="d-flex flex-row justify-content-between my-3">
                    <p class="title">SWIFT Code</p>
                    <p class="value">{{ swiftCode }}</p>
                </div>
                <div v-if="isForeinger" class="d-flex flex-row justify-content-between my-3">
                    <p class="title">{{ $t('WITHDRAWALS.NOTES') }}</p>
                    <p class="value">{{ notes }}</p>
                </div>
                <div v-if="reason && !showInput && isFailed" class="d-flex flex-row justify-content-between my-3">
                    <p class="title">{{ $t('WITHDRAWALS.REASON') }}</p>
                    <p class="value">{{ reason }}</p>
                </div>
                <div v-if="showInput" class="info-container mt-4 d-flex flex-row align-items-center">
                    <img src="@/assets/img/info-circle.svg" alt="" width="36" height="36">
                    <p class="text-left ml-3">{{ $t('BANK_ACCOUNT_HISTORY.CONFIRM_NAME_NOTE') }}</p>
                </div>
                <div v-if="showInput" class="mt-3 d-flex flex-row justify-content-end">
                    <b-button @click="verify" class="bg-main-color color-white" type="submit" :disabled="!enabledVerify"
                        style="padding: 8px 36px;">
                        {{ $t("WITHDRAWALS.VERIFY") }}
                    </b-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';
import { BANK_TYPE } from "../constants/constants"
import withdrawalsService from '../services/withdrawals.service';

export default {
    emits: ['on-close', 'on-success'],
    data() {
        return {
            isOpen: false,
            bankAccount: null,
            accountHolderName: '',
        }
    },

    methods: {
        openPopup(bankAccount) {
            this.bankAccount = bankAccount
            this.isOpen = true
        },
        close() {
            this.isOpen = false
            this.$emit('on-close')
        },

        async verify() {
            if (this.accountHolderName) {
                const res = await withdrawalsService.verifyAccountHolder({
                    account_holder_name: this.accountHolderName,
                });
                if (res) {
                    this.isOpen = false
                    this.$emit('on-success')
                }
            }
        }
    },

    computed: {
        note() {
            if (!this.bankAccount) {
                return ''
            }
            if (this.bankAccount.status !== 'FAILED') {
                return this.$t(`BANK_STATUS_NOTE.${this.bankAccount.status}`)
            }
            return '';
        },

        time() {
            if (!this.bankAccount) {
                return ''
            }
            return moment(this.bankAccount.created_at).format('DD/MM/YYYY HH:mm')
        },

        status() {
            if (!this.bankAccount) {
                return ''
            }
            return this.$t(`BANK_STATUS.${this.bankAccount.status}`)
        },

        isFailed() {
            if (!this.bankAccount) {
                return false
            }
            return this.bankAccount.status === 'FAILED'
        },

        bankName() {
            return `${this.bankAccount && this.bankAccount.bank_name || ''}`;
        },

        bankAccountNumber() {
            return this.bankAccount && this.bankAccount.account_number;
        },

        bankAccountHolder() {
            return this.bankAccount && this.bankAccount.account_holder_name;
        },

        isForeinger() {
            return this.bankAccount && this.bankAccount.bank_type === BANK_TYPE.FOREIGNER;
        },

        bankCountry() {
            return this.bankAccount && this.bankAccount.bank_country;
        },

        bankAddress() {
            return this.bankAccount && this.bankAccount.bank_address;
        },

        swiftCode() {
            return this.bankAccount && this.bankAccount.swift_code;
        },

        notes() {
            return this.bankAccount && this.bankAccount.note;
        },

        reason() {
            return this.bankAccount && this.bankAccount.reason;
        },

        showInput() {
            return this.bankAccount && this.bankAccount.status === 'NEED_CONFIRM_NAME'
        },

        enabledVerify() {
            return this.accountHolderName.length === this.bankAccountHolder.length
        },
    },
}
</script>

<style lang="scss" scoped>
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #3F3F3F;

    .popup-content {
        position: relative;
        background-color: #fff;
        padding: 20px 30px 20px 30px;
        border-radius: 20px;
        text-align: center;
        width: 98%;
        max-height: 98%;
        margin: auto;
        overflow: auto;
        -ms-overflow-style: none;
        scrollbar-width: none;

        ::-webkit-scrollbar {
            display: none;
        }

        @media screen and (min-width: 600px) {
            width: 80%;
        }

        @media screen and (min-width: 800px) {
            width: 70%;
        }

        @media screen and (min-width: 1100px) {
            width: 50%;
        }

        @media screen and (min-width: 1300px) {
            width: 40%;
        }

        .img-clock {
            width: 14px;
            height: 14px;
        }

        .title {
            color: #7C7C7C;
            font-weight: 500;
        }

        .value {
            font-weight: 500;
        }

        .input-account-name {
            width: 100%;
            border-radius: 8px;
            padding: 6px 16px;
            text-align: end;
            font-weight: 600;
            border: gray solid 1px;
            font-size: 20px;

            &:focus {
                border: var(--primary-color) solid 2px;
            }
        }

        .info-container {
            padding: 16px;
            background-color: #FFEED9;
            border: #FFA705 solid 1px;
            border-radius: 8px;
        }
    }

    .btn-close {
        background-color: rgb(221, 221, 221);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }
}
</style>