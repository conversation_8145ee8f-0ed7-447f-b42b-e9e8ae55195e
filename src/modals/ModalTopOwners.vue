<template>
    <div class="popup" v-if="showModal" style="z-index: 10001;">
        <div class="popup-content d-flex flex-column">
            <div class="d-flex justify-content-between">
                <div style="width: 30px;"></div>
                <div>
                    <p class="p-0 m-0">{{ $t('propertyDetail.TOKEN_HOLDERS_LEADERBOARD') }}</p>
                    <p class="font-22 font-weight-bold mb-2 p-0 m-0 text-center">{{ propertyName }}</p>
                </div>
                <div class="btn-close" @click="close()">
                    <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
                </div>
            </div>
            <div class="d-flex flex-column align-items-center flex-grow-1">
                <div class="d-flex flex-column align-items-center w-100">
                    <div class="d-flex flex-column align-items-center w-100 text-center">
                        <div class="top-1-container d-flex flex-column align-items-center">
                            <div class="avatar-container" :class="{ selected: top1Rank?.uuid === currentUserUuid }">
                                <img v-if="top1Rank?.avatar_url" :src="getUrl(top1Rank?.avatar_url)" />
                                <img v-else-if="top1Rank" class="default" src="@/assets/img/logo_white.png" />
                            </div>
                            <img class="img-bg" src="@/assets/img/ranking/rank-1.png" alt="" />
                            <p v-if="top1Rank" class="font-weight-bold text-name p-0 m-0 d-flex align-items-center">
                                <span>{{ top1Rank ? top1Rank.username : '' }}</span>
                                <icon-checker v-if="top1Rank.completed_kyc" :type="'blue'" class="ml-1"/>
                            </p>
                            <p v-if="top1Rank" class="text-id p-0 m-0">{{ $t('GORO_ID') }}: {{ top1Rank ? top1Rank.uuid : '' }}</p>
                        </div>
                        <div class="d-flex flex-row justify-content-between justify-content-md-around top23">
                            <div class="top-23-container d-flex flex-column align-items-center">
                                <div class="avatar-container"
                                    :class="{ selected2: top2Rank?.uuid === currentUserUuid }">
                                    <img v-if="top2Rank?.avatar_url" class="avatar"
                                        :src="getUrl(top2Rank?.avatar_url)" />
                                    <img v-else-if="top2Rank" class="default" src="@/assets/img/logo_white.png" />
                                </div>
                                <img class="img-bg" src="@/assets/img/ranking/rank-2.png" alt="" />
                                <p v-if="top2Rank" class="font-weight-bold text-name p-0 m-0 d-flex align-items-center">
                                    <span>{{ top2Rank ? top2Rank.username : '' }}</span>
                                    <icon-checker v-if="top2Rank.completed_kyc" :type="'blue'" class="ml-1"/>
                                </p>
                                <p v-if="top2Rank" class="text-id p-0 m-0">{{ $t('GORO_ID') }}: {{ top2Rank ? top2Rank.uuid : '' }}</p>
                                <div v-if="!top2Rank" style="height: 40px;"></div>
                            </div>
                            <div class="top-23-container d-flex flex-column align-items-center">
                                <div class="avatar-container"
                                    :class="{ selected3: top3Rank?.uuid === currentUserUuid }">
                                    <img v-if="top3Rank?.avatar_url" class="avatar"
                                        :src="getUrl(top3Rank?.avatar_url)" />
                                    <img v-else-if="top3Rank" class="default" src="@/assets/img/logo_white.png" />
                                </div>
                                <img class="img-bg" src="@/assets/img/ranking/rank-3.png" alt="" />
                                <p v-if="top3Rank" class="font-weight-bold text-name p-0 m-0 d-flex align-items-center">
                                    <span>{{ top3Rank ? top3Rank.username : '' }}</span>
                                    <icon-checker v-if="top3Rank.completed_kyc" :type="'blue'" class="ml-1"/>
                                </p>
                                <p v-if="top3Rank" class="text-id p-0 m-0">{{ $t('GORO_ID') }}: {{ top3Rank ? top3Rank.uuid : '' }}</p>
                                <div v-if="!top3Rank" style="height: 40px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="other-rank-container mt-3 mx-1 flex-grow-1 w-100">
                    <div v-for="(item, index) in otherRanks" class="item d-flex flex-row align-items-center"
                        :class="{ selected: currentUserUuid === item.uuid }">
                        <p class="m-0 mr-3 font-weight-bold font-18 text-right" style="min-width: 30px">{{ item.rank }}{{ item.rank !== 'N/A' ? '.' : '' }}
                        </p>
                        <img v-if="item.avatar_url" class="avatar" :src="getUrl(item.avatar_url)" />
                        <default-avatar width="32" height="32" v-else></default-avatar>
                        <div class="ml-3 d-flex flex-column flex-md-row align-items-start align-items-md-center w-100 text-left">
                            <p class="font-weight-bold m-0 font-14 flex-grow-1 d-flex align-items-center">
                                <span>{{ item.username }}</span>
                                <icon-checker v-if="item.completed_kyc" :type="'blue'" class="ml-1"/>
                            </p>
                            <p class="m-0 font-14">{{ $t('GORO_ID') }}: {{ item.uuid }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center" v-if="items && !items.length">{{ $t('propertyDetail.THIS_PROPERTY_HAS_NO_OWNERS') }}
            </p>
        </div>
    </div>
</template>

<script>

import DefaultAvatar from "../components/DefaultAvatar.vue"
import propertiesService from "../services/properties.service"
import { urlImage } from "../helpers/common"
import IconChecker from "../components/IconChecker.vue"

export default {
    components: {
        DefaultAvatar,
        IconChecker,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        propertyId: {
            type: Number,
        },
        propertyName: {
            type: String,
        },
    },
    emits: ['on-close'],
    data() {
        return {
            showModal: false,
            items: null,
            totalCount: 0,
        }
    },

    watch: {
        show(value) {
            this.showModal = value
            if (value && !this.items) {
                this.getTokenHolders()
            }
            if (value) {
                document.body.style.position = 'fixed';
                document.body.style.top = 0;
                document.body.style.left = 0;
                document.body.style.right = 0;
            } else {
                document.body.style.position = '';
            }
        }
    },

    methods: {
        async getTokenHolders() {
            const res = await propertiesService.getTopOwners(this.propertyId)
            if (res && res.data) {
                this.items = res.data || []
                this.totalCount = res.total
            }
        },

        getUrl(url) {
            return urlImage({ image: url })
        },

        close() {
            this.showModal = false
            this.$emit('on-close')
        }
    },

    computed: {
        rows() {
            return this.items || []
        },

        otherRanks() {
            if (this.rows.length > 3) {
                let items = [...this.rows]
                items.splice(0, 3)
                return items
            }
            return []
        },

        top1Rank() {
            if (this.rows.length) {
                return this.rows[0]
            }
            return null
        },

        top2Rank() {
            if (this.rows.length > 1) {
                return this.rows[1]
            }
            return null
        },

        top3Rank() {
            if (this.rows.length > 2) {
                return this.rows[2]
            }
            return null
        },

        currentUserUuid() {
            return this.$store.getters.userProfile?.uuid
        }
    },
}
</script>

<style lang="scss" scoped>
.popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    color: black;

    .popup-content {
        position: relative;
        background-color: #fff;
        padding: 30px;
        border-radius: 20px;
        text-align: center;
        width: 98%;
        height: 98%;
        margin: auto;
        overflow: hidden;

        @media screen and (min-width: 600px) {
            width: 80%;
        }

        @media screen and (min-width: 800px) {
            width: 70%;
        }

        @media screen and (min-width: 1100px) {
            width: 50%;
        }

        @media screen and (min-width: 1300px) {
            width: 40%;
        }
    }

    .btn-close {
        background-color: rgb(221, 221, 221);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }

    .other-rank-container {
        background-color: #F6F6F6;
        border-radius: 20px;
        height: 200px;
        overflow-y: scroll;

        .item {
            width: 100%;
            padding: 3px 20px 3px 20px;

            &.selected {
                color: #fff;
                background-color: var(--primary-color);
                border-radius: 16px;
                position: -webkit-sticky;
                position: sticky;
                top: 0;
                bottom: 0;
            }

            .avatar {
                width: 32px;
                height: 32px;
                min-width: 32px;
                min-height: 32px;
                border-radius: 50%;
                object-fit: cover;
                border: 2px solid var(--primary-color);
            }
        }
    }

    .top-1-container {
        width: 120px;
        position: relative;

        .img-bg {
            width: 100%;
            object-fit: contain;
            z-index: 1;
        }

        .text-name {
            font-size: 14px;
        }

        .text-id {
            font-size: 12px;
        }

        @media screen and (max-width: 550px) {
            width: 100px;

            .text-name {
                font-size: 13px;
            }

            .text-id {
                font-size: 11px;
            }
        }

        @media screen and (max-width: 320px) {
            width: 80px;

            .text-name {
                font-size: 12px;
            }

            .text-id {
                font-size: 9px;
            }
        }

        .avatar-container {
            position: absolute;
            top: 30px;
            left: 11px;
            width: 98px;
            height: 98px;
            border-radius: 50%;
            overflow: hidden;
            z-index: 0;

            &.selected {
                border: 3px solid #FFCF13;
            }

            @media screen and (max-width: 550px) {
                top: 26px;
                left: 10px;
                width: 81px;
                height: 81px;
            }

            @media screen and (max-width: 320px) {
                top: 20px;
                left: 8px;
                width: 65px;
                height: 65px;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .default {
                border-radius: 50%;
                object-fit: cover;
                border: 1px solid var(--primary-color);
                padding: 6px;
                background-color: var(--primary-color);
            }
        }
    }

    .top23 {
        width: 90%;
        margin-top: -100px;

        @media screen and (max-width: 550px) {
            margin-top: -80px;
        }

        @media screen and (max-width: 320px) {
            margin-top: -70px;
        }

        .top-23-container {
            width: 100px;
            position: relative;

            .text-name {
                font-size: 14px;
            }

            .text-id {
                font-size: 12px;
            }

            @media screen and (max-width: 550px) {
                width: 80px;

                .text-name {
                    font-size: 13px;
                }

                .text-id {
                    font-size: 11px;
                }
            }

            @media screen and (max-width: 320px) {
                width: 65px;

                .text-name {
                    font-size: 12px;
                }

                .text-id {
                    font-size: 9px;
                }
            }

            .img-bg {
                width: 100%;
                object-fit: contain;
                z-index: 1;
            }

            .avatar-container {
                position: absolute;
                top: 7px;
                left: 11.5px;
                width: 78px;
                height: 78px;
                border-radius: 50%;
                overflow: hidden;
                z-index: 0;

                &.selected2 {
                    border: 3px solid #9EA8BA;
                }

                &.selected3 {
                    border: 3px solid #DD722A;
                }

                @media screen and (max-width: 550px) {
                    top: 5px;
                    left: 8px;
                    width: 64px;
                    height: 64px;
                }

                @media screen and (max-width: 320px) {
                    top: 4.3px;
                    left: 7.3px;
                    width: 51px;
                    height: 51px;
                }

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .default {
                    border-radius: 50%;
                    object-fit: cover;
                    border: 1px solid var(--primary-color);
                    padding: 6px;
                    background-color: var(--primary-color);
                }
            }
        }
    }
}
</style>
