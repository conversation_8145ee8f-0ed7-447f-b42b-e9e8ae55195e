<template>
  <b-modal v-model="showModal" id="modal-prevent-closing"
           ref="modal" @hidden="$emit('on-close')" hide-footer>
    <template #modal-header>
      <p class="font-22 font-weight-bold m-0">{{ $t("SELL_TOKEN.YOUR_STATUS_ON", { propertyName: propertyName }) }}</p>
      <button id="btn_closeModalSellableStatus" type="button" class="close font-weight-bold" aria-label="Close" @click="$emit('on-close')">×</button>
    </template>
    <p class="font-18">- {{ $t("SELL_TOKEN.OWNING_TOKENS", { value: formatNumberIntl(owningTokens) }) }}</p>
    <p v-if="assetLockedTokens > 0" class="font-18 mb-0 mt-3"
       style="cursor: pointer" @click="toggleCollapse('lockedTokens')">
      - {{ $t("SELL_TOKEN.LOCKED_TOKENS", { value: formatNumberIntl(assetLockedTokens) }) }}
      <b-icon :icon='collapses.lockedTokens ? "caret-up-fill" : "caret-down-fill"' scale="0.8"
              style="margin-left: -3px; margin-bottom: -1.5px"></b-icon>
    </p>
    <b-collapse v-model="collapses.lockedTokens" id="collapse-lockedTokens">
      <div v-for="(item, index) in rows" class="item d-flex flex-row align-items-center">
        <div>
          <p class="font-weight-bold m-0 font-18">
            {{ formatNumberIntl(item.num_of_tokens) }}
          </p>
          <p class="m-0 font-14">
            {{ $t("ASSETS.LOCKED_UNTIL") }}: <span class="font-12 font-weight-bold">{{ item.lock_until }}</span>
          </p>
        </div>
      </div>
    </b-collapse>
    <p v-if="pendingSellTokens > 0" class="font-18 mt-3">
      - {{ $t("SELL_TOKEN.PENDING_TOKENS", { value: formatNumberIntl(pendingSellTokens) }) }}
    </p>
    <p v-if="owningTokens >0" class="font-18 font-weight-bold mt-3">
      - {{ $t("SELL_TOKEN.AVAILABLE_TOKENS_TO_SELL", { value: formatNumberIntl(availableTokens) }) }}
    </p>
  </b-modal>
</template>

<script>

import store from "@/store/store"
import { formatNumberIntl } from "@/helpers/common";

export default {
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    propertyUuid: {
      type: String,
    },
    propertyName: {
      type: String,
    },
    tokensStatus: {
      type: Object,
      required: true,
    },
  },
  emits: ["on-close"],
  data () {
    return {
      showModal: false,
      collapses: {
        lockedTokens: false,
      },
    }
  },

  watch: {
    show (value) {
      this.showModal = value
    },
  },

  methods: {
    formatNumberIntl,
    toggleCollapse (collapseId) {
      this.collapses[collapseId] = !this.collapses[collapseId]
    },
  },

  computed: {
    rows () {
      return this.assetLockedTokensDetail || []
    },

    maximumSellAmount () {
      return store.state.configs && parseInt(store.state.configs.sell_token_maximum_amount) || 1000
    },

    owningTokens () {
      return this.tokensStatus && parseInt(this.tokensStatus.asset_owning_tokens) || 0
    },

    pendingSellTokens () {
      return this.tokensStatus && parseInt(this.tokensStatus.pending_sell_tokens) || 0
    },

    assetSellableTokens () {
      return this.tokensStatus && parseInt(this.tokensStatus.asset_sellable_tokens) || 0
    },

    assetLockedTokens () {
      return this.tokensStatus && parseInt(this.tokensStatus.asset_locked_tokens) || 0
    },

    assetLockedTokensDetail () {
      return this.tokensStatus && this.tokensStatus.asset_locked_tokens_detail || []
    },

    availableTokens () {
      let availableTokens = this.assetSellableTokens - this.pendingSellTokens || 0
      return Math.min(availableTokens, this.maximumSellAmount)
    },
  },
}
</script>

<style lang="scss" scoped>
.item {
  width: 100%;
  padding: 10px 20px 10px 20px;
  border: 1px solid rgb(230, 230, 230);
  background-color: white;
  margin-bottom: 5px;
  margin-left: 10px;
  margin-right: 10px;
  -webkit-box-shadow: 0 1px 1px rgba(7, 55, 99, 0.16);
  box-shadow: 0 1px 1px rgba(7, 55, 99, 0.16);
}
</style>
