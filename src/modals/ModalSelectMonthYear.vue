<template>
  <b-modal v-model="showModal" header-class="modal-header no-border" footer-class="modal-footer" hide-footer modal-class="cls-goro-custom-modal cls-pickdate-modal" @hidden="$emit('on-close')" centered="true">
    <template #modal-header>
      <div class="d-flex justify-content-between align-items-center w-100">
          <div style="width: 30px;"></div>
          <h5 class="font-20 font-weight-bold header-title mb-0">
            {{ $t('FILTER.SELECT_MONTH_AND_YEAR') }}
          </h5>
          <button class="btn-close" @click="onClose()">
            <b-icon icon="x-lg" style="color: gray;" scale="1.6"></b-icon>
          </button>
      </div>
    </template>
    <div class="popup-content d-flex flex-column align-items-center p-0">
      <div class="cls-vue-scroll-picker-wrapper w-100">
        <div class="d-flex flex-row justify-content-center w-100">
          <VueScrollPicker class="cls-month-scroll" :options="months" :model-value="currentMonth" @update:model-value="changeMonth"/>
          <VueScrollPicker class="cls-year-scroll" :options="years" :model-value="currentYear" @update:model-value="changeYear"/>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-3 w-100">
          <b-button @click="resetMonthYear" class="btn btn-none btn-main white-reset font-13 font-weight mr-3" style="padding: 6px 25px 7px 25px;">
              {{ $t("common.RESET") }}
          </b-button>
          <b-button @click="applyFilter" class="btn btn-none btn-main font-13 font-weight" style="padding: 6px 25px 7px 25px;" :disabled="!enabledApply">
              {{ $t("common.APPLY") }}
          </b-button>
      </div>
    </div>
  </b-modal>
</template>

<script>
import moment from 'moment'
import "vue-scroll-picker/lib/style.css"
import { VueScrollPicker } from 'vue-scroll-picker'
export default {
  props: {
    show: {
      type: Boolean,
      default: false,

    },
  },
  emits: ['on-close', 'on-apply'],
  components: {
    VueScrollPicker,
  },
  data() {
    return {
      showModal: false,
      currentYear: moment().year(),
      currentMonth: moment().month() + 1,
      selectedMonthYear: null,
      enabledApply: true
    }
  },
  watch: {
    show(value) {
      this.showModal = value
    },
    currentMonth() {
      this.setSelectedMonthYear()
    },
    currentYear() {
      this.setSelectedMonthYear()
    },
  },
  mounted() {
    this.setSelectedMonthYear()
  },
  methods: {
      onClose() {
        this.showModal = false
        this.$emit('on-close')
      },
      applyFilter() {
        this.$emit('on-apply', this.selectedMonthYear)
        this.onClose()
      },
      resetMonthYear() {
        this.currentYear = moment().year()
        this.currentMonth = moment().month() + 1
        this.$emit('on-apply', null)
        this.onClose()
      },
      changeMonth(value) {
        this.currentMonth = value
      },
      changeYear(value) {
        this.currentYear = value
      },
      setSelectedMonthYear() {
        this.selectedMonthYear = `${this.currentYear}-${this.currentMonth}`
      }
  },

  computed: {
    years() {
      const currYear = moment().year();
      const lastYear = 2022;
      return Array.from({ length: currYear - lastYear + 1 }, (_, index) => lastYear + index).reverse();
    },
    months() {
      const locale = this.$i18n.locale.toLowerCase();
      moment.locale(locale);
      const monthNames = moment.months();
      return monthNames.map((name, index) => ({
        name,
        value: index + 1,
      }));
    },
  }
}
</script>

<style lang="scss">
.dp__outer_menu_wrap{
  flex: 1 !important;
}
.cls-pickdate-modal{
  .dp__main{
    .dp__outer_menu_wrap{
      flex: 1 !important;
      .dp__instance_calendar{
        .dp__menu_inner{
          .dp__month_year_row{
            .dp__month_year_select{
              font-weight: 700;
            }
          }
          .dp__calendar{
            .dp__calendar_row{
              margin: 10px 0 !important;
              .dp__calendar_item{
                flex: 1;
                flex-grow: 1;
                .dp__cell_inner{
                  width: 100% !important;
                  height: 43px !important;
                  padding: 0 !important;
                  border: 0 !important;
                  span.show-date{
                    display: block;
                    width: 100%;
                    height: 100%;
                    line-height: 30px;
                    background-color: transparent;
                    padding: 6px;
                  }
                  &.dp__range_between{
                    span.show-date{
                      color: #000000 !important;
                      background-color: #CBECEE !important;
                    }
                  }
                  &.dp__range_start,
                  &.dp__range_end{
                    width: 43px !important;
                    height: 43px !important;
                    background-color: #CBECEE;
                    span.show-date{
                      color: #fff !important;
                      border-radius: 100%;
                      background-color: #00918E !important;
                    }
                  }
                  &.dp__range_start{
                    border-radius: 100% 0 0 100%;
                  }
                  &.dp__range_end{
                    border-radius: 0 100% 100% 0;
                  }
                  &.dp__range_start{
                    &.dp__range_end{
                      border-radius: 100%;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

  }
}

.cls-vue-scroll-picker-wrapper{
  .vue-scroll-picker{
    // background-color: #CBECEE;
    .vue-scroll-picker-item{
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
      font-size: 16px;
      font-weight: 600;
      line-height: 19.2px;
      text-align: center;
      color: #303030;
      padding: 4px;
      &.vue-scroll-picker-item-selected{
        color: #303030;
        background-color: #CBECEE;
        border-radius: 18px;
      }
    }
    .vue-scroll-picker-layer-top{
      border-bottom: none !important;
    }
    .vue-scroll-picker-layer-bottom{
      border-top: none !important;
    }

    &.cls-month-scroll{
      .vue-scroll-picker-item{
        &.vue-scroll-picker-item-selected{
          border-radius: 18px 0 0 18px !important;
        }
      }
    }
    &.cls-year-scroll{
      .vue-scroll-picker-item{
        &.vue-scroll-picker-item-selected{
          border-radius: 0 18px 18px 0 !important;
        }
      }
    }
  }
}
</style>