<template>
  <div class="popup" v-if="isOpen" style="z-index: 10001;">
    <div class="popup-content d-flex flex-column">
      <div class="d-flex justify-content-between">
        <div></div>
        <div class="btn-close" @click="close()">
          <b-icon icon="x" style="color: gray;" scale="1.6"></b-icon>
        </div>
      </div>
      <div class="d-flex flex-column align-items-center pb-3">
        <div class="icon-bg d-flex justify-content-center align-items-center">
          <img :src="require(`@/assets/img/balance-history/${history.type.toLowerCase()}.png`)" alt=""/>
        </div>
        <p class="font-24 font-semibold mt-3">{{ $t(`BALANCE_HISTORY.TYPE_MAP.${history.type}`) }}</p>
        <div class="d-flex flex-row align-items-center">
          <img class="img-clock" src="@/assets/img/clock.png" alt=""/>
          <p class="color-gray font-13 ml-2">{{ getTime(history.created_at) }}</p>
        </div>
        <div class="mt-4 d-flex flex-column align-items-start w-100 px-0 px-md-4">
          <p v-if="transaction" class="font-20 font-semibold">{{ $t('TRANSACTION.TRANSACTION_DETAILS') }}</p>
          <div v-if="transaction" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
            <p>{{ $t('TRANSACTION.TRX_ID') }}</p>
            <div class="d-flex flex-row align-items-center">
              <p>{{ transactionExternalId }}</p>
              <img class="img-copy ml-2" src="@/assets/img/copy.png" alt="" @click="copyTransactionId"/>
            </div>
          </div>
          <div v-if="transaction" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
            <p>{{ $t('BALANCE_HISTORY.STATUS') }}</p>
            <p>{{ status }}</p>
          </div>
          <div v-if="transactionProperty" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
            <p>{{ $t('SELL_TOKEN_TABLE_HEADER.PROPERTY') }}</p>
            <p>{{ transactionProperty.name }}</p>
          </div>
          <div v-if="numOfTokens"
               class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
            <p>{{ $t('RENTAL_INCOME.TOTAL_TOKENS') }}</p>
            <p>{{ formatNumberIntl(numOfTokens) }}</p>
          </div>
          <div class="divider"></div>

          <div v-if="isBuy" class="d-flex flex-column align-items-start w-100">
            <p class="font-20 font-semibold">{{ $t('BALANCE_HISTORY.PAYMENT_DETAILS') }}</p>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('BALANCE_HISTORY.PAYMENT_METHOD') }}</p>
              <p>{{ paymentMethod }}</p>
            </div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('ORDER.PRICE') }}</p>
              <p>{{ exchangeValue(pricePerToken) }}/{{ $t('common.TOKEN') }}</p>
            </div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTION.VALUE') }}</p>
              <p>{{ exchangeValue(baseTokenAmount) }}</p>
            </div>
            <div v-if="balanceUsed" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('ORDER.BALANCE_USED') }}</p>
              <p class="red-text">-{{ exchangeValue(balanceUsed) }}</p>
            </div>
            <div v-if="displayTransactionFees" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTION_FEE.TITLE') }}</p>
              <p v-if="transactionFee > 0">{{ exchangeValue(transactionFee) }}</p>
              <p v-else class="value">{{ $t('common.FREE') }}</p>
            </div>
            <div v-if="displayProcessingFees" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('PROCESSING_FEE.TITLE') }}</p>
              <p v-if="processingFee > 0">{{ exchangeValue(processingFee) }}</p>
              <p v-else class="value">{{ $t('common.FREE') }}</p>
            </div>
            <div v-if="displayVoucherInfo" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('VOUCHER.PROMO_DISCOUNT') }} <span v-if="voucherCode">({{ voucherCode }})</span></p>
              <p class="red-text">
                <template v-if="voucherRewardAmount > 0">
                  -{{ exchangeValue(voucherRewardAmount) }}
                </template>
                <template v-else>-</template>
              </p>
            </div>
            <div v-if="transactionNote" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTIONS_TABLE_HEADER.DESCRIPTION') }}</p>
              <p class="ml-3">{{ transactionNote }}</p>
            </div>
            <div class="divider"></div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p class="font-20 font-semibold">{{ $t('PAYMENT.TOTAL_PAYMENT') }}</p>
              <p class="font-20 font-semibold">{{ exchangeValue(buyTokenTotalPayment) }}</p>
            </div>
          </div>

          <div v-if="isSell" class="d-flex flex-column align-items-start w-100">
            <p class="font-20 font-semibold">{{ $t('BALANCE_HISTORY.SALES_DETAILS') }}</p>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('ORDER.PRICE') }}</p>
              <p>{{ exchangeValue(pricePerToken) }}/{{ $t('common.TOKEN') }}</p>
            </div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTION.VALUE') }}</p>
              <p>{{ exchangeValue(baseTokenAmount) }}</p>
            </div>
            <div v-if="displayTransactionFees" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTION_FEE.TITLE') }}</p>
              <p v-if="transactionFee > 0" class="ml-3 red-text">-{{ exchangeValue(transactionFee) }}</p>
              <p v-else class="value">{{ $t('common.FREE') }}</p>
            </div>
            <div v-if="displayVoucherInfo" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('VOUCHER.PROMO_DISCOUNT') }} <span v-if="voucherCode">({{ voucherCode }})</span></p>
              <p>
                <template v-if="voucherRewardAmount > 0">
                  {{ exchangeValue(voucherRewardAmount) }}
                </template>
                <template v-else>-</template>
              </p>
            </div>
            <div v-if="transactionNote" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTIONS_TABLE_HEADER.DESCRIPTION') }}</p>
              <p>{{ transactionNote }}</p>
            </div>
            <div class="divider"></div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p class="font-20 font-semibold">{{ $t('SELL_TOKEN.RECEIVED_AMOUNT') }}</p>
              <p class="font-20 font-semibold">{{ exchangeValue(sellTokenReceivedAmount) }}</p>
            </div>
          </div>

          <div v-if="isSwap" class="d-flex flex-column align-items-start w-100">
            <p class="font-20 font-semibold">{{ $t('SWAP_TOKEN.SWAP_DETAILS') }}</p>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('ORDER.PRICE') }}</p>
              <p>{{ exchangeValue(pricePerToken) }}/{{ $t('common.TOKEN') }}</p>
            </div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTION.VALUE') }}</p>
              <p>{{ exchangeValue(baseTokenAmount) }}</p>
            </div>
            <div v-if="displayTransactionFees" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTION_FEE.TITLE') }}</p>
              <p v-if="transactionFee > 0" class="ml-3 red-text">-{{ exchangeValue(transactionFee) }}</p>
              <p v-else class="value">{{ $t('common.FREE') }}</p>
            </div>
            <div v-if="displayVoucherInfo" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('VOUCHER.PROMO_DISCOUNT') }} <span v-if="voucherCode">({{ voucherCode }})</span></p>
              <p>
                <template v-if="voucherRewardAmount > 0">
                  {{ exchangeValue(voucherRewardAmount) }}
                </template>
                <template v-else>-</template>
              </p>
            </div>
            <div v-if="swapTokensForFeeCover" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('SWAP_TOKEN.TOKENS_DEDUCTED_FOR_FEE') }} (-{{ formatNumberIntl(swapTokensForFeeCover) }} {{ swapTokensForFeeCover > 1 ? $t("PAYMENT.TOKENS") : $t("PAYMENT.TOKEN") }})</p>
              <p>{{ exchangeValue(swapTokensForFeeCover * pricePerToken) }}</p>
            </div>
            <div v-if="transactionNote" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTIONS_TABLE_HEADER.DESCRIPTION') }}</p>
              <p>{{ transactionNote }}</p>
            </div>
            <div class="divider"></div>
            <div v-if="swapRefundedAmount <= 0" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p class="font-20 font-semibold">{{ $t('ORDER.BALANCE_USED') }}</p>
              <p class="font-20 font-semibold">{{ exchangeValue(balanceUsed) }}</p>
            </div>
            <div v-else class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p class="font-20 font-semibold">{{ $t('SWAP_TOKEN.CREDITED_TO_BALANCE') }}</p>
              <p class="font-20 font-semibold">{{ exchangeValue(swapRefundedAmount) }}</p>
            </div>
          </div>

          <div v-if="isWithdrawal" class="d-flex flex-column align-items-start w-100">
            <p class="font-20 font-semibold">{{ $t('BALANCE_HISTORY.WITHDRAW_DETAILS') }}</p>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('WITHDRAWALS.AMOUNT') }}</p>
              <p>{{ exchangeValue(withdrawalRequiredAmount) }}</p>
            </div>
            <div v-if="displayVoucherInfo" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('VOUCHER.PROMO_DISCOUNT') }} <span v-if="voucherCode">({{ voucherCode }})</span></p>
              <p>
                <template v-if="voucherRewardAmount > 0">
                  {{ exchangeValue(voucherRewardAmount) }}
                </template>
                <template v-else>-</template>
              </p>
            </div>
            <div v-if="displayProcessingFees" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTION_FEE.TITLE') }}</p>
              <p v-if="processingFee > 0" class="red-text">-{{ exchangeValue(processingFee) }}</p>
              <p v-else class="value">{{ $t('common.FREE') }}</p>
            </div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('SELL_TOKEN.RECEIVED_AMOUNT') }}</p>
              <p>{{ exchangeValue(withdrawalReceivedAmount) }}</p>
            </div>
            <div v-if="transactionNote" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTIONS_TABLE_HEADER.DESCRIPTION') }}</p>
              <p class="ml-3">{{ transactionNote }}</p>
            </div>
            <div class="divider"></div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p class="font-20 font-semibold">{{ $t('BALANCE_HISTORY.TOTAL_WITHDRAW') }}</p>
              <p class="font-20 font-semibold">{{ exchangeValue(withdrawalRequiredAmount) }}</p>
            </div>
          </div>

          <div v-if="isAdd" class="d-flex flex-column align-items-start w-100">
            <p class="font-20 font-semibold">{{ $t('BALANCE_HISTORY.DEPOSITED_DETAILS') }}</p>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('WITHDRAWALS.AMOUNT') }}</p>
              <p>{{ exchangeValue(depositedAmount) }}</p>
            </div>
            <div v-if="transactionNote" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTIONS_TABLE_HEADER.DESCRIPTION') }}</p>
              <p class="ml-3">{{ transactionNote }}</p>
            </div>
            <div class="divider"></div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p class="font-20 font-semibold">{{ $t('BALANCE_HISTORY.TOTAL_DEPOSITED') }}</p>
              <p class="font-20 font-semibold">{{ exchangeValue(depositedAmount) }}</p>
            </div>
          </div>

          <div v-if="isDeduct" class="d-flex flex-column align-items-start w-100">
            <p class="font-20 font-semibold">{{ $t('BALANCE_HISTORY.DEDUCTED_DETAILS') }}</p>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('WITHDRAWALS.AMOUNT') }}</p>
              <p>{{ exchangeValue(deductedAmount) }}</p>
            </div>
            <div v-if="transactionNote" class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p>{{ $t('TRANSACTIONS_TABLE_HEADER.DESCRIPTION') }}</p>
              <p class="ml-3">{{ transactionNote }}</p>
            </div>
            <div class="divider"></div>
            <div class="d-flex flex-row align-items-start justify-content-between w-100 mt-2">
              <p class="font-20 font-semibold">{{ $t('BALANCE_HISTORY.TOTAL_DEDUCTED') }}</p>
              <p class="font-20 font-semibold">{{ exchangeValue(deductedAmount) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment';
import { exchange, formatNumberIntl, getTransactionStatus, notify } from '@/helpers/common';

export default {
  emits: ['on-close'],
  data() {
    return {
      isOpen: false,
      transaction: null,
      history: null,
    }
  },
  watch: {
    isOpen(value) {
      if (value) {
        document.body.style.position = 'fixed';
        document.body.style.top = 0;
        document.body.style.left = 0;
        document.body.style.right = 0;
      } else {
        document.body.style.position = '';
      }
    },
  },
  methods: {
    formatNumberIntl,
    openPopup(transaction, history) {
      this.isOpen = true
      this.transaction = transaction
      this.history = history
    },
    close() {
      this.isOpen = false
      this.$emit('on-close')
    },
    getTime(date) {
      return moment(date).format('DD/MM/YYYY HH:mm:ss')
    },

    exchangeValue(value) {
      return exchange(value)
    },

    copyTransactionId() {
      navigator.clipboard.writeText(this.transaction.external_id);
      notify({ text: this.$t('common.COPIED') });
    },
  },

  computed: {
    status() {
      return this.transaction && this.$t(`TRANSACTIONS_STATUS.${getTransactionStatus(this.transaction)}`) || "";
    },
    isBuy() {
      return this.history.type === 'BUY'
    },
    isSell() {
      return this.history.type === 'SELL'
    },
    isSwap() {
      return this.history.type === 'SWAP'
    },
    isWithdrawal() {
      return this.history.type === 'WITHDRAW'
    },
    isAdd() {
      return this.history.type === 'ADD'
    },
    isDeduct() {
      return this.history.type === 'DEDUCT'
    },
    transactionExternalId() {
      const id = this.transaction && this.transaction.external_id || ""
      if (id.length > 15) {
        return `${id.substring(0, 12)}...${id.substring(id.length - 3, id.length)}`
      }
      return id
    },
    transactionProperty() {
      return this.transaction && this.transaction.property
    },
    transactionNote() {
      return this.transaction && this.transaction.note || ""
    },
    numOfTokens() {
      return this.transaction && this.transaction.num_of_tokens || 0
    },
    pricePerToken() {
      return this.transaction && this.transactionProperty?.price_per_token || 0
    },
    baseTokenAmount() {
      return this.pricePerToken * this.numOfTokens
    },
    balanceUsed() {
      return this.transaction && this.transaction.amount_from_balance + this.transaction.transaction_fee_from_balance || 0
    },
    displayTransactionFees() {
      return this.transactionFee > 0 || this.$store.getters.configs?.display_transaction_fees || false;
    },
    transactionFee() {
      return this.transaction && this.transaction.transaction_fee + this.transaction.transaction_fee_from_balance + this.transaction.transaction_fee_from_voucher || 0
    },
    displayProcessingFees() {
      if (this.processingFee > 0) return true;
      const payment = this.transaction?.payment;
      if (!payment || payment.is_stripe || payment.is_paynow_qr) return true;
      return this.$store.getters.configs?.display_xendit_processing_fees !== false;
    },
    processingFee() {
      return this.transaction && this.transaction.fee + this.transaction.fee_from_voucher || 0
    },
    displayVoucherInfo() {
      if (this.voucherCode || this.voucherRewardAmount > 0) {
        return true
      }
      return this.$store.getters.configs?.enable_voucher_code_input || false
    },
    voucherCode() {
      return this.transaction && this.transaction.voucher_code && this.transaction.voucher_code.code
    },
    voucherRewardAmount() {
      return this.transaction && this.transaction.amount_from_voucher + this.transaction.transaction_fee_from_voucher + this.transaction.fee_from_voucher;
    },
    buyTokenTotalPayment() {
      return this.baseTokenAmount - this.balanceUsed + this.transactionFee + this.processingFee - this.voucherRewardAmount;
    },
    sellTokenReceivedAmount() {
      return this.baseTokenAmount - this.transactionFee - this.processingFee + this.voucherRewardAmount
    },
    swapTokensForFeeCover() {
      return this.transaction && this.transaction.tokens_for_fee_cover || 0
    },
    swapRefundedAmount() {
      return this.transaction && this.transaction.refunded_amount || 0
    },
    withdrawalReceivedAmount() {
      return this.transaction && this.transaction.withdrawal && this.transaction.withdrawal.amount || 0
    },
    withdrawalRequiredAmount() {
      return this.transaction && this.transaction.withdrawal && this.transaction.withdrawal.required_amount || 0
    },
    depositedAmount() {
      return this.history && this.history.inc_balance || 0
    },
    deductedAmount() {
      return this.history && this.history.dec_balance || 0
    },
    paymentMethod() {
      if (this.transaction && this.transaction.payment) {
        const isPayNowQR = this.transaction.payment.is_paynow_qr
        const isStripe = this.transaction.payment.is_stripe
        const isOrder = this.transaction.order
        const method = isPayNowQR ? 'PayNow QR' : this.transaction.payment.payment_method || (isStripe ? 'Stripe' : isOrder ? this.$t('PAYMENT.BANK_TRANSFER') : 'Xendit')
        const displayMethod = this.transaction.payment.payment_channel ? `${this.transaction.payment.payment_channel} (${method})` : method
        if (this.baseTokenAmount === this.balanceUsed) {
          return this.$t('BALANCE_HISTORY.GORO_BALANCE')
        } else if (this.balanceUsed > 0) {
          return `${displayMethod} + ${this.$t('BALANCE_HISTORY.GORO_BALANCE')}`
        }
        return displayMethod
      }
      return '-'
    },
  },
}
</script>

<style lang="scss" scoped>
.popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;

  .popup-content {
    position: relative;
    background-color: #fff;
    padding: 20px 30px 20px 30px;
    border-radius: 20px;
    text-align: center;
    width: 98%;
    max-height: 98%;
    margin: auto;
    overflow: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;

    ::-webkit-scrollbar {
      display: none;
    }

    @media screen and (min-width: 600px) {
      width: 80%;
    }

    @media screen and (min-width: 800px) {
      width: 70%;
    }

    @media screen and (min-width: 1100px) {
      width: 50%;
    }

    @media screen and (min-width: 1300px) {
      width: 40%;
    }

    .icon-bg {
      background-color: var(--primary-color);
      width: 96px;
      height: 96px;
      border-radius: 50%;

      img {
        width: 48px;
        height: 48px;
      }
    }

    .img-clock {
      width: 14px;
      height: 14px;
    }

    .divider {
      width: 100%;
      height: 1px;
      background-color: rgb(214, 214, 214);
      margin-top: 16px;
      margin-bottom: 16px;
    }

    .img-copy {
      width: 12px;
      height: 12px;
      cursor: pointer;
    }

    .red-text {
      color: #A41100;
    }
  }

  .btn-close {
    background-color: rgb(221, 221, 221);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}
</style>