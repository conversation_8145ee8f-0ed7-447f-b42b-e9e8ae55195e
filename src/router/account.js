import roles from "../constants/roles"
import AssetsOverview from "../pages/account/AssetsOverview.vue"
import { FOREIGNER, ROUTE_META_PAGE_TYPES } from "@/constants/constants"

const account = [
  {
    path: "/account",
    component: () => import("../layout/account/Account.vue"),
    name: "account",
    redirect: "/account/assets-overview",
    meta: {
      authorize: [roles.User],
      pageType: ROUTE_META_PAGE_TYPES.ACCOUNT,
      footer: 'hidden'
    },
    children: [
      {
        path: "assets-overview",
        component: AssetsOverview,
        name: "assetsOverview",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "buy-property",
        component: () => import("../pages/account/BuyProperty.vue"),
        name: "buyProperty",
      },
      {
        path: "referrals",
        component: () => import("../pages/account/Referral.vue"),
        name: "referrals",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "my-account",
        component: () => import("../pages/account/MyAccount.vue"),
        name: "myAccount",
        redirect: "/account/my-account/basic-info",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "my-account/basic-info",
        component: () => import("../pages/account/MyAccount.vue"),
        name: "basicInfo",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "my-account/kyc",
        component: () => import("../pages/account/Kyc.vue"),
        name: "kYC",
      },
      {
        path: "transactions",
        component: () => import("../pages/account/Transactions.vue"),
        name: "transactions",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "pending-tasks",
        component: () => import("../pages/account/PendingTasks.vue"),
        name: "pendingTasks",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "rental-income-reports",
        component: () => import("../pages/account/RentalIncomeReports.vue"),
        name: "rentalIncomeReports",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "pay",
        component: () => import("../pages/account/Pay.vue"),
        name: "pay",
      },
      {
        path: "pay/success",
        component: () => import("../pages/account/PaySuccess.vue"),
        name: "paySuccess",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "pay/failed",
        component: () => import("../pages/account/PayFailed.vue"),
        name: "payFailed",
      },
      {
        path: "order/detail",
        component: () => import("../pages/account/OrderDetail.vue"),
        name: "orderDetail",
      },
      {
        path: "sell-request",
        component: () => import("../pages/account/SellTokenRequest.vue"),
        name: "sellTokenRequest",
      },
      {
        path: "sell-request/success",
        component: () => import("../pages/account/SellTokenRequestSuccess.vue"),
        name: "sellTokenRequestSuccess",
      },
      {
        path: "swap-token",
        component: () => import("../pages/account/SwapToken.vue"),
        name: "swapToken",
      },
      {
        path: "swap-token/success",
        component: () => import("../pages/account/SwapTokenSuccess.vue"),
        name: "swapTokenSuccess",
      },
      {
        path: "withdrawals",
        name: "withdrawals",
        redirect: "/account/withdrawals/history",
        component: () => import("../pages/account/withdrawals/Withdrawals.vue"),
        children: [
          {
            path: "history",
            component: () => import("../pages/account/withdrawals/WithdrawalHistory.vue"),
            name: "withdrawalHistory",
            meta: {
              showQuiz: true
            },
          },
          {
            path: "bank-account-history",
            component: () => import("../pages/account/withdrawals/BankAccountHistory.vue"),
            name: "bankAccountHistory",
            meta: {
              showQuiz: true
            },
          },
          {
            path: "create-withdrawal",
            component: () => import("../pages/account/withdrawals/CreateWithdrawal.vue"),
            name: "createWithdrawal",
            meta: {
              showQuiz: true
            },
          },
        ],
      },
      {
        path: "confirm-bank-account",
        component: () => import("../pages/account/ConfirmBankAccount.vue"),
        name: "confirmBankAccount",
      },
      {
        path: "property/:uuid",
        component: () => import("../pages/marketplace/Detail.vue"),
        name: "accountPropertyDetail",
      },
      {
        path: "security/pin",
        component: () => import("../pages/account/Pin.vue"),
        name: "pin",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "security/password",
        component: () => import("../pages/account/Password.vue"),
        name: "password",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "security/change-password",
        component: () => import("../pages/account/ChangePassword.vue"),
        name: "changePassword",
      },
      {
        path: "security/two-factor-auth",
        component: () => import("../pages/account/TwoFactorAuthentication.vue"),
        name: "twoFactorAuth",
        meta: {
          showQuiz: true
        },
      },
      {
        path: "balance-history",
        component: () => import("../pages/account/BalanceHistory.vue"),
        name: "balanceHistory",
      },
    ],
  },
  {
    path: "/registration-complete",
    component: () => import("../layout/account/Account.vue"),
    name: "registrationCompleteParent",
    meta: {
      authorize: [roles.User],
      pageType: ROUTE_META_PAGE_TYPES.ACCOUNT,
      hideEarnMessage: true,
      containerClass: 'cls-custom-container-fluid container-fluid'
    },
    children: [
      {
        path: "",
        component: () => import("../pages/account/RegistrationComplete.vue"),
        name: "registrationComplete",
      },
    ]
  }
]

/**
 * Check the route path match the prefixes conditions
 * 
 * @param {*} path 
 * @param {*} prefixes 
 * @returns 
 */
function startsWithAny(path, prefixes) {
  if (!path || !prefixes) {
    return false
  }
  return prefixes.some(prefix => path.startsWith(prefix));
}

function createNewRoute (route, routePathPrefix, routeNameSuffix) {
  // Clone the route to avoid mutating the original
  let newRoute = { ...route }
  const allowedPaths = [
    "/account"
  ];

  if (newRoute.path && startsWithAny(newRoute.path, allowedPaths)) {
  // if (newRoute.path && newRoute.path.startsWith("/account")) { // Old condition
    newRoute.path = `${routePathPrefix}${newRoute.path}`
  }

  if (newRoute.redirect && startsWithAny(newRoute.redirect, allowedPaths)) {
  // if (newRoute.redirect && newRoute.redirect.startsWith("/account")) { // Old condition
    newRoute.redirect = `${routePathPrefix}${newRoute.redirect}`
  }

  if (newRoute.name) {
    newRoute.name = `${newRoute.name}${FOREIGNER.ROUTE_NAME_SUFFIX}`
  }
  if (newRoute.children) {
    newRoute.children = newRoute.children.map(route =>
      createNewRoute(route, routePathPrefix, routeNameSuffix)
    )
  }
  return newRoute
}

const accountForeigner = account.map(route =>
  createNewRoute(route, FOREIGNER.ROUTE_PATH_PREFIX, FOREIGNER.ROUTE_NAME_SUFFIX)
)

export { account, accountForeigner }
