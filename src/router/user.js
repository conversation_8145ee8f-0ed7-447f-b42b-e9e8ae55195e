import NotFound from "../pages/NotFoundPage"
import Home from "../pages/Home.vue"
import {FOREIGNER, ROUTE_META_PAGE_TYPES} from "@/constants/constants"

const user = [
    {
        path: "/",
        component: Home,
        name: "home",
    },
    {
        path: "/landing",
        component: () => import("../pages/Landing.vue"),
        name: "landing",
        meta: {
            header: "hidden",
            footer: "hidden",
            virtual_balance_bubble: "hidden"
        },
    },
    {
        path: "/landing-wa",
        component: () => import("../pages/LandingWa.vue"),
        name: "landingWa",
        meta: {
            header: "hidden",
            footer: "hidden",
            virtual_balance_bubble: "hidden"
        },
    },
    {
        path: "/register",
        component: () => import("../pages/auth/Register.vue"),
        name: "register",
    },
    {
        path: "/login",
        component: () => import("../pages/auth/Login.vue"),
        name: "login",
    },
    {
        path: "/login-admin",
        component: () => import("../pages/admin/LoginAdmin.vue"),
        name: "loginAdmin",
    },
    {
        path: "/forgot-password",
        component: () => import("../pages/auth/PasswordForgot.vue"),
        name: "forgotPassword",
    },
    {
        path: "/reset-password",
        component: () => import("../pages/auth/PasswordReset.vue"),
        name: "resetPassword",
    },
    {
        path: "/verify-email",
        component: () => import("../pages/auth/VerifyEmail.vue"),
        name: "verifyEmail",
    },
    {
        path: "/marketplace",
        component: () => import("../pages/marketplace/FilterList.vue"),
        name: "marketplace",
    },
    {
        path: "/golearn",
        name: "goLearn",
        component: () => import("../pages/golearn/Playlist.vue"),
        meta: {
            footer: 'hidden',
        },
        children: [
            {
                path: "",
                component: () => import("../pages/golearn/Playlist.vue"),
                name: "goLearnMain",
            },
            {
                path: ":slug",
                component: () => import("../pages/golearn/Playlist.vue"),
                name: "goLearnPlaylist",
            },
            {
                path: "video/:uuid",
                component: () => import("../pages/golearn/Playlist.vue"),
                name: "goLearnVideo",
            },
        ]
    },
    {
        path: "/events",
        component: () => import("../pages/event/Events.vue"),
        name: "events",
    },
    {
        path: "/event/:uuid",
        component: () => import("../pages/event/EventDetail.vue"),
        name: "eventDetail",
    },
    {
        path: "/about-us",
        component: () => import("../pages/AboutUs.vue"),
        name: "aboutUs",
    },
    {
        path: "/property/:uuid",
        component: () => import("../pages/marketplace/Detail.vue"),
        name: "propertyDetail",
    },
    {
        path: "/property-image/:id",
        component: () => import("../pages/PropertyImage.vue"),
        name: "propertyImage",
    },
    {
        path: "/privacy-policy",
        component: () => import("../pages/PrivacyPolicy.vue"),
        name: "privacyPolicy",
    },
    {
        path: "/terms-and-conditions",
        component: () => import("../pages/TermsAndConditions.vue"),
        name: "termsAndConditions",
    },
    {
        path: "/buy-success",
        component: () => import("../pages/BuySuccess.vue"),
        name: "buySuccess",
    },
    {
        path: "/get-browser-session-id",
        component: () => import("../pages/BrowserSessionId.vue"),
        name: "browserSessionId",
    },
    {
        path: "/order-detail",
        component: () => import("../pages/account/OrderDetail.vue"),
        name: "publicOrderDetail",
    },
    { path: "/:pathMatch(.*)*", component: NotFound },
]

function createNewRoute (route, routePathPrefix, routeNameSuffix) {
    const newRoute = { ...route };
    // Only prefix if not NotFound
    if (newRoute.path && newRoute.path !== "/:pathMatch(.*)*") {
        newRoute.path = `${routePathPrefix}${newRoute.path}`;
    }
    if (newRoute.name) {
        newRoute.name = `${newRoute.name}${routeNameSuffix}`;
    }
    if (newRoute.children) {
        // For children, don't prefix the path again (they are nested under parent)
        newRoute.children = newRoute.children.map(child =>
            createNewRoute(child, "", routeNameSuffix)
        );
    }
    return newRoute;
}

const userForeigner = user.map(route =>
    createNewRoute(route, FOREIGNER.ROUTE_PATH_PREFIX, FOREIGNER.ROUTE_NAME_SUFFIX)
);

export {user, userForeigner}
