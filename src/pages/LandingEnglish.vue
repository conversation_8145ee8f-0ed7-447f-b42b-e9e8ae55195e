<!--suppress HtmlUnknownTag -->
<template>
  <div class="landing">
    <div class="main__content">
      <b-container>
        <b-row>
          <b-col cols="11" xl="6" lg="7" md="6" sm="6">
            <div class="pt-2 pb-4">
              <h1 class="font-46">{{ $t("LANDING.TITLE") }}</h1>
              <p class="font-18">{{ $t("LANDING.DESCRIPTION_EN") }}</p>
            </div>
            <b-row style="margin-left: 1px;" align-v="end" class="pb-1">
              <b-button id="btn_LandingEn_RequestInvitation" class="btn-main mt-2 font-16" type="submit" style="border-radius: 18px; padding: 12px 40px;"
                        variant="none" @click="scrollToCalendlyForm()">
                {{ $t("LANDING.REQUEST_INVITATION") }}
              </b-button>
            </b-row>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="pt-4 pb-4" style="background-color: var(--primary-background-darker-color)">
      <b-container>
        <b-row align-h="center">
          <b-col class="text-center" cols="10" lg="8" xl="6">
            <h1 class="font-38">{{ $t("LANDING.FEATURED_PROPERTIES") }}</h1>
            <label class="mb-3 font-18">{{ $t("LANDING.FEATURED_PROPERTIES_DESCRIPTION") }}</label>
          </b-col>
        </b-row>
        <b-row class="justify-content-md-center pt-4 pb-1">
          <b-col cols="12" xl="4" lg="4" md="9" class="pb-4" v-for="property in properties" :key="property.id">
            <property-light-card-for-landing :property="property" show-irr-ery="true"
                                       :bottom-button-text='$t("LANDING.REQUEST_INVITATION")'
                                       @on-clicked="scrollToCalendlyForm()"/>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="pb-4 pt-4 pb-lg-5 pt-lg-5" style="background-color: var(--primary-background-color)">
      <b-container>
        <b-row align-h="center">
          <b-col class="text-center" cols="10" lg="8" xl="6">
            <h1 class="font-38 mt-0">{{ $t("LANDING.WHY") }}</h1>
            <label class="col-10 col-lg-9 col-xl-8">{{ $t("LANDING.WHY_DESCRIPTION") }}</label>
          </b-col>
        </b-row>
        <b-row class="pt-3">
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="@/assets/img/ic_appreciation.svg" alt=""/>
              <h5>{{ $t("LANDING.VALUE_APPRECIATION") }}</h5>
              <p class="font-16">{{ $t("LANDING.VALUE_APPRECIATION_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="@/assets/img/ic_inflation.svg" alt=""/>
              <h5>{{ $t("LANDING.HEDGE_FOR_INFLATION") }}</h5>
              <p class="font-16">{{ $t("LANDING.HEDGE_FOR_INFLATION_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="@/assets/img/ic_income.svg" alt=""/>
              <h5>{{ $t("LANDING.PASSIVE_INCOME") }}</h5>
              <p class="font-16">{{ $t("LANDING.PASSIVE_INCOME_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="@/assets/img/ic_wealth.svg" alt=""/>
              <h5>{{ $t("LANDING.STOREHOLD_OF_WEALTH") }}</h5>
              <p class="font-16">{{ $t("LANDING.STOREHOLD_OF_WEALTH_DESCRIPTION") }}</p>
            </div>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="text-center pb-3 pt-3" style="background-color: var(--primary-background-darker-color)">
      <b-container class="benefit-container">
        <b-row class="align-items-center" align-h="around">
          <b-col cols="12" xl="6" lg="6">
            <h1 class="font-38 text-center mt-3">{{ $t("LANDING.OUR_INVESTOR_BENEFIT") }}</h1>
            <p class="font-18 text-center">{{ $t("LANDING.HIGH_RETURNS_AND_LOW_VOLATILITY") }}</p>
            <img class="mt-4 mb-3" style="width: 110%; margin-left: -20px" src="@/assets/img/landing_benefit_1.png"
                 alt="Landing Benefit 1">
          </b-col>
          <b-col cols="12" xl="6" lg="6">
            <img class="mt-2 mb-3" style="width: 105%;" src="@/assets/img/landing_benefit_2.png"
                 alt="Landing Benefit 2">
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="text-center pb-1 pt-3" style="background-color: var(--primary-background-color)">
      <b-container>
        <b-row class="align-items-center" align-h="around">
          <b-col cols="12">
            <h1 class="font-38 text-center">{{ $t("LANDING.TESTIMONIALS") }}</h1>
            <b-row v-if="innerWidth >= 992">
              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-2-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%"
                           src="@/assets/img/landing_testimonial_florian_k.png" alt="Florian K">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_1_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">{{ $t("LANDING.TESTIMONIAL_1_TITLE") }}</p>
                    </b-col>
                  </b-row>
                  <b-row>
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_1_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-2-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%"
                           src="@/assets/img/landing_testimonial_karan_s.png" alt="Karan S">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_2_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">
                        {{ $t("LANDING.TESTIMONIAL_2_TITLE") }}
                      </p>
                    </b-col>
                  </b-row>
                  <b-row>
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_2_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>

              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-2-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%"
                           src="@/assets/img/landing_testimonial_alvin_d.png" alt="Alvin D">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_3_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">
                        {{ $t("LANDING.TESTIMONIAL_3_TITLE") }}
                      </p>
                    </b-col>
                  </b-row>
                  <b-row>
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_3_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>
            </b-row>
            <b-row v-if="innerWidth < 992">
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-2-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;"
                         src="@/assets/img/landing_testimonial_florian_k.png" alt="Florian K.">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_1_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_1_TITLE") }}</p>
                      <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_1_COMMENT") }}</p>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-2-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;"
                         src="@/assets/img/landing_testimonial_karan_s.png" alt="Karan S.">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_2_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_2_TITLE") }}</p>
                      <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_2_COMMENT") }}</p>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-2-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;"
                         src="@/assets/img/landing_testimonial_alvin_d.png" alt="Alvin D.">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_3_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_3_TITLE") }}</p>
                      <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_3_COMMENT") }}</p>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
            </b-row>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div ref="calendlyForm" style="background-color: var(--primary-background-darker-color);overflow:hidden;">
      <h1 class="font-38 text-center pt-4 mb-5">{{ $t("LANDING.SCHEDULE_A_CALL") }}</h1>
      <CalendlyScriptLoader :style="{ marginTop: innerWidth > 650 ? '-60px' : '0' }"/>
    </div>
  </div>
</template>

<script>
import { urlImage } from "@/helpers/common"
import propertiesService from "../services/properties.service"
import PropertyLightCardForLanding from "../components/Cards/PropertyLightCardForLanding"
import RippleButton from "../components/RippleButton.vue"
import { defineRule, Form, Field } from "vee-validate"
import CalendlyScriptLoader from "@/scripts/CalendlyScriptLoader"
import { gtmTrackEvent } from "../helpers/gtm"
import { GTM_EVENT_NAMES } from "../constants/gtm"
import { STORAGE_KEYS } from "@/constants/constants";

let DEFAULT_RESEND_OTP_TIME_IN_SECONDS = 120

export default {
  components: {
    Field,
    Form,
    defineRule,
    PropertyLightCardForLanding,
    RippleButton,
    CalendlyScriptLoader
  },

  data () {
    return {
      title: "GORO | Property for All",
      innerWidth: 0,
      properties: [],
    }
  },
  async mounted () {
    window.fbq("track", "Landing:PageView")
    this.$i18n.locale = "en"
    this.handleWindowResize(); // Call the handler on component mount
    window.addEventListener('resize', this.handleWindowResize);
    await this.getProperties()
  },
  async beforeUpdate () {
    this.$i18n.locale = "en"
  },
  async beforeDestroy () {
    window.removeEventListener('resize', this.handleWindowResize);
    if (this.userProfile) {
      const preferredLocale = localStorage.getItem(STORAGE_KEYS.PREFERRED_LOCALE.key)
      if (preferredLocale) {
        this.$i18n.locale = preferredLocale
      }
    }
  },
  methods: {
    handleWindowResize () {
      this.innerWidth = window.innerWidth
    },
    getAvatar (images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    async getProperties () {
      const result = await propertiesService.getFeatures()
      if (result && result.data) {
        this.properties = result.data
      }
    },
    onWhatIsGOROClicked () {
      window.fbq("trackCustom", "Landing:WhatIsGOROClicked")
      gtmTrackEvent({
        event: GTM_EVENT_NAMES.WHAT_IS_GORO,
      })
    },
    scrollToCalendlyForm () {
      window.fbq("trackCustom", "Landing:JoinNowClicked")
      gtmTrackEvent({
        event: GTM_EVENT_NAMES.USER_CLICKED_JOINNOW,
      })
      this.$refs.calendlyForm.scrollIntoView({ behavior: "smooth" })
    },
  },
  computed: {
    userProfile () {
      return this.$store.getters.userProfile
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
}
</script>
<style lang="scss">
.landing {
  padding-top: 0px !important;

  h1 {
    font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
    font-weight: 500;
    color: var(--primary-color);
  }

  p {
    color: var(--primary-color);
  }

  .main__content {
    padding-top: 10px;
    padding-bottom: 40px;
    background-size: cover;
    background-image: url('~@/assets/img/landing_banner_min_w_1100.png');

    @media only screen and (max-width: 1100px) {
      background-image: url('~@/assets/img/landing_banner_max_w_1100.png');
    }

    @media only screen and (max-width: 800px) {
      background-image: url('~@/assets/img/landing_banner_max_w_800.png');
    }

    @media only screen and (max-width: 650px) {
      background-image: url('~@/assets/img/landing_banner_max_w_650.png');
    }
  }

  .w-card {
    padding: 15px 25px 25px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-color);
    border-radius: 16px;

    img {
      width: 24px;
      height: 24px;
      margin-bottom: 5px;
    }

    label {
      padding-left: 0.5rem;
      font-size: 20px;
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-2-a {
    padding: 15px 25px 50px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-darker-color);
    border-radius: 32px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 22px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-2-b {
    padding: 10px 25px 15px;
    margin-left: 15px;
    margin-right: 15px;
    background-color: var(--primary-background-darker-color);
    border-radius: 32px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 22px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-3 {
    padding: 15px 25px 30px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-color);
    border-radius: 16px;

    img {
      width: 30px;
      height: 30px;
    }

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 18px;
      color: var(--primary-background-color);
    }

    p {
      font-size: 16px;
      color: var(--primary-background-color);
    }
  }

  .benefit-container {
    //max-width: 1200px;
  }
}
</style>
