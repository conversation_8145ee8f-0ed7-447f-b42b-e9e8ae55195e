<template xmlns="http://www.w3.org/1999/html">
  <div class="home">
    <div v-if="!calculatorOnly" class="main__content goro-new-banner cls-banner-section" :class="getSectionThemeClass(sectionsName.BANNER)">
      <b-container class="goro-banner-container">
        <b-row>
          <b-col cols="12" xl="6" lg="6" md="8" sm="8" class="goro-content-banner">
            <div class="pb-2">
              <h1 class="font-46">{{ $t("LANDING.TITLE") }}</h1>
              <p class="font-18">{{ $t("LANDING.DESCRIPTION") }}</p>
            </div>
            <b-row style="margin-left: 1px;" class="goro-main-actions mt-3">
              <ripple-button id="btn_home_viewGoroVideo" ref="videoButton" class="mr-2" :video-id='goroVideoId'></ripple-button>
              <router-link v-if="!userProfile" :to="{ name: 'register' }">
                <b-button id="btn_home_getStartedEvent" class="btn-main goro-view-property" variant="none" @click="trackGtmEvent(gtmEvent.getStartedEvent)">
                  {{ $t("AUTH.GET_STARTED") }}
                </b-button>
              </router-link>
              <router-link v-if="userProfile" :to="{ name: 'marketplace' }">
                <b-button id="btn_home_top_buyProperty" class="btn-main goro-view-property" variant="none" @click="trackGtmEvent(gtmEvent.buyPropertiesEvent)">
                  {{ $t("ASSETS.VIEW_PROPERTIES") }}
                </b-button>
              </router-link>
            </b-row>
            <div v-if="!hasEventBanner" class="ml-2 mt-5">
              <p class="font-semibold" style="font-size: 15px">{{ $t("SANDBOX.HEADER_FOOTER") }}</p>
            </div>
            <EventBanner @loaded="hasEventBanner = true"/>
          </b-col>
          <b-col cols="12" xl="6" lg="6" md="4" sm="4" class="goro-banner-carousel">
            <div class="parent">
              <Vue3Marquee :clone="true" :vertical="true" :direction="'reverse'" :duration="durationMarquee" class="block1">
                <div v-for="(item, index) in carouselItems.block1" :key="index" class="item">
                  <img width="180" height="237" :src="require(`@/assets/img/banners/new-carousels/${item.image}`)" alt="Item" class="item-image"/>
                </div>
              </Vue3Marquee>
              <Vue3Marquee :clone="true" :vertical="true" :duration="durationMarquee" class="block2">
                <div v-for="(item, index) in carouselItems.block2" :key="index" class="item">
                  <img width="180" height="237" :src="require(`@/assets/img/banners/new-carousels/${item.image}`)" alt="Item" class="item-image"/>
                </div>
              </Vue3Marquee>
              <Vue3Marquee :clone="true" :vertical="true" :direction="'reverse'" :duration="durationMarquee" class="block3">
                <div v-for="(item, index) in carouselItems.block3" :key="index" class="item">
                  <img width="180" height="237" :src="require(`@/assets/img/banners/new-carousels/${item.image}`)" alt="Item" class="item-image"/>
                </div>
              </Vue3Marquee>
            </div>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div v-if="false" class="pt-4 pb-4 cls-download-our-app-section" :class="getSectionThemeClass(sectionsName.DOWNLOAD_OUR_APP)">
      <b-container>
        <div class="text-center">
          <h1 class="font-38 mt-0">{{ $t("LANDING.DOWNLOAD_OUR_APP") }}</h1>
        </div>
        <b-row class="pt-2 justify-content-center">
          <a class="mx-2 mt-2" :href="storeLink.appStore" target="_blank" @click="trackGtmEvent(gtmEvent.appStoreEvent)">
            <img width="220" height="65" src="../assets/img/store/appstore.png" alt=""/>
          </a>
          <a class="mx-2 mt-2" :href="storeLink.playStore" target="_blank" @click="trackGtmEvent(gtmEvent.playStoreEvent)">
            <img width="220" height="65" src="../assets/img/store/playstore.png" alt=""/>
          </a>
        </b-row>
      </b-container>
    </div>
    <div v-if="!calculatorOnly" class="pt-4 pb-4 goro-light-section cls-how-it-work-section" :class="getSectionThemeClass(sectionsName.HOW_IT_WORKS)">
      <b-container>
        <div class="text-center">
          <h1 class="font-38 mt-0">{{ $t("LANDING.HOW_IT_WORKS") }}</h1>
          <p class="font-18 mb-3">{{ $t("LANDING.HOW_IT_WORKS_DESCRIPTION") }}</p>
        </div>
        <b-row class="pt-2">
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card">
              <img src="../assets/img/ic_browse.svg" alt=""/>
              <label>{{ $t("LANDING.BROWSE") }}</label>
              <p class="font-16">{{ $t("LANDING.BROWSE_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card">
              <img src="../assets/img/ic_purchase.svg" alt=""/>
              <label>{{ $t("LANDING.PURCHASE") }}</label>
              <p>{{ $t("LANDING.PURCHASE_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card">
              <img src="../assets/img/ic_earn.svg" alt=""/>
              <label>{{ $t("LANDING.EARN") }}</label>
              <p>{{ $t("LANDING.EARN_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card">
              <img src="../assets/img/ic_exit.svg" alt=""/>
              <label>{{ $t("LANDING.EXIT") }}</label>
              <p>{{ $t("LANDING.EXIT_DESCRIPTION") }}</p>
            </div>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div v-if="!calculatorOnly" class="pt-1 pb-4 cls-featured-properties-section" :class="getSectionThemeClass(sectionsName.FEATURED_PROPERTIES)">
      <b-container class="mt-0">
        <b-row align-h="center">
          <b-col class="text-center" cols="10" lg="8" xl="6">
            <h1 class="font-38">{{ $t("LANDING.FEATURED_PROPERTIES") }}</h1>
            <label class="mb-3 font-18">{{ $t("LANDING.FEATURED_PROPERTIES_DESCRIPTION") }}</label>
          </b-col>
        </b-row>
        <b-row class="justify-content-md-center pt-4 pb-1">
          <b-col cols="12" xl="4" lg="4" md="4" class="pb-4" v-for="property in propertiesFeatured" :key="property.id">
            <property-light-card :property="property"/>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="pt-1 pb-1 goro-light-section cls-calculator-simulation-section" style="position: relative;" ref="calculatorComponent" :class="getSectionThemeClass(sectionsName.CALCULATOR_SIMULATION)">
      <div ref="calculatorHiddenComponent" style="height: 0px; position: absolute; top: -80px; pointer-events: none;"></div>
      <b-container :class="`${calculatorOnly ? '' : 'mt-4'} mb-3`">
        <h1 v-if="!calculatorOnly" class="font-38 text-center mt-0 mb-0">{{ $t("LANDING.CALCULATOR_SIMULATION") }}</h1>
        <b-row align-h="center">
          <b-col cols="12" lg="6" xl="6" class="mt-3 mb-2 pr-3">
            <div class="calculator-card mt-3">
              <h5>{{ $t("LANDING.SELECT_PROPERTY") }}</h5>
              <v-select class="goro-select" v-model="propertyForCalculator" :options="propertiesForCalculator"
                        :clearable="false" label="name">
                <template v-slot:option="option">
                  {{ option.name }}
                </template>
                <template #selected-option="option">
                  {{ option.name }}
                </template>
              </v-select>
              <div class="d-flex justify-content-center">
                <div class="segmented-control mt-4">
                  <b-button id="btn_home_oneTimeInvestment" :class="{ active: !monthlyInvestment}" variant="none" @click="monthlyInvestment = false">
                    {{ $t("LANDING.ONE_TIME") }}
                  </b-button>
                  <b-button id="btn_home_monthlyInvestment" :class="{ active: monthlyInvestment }" variant="none" @click="monthlyInvestment = true">
                    {{ $t("LANDING.MONTHLY") }}
                  </b-button>
                </div>
              </div>
              <div class="d-flex justify-content-between">
                <h5 v-if="!monthlyInvestment" class="mt-4">
                  {{ $t("LANDING.INITIAL_PURCHASE", { value: currency }) }}
                </h5>
                <h5 v-if="monthlyInvestment" class="mt-4">
                  {{ $t("LANDING.MONTHLY_PURCHASE", { value: currency }) }}
                </h5>
              </div>
              <b-form-input v-model="initialPurchase" type="text" style="height: 40px; color: var(--primary-color)"
                            :placeholder="initialPurchaseHint" :formatter="formatInitialPurchase"/>

              <h5 class="mt-4">{{ $t("LANDING.PERIOD", { value: investmentPeriod }) }}</h5>
              <Slider v-model="investmentPeriod" class="slider mt-2 mb-3"
                      :min='1' :max='10' :step='1' :lazy='false' :tooltips='false'/>

              <div class="d-flex justify-content-between" style="margin-top: -5px">
                <h5 class="mt-3">{{ $t("LANDING.EXPECTED_RENTAL_YIELD") }}</h5>
                <p class="rental-yield">{{ rentalYieldPercentFormatted }}%</p>
              </div>
              <div class="d-flex justify-content-between mb-3">
                <div>
                  <b-form-radio v-model="selectedEry" value="LAST_MONTH" class="mt-0">
                    {{ $t("LANDING.LAST_MONTH") }}
                  </b-form-radio>
                  <b-form-radio v-model="selectedEry" value="AVERAGE" class="mt-2">
                    {{ $t("LANDING.AVERAGE") }}
                  </b-form-radio>
                </div>
              </div>
              <div v-if="false" class="d-flex justify-content-between" style="margin-top: 12px">
                <div>
                  <b-form-checkbox v-model="includeEca"
                                   style="font-weight: 400; font-size: 16px; z-index: 0; margin-top: 9px">
                    {{ $t("LANDING.INCLUDE_EXPECTED_CAPITAL_APPRECIATION") }}
                    <img class="compound-tooltip-icon" src="../assets/img/info-circle-fill.png" id="tooltip-eca" alt="">
                  </b-form-checkbox>
                  <b-tooltip variant="secondary" target="tooltip-eca" triggers="hover" placement="top">
                    {{ $t("propertyDetail.ECA_ANNUAL_TOOLTIP") }}
                  </b-tooltip>
                </div>
                <p class="rental-yield font-28">{{ ecaPercent }}%</p>
              </div>
              <b-form-checkbox v-model="compoundByReinvesting"
                               class="mt-1" style="font-weight: 400; font-size: 16px; z-index: 0">
                {{ $t("LANDING.COMPOUND_BY_AUTO_PURCHASE") }}
                <img class="compound-tooltip-icon"
                     src="../assets/img/info-circle-fill.png" id="tooltip-compound" alt="">
              </b-form-checkbox>
              <b-tooltip variant="secondary" target="tooltip-compound" triggers="hover" placement="top">
                {{ $t("LANDING.COMPOUND_BY_AUTO_PURCHASE_TOOLTIP", { value: exchangeValue(10000) }) }}
              </b-tooltip>
            </div>
            <p class="font-12 mt-4 ml-3 mr-3" v-html="$t('LANDING.CALCULATOR_DISCLAIMER')"></p>
          </b-col>
          <b-col cols="12" lg="6" xl="6" class="mt-3 mb-0 pl-3">
            <b-col class="calculator-card mt-3">
              <div class="expected-income">
                <p class="projected-text font-23 mb-3" :class="compoundByReinvesting ? 'mt-4' : 'mt-5' "
                   v-html="$t('LANDING.PROJECTED_INCOME_RETURN', { year: investmentPeriod > 1 ? $t('LANDING.YEARS', { value: investmentPeriod }) : $t('LANDING.YEAR', { value: investmentPeriod }) })">
                </p>
                <p class="mt-3 mb-3">{{ $t('LANDING.PURCHASE_TOKEN_DESCRIPTION', { purchase: this.initialPurchaseFormat, token: formatNumberIntl(this.purchaseToken) }) }} {{ this.purchaseToken > 1 ? $t('common.TOKENS') : $t('common.TOKEN') }}</p>
                <p class="mt-3" v-html="$t('LANDING.ESTIMATED_RENTAL_YIELD_RECEIVED', { year: investmentPeriod > 1 ? $t('LANDING.YEARS', { value: investmentPeriod }) : $t('LANDING.YEAR', { value: investmentPeriod }) })">
                </p>
                <h1 class="font-38 mt-1 mb-0">
                  {{ exchangeValue(compoundByReinvesting ? expectedIncomeByReinvestingIdr : expectedIncomeIdr) }}
                </h1>
                <h5 class="font-bold mt-0 mb-0" :class="compoundByReinvesting ? 'mb-0' :'mb-4'">
                  {{
                    compoundByReinvesting
                      ? this.getEquivalentTokens(this.expectedIncomeByReinvestingIdr)
                      : this.getEquivalentTokens(this.expectedIncomeIdr)
                  }}
                </h5>
                <div class="expected-income-divider"
                     :style="{ visibility: compoundByReinvesting ? 'visible' : 'hidden' }"/>
                <b-row class="cls-auto-reinvest-rental-income-detail  pl-3 pr-3 mt-1 pb-3" :style="{ visibility: compoundByReinvesting ? 'visible' : 'hidden' }">
                  <b-col cols="12" md="7" class="pl-0 pr-3">
                    <p class="font-16 text-left reinvest-text"> {{ $t("LANDING.EXPECTED_INCOME_WITH_COMPOUND") }}</p>
                  </b-col>
                  <b-col cols="12" md="5" class="pl-0 pr-1">
                    <p class="font-medium font-16 text-right reinvest-number">
                      +{{ exchangeValue(expectedIncomeByReinvestingIdr - expectedIncomeIdr) }}
                    </p>
                    <p class="font-medium font-14 text-right reinvest-number">
                      {{ this.getEquivalentTokens(this.expectedIncomeByReinvestingIdr - this.expectedIncomeIdr) }}
                    </p>
                  </b-col>
                </b-row>
              </div>
            </b-col>
            <div class="total-asset-card mt-4">
              <p v-html="$t('LANDING.TOTAL_ASSETS_IN_YEAR', { year: investmentPeriod > 1 ? $t('LANDING.YEARS', { value: investmentPeriod }) : $t('LANDING.YEAR', { value: investmentPeriod }) })">
              </p>
              <p class="font-bold font-33 text-left">{{ totalAsset }}</p>
              <p class="font-bold mt-0">{{ totalAssetTokensDetail }}</p>
            </div>
            <b-button id="btn_home_calculatorInvest" class="btn-main mt-4 mb-3" style="width: 100%; height: 45px" variant="none"
                      @click="onCalculatorInvestClicked">
              {{ $t("ASSETS.VIEW_PROPERTIES") }}
            </b-button>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div v-if="!calculatorOnly" class="pb-4 pt-4 pb-lg-5 pt-lg-5 goro-light-section cls-why-description-section" :class="getSectionThemeClass(sectionsName.WHY_DESCRIPTION)">
      <b-container>
        <b-row align-h="center">
          <b-col class="text-center" cols="10" lg="8" xl="8">
            <h1 class="font-38 mt-0">{{ $t("LANDING.WHY") }}</h1>
            <label class="col-10 col-lg-9 col-xl-8">{{ $t("LANDING.WHY_DESCRIPTION") }}</label>
          </b-col>
        </b-row>
        <b-row class="pt-3">
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="../assets/img/ic_appreciation_primary.svg" alt=""/>
              <h5 class="font-medium">{{ $t("LANDING.VALUE_APPRECIATION") }}</h5>
              <p class="font-16">{{ $t("LANDING.VALUE_APPRECIATION_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="../assets/img/ic_inflation_primary.svg" alt=""/>
              <h5 class="font-medium">{{ $t("LANDING.HEDGE_FOR_INFLATION") }}</h5>
              <p class="font-16">{{ $t("LANDING.HEDGE_FOR_INFLATION_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="../assets/img/ic_income_primary.svg" alt=""/>
              <h5 class="font-medium">{{ $t("LANDING.PASSIVE_INCOME") }}</h5>
              <p class="font-16">{{ $t("LANDING.PASSIVE_INCOME_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="../assets/img/ic_wealth_primary.svg" alt=""/>
              <h5 class="font-medium">{{ $t("LANDING.STOREHOLD_OF_WEALTH") }}</h5>
              <p class="font-16">{{ $t("LANDING.STOREHOLD_OF_WEALTH_DESCRIPTION") }}</p>
            </div>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div v-if="!calculatorOnly" class="goro-light-section cls-outstanding-section" style="padding-top: 42px; padding-bottom: 42px" :class="getSectionThemeClass(sectionsName.OUTSTANDING)">
      <b-container>
        <b-row class="statistic-card">
          <b-col cols="12" xl="3" lg="6" md="6" align="center" class="mt-3 mb-3">
            <p class="font-44 font-bold">{{ registeredUsers }}</p>
            <p class="font-20">{{ $t("LANDING.REGISTERED_USERS") }}</p>
          </b-col>
          <b-col cols="12" xl="3" lg="6" md="6" align="center" class="mt-3 mb-3">
            <p class="font-44 font-bold">{{ investedInProperty }}</p>
            <p class="font-20">{{ $t("LANDING.INVESTED_IN_PROPERTY") }}</p>
          </b-col>
          <b-col cols="12" xl="3" lg="6" md="6" align="center" class="mt-3 mb-3">
            <p class="font-44 font-bold">{{ totalIncomePaid }}</p>
            <p class="font-20">{{ $t("LANDING.TOTAL_INCOME_PAID") }}</p>
          </b-col>
          <b-col cols="12" xl="3" lg="6" md="6" align="center" class="mt-3 mb-3">
            <p class="font-44 font-bold">{{ numOfProperties }}</p>
            <p class="font-20">{{ $t("LANDING.PROPERTIES") }}</p>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div v-if="!calculatorOnly" class="text-center pb-3 pt-3 cls-our-investor-benefit-section" :class="getSectionThemeClass(sectionsName.OUR_INVESTOR_BENEFIT)">
      <b-container class="benefit-container">
        <b-row class="align-items-center" align-h="around">
          <b-col cols="12" xl="6" lg="6" class="position-relative">
            <h1 class="font-38 text-center mt-3">{{ $t("LANDING.OUR_INVESTOR_BENEFIT") }}</h1>
            <p class="font-18 text-center">{{ $t("LANDING.HIGH_RETURNS_AND_LOW_VOLATILITY") }}</p>
            <img v-if="this.$i18n.locale !== 'id'" class="mt-4 mb-3" style="width: 110%; margin-left: -20px"
                 src="../assets/img/landing_benefit_1.png" alt="Landing Benefit 1">
            <img v-if="this.$i18n.locale === 'id'" class="mt-4 mb-3" style="width: 110%; margin-left: -20px"
                 src="../assets/img/landing_benefit_1_id.png" alt="Landing Benefit 1">
            <p class="information-caption">{{ $t("LANDING.OUR_INVESTOR_BENEFIT_CAPTION") }}</p>
          </b-col>
          <b-col cols="12" xl="6" lg="6">
            <img v-if="this.$i18n.locale !== 'id'" class="mt-2 mb-3" style="width: 105%;"
                 src="../assets/img/landing_benefit_2.png" alt="Landing Benefit 2">
            <img v-if="this.$i18n.locale === 'id'" class="mt-4 mb-3" style="width: 110%; margin-left: -20px"
                 src="../assets/img/landing_benefit_2_id.png" alt="Landing Benefit 2">
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div v-if="!calculatorOnly" class="text-center pb-4 pt-3 goro-attestation-report-section goro-light-section" :class="attReportContainerClass">
      <b-container class="goro-attestation-report-container">
        <b-row class="align-items-center" align-h="around">
          <b-col cols="12">
            <h1 class="font-38 text-center mt-3 mb-0">{{ $t("ATTESTATION_REPORT.HEADING") }}</h1>
          </b-col>
        </b-row>
        <div class="pt-4">
          <Carousel
            v-bind="attReportSliderConfigs"
            class="carousel cls-card-items"
            :class="getClassAlignAttReportSlider"
            ref="attReportSlider"
            >
              <Slide
                  v-for="(doc, index) in attestationReports"
                  :key="index"
                  class="pb-4 cls-card-item"
              >
                <a class="cls-item-link" :href="attestationReportLink(doc)" target="_blank" :title="doc.title">
                  <div class="cls-card-content d-flex flex-row justify-content-start align-items-center">
                    <div class="cls-thumb">
                      <img src="@/assets/img/icons/document_report.svg" :alt="doc.title" width="30" height="40" class="document-thumb">
                    </div>
                    <div class="cls-document">
                      <h3 class="font-20 font-weight-bold mt-0 mb-1">
                        {{ doc.title }}
                      </h3>
                      <p class="font-16">
                        {{ $t("ATTESTATION_REPORT.QUARTER_SYMBOL") }}{{ doc.quarter }} {{ doc.year }}
                      </p>
                    </div>
                  </div>
                </a>
              </Slide>
              <template #addons>
                <Pagination />
              </template>
          </Carousel>
        </div>
      </b-container>
    </div>
    <div v-if="!calculatorOnly" class="text-center pb-4 pt-3 goro-light-section cls-testimonials-section" :class="getSectionThemeClass(sectionsName.TESTIMONIALS)">
      <b-container>
        <b-row class="align-items-center" align-h="around">
          <b-col cols="12">
            <h1 class="font-38 text-center mt-3 mb-0">{{ $t("LANDING.TESTIMONIALS") }}</h1>
            <b-row v-if="innerWidth >= 992">
              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-4-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%" :src="testimonial_1_photo"
                           alt="Testimonial 1">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_1_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">{{
                          $t("LANDING.TESTIMONIAL_1_TITLE")
                        }}</p>
                    </b-col>
                  </b-row>
                  <b-row class="review-content">
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_1_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-4-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%" :src="testimonial_2_photo"
                           alt="Testimonial 2">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_2_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">
                        {{ $t("LANDING.TESTIMONIAL_2_TITLE") }}
                      </p>
                    </b-col>
                  </b-row>
                  <b-row class="review-content">
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_2_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>

              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-4-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%" :src="testimonial_3_photo"
                           alt="Testimonial 3">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_3_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">
                        {{ $t("LANDING.TESTIMONIAL_3_TITLE") }}
                      </p>
                    </b-col>
                  </b-row>
                  <b-row class="review-content">
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_3_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>
            </b-row>
            <b-row v-if="innerWidth < 992">
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-4-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;" :src="testimonial_1_photo"
                         alt="Testimonial 1">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_1_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_1_TITLE") }}</p>
                      <div class="review-content">
                        <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_1_COMMENT") }}</p>
                      </div>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-4-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;" :src="testimonial_2_photo"
                         alt="Testimonial 2">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_2_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_2_TITLE") }}</p>
                      <div class="review-content">
                        <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_2_COMMENT") }}</p>
                      </div>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-4-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;" :src="testimonial_3_photo"
                         alt="Testimonial 2">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_3_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_3_TITLE") }}</p>
                      <div class="review-content">
                        <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_3_COMMENT") }}</p>
                      </div>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
            </b-row>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div v-if="!calculatorOnly" class="text-center pb-4 pt-1 cls-featured-on-section" :class="getSectionThemeClass(sectionsName.FEATURED_ON)">
      <b-container>
        <b-row class="pb-3">
          <b-col cols="12">
            <h1 class="font-38 mt-3">{{ $t("LANDING.FEATURED_ON") }}</h1>
            <b-row align-h="center" align-v="center" class="pt-3">
              <b-col cols="1" class="horizontal-navigation d-flex justify-content-start" @click="scrollToPrev">
                <b-icon icon="chevron-left" scale="1.9"></b-icon>
              </b-col>
              <b-col cols="10">
                <carousel v-bind="horizontalSettings" ref="horizontal" :breakpoints="breakpoints" class="horizontal goro-featured-on-list"
                          :wrap-around="true" :autoplay="timeout">
                  <slide v-for="(item, index) in featuredItems" :key="index" class="list-item">
                    <a :href="item.url" target="_blank" class="item-link">
                      <img :src="item.image" alt="Item" class="item-image"/>
                    </a>
                  </slide>

                  <template #addons>
                    <!-- <navigation /> -->
                    <!-- <pagination /> -->
                  </template>
                </carousel>
              </b-col>
              <b-col cols="1" class="horizontal-navigation d-flex justify-content-end" @click="scrollToNext">
                <b-icon icon="chevron-right" scale="1.9"></b-icon>
              </b-col>
            </b-row>
          </b-col>
        </b-row>
      </b-container>
    </div>

    <div v-if="!calculatorOnly" class="pb-4 pt-4 pb-lg-5 pt-lg-5 goro-light-section cls-join-the-future-today-section" :class="getSectionThemeClass(sectionsName.JOIN_THE_FUTURE_TODAY)">
      <b-container class="text-center">
        <div class="join-goro">
          <h1 class="font-38">{{ $t("LANDING.JOIN_THE_FUTURE_TODAY") }}</h1>
          <p class="font-18">{{ $t("LANDING.ONLY_TAKE_5_MINUTES") }}</p>
          <router-link :to="{ name: 'marketplace' }">
            <b-button id="btn_home_bottom_buyProperty" class="btn-main mt-4 mb-4 pl-4 pr-4" variant="none" @click="trackGtmEvent(gtmEvent.buyPropertiesEvent)">
              {{ $t("ASSETS.VIEW_PROPERTIES") }}
            </b-button>
          </router-link>
        </div>
      </b-container>
    </div>
  </div>
</template>

<script>
import { exchange, urlImage, numberWithCommas, formatNumberIntl } from "../helpers/common"
import propertiesService from "../services/properties.service"
import PropertyLightCard from "../components/Cards/PropertyLightCard"
import RippleButton from "../components/RippleButton.vue"
import EventBanner from "../components/Events/EventBanner.vue"
import attestationReportService from "../services/attestation_report.service"
import { Field, Form } from "vee-validate"
import commonService from "../services/common.service"
import "vue3-carousel/dist/carousel.css"
import { Carousel, Navigation, Pagination, Slide } from "vue3-carousel"
import { FOREIGNER, INDO } from "../constants/constants"
import externalSites from "../constants/externalSites"
import Slider from "@vueform/slider"
import { gtmTrackEvent } from "../helpers/gtm"
import { GTM_EVENT_NAMES } from "../constants/gtm"
import { Vue3Marquee } from "vue3-marquee"
import { getS3DocumentUrl } from "../helpers/common"

export default {
  components: {
    Field,
    Form,
    PropertyLightCard,
    RippleButton,
    Carousel,
    Slide,
    Pagination,
    Navigation,
    Slider,
    Vue3Marquee,
    EventBanner
  },
  data () {
    return {
      title: "GORO | Property for All",
      innerWidth: 0,
      videoButtonWidth: 0,
      statisticConfigKey: "statistic_numbers",
      statisticNumbers: null,
      propertiesFeatured: [],
      propertiesForCalculator: [],
      propertyForCalculator: null,
      monthlyInvestment: false,
      initialPurchase: null,
      investmentPeriod: 5,
      pricePerToken: 10000,
      selectedEry: "LAST_MONTH",
      includeEca: false,
      compoundByReinvesting: false,
      durationMarquee: 35,
      featuredItems: [
        {
          image: require("../assets/img/featureds/the-big-spark.png"),
          url: "https://www.channelnewsasia.com/the-big-spark",
        },
        {
          image: require("../assets/img/featureds/dsa.png"),
          url: "https://www.dealstreetasia.com/stories/indonesia-proptech-goro-round-356958",
        },
        {
          image: require("../assets/img/featureds/tech_in_asia.png"),
          url: "https://www.techinasia.com/iterative-leads-id-proptech-firm-goros-1m-round",
        },
        {
          image: require("../assets/img/featureds/e27.png"),
          url: "https://e27.co/goro-raises-us1m-to-democratise-indonesian-property-investment-amidst-economic-challenges-20230808/",
        },
        {
          image: require("../assets/img/featureds/technode.png"),
          url: "https://technode.global/2023/08/09/indonesias-goro-raises-1m-funding-in-pre-seed-round-led-by-iterative/",
        },
        {
          image: require("../assets/img/featureds/Detik.png"),
          url: "https://finance.detik.com/fintech/d-7677053/perusahaan-fintech-investasi-ini-masuk-regulatory-sandbox-ojk",
        },
        {
          image: require("../assets/img/featureds/Kompas.png"),
          url: "https://money.kompas.com/read/2024/12/09/182927226/platform-tokenisasi-properti-goro-masuk-regulatory-sandbox-ojk",
        },
        {
          image: require("../assets/img/featureds/Liputan6.png"),
          url: "https://www.liputan6.com/bisnis/read/5515334/resmi-goro-bergabung-jadi-peserta-sandbox-ojk",
        },
        {
          image: require("../assets/img/featureds/IDN_Times.png"),
          url: "https://www.idntimes.com/business/economy/triyan-pangastuti/goro-resmi-bergabung-jadi-peserta-sandbox-ojk",
        },
        {
          image: require("../assets/img/featureds/Kontan.png"),
          url: "https://investasi.kontan.co.id/news/goro-masuk-dalam-sandbox-ojk-dengan-mengusung-tokenisasi-properti",
        },
        {
          image: require("../assets/img/featureds/Medcom.png"),
          url: "https://www.medcom.id/ekonomi/bisnis/4baOYRJK-canggih-inovasi-ini-bisa-bikin-investasi-properti-lebih-mudah",
        },
        {
          image: require("../assets/img/featureds/MI.png"),
          url: "https://mediaindonesia.com/ekonomi/723696/5-langkah-memulai-investasi-properti-fraksional-di-era-digital",
        },
        {
          image: require("../assets/img/featureds/katadata.png"),
          url: "https://katadata.co.id/desysetyowati/digital/64d319fbc66d8/iteratif-dan-veteran-properti-suntik-startup-goro-rp-15-2-miliar",
        },
        {
          image: require("../assets/img/featureds/abo.png"),
          url: "https://www.asiabusinessoutlook.com/news/property-startup-goro-secures-1-million-in-preseed-round-led-by-iterative-nwid-4442.html",
        },
        {
          image: require("../assets/img/featureds/daily_social.png"),
          url: "https://dailysocial-id.cdn.ampproject.org/c/s/dailysocial.id/amp/post/goro-peroleh-pendanaan-pra-awal-rp152-miliar-dipimpin-iterative",
        },
        {
          image: require("../assets/img/featureds/IDX_Channel.png"),
          url: "https://www.youtube.com/watch?v=Nf1NHF68wCg&t=408s",
        },
        {
          image: require("@/assets/img/featureds/backscoop.png"),
          url: "https://www.backscoop.com/newsletter-posts/indonesias-property-market-just-got-friendlier-with-goro",
        },
        {
          image: require("@/assets/img/featureds/krasia.png"),
          url: "https://kr-asia.com/unlocking-access-to-real-estate-goros-tokenization-of-rental-properties-in-indonesia",
        },
        {
          image: require("../assets/img/featureds/money_abroad.png"),
          url: "https://www.moneyabroad.co/newsletter/investing-in-bali-real-estate-robert-hoving",
        }
      ],
      carouselItems: {
        block1: [
          {
            image: 'goro-banner-2.jpg',
          },
          {
            image: 'goro-banner-3.jpg',
          },
          {
            image: 'goro-banner-4.jpg',
          },
          {
            image: 'goro-banner-10.jpg',
          },
        ],
        block2: [
          {
            image: 'goro-banner-1.jpg',
          },
          {
            image: 'goro-banner-5.jpg',
          },
          {
            image: 'goro-banner-9.jpg',
          },
          {
            image: 'goro-banner-11.jpg',
          },
        ],
        block3: [
          {
            image: 'goro-banner-6.jpg',
          },
          {
            image: 'goro-banner-7.jpg',
          },
          {
            image: 'goro-banner-8.jpg',
          },
        ],
      },
      hasPrev: false,
      hasNext: false,
      timeout: 3500,
      isScrolling: false,
      horizontalSettings: {
        itemsToShow: 1,
        snapAlign: "center",
      },
      breakpoints: {
        300: {
          itemsToShow: 1,
          snapAlign: "center",
        },
        500: {
          itemsToShow: 1.75,
          snapAlign: "center",
        },
        768: {
          itemsToShow: 2.25,
          snapAlign: "center",
        },
        // 900 and up
        900: {
          itemsToShow: 2.5,
          snapAlign: "center",
        },
        1000: {
          itemsToShow: 3.5,
          snapAlign: "start",
        },
        1200: {
          itemsToShow: 4,
          snapAlign: "start",
        }
      },
      storeLink: {
        playStore: externalSites.STORE.GOOGLE_PLAY,
        appStore: externalSites.STORE.APP_STORE,
      },
      gtmEvent: {
        appStoreEvent: GTM_EVENT_NAMES.APP_STORE_BUTTON,
        playStoreEvent: GTM_EVENT_NAMES.PLAY_STORE_BUTTON,
        buyPropertiesEvent: GTM_EVENT_NAMES.BUY_PROPERTIES,
        getStartedEvent: GTM_EVENT_NAMES.GET_STARTED,
      },
      attestationReports: [],
      sectionsName: {
        BANNER: "BANNER",
        DOWNLOAD_OUR_APP: "DOWNLOAD_OUR_APP",
        OUTSTANDING: "OUTSTANDING",
        FEATURED_ON: "FEATURED_ON",
        HOW_IT_WORKS: "HOW_IT_WORKS",
        FEATURED_PROPERTIES: "FEATURED_PROPERTIES",
        CALCULATOR_SIMULATION: "CALCULATOR_SIMULATION",
        OUR_INVESTOR_BENEFIT: "OUR_INVESTOR_BENEFIT",
        ATTESTATION_REPORT: "ATTESTATION_REPORT",
        TESTIMONIALS: "TESTIMONIALS",
        WHY_DESCRIPTION: "WHY_DESCRIPTION",
        JOIN_THE_FUTURE_TODAY: "JOIN_THE_FUTURE_TODAY"
      },
      sectionConfigs: [],
      hasEventBanner: false,
      calculatorOnly: this.$route.query.calculator,
    }
  },
  async created () {
    this.sectionConfigs = [
      {
        section: this.sectionsName.BANNER,
        isEnabled: true,
        isDarkerMode: false
      },
      // {
      //   section: this.sectionsName.DOWNLOAD_OUR_APP,
      //   isEnabled: false,
      //   isDarkerMode: false
      // },
      {
        section: this.sectionsName.HOW_IT_WORKS,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.FEATURED_PROPERTIES,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.CALCULATOR_SIMULATION,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.WHY_DESCRIPTION,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.OUTSTANDING,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.OUR_INVESTOR_BENEFIT,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.ATTESTATION_REPORT,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.TESTIMONIALS,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.FEATURED_ON,
        isEnabled: true,
        isDarkerMode: false
      },
      {
        section: this.sectionsName.JOIN_THE_FUTURE_TODAY,
        isEnabled: true,
        isDarkerMode: false
      },
    ];
  },
  async mounted() {
    if (!this.calculatorOnly) {
      this.handleWindowResize()
      this.videoButtonWidth = this.$refs.videoButton.$el.clientWidth
      window.addEventListener("resize", this.handleWindowResize)
      await this.getStatisticNumbers()
      await this.getProperties()
      await this.getPropertiesForCalculator()
      await this.getAttestationReports()
    } else {
      await this.getPropertiesForCalculator()
    }

    if (window.location.hash === '#calculator') {
      this.scrollToCalculatorComponent()
    }
  },
  async beforeDestroy () {
    if (!this.calculatorOnly) {
      window.removeEventListener("resize", this.handleWindowResize)
    }
  },
  methods: {
    formatNumberIntl,
    scrollToCalculatorComponent() {
      this.$refs.calculatorHiddenComponent.scrollIntoView({ behavior: 'smooth', block: 'start' })
    },
    handleWindowResize () {
      this.innerWidth = window.innerWidth
    },
    getAvatar (images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    async getStatisticNumbers () {
      try {
        const result = await commonService.getConfigDetail(this.statisticConfigKey, true)
        if (result && result.data) {
          this.statisticNumbers = JSON.parse(result.data.c_value)
        }
      } catch (ex) {}
    },
    async getProperties () {
      const result = await propertiesService.getFeatures()
      if (result && result.data) {
        this.propertiesFeatured = result.data
      }
    },
    async getPropertiesForCalculator () {
      const result = await propertiesService.getPropertiesForCalculator()
      if (result) {
        this.propertiesForCalculator = result
        if (this.propertiesForCalculator.length > 0) {
          this.propertyForCalculator = this.propertiesForCalculator[0]
        }
      }
    },
    async getAttestationReports() {
      const result = await attestationReportService.getAttestationResports()
      if (result && result.data) {
        this.attestationReports = result.data
      }
    },
    attestationReportLink(doc) {
      if (!doc || !doc.document_link) {
        return ""
      }
      const link = getS3DocumentUrl(doc.document_link)
      return link
    },
    scrollToNext () {
      this.$refs.horizontal.next()
    },
    scrollToPrev () {
      this.$refs.horizontal.prev()
    },
    play () {
      if (!this.hasNext && this.hasPrev) {
        this.$refs.horizontal.slideTo(0)
        return
      }
      if (this.hasNext) {
        this.$refs.horizontal.next()
      } else if (!this.hasNext && !this.hasPrev) {
        //some browsers got both hasNext/hasPrev == false, so we do the trick manual here
        this.$refs.horizontal.slideTo(1)
      }
    },
    async onCalculatorInvestClicked () {
      const uuid = this.propertyForCalculator != null && this.propertyForCalculator.status === "available" ? this.propertyForCalculator.uuid : null
      if (this.$route.query.mobile) {
        if (window.GOROWebChannel && window.GOROWebChannel.postMessage) {
          const payload = {
            action: 'clicked_invest',
            data: {
              uuid: uuid,
            }
          }
          window.GOROWebChannel.postMessage(JSON.stringify(payload));
        }
      } else {
        if (uuid) {
          await this.$router.push({ name: "propertyDetail", params: { uuid } })
        } else {
          await this.$router.push({ name: "marketplace" })
        }
      }
    },

    formatInitialPurchase (value) {
      const locale = this.$i18n.locale
      let separator = "."
      if (locale.toLowerCase() !== 'id') {
        separator = ","
      }

      if (value) {
        // Remove non-numeric characters
        let formattedValue = value.toString().replace(/[^0-9]/g, "")
        // Remove leading zeros
        formattedValue = formattedValue.replace(/^0+/g, "")
        let maxValue = 100000000000
        if (Number(formattedValue) > maxValue) {
          return String(maxValue).replace(/\B(?=(\d{3})+(?!\d))/g, separator)
        } else {
          return formattedValue.replace(/\B(?=(\d{3})+(?!\d))/g, separator)
        }
      }
      return null
    },
    getEquivalentTokens (value) {
      let pricePerToken = this.pricePerToken
      let equivalentTokens = Math.floor(value / pricePerToken)
      return `(${formatNumberIntl(equivalentTokens)} ${equivalentTokens > 1 ? this.$t("PAYMENT.TOKENS") : this.$t("PAYMENT.TOKEN")})`
    },
    exchangeValue (value) {
      return exchange(value, 100, false)
    },
    trackGtmEvent(name){
      gtmTrackEvent({
        event: name,
      })
    },
    tokenLabel (value) {
      return value > 1 ? this.$t("PAYMENT.TOKENS") : this.$t("PAYMENT.TOKEN")
    },
    getSectionByName(name) {
      const idx = this.sectionConfigs.findIndex(config => config.section === name)
      if (idx !== -1) {
        const section = this.sectionConfigs[idx]
        return {
          ...section,
          idx
        }
      }
      return null
    },
    getPreviousSection(idx) {
      if (idx <= 0 || idx >= this.sectionConfigs.length) {
        return null
      }

      for (let i = idx - 1; i >= 0; i--) {
        if (this.sectionConfigs[i].isEnabled) {
          const section = this.sectionConfigs[i]
          return {
            ...section,
            idx
          }
        }
      }

      return null
    },
    getSectionThemeClass(sectionName = null) {
      const darkerTheme = 'cls-darker-theme-mode'
      const lightTheme = 'cls-light-theme-mode'
      if (!sectionName) {
        sectionName = this.sectionsName.BANNER
      }
      let section = this.getSectionByName(sectionName)
      if (section) {
        if (section.idx > 0) {
          let previousSection = this.getPreviousSection(section.idx)
          if (previousSection) {
            section.isDarkerMode = !previousSection.isDarkerMode
          }
        }
        this.sectionConfigs[section.idx].isDarkerMode = section.isDarkerMode
        return section.isDarkerMode ? darkerTheme : lightTheme
      }
      return lightTheme
    },
  },
  computed: {
    userProfile () {
      return this.$store.getters.userProfile
    },
    goroVideoId () {
      if (this.$i18n.locale === "id") {
        return "2V8JA5L1zbk"
      } else {
        return "2uAcQKWbDPU"
      }
    },
    registeredUsers () {
      if (this.statisticNumbers) {
        if (this.$i18n.locale === FOREIGNER.LOCALE) {
          return this.statisticNumbers.registered_users.en_UK
        } else if (this.$i18n.locale === "en") {
          return this.statisticNumbers.registered_users.en
        } else {
          return this.statisticNumbers.registered_users.id
        }
      }
      return "70K+"
    },
    investedInProperty () {
      if (this.$i18n.locale === FOREIGNER.LOCALE) {
        if (this.statisticNumbers) {
          return this.statisticNumbers.invested_in_property.en_UK
        }
        return "$3M+"
      } else if (this.$i18n.locale === "en") {
        if (this.statisticNumbers) {
          return this.statisticNumbers.invested_in_property.en
        }
        return "IDR50B+"
      } else {
        if (this.statisticNumbers) {
          return this.statisticNumbers.invested_in_property.id
        }
        return "Rp.50M+"
      }
    },
    totalIncomePaid () {
      if (this.$i18n.locale === FOREIGNER.LOCALE) {
        if (this.statisticNumbers) {
          return this.statisticNumbers.total_income_paid.en_UK
        }
        return "$200K+"
      } else if (this.$i18n.locale === "en") {
        if (this.statisticNumbers) {
          return this.statisticNumbers.total_income_paid.en
        }
        return "IDR3B+"
      } else {
        if (this.statisticNumbers) {
          return this.statisticNumbers.total_income_paid.id
        }
        return "Rp.3M+"
      }
    },
    numOfProperties () {
      if (this.statisticNumbers) {
        if (this.$i18n.locale === FOREIGNER.LOCALE) {
          return this.statisticNumbers.properties.en_UK
        } else if (this.$i18n.locale === "en") {
          return this.statisticNumbers.properties.en
        } else {
          return this.statisticNumbers.properties.id
        }
      }
      return "16"
    },
    testimonial_1_photo () {
      if (this.$i18n.locale === "id") {
        return require("../assets/img/landing_testimonial_danisworo.png")
      } else {
        return require("../assets/img/landing_testimonial_florian_k.png")
      }
    },
    testimonial_2_photo () {
      if (this.$i18n.locale === "id") {
        return require("../assets/img/landing_testimonial_sofyan.png")
      } else {
        return require("../assets/img/landing_testimonial_karan_s.png")
      }
    },
    testimonial_3_photo () {
      if (this.$i18n.locale === "id") {
        return require("../assets/img/landing_testimonial_aldi.png")
      } else {
        return require("../assets/img/landing_testimonial_alvin_d.png")
      }
    },
    currency () {
      let currency = INDO.CURRENCY
      if (this.userProfile && this.userProfile.preferred_currency) {
        currency = this.userProfile.preferred_currency
      } else if (this.$i18n.locale === FOREIGNER.LOCALE) {
        currency = FOREIGNER.CURRENCY
      }
      return currency
    },
    initialPurchaseHint () {
      if (this.currency === INDO.CURRENCY) {
        return this.$t("LANDING.INITIAL_PURCHASE_HINT", { value: this.exchangeValue(10000) })
      } else {
        return this.$t("LANDING.INITIAL_PURCHASE_HINT", { value: this.currency })
      }
    },
    initialPurchaseValue () {
      if (this.initialPurchase) {
        let value = this.initialPurchase.replace(/[^0-9]/g, "")
        return Number(value)
      }
      return 0
    },
    initialPurchaseValueIdr () {
      if (this.currency === INDO.CURRENCY) {
        return this.initialPurchaseValue
      } else {
        const rates = this.$store.getters.exchangeRates
        if (rates && rates[this.currency]) {
          return Math.floor(this.initialPurchaseValue / rates[this.currency])
        }
      }
      return 0
    },
    totalPurchaseValueIdr () {
      if (this.monthlyInvestment) {
        let totalPurchaseValue = 0
        let months = 12
        for (let i = 0; i < this.investmentPeriod; i++) {
          for (let j = 0; j < months; j++) {
            totalPurchaseValue += this.initialPurchaseValueIdr
          }
        }
        return totalPurchaseValue
      } else {
        return this.initialPurchaseValueIdr
      }
    },
    rentalYieldPercent () {
      if (this.propertyForCalculator != null) {
        if (this.selectedEry === "LAST_MONTH") {
          return this.propertyForCalculator.last_month_rental_yield
        } else if (this.selectedEry === "AVERAGE") {
          return this.propertyForCalculator.average_rental_yield
        }
      }
      return 0
    },
    rentalYieldPercentFormatted() {
      return formatNumberIntl(this.rentalYieldPercent)
    },
    ecaPercent () {
      if (this.propertyForCalculator != null) {
        return this.propertyForCalculator.eca
      }
      return 0
    },
    expectedIncomeIdr () {
      if (this.propertyForCalculator != null) {
        if (this.monthlyInvestment) {
          let months = 12
          let monthlyRentalYieldPercent = this.rentalYieldPercent / months
          let monthlyCapitalAppreciationPercent = this.includeEca ? this.ecaPercent / months : 0
          let totalInvestmentValue = 0
          let expectedIncome = 0
          for (let i = 0; i < this.investmentPeriod; i++) {
            for (let j = 0; j < months; j++) {
              totalInvestmentValue += this.initialPurchaseValueIdr
              expectedIncome += totalInvestmentValue * (monthlyRentalYieldPercent + monthlyCapitalAppreciationPercent)/ 100
            }
          }
          return Math.floor(expectedIncome)
        } else {
          let capitalAppreciationPercent = this.includeEca ? this.ecaPercent : 0
          return Math.floor(this.initialPurchaseValueIdr
            * (this.rentalYieldPercent + capitalAppreciationPercent) / 100 * this.investmentPeriod)
        }
      }
      return 0
    },
    expectedIncomeByReinvestingIdr () {
      if (this.propertyForCalculator != null) {
        if (this.compoundByReinvesting) {
          let months = 12
          let pricePerToken = 10000
          let monthlyRentalYieldPercent = this.rentalYieldPercent / months
          let monthlyCapitalAppreciationPercent = this.includeEca ? this.ecaPercent / months : 0
          let totalInvestmentValue = 0
          let compoundIncome = 0
          for (let i = 0; i < this.investmentPeriod; i++) {
            for (let j = 0; j < months; j++) {
              if (totalInvestmentValue === 0 || this.monthlyInvestment) {
                totalInvestmentValue += this.initialPurchaseValueIdr
              }
              let amountCanReinvest = Math.floor(compoundIncome / pricePerToken) * pricePerToken
              compoundIncome += (totalInvestmentValue + amountCanReinvest)
                * (monthlyRentalYieldPercent + monthlyCapitalAppreciationPercent) / 100
            }
          }
          return Math.floor(compoundIncome)
        }
      }
      return 0
    },
    totalAsset () {
      if (this.compoundByReinvesting) {
        return this.exchangeValue(this.totalPurchaseValueIdr + this.expectedIncomeByReinvestingIdr)
      } else {
        return this.exchangeValue(this.totalPurchaseValueIdr + this.expectedIncomeIdr)
      }
    },
    purchaseToken() {
      return Math.floor(this.initialPurchaseValueIdr / this.pricePerToken)
    },
    initialPurchaseFormat() {
      return `${this.currency}${numberWithCommas(this.initialPurchase || 0)}`
    },
    totalAssetTokensDetail() {
      if (this.compoundByReinvesting) {
        let equivalentTokens = Math.floor((this.totalPurchaseValueIdr + this.expectedIncomeByReinvestingIdr) / this.pricePerToken)
        let totalPurchaseTokens = Math.floor(this.totalPurchaseValueIdr / this.pricePerToken)
        let expectedIncomeByReinvestingTokens = Math.floor(this.expectedIncomeByReinvestingIdr / this.pricePerToken)
        if (!totalPurchaseTokens || !expectedIncomeByReinvestingTokens) {
          return `(${formatNumberIntl(equivalentTokens)} ${this.tokenLabel(equivalentTokens)})`
        }
        return `(${formatNumberIntl(totalPurchaseTokens)} + ${formatNumberIntl(expectedIncomeByReinvestingTokens)} = ${formatNumberIntl(equivalentTokens)} ${this.tokenLabel(equivalentTokens)})`
      } else {
        let equivalentTokens = Math.floor((this.totalPurchaseValueIdr + this.expectedIncomeIdr) / this.pricePerToken)
        let totalPurchaseTokens = Math.floor(this.totalPurchaseValueIdr / this.pricePerToken)
        let expectedIncomeTokens = Math.floor(this.expectedIncomeIdr / this.pricePerToken)

        if (!totalPurchaseTokens || !expectedIncomeTokens) {
          return `(${formatNumberIntl(equivalentTokens)} ${this.tokenLabel(equivalentTokens)})`
        }
        return `(${formatNumberIntl(totalPurchaseTokens)} + ${formatNumberIntl(expectedIncomeTokens)} = ${formatNumberIntl(equivalentTokens)} ${this.tokenLabel(equivalentTokens)})`
      }
    },
    attReportSliderConfigs() {
      return  {
        itemsToShow: 1,
        snapAlign: 'center',
        breakpoints: {
          300: {
            itemsToShow: 1.15,
            snapAlign: "center",
          },
          500: {
            itemsToShow: 1.5,
            snapAlign: "center",
          },
          768: {
            itemsToShow: 1.75,
            snapAlign: "center",
          },
          900: {
            itemsToShow: 2,
            snapAlign: "start",
          },
          1200: {
            itemsToShow: 2.75,
            snapAlign: "start",
          },
          1300: {
            itemsToShow: 3,
            snapAlign: "start",
          }
        }
      }
    },
    getClassAlignAttReportSlider() {
      const windowWidth = window.innerWidth
      if ((windowWidth >= 1300) && this.attestationReports.length < 3) {
        return "cls-custom-align-carousel"
      }
      if ((windowWidth >= 1200) && this.attestationReports.length < 3) {
        return "cls-custom-align-carousel"
      }
      if ((windowWidth >= 900) && this.attestationReports.length < 2) {
        return "cls-custom-align-carousel"
      }
      if ((windowWidth >= 768) && this.attestationReports.length < 2) {
        return "cls-custom-align-carousel"
      }
    },
    attReportContainerClass() {
      let section = this.getSectionByName(this.sectionsName.ATTESTATION_REPORT)
      const themeClass = this.getSectionThemeClass(this.sectionsName.ATTESTATION_REPORT)
      this.sectionConfigs[section.idx].isEnabled = false
      if (this.attestationReports && this.attestationReports.length > 0) {
        this.sectionConfigs[section.idx].isEnabled = true
        return `${themeClass}`
      }
      return `${themeClass} cls-hide-att-report-container`
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
}

</script>
<style lang="scss">
.home {
  h1 {
    font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
    font-weight: 500;
    color: var(--primary-color);
  }

  p {
    color: var(--primary-color);
  }

  .main__content {
    // padding-top: 20px;
    // padding-bottom: 75px;
    // background-size: cover;
    // background-image: url('../assets/img/landing_banner_min_w_1100.png');

    // @media only screen and (max-width: 1100px) {
    //   background-image: url('../assets/img/landing_banner_max_w_1100.png');
    // }

    // @media only screen and (max-width: 800px) {
    //   background-image: url('../assets/img/landing_banner_max_w_800.png');
    // }

    // @media only screen and (max-width: 650px) {
    //   background-image: url('../assets/img/landing_banner_max_w_650.png');
    // }

    &.goro-new-banner{
      .goro-banner-container{
        margin-top: 0;
        margin-bottom: 0;
        max-width: 1400px;
        padding: 0 60px !important;
        // max-height: 530px;
        @media only screen and (max-width: 650px) {
          padding: 0 15px !important;
        }
      }
      .goro-content-banner{
          padding: 6rem 0 12rem 0;
          @media only screen and (max-width: 10240px) {
            padding: 3rem 15px 6rem 15px;
          }
          @media only screen and (max-width: 650px) {
            padding: 3rem 15px 6rem 15px;
          }
          @media only screen and (max-width: 575px) {
            background-image: url('../assets/img/landing_banner_max_w_650.png');
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
          }
      }
      .bg-image{
        background-image: url('../assets/img/banners/new_landing_banner_max_w_1400.png');
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;

        @media only screen and (max-width: 1024px) {
          background-image: url('../assets/img/banners/new_landing_banner_min_w_1400.png');
        }
        @media only screen and (max-width: 1000px) {
          background-image: url('../assets/img/banners/new_landing_banner_max_w_1000.png');
        }
        @media only screen and (max-width: 575px) {
          background-image: none !important;
        }
      }

      .goro-main-actions{
        @media only screen and (max-width: 1024px) {
          flex-direction: column;
          gap: 10px;
        }
        .goro-view-property{
          padding: 0.375rem 2rem;
          width: 200px;
        }
      }

      .goro-banner-carousel{
        @media only screen and (max-width: 575px) {
          display: none !important;
        }
        .parent{
          // position:relative;
          display: flex;
          justify-content: flex-end;
          height: 670px;
          overflow: hidden;

          .cols{
            display:flex;
            flex-wrap:wrap;
            width:200px;
          }

          .item{
            width:100%;
            padding: 10px;
            overflow: hidden;
            img{
              width: 100%;
              height: auto;
              border-radius: 15px;
            }
          }

          .block2{
            @media only screen and (max-width: 1000px) {
              display: none !important;
            }
          }
          .block3{
            animation-name: slidedown2;
            @media only screen and (max-width: 1024px) {
              display: none !important;
            }
          }
        }
      }
    }
  }

  .statistic-card {
    padding: 25px 50px 30px;
    margin-left: 1px;
    margin-right: 1px;
    background-color: var(--primary-background-color);
    border-radius: 48px;
  }

  .w-card {
    padding: 15px 25px 25px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-darker-color);
    border-radius: 16px;

    img {
      width: 24px;
      height: 24px;
      margin-bottom: 5px;
    }

    label {
      padding-left: 0.5rem;
      font-size: 20px;
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-2 {
    padding: 15px 25px 30px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-darker-color);
    border-radius: 16px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 18px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-3 {
    padding: 15px 25px 30px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-color);
    border-radius: 16px;

    img {
      width: 30px;
      height: 30px;
    }

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 18px;
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-4-a {
    padding: 15px 25px 50px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-darker-color);
    border-radius: 32px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 22px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-4-b {
    padding: 10px 25px 15px;
    margin-left: 15px;
    margin-right: 15px;
    background-color: var(--primary-background-darker-color);
    border-radius: 32px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 22px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .calculator-card {
    padding: 28px 32px 28px 32px;
    background-color: var(--primary-background-darker-color);
    border-radius: 28px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 18px;
      font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
      font-weight: 300;
      color: var(--primary-color);
    }

    p {
      font-size: 18px;
    }

    .rental-yield {
      width: 130px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 0 0 5px;
      //background-color: white;
      color: var(--primary-color);
      //border-radius: 10px;
      font-size: 38px;
      font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
      font-weight: 600;
    }

    .projected-text {
      margin-left: -4px;
      margin-right: -4px;
      padding: 8px 10px;
      background-color: #afe1e1;
      border-radius: 15px;
    }

    .expected-income {
      height: 356px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: center;
      @media only screen and (max-width: 992px) {
        height: auto;
      }
      .expected-income-divider {
        width: auto;
        height: 1px;
        background-color: var(--primary-lighter-color);
        margin: 55px 2px 15px;
      }
      .cls-auto-reinvest-rental-income-detail{
        @media only screen and (max-width: 992px) {
          .reinvest-text{
            text-align: center !important
          }
          .reinvest-number{
            text-align: center !important
          }
        }
      }
    }

    .custom-control-input:checked ~ .custom-control-label::before {
      color: white;
      border-color: var(--primary-color);
      box-shadow: 0 0 5px var(--primary-lighter-color);
      background-color: var(--primary-color);
    }

    .slider {
      --slider-bg: white;
      --slider-height: 8px;
      --slider-connect-bg: var(--primary-color);
      --slider-tooltip-bg: var(--primary-color);
      --slider-handle-width: 18px;
      --slider-handle-height: 18px;
      --slider-handle-bg: var(--primary-color);
      --slider-handle-ring-color: none;
    }

    .segmented-control {
      display: flex;
      min-width: 265px;
      background-color: white;
      border-radius: 50px;
    }

    .segmented-control button {
      flex: 1;
      padding: 8px 10px;
      margin-left: 1px;
      margin-right: 1px;
      cursor: pointer;
      border: none;
      border-radius: 50px;
      color: var(--primary-color);
      background-color: white;
      box-shadow: none;
    }

    .segmented-control button.active {
      color: white;
      background-color: var(--primary-color);
      font-weight: bolder;
    }
  }

  .total-asset-card {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 28px;
    padding: 16px 32px;
  }

  .join-goro {
    padding: 0.6rem 5% 1rem;
    margin-top: 5rem;
    background-color: var(--primary-background-color);
    //box-shadow: 0 4px 10px var(--primary-darker-color);
    border-radius: 16px;
  }

  .show-hide {
    width: 40px;
    background-color: rgba(0, 102, 102, 0.9);;
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    transition: .5s;

    &:hover {
      background-color: rgba(0, 102, 102, 1);;
    }
  }

  .horizontal {
    margin-left: -50px;
    margin-right: -50px;

    @media only screen and (max-width: 1200px) {
      margin-left: -42px;
      margin-right: -42px;
    }

    @media only screen and (max-width: 996px) {
      margin-left: -24px;
      margin-right: -24px;
    }

    @media only screen and (max-width: 768px) {
      margin-left: -12px;
      margin-right: -12px;
    }

    @media only screen and (max-width: 650px) {
      margin-left: -10px;
      margin-right: -10px;
    }
  }

  .goro-featured-on-list{
    .list-item {
      width: auto;
      height: 118px;
      // margin-left: 8px;
      // margin-right: 8px;
      background-color: transparent;
      padding: 7px;
      &:hover {
        background-color: transparent;
        cursor: pointer;
      }

      &:focus {
        background-color: transparent;
      }

      .item-link{
        border: 2px solid #348685;
        border-radius: 20px;
        height: 100%;
        padding: 5px 14px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover{
          border: 2px solid var(--primary-hover-color);
        }
        .item-image {
          width: auto;
          height: 100%;
          object-fit: contain;
          max-width: 100%;
        }
      }
    }
  }

  .carousel__track {
    margin-bottom: 0;
  }

  .horizontal-navigation {
    color: var(--primary-color);

    &:hover {
      cursor: pointer;
      color: var(--primary-hover-color);
    }
  }

  .compound-tooltip-icon {
    margin-bottom: 2px;
    width: 18px;
    height: 18px;
    z-index: 1;
  }

  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type=number] {
    -moz-appearance: textfield;
  }

  .information-caption{
    position: absolute;
    left: 0px;
    bottom: 35px;
    width: 100%;
    @media only screen and (max-width: 768px) {
      bottom: 15px !important;
    }
  }

  /**
   * Style for light card
   */
  .goro-light-section{
    .w-card{
      background-color: var(--primary-background-color);
    }

    .calculator-card{
      background-color: var(--primary-background-color);
    }

    .w-card-2{
      background-color: var(--primary-background-color);
    }
    .w-card-4-a,
    .w-card-4-b{
      background-color: var(--primary-background-darker-color);
    }

    .w-card-4-a{
      .review-content{
        position: relative;
        &::before{
          position: absolute;
          content: "";
          top: -13px;
          left: -4px;
          width: 50px;
          height: 50px;
          background-image: url('~@/assets/img/quote-prev.png');
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
          z-index: 0;
          opacity: .5;
        }
        p{
          z-index: 1;
        }
      }
    }

    .w-card-4-b{
      .review-content{
        position: relative;
        &::before{
          position: absolute;
          content: "";
          top: -18px;
          left: -10px;
          width: 50px;
          height: 50px;
          background-image: url('~@/assets/img/quote-prev.png');
          background-position: center;
          background-size: cover;
          background-repeat: no-repeat;
          z-index: 0;
          opacity: .5;
        }
        p{
          position: inherit;
          z-index: 1;
        }
      }
    }

    .w-card-3{
      background-color: var(--primary-background-color);
    }

    .join-goro{
      background-color: #fff;
      margin-top: 0 !important
    }
  }

  .goro-attestation-report-section{
    &.cls-hide-att-report-container{
      height: 0px !important;
      padding-bottom: 0 !important;
      padding-top: 0 !important;
      overflow: hidden !important;
    }
    .cls-card-items{
      &.cls-custom-align-carousel{
        .carousel__viewport{
          .carousel__track{
            transform: translateX(0) !important;
            justify-content: center;
          }
        }
      }
      .cls-card-item{
        padding: 10px;
        .cls-item-link{
          display: block;
          width: 100%;
        }
        .cls-card-content{
          width: 100%;
          box-shadow: 0px 3px 18.5px 0px #0000001A;
          border-radius: 12px;
          background-color: #fff;
          padding: 17px;
          .cls-thumb{
            border-radius: 9px;
            background-color: #E3F8FB;
            padding: 13px;
            margin-right: 17px;
            img{
              width: 30px;
            }
          }
          .cls-document{
            flex: 1;
            text-align: left;
            h3{
              font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
              color: #006666;
              text-align: left;
            }
            p{
              font-weight: 400;
              color: #006666;
              text-align: left;
            }
          }
        }
        .cls-item-link{
          &:hover{
            .cls-card-content{
              .cls-thumb{
                opacity: .8;
              }
              .cls-document{
                opacity: .8;
              }
            }
          }
        }
      }
      .carousel__pagination{
        margin-left: 0;
        padding-left: 0;
        .carousel__pagination-item{
          .carousel__pagination-button{
            &::after{
              background-color: #73A7AA !important;
              height: 10px;
              width: 16px;
              border-radius: 10px;
            }
            &:hover,
            &:focus,
            &:active{
              &::after{
                background-color: #006867 !important;
              }
            }
            &.carousel__pagination-button--active{
              &::after{
                background-color: #006867 !important;
                width: 42px;
              }
            }
          }
        }
      }
    }
  }
  .cls-darker-theme-mode{
    background-color: var(--primary-background-darker-color);
  }
  .cls-light-theme-mode{
    background-color: var(--primary-background-color);
  }
  .cls-banner-section{
    &.cls-darker-theme-mode{

    }
    &.cls-light-theme-mode{

    }
  }
  .cls-download-our-app-section{
    &.cls-darker-theme-mode{

    }
    &.cls-light-theme-mode{

    }
  }
  .cls-outstanding-section{
    &.cls-darker-theme-mode{
      .statistic-card {
        background-color: var(--primary-background-color);
      }
    }
    &.cls-light-theme-mode{
      .statistic-card {
        background-color: var(--primary-background-darker-color);
      }
    }
  }
  .cls-featured-on-section{
    &.cls-darker-theme-mode{

    }
    &.cls-light-theme-mode{

    }
  }
  .cls-how-it-work-section{
    &.cls-darker-theme-mode{
      .w-card{
        background-color: var(--primary-background-color);
      }
    }
    &.cls-light-theme-mode{
      .w-card{
        background-color: var(--primary-background-darker-color);
      }
    }
  }
  .cls-featured-properties-section{
    &.cls-darker-theme-mode{

    }
    &.cls-light-theme-mode{

    }
  }
  .cls-calculator-simulation-section{
    &.cls-darker-theme-mode{
      .calculator-card {
        background-color: var(--primary-background-color);
      }
    }
    &.cls-light-theme-mode{
      .calculator-card {
        background-color: var(--primary-background-darker-color);
      }
    }
  }
  .goro-attestation-report-section{
    &.cls-darker-theme-mode{

    }
    &.cls-light-theme-mode{

    }
  }
  .cls-testimonials-section{
    &.cls-darker-theme-mode{
      .w-card-4-a,
      .w-card-4-b{
        background-color: var(--primary-background-color);
      }
    }
    &.cls-light-theme-mode{
      .w-card-4-a,
      .w-card-4-b{
        background-color: var(--primary-background-darker-color);
      }
    }
  }
  .cls-why-description-section{
    &.cls-darker-theme-mode{
      .w-card-3{
        background-color: var(--primary-background-color);
      }
    }
    &.cls-light-theme-mode{
      .w-card-3{
        background-color: var(--primary-background-darker-color);
      }
    }
  }
  .cls-join-the-future-today-section{
    &.cls-darker-theme-mode{

    }
    &.cls-light-theme-mode{

    }
  }
}

@import "~@vueform/slider/themes/default.css";
</style>
