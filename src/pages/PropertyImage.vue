<template>
  <div class="image-container">
    <img class="property-image" :src="propertyPrimaryImageUrl" alt="Property image">
  </div>
</template>

<script>

import propertiesService from "../services/properties.service";
import {urlImage} from "../helpers/common";

export default {
  data() {
    return {
      title: "Property Image",
      propertyId: this.$route.params.id,
      propertyPrimaryImage: '',
    }
  },
  async mounted() {
    await this.getProperty();
  },
  methods: {
    async getProperty() {
      const res = await propertiesService.getPrimaryImage(this.propertyId);
      this.propertyPrimaryImage = res.data
    },
  },
  computed: {
    propertyPrimaryImageUrl() {
      return this.propertyPrimaryImage ? urlImage(this.propertyPrimaryImage) : '';
    },
  },
  metaInfo() {
    return {
      title: this.title,
      meta: [
        {property: "og:title", content: this.title},
        {property: "og:site_name", content: this.title},
      ],
    }
  },
}
</script>
<style scoped>
.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.property-image {
  max-width: 100%;
  max-height: 100%;
}
</style>
