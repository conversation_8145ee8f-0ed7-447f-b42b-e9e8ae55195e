<template>
  <div class="h-100 position-relative font-new-acumin">
    <div class="cls-video-player-container position-relative">
      <!-- Video Element -->
      <video
        v-id="video"
        ref="videoElement"
        :key="video.uuid"
        :src="video.url"
        playsinline
        loop
        autoplay
        :muted="isMuted"
        @click="togglePlay"
        @timeupdate="updateProgress"
        class="h-100 w-100 cls-video-player"
      ></video>
      <div v-if="video" class="cls-description">
        <div class="cls-top"></div>
        <div class="cls-description-content">
          <h4 class="title font-20 mt-0 mb-1">{{ video.title }}</h4>
          <div class="description font-16 mt-0">
            <p
              class="cls-video-player-description"
              v-html="formattedDescription"
            ></p>
            <span
              v-if="showSeeMoreDescription && isShowingDetail"
              class="cls-see-more font-16 font-weight-bold"
              @click="showDetail"
              >{{ $t("GO_LEARN.SEE_LESS") }}</span
            >
            <span
              v-if="showSeeMoreDescription && !isShowingDetail"
              class="cls-see-more font-16 font-weight-bold"
              @click="showDetail"
              >{{ $t("GO_LEARN.SEE_MORE") }}</span
            >
          </div>
          <b-tooltip
            v-for="(glossaryTooltip, idx) in glossaryTooltips"
            :key="idx"
            variant="secondary"
            :target="glossaryTooltip.id"
            triggers="hover"
            placement="top"
          >
            {{ glossaryTooltip.value }}
          </b-tooltip>
        </div>
      </div>

      <!-- Back to playlist -->
      <button
        :key="`btn-back-playlist-${video.uuid}`"
        v-if="isOnMobile"
        class="cls-btn-play-back-playlist position-absolute translate-middle"
        @click.stop="showPlaylistModal"
      >
        <img class="cls-play" src="@/assets/img/golearn/back.svg" />
      </button>

      <!-- Pause/Play Button -->
      <button
        :key="`btn-play-${video.uuid}`"
        v-if="video && !isPlaying"
        class="cls-btn-play-pause position-absolute top-50 start-50 translate-middle"
        @click.stop="togglePlay"
      >
        <img class="cls-play" src="@/assets/img/golearn/play.svg" />
      </button>
      <button
        :key="`btn-pause-${video.uuid}`"
        v-if="false"
        class="cls-btn-play-pause cls-pause position-absolute top-50 start-50 translate-middle"
        @click.stop="togglePlay"
      >
        <img class="cls-play paused" src="@/assets/img/golearn/paused.svg" />
      </button>

      <!-- Mute/Unmute Button -->
      <button
        v-if="video"
        :key="`btn-mute-${video.uuid}`"
        class="cls-btn-mute position-absolute"
        @click.stop="toggleMute"
      >
        <img
          v-if="isMuted"
          class="cls-mute muted"
          src="@/assets/img/golearn/muted.svg"
        />
        <img
          v-if="!isMuted"
          class="cls-mute"
          src="@/assets/img/golearn/mute.svg"
        />
      </button>

      <!-- Progress Bar -->
      <div
        v-if="video"
        :key="`progress-${video.uuid}`"
        class="progress-bar-wrapper position-absolute bottom-0 start-0 w-100"
      >
        <div class="progress-bar" :style="{ width: progress + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { formattedVideoDescription } from "../../helpers/common";

export default {
  props: {
    video: Object,
    isOnMobile: Boolean,
    showingDetail: Boolean,
  },
  data() {
    return {
      isAutoplay: true,
      isPlaying: true,
      isMuted: true,
      volume: 0.5,
      progress: 0,
      limitText: 70,
      isShowingDetail: false,
      glossaryTooltips: [],
    };
  },
  mounted() {
    const video = this.$refs.videoElement;
    video.volume = this.volume;
    this.handleWindowResize();
    window.addEventListener("resize", this.handleWindowResize);
    this.forcePlayVideo();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleWindowResize);
  },
  computed: {
    showSeeMoreDescription() {
      return (
        (this.video &&
          this.video.description &&
          this.video.description.length) > this.limitText
      );
    },
    formattedDescription() {
      let { glossary } = this.video;
      let shortDescription = this.shortDescription(this.video.description);
      const { description, glossaryTooltips } = formattedVideoDescription(
        shortDescription,
        glossary,
        "glossary-key"
      );
      this.glossaryTooltips = glossaryTooltips;
      return description;
    },
  },
  methods: {
    forcePlayVideo() {
      const self = this;
      const video = this.$refs.videoElement;
      video
        .play()
        .then(() => {
          self.isPlaying = true;
        })
        .catch((err) => {
          console.warn(
            "Autoplay prevented. Waiting for user interaction.",
            err
          );
        });
    },
    handleWindowResize() {
      const videoWidth = this.$refs.videoElement.offsetWidth;
      const videoHeight = this.$refs.videoElement.offsetHeight;
    },
    shortDescription(description) {
      return description.length > this.limitText
        ? description.slice(0, this.limitText) + " ... "
        : description;
    },
    togglePlay() {
      const self = this;
      const video = this.$refs.videoElement;
      if (this.isPlaying) {
        // Pause the video
        video.pause();
        this.isPlaying = false;
      } else {
        // Play the video
        video
          .play()
          .then(() => {
            self.isPlaying = true;
            self.isMuted = false;
          })
          .catch((err) => {
            console.error("Failed to play the video:", err);
          });
      }
    },
    toggleMute() {
      this.isMuted = !this.isMuted;
    },
    updateProgress() {
      const video = this.$refs.videoElement;
      if (video) {
        this.progress = (video.currentTime / video.duration) * 100;
      }
    },
    showDetail() {
      this.isShowingDetail = !this.isShowingDetail;
      if (this.isShowingDetail) {
        this.$emit("showDetail");
      } else {
        this.$emit("closeDetail");
      }
    },
    showPlaylistModal() {
      this.$emit("showPlaylistModal");
    },
    reset() {
      this.isAutoplay = true;
      this.isPlaying = true;
      this.isMuted = true;
      this.progress = 0;
    },
  },
  watch: {
    video(video) {
      this.reset();
      this.handleWindowResize();
      this.forcePlayVideo();
    },
    showingDetail(value) {
      this.isShowingDetail = value;
    },
  },
};
</script>

<style lang="scss" scoped>
.cls-video-player-container {
  background-color: #000;
  position: relative;
  width: 408px;
  height: 100%;
  overflow: hidden;
  @media screen and (max-width: 400px) {
    width: 100%;
  }
  .cls-description {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    // background-image: url('~@/assets/img/golearn/bg-videoplayer-description.png');
    // background-repeat: no-repeat;
    // background-position: top center;
    // height: 302px;
    background-image: linear-gradient(
      180deg,
      transparent,
      rgba(0, 0, 0, 0.1) 12.5%,
      rgba(0, 0, 0, 0.6)
    );
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-start;
    .cls-top {
      padding: 15px;
      flex: 1;
    }
    .cls-description-content {
      padding: 20px 15px;
      flex: 1;
      .title {
        font-weight: 700;
        line-height: 26.4px;
        color: #fff;
      }
      .description {
        color: #fff;
        font-weight: 382;
        line-height: 19.2px;
        white-space: pre-line;
        .cls-video-player-description {
          display: contents;
        }
        .cls-see-more {
          color: #fff;
          cursor: pointer;
          opacity: 0.8;
          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }

  .progress-bar-wrapper {
    bottom: 5px;
    height: 3px;
    background-color: #d9d9d9b2;
    .progress-bar {
      height: 100%;
      background-color: #fff;
      transition: width 0.1s;
    }
  }
  .cls-btn-mute {
    width: 36px;
    height: 36px;
    background: rgba(0, 0, 0, 0.7);
    position: absolute;
    top: 10px;
    right: 10px;
    border: none;
    border-radius: 50%;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover,
    &:focus,
    &:active {
      opacity: 0.7;
    }
    img {
      width: 23px;
    }
  }
  .cls-btn-play-back-playlist {
    width: 40px;
    height: 35px;
    background-color: transparent;
    position: absolute;
    top: 10px;
    left: 10px;
    border: none;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover,
    &:focus,
    &:active {
      opacity: 0.7;
    }
    img {
      width: 100%;
    }
  }
  .cls-btn-play-pause {
    position: absolute;
    top: calc(50% - 40px);
    left: calc(50% - 33px);
    z-index: 10;
    opacity: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
    &:hover,
    &:focus,
    &:active {
      opacity: 0.7;
    }
    img {
      width: 66px;
      height: 80px;
    }
    &.cls-pause {
      opacity: 0;
    }
  }
  &:hover {
    .cls-btn-play-pause {
      &.cls-pause {
        opacity: 1;
      }
    }
  }
}

// .btn-play-pause,
// .btn-mute {
//   z-index: 10;
//   background-color: rgba(255, 255, 255, 0.7);
//   border: none;
//   border-radius: 50%;
//   padding: 10px;
// }
// .btn-mute {
//   top: 10px;
//   right: 10px;
// }
.volume-slider-wrapper {
  display: inline-block;
  width: 20px;
  position: absolute;
  bottom: 40px;
  opacity: 0.8;
  transform: rotate(-90deg);
  transform-origin: right;
}
.volume-slider {
  width: 100px;
  appearance: none;
  background-color: #007bff;
}
</style>
