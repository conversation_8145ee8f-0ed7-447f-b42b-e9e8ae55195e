<template>
  <PerfectScrollbar :options="scrollOptions">
    <div class="cls-videos-list-contaner font-new-acumin">
      <ul class="cls-videos-listing">
        <li
          v-for="(video, eps) in videos"
          :key="video.uuid"
          :class="['video-item', { active: video.uuid === currentVideoUuid }]"
          @click="selectVideo(video.uuid)"
        >
          <div class="d-flex align-items-center">
            <img
              :src="getThumb(video)"
              alt="thumbnail"
              class="thumbnail me-2"
            />
            <div class="video-info">
              <h4 class="title font-22 mt-0 mb-2">{{ video.title }}</h4>
              <small class="description font-16 mt-0">{{
                shortDescription(video.description)
              }}</small>
              <p class="duration mt-2 font-14">{{ video.duration_format }}</p>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </PerfectScrollbar>
</template>

<script>
import { urlImage } from "../../helpers/common";
import { PerfectScrollbar } from "vue3-perfect-scrollbar";
import "vue3-perfect-scrollbar/style.css";

export default {
  components: {
    PerfectScrollbar,
  },
  props: {
    videos: Array,
    currentVideoUuid: Number,
  },
  data() {
    return {
      scrollOptions: {
        suppressScrollX: true,
      },
      limitText: 80,
    };
  },
  methods: {
    getThumb(video) {
      return urlImage({ image: video.thumb });
    },
    selectVideo(uuid) {
      this.$emit("selectVideo", uuid);
    },
    shortDescription(description) {
      return description.length > this.limitText
        ? description.slice(0, this.limitText) + " ..."
        : description;
    },
  },
};
</script>

<style lang="scss" scoped>
.cls-videos-list-contaner {
  height: 100%;
}
.cls-videos-listing {
  list-style-type: none;
  padding: 0;
  margin: 0;
  .video-item {
    padding: 20px;
    margin-bottom: 0;
    cursor: pointer;
    .thumbnail {
      width: 85px;
      height: 113px;
      object-fit: cover;
      border: 0.5px slid #d9d9d9;
      border-radius: 6px;
      margin-right: 15px;
    }
    .video-info {
      text-align: left;
      flex: 1;
      .eps {
        font-weight: 600;
        line-height: 24px;
        color: #333;
      }
      .title {
        font-weight: 600;
        line-height: 26.4px;
        color: #333;
      }
      .description {
        font-weight: 500;
        line-height: 19.2px;
        color: #333;
        white-space: pre-line;
      }
      .duration {
        font-weight: 500;
        line-height: 16.8px;
        color: #9d9d9d;
      }
    }
    &:hover,
    &.active {
      background-color: #cbecee;
    }
  }
}
</style>
