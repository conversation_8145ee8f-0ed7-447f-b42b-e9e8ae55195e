<template>
<div class="mt-5 pt-5" style="padding-top:240px !important;margin-top:300px;">Video</div>
</template>

<script>
export default {
  data() {
    return {
      title: "GoLearn",
    };
  },
  async mounted() {},
  methods: {},
  computed: {},
  metaInfo() {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
</style>
