<template>
  <div ref="goLearnPage" class="cls-page-layout cls-page-golearn font-new-acumin">
    <b-container>
      <div class="mb-1 mb-lg-4" ref="headerContainer">
        <div class="row">
          <div class="col-12">
            <h1 class="font-28 font-weight-bold color-green-dark mt-3 mt-lg-4">
              {{ $t("GO_LEARN.TITLE") }}
            </h1>
          </div>
        </div>
      </div>

      <div class="cls-golearn-container pb-3">
        <div v-if="!currentVideo && !isLoading && (isIndonesian || (!isIndonesian && !comingSoonVideoUrl))" class="cls-golearn-coming">
          <img
            class="cls-img mt-1 mb-0 mb-lg-2"
            :src="require(`@/assets/img/golearn/${comingGoLearnImg}`)"
          />
          <div class="cls-content-message mb-2">
            <h3 class="cls-title font-30 font-weight-bold mt-3">
              {{ $t("GO_LEARN.COMING_TITLE") }}
            </h3>
            <p class="cls-message font-22">{{ $t("GO_LEARN.COMING_MESSAGE") }}</p>
          </div>
        </div>
        <div v-if="!currentVideo && !isLoading && !isIndonesian && comingSoonVideoUrl" class="cls-golearn-coming">
          <video
            controls
            :src="comingSoonVideoUrl"
            playsinline
            autoplay
            :muted="true"
            class="cls-video-coming-soon"
          ></video>
        </div>
        <div v-if="currentVideo" class="cls-golearn-content">
          <!-- Left: Video List -->
          <div v-if="!isOnMobile" class="cls-golearn-column cls-playlist-column">
            <div
              ref="playlistHeaderContainer"
              v-if="currentPlayList && !isLoading"
              class="cls-playlist text-ceneter d-flex flex-column align-items-center justify-content-center mb-3"
            >
              <h2 class="font-20 title mt-0 mb-0">
                {{ currentPlayList.title }}
              </h2>
              <p class="font-16 episode">
                {{ $t("GO_LEARN.EPISODE", { total: totalVideoInPlaylist }) }}
              </p>
            </div>
            <VideoList
              :videos="videos"
              :style="{ height: videoListHeight }"
              :currentVideoUuid="currentVideoUuid"
              @selectVideo="selectVideo"
            />
          </div>

          <!-- Middle: Video Player -->
          <div
            class="cls-golearn-column cls-video-player-column d-flex align-items-start justify-content-center"
            :style="{ height: pageContentHeight }"
          >
            <VideoPlayer
              ref="refVideoPlayer"
              v-if="currentVideo"
              :video="currentVideo"
              :isOnMobile="isOnMobile"
              :showingDetail="showingDetail"
              @showDetail="showDetail"
              @closeDetail="closeDetail"
              @showPlaylistModal="showPlaylistModal"
            />
            <VideoDetail
              v-if="!isOnMobile && showingDetail && currentVideo"
              :style="{ height: pageContentHeight }"
              :video="currentVideo"
              @closeDetail="closeDetail"
            />
            <div
              v-if="!isOnMobile && videos && videos.length > 0"
              class="navigation-buttons ml-3"
            >
              <button
                :disabled="disabledPrevious"
                class="btn btn-secondary mt-2 mb-2"
                @click="previousVideo"
              >
                <img
                  class="cls-previous"
                  src="@/assets/img/golearn/previous.svg"
                />
              </button>
              <button
                :disabled="disabledNext"
                class="btn btn-secondary mt-2 mb-2"
                @click="nextVideo"
              >
                <img class="cls-next" src="@/assets/img/golearn/next.svg"/>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-container>
    <b-modal
      v-model="isShowPlaylistModal"
      centered
      modal-class="goro-general-fe-side-modal cls-golearn-modal-panel cls-bottom-modal"
      body-class="p-3"
      hide-footer
      @hide="onClosePlaylistModal"
      :no-close-on-backdrop="true"
    >
      <div class="font-new-acumin">
        <div
          v-if="currentPlayList && !isLoading"
          class="cls-playlist text-ceneter d-flex flex-column align-items-center justify-content-center mb-3"
        >
          <h2 class="font-20 title mt-0 mb-0">
            {{ currentPlayList.title }}
          </h2>
          <p class="font-16 episode">
            {{ $t("GO_LEARN.EPISODE", { total: totalVideoInPlaylist }) }}
          </p>
        </div>
        <VideoList
          :videos="videos"
          :style="{ height: videoListHeight }"
          :currentVideoUuid="currentVideoUuid"
          @selectVideo="selectVideo"
        />
      </div>
    </b-modal>

    <b-modal
      v-model="isShowVideoDetailModal"
      centered
      modal-class="goro-general-fe-side-modal cls-golearn-modal-panel cls-bottom-modal"
      body-class="p-3"
      hide-footer
      @hide="closeDetail"
      :no-close-on-backdrop="true"
    >
      <div class="font-new-acumin">
        <VideoDetail
          v-if="isShowVideoDetailModal && currentVideo"
          :style="{ height: pageContentHeight }"
          :video="currentVideo"
          :showModal="true"
          @closeDetail="closeDetail"
        />
      </div>
    </b-modal>
  </div>
</template>

<script>
import {formatSeconds} from "../../helpers/common";
import videosService from "../../services/videos.service";
import VideoList from "./VideoList.vue";
import VideoPlayer from "./VideoPlayer.vue";
import VideoDetail from "./VideoDetail.vue";
import i18n from "../../i18n";
import {INDO} from "@/constants/constants";

export default {
  components: {
    VideoList,
    VideoPlayer,
    VideoDetail,
  },
  data () {
    return {
      title: "GoLearn",
      videos: [],
      perPage: 100,
      playlistSlug: null,
      currentVideoUuid: null,
      currentVideo: null,
      showingDetail: false,
      pageHeight: 0,
      headerHeight: 0,
      playlistHeaderheight: 0,
      videoPlayerWidth: 0,
      pageContentHeight: 300,
      videoListHeight: 300,
      tmpSpace: 130, // px
      isLoading: false,
      videoUuid: null,
      isOnMobile: false,
      isShowPlaylistModal: false,
      isShowVideoDetailModal: false,
      isShowGolearnComing: true,
      disabledPrevious: true,
      disabledNext: false,
    };
  },
  async mounted () {
    await this.fetchData();
    this.handleWindowResize();
    window.addEventListener("resize", this.handleWindowResize);
  },
  beforeDestroy () {
    window.removeEventListener("resize", this.handleWindowResize);
  },
  methods: {
    resetDataVideo () {
      this.videos = [];
      this.currentVideo = null;
      this.currentVideoUuid = null;
      this.isShowPlaylistModal = false;
      this.isShowVideoDetailModal = false;
    },
    async fetchData () {
      this.resetDataVideo();
      this.videoUuid = this.$route.params.uuid;
      this.playlistSlug = this.$route.params.slug;
      try {
        if (this.videoUuid) {
          await this.getVideoDetail();
        } else {
          if (!this.playlistSlug) {
            this.playlistSlug = this.firstPlaylistSlug;
          }

          if (this.playlistSlug) {
            await this.getPlaylistVideos();
          }
        }
      } catch (e) {
        this.isLoading = false;
      }
    },
    handleWindowResize () {
      if (this.$refs.goLearnPage) {
        this.pageHeight = this.$refs.goLearnPage.offsetHeight;
        const element = this.$refs.goLearnPage;
        const style = window.getComputedStyle(element);
        const paddingTop = parseFloat(style.paddingTop);
        const paddingBottom = parseFloat(style.paddingBottom);
        const heightWithoutPadding =
          window.innerHeight - paddingTop - paddingBottom;

        this.pageHeight = heightWithoutPadding;
      }
      if (this.$refs.headerContainer) {
        this.headerHeight = this.$refs.headerContainer.offsetHeight;
      }
      if (this.$refs.playlistHeaderContainer) {
        this.playlistHeaderheight =
          this.$refs.playlistHeaderContainer.offsetHeight;
      }
      if (this.$refs.refVideoPlayer) {
        this.videoPlayerWidth = this.$refs.refVideoPlayer.$el.offsetWidth;
      }

      let height = this.pageHeight - this.headerHeight - this.tmpSpace;
      height = height > 300 ? height : 300;
      this.pageContentHeight = `${height}px`;
      this.videoListHeight = `${height - this.playlistHeaderheight}px`;

      if (window.innerWidth <= 991) {
        this.isOnMobile = true;
      } else {
        this.isOnMobile = false;
      }
    },
    async getVideoDetail () {
      this.isLoading = true;
      const res = await videosService.getVideo(this.videoUuid);
      if (res && res.data) {
        const currentVideo = res.data;
        if (currentVideo && currentVideo.playlist) {
          this.playlistSlug = currentVideo.playlist.slug || null;
        }

        if (this.playlistSlug) {
          await this.getPlaylistVideos(1, currentVideo.uuid);
        }
      }
      this.isLoading = false;
    },
    async getPlaylistVideos (page = 1, showVideoUuid = null) {
      const filters = {
        per_page: this.perPage,
        page,
      };
      this.isLoading = true;
      const res = await videosService.getPlaylistVideos(
        this.playlistSlug,
        filters
      );

      if (res && res.data) {
        let totalVideos = res.data.length || 0;
        this.videos = res.data.map((item, idx) => {
          return {
            ...item,
            eps: totalVideos-- + (page - 1) * this.perPage,
            duration_format: formatSeconds(item.duration),
          };
        });

        if (showVideoUuid) {
          this.currentVideoUuid = showVideoUuid;
        } else {
          if (this.videos.length > 0) {
            this.currentVideoUuid = this.videos[0].uuid;
          }
        }
      }
      this.isLoading = false;
    },
    selectVideo (videoUuid) {
      this.currentVideoUuid = videoUuid;
      this.showingDetail = false;
      this.isShowPlaylistModal = false;
      this.$router.push({ name: "goLearnVideo", params: { uuid: videoUuid } });
    },
    showDetail () {
      this.showingDetail = true;
      if (this.isOnMobile) {
        this.isShowVideoDetailModal = true;
      }
    },
    closeDetail () {
      this.showingDetail = false;
      this.isShowVideoDetailModal = false;
    },
    resetButtonMove () {
      this.disabledPrevious = false;
      this.disabledNext = false;
    },
    previousVideo () {
      this.resetButtonMove();
      const currentIndex = this.videos.findIndex(
        (video) => video.uuid === this.currentVideoUuid
      );
      if (currentIndex > 0) {
        this.currentVideoUuid = this.videos[currentIndex - 1].uuid;
      }

      if (currentIndex - 1 === 0) {
        this.disabledPrevious = true;
      }
    },
    nextVideo () {
      this.resetButtonMove();
      const currentIndex = this.videos.findIndex(
        (video) => video.uuid === this.currentVideoUuid
      );
      if (currentIndex < this.videos.length - 1) {
        this.currentVideoUuid = this.videos[currentIndex + 1].uuid;
      }
      if (currentIndex + 1 === this.videos.length - 1) {
        this.disabledNext = true;
      }
    },
    showPlaylistModal () {
      this.isShowPlaylistModal = true;
    },
    onClosePlaylistModal () {
      this.isShowPlaylistModal = false;
    },
  },
  computed: {
    comingGoLearnImg () {
      const locale = i18n.global.locale.value;
      let imagePrefix = "Coming-Soon-GoLearn";

      if (this.isOnMobile) {
        if (locale && locale.toLowerCase() === 'id') {
          return `${imagePrefix}-mobile-${locale.toLowerCase()}.png`;
        }
        return "Coming-Soon-GoLearn-mobile-en.png";
      } else {
        if (locale && locale.toLowerCase() === 'id') {
          return `${imagePrefix}-${locale.toLowerCase()}.png`;
        }
        return "Coming-Soon-GoLearn-en.png";
      }
    },
    playlists () {
      return this.$store.getters.goLearnPlaylists;
    },
    currentPlayList () {
      if (!this.playlists || this.playlists.length === 0) {
        return null;
      }
      return this.playlists.find(
        (playlist) => playlist.slug === this.playlistSlug
      );
    },
    firstPlaylist () {
      if (this.playlists && this.playlists.length > 0) {
        return this.playlists[0];
      }
      return null;
    },
    firstPlaylistSlug () {
      if (this.firstPlaylist && this.firstPlaylist.slug) {
        return this.firstPlaylist.slug;
      }
      return null;
    },
    totalVideoInPlaylist () {
      return this.videos.length;
    },
    getCurrentVideo () {
      return this.videos.find((video) => video.uuid === this.currentVideoUuid);
    },
    getHeightContent () {
    },
    isIndonesian () {
      if (this.$store.getters.userProfile && this.$store.getters.userProfile.iso_country_code) {
        return this.$store.getters.userProfile.iso_country_code === INDO.ISO_COUNTRY_CODE
      }
      return false
    },
    comingSoonVideoUrl () {
      if (this.$store.getters.configs && this.$store.getters.configs.golearn_coming_soon_international_video_link) {
        return this.$store.getters.configs.golearn_coming_soon_international_video_link
      }
      return null
    },
  },
  watch: {
    async $route (to, from) {
      const routesNameAllowedFetchData = [
        "goLearnPlaylist",
        "goLearnPlaylistForeigner",
        "goLearnMain",
        "goLearnMainForeigner"
      ]
      if (routesNameAllowedFetchData.includes(to.name)) {
        await this.fetchData();
      }
    },
    async currentVideoUuid () {
      this.currentVideo = this.videos.find((video, idx) => {
        this.resetButtonMove();
        if (video.uuid === this.currentVideoUuid) {
          if (idx === 0) {
            this.disabledPrevious = true;
          }
          if (idx === this.videos.length - 1) {
            this.disabledNext = true;
          }
        }
        return video.uuid === this.currentVideoUuid;
      });
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.cls-page-golearn {
  // overflow: hidden;
  .cls-golearn-container {
    .cls-golearn-content {
      display: flex;
      align-items: flex-start;
      justify-content: center;

      .cls-golearn-column {
        &.cls-playlist-column {
          width: 400px;
          padding-right: 20px;
          @media only screen and (max-width: 1300px) {
            width: 320px;
          }
          @media only screen and (max-width: 1200px) {
            width: 300px;
          }
        }

        &.cls-video-player-column {
          flex: 1;
          justify-content: flex-start !important;
          @media screen and (max-width: 992px) {
            justify-content: center !important;
          }
        }
      }
    }

    .navigation-buttons {
      display: flex;
      margin-top: 20px;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      height: 100%;

      .btn {
        box-shadow: 0px 0px 8.4px 0px #00000040;
        width: 66px;
        height: 48px;
        background-color: #f4f4f4;
        border-radius: 93px;
        border: none;
        opacity: 1;
        padding: 10px 12px 12px 12px !important;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }

        img {
          width: 24px;
          height: auto;
        }

        &.disabled,
        &:disabled {
          opacity: 0.4;

          img {
            opacity: 0.4;
          }
        }
      }
    }
  }

  .cls-playlist {
    .title {
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }

    .episode {
      font-weight: 500;
      line-height: 19.2px;
      color: #9d9d9d;
    }
  }

  .cls-golearn-coming {
    width: 620px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-self: center;
    @media screen and (max-width: 768px) {
      width: 100%;
    }

    .cls-img {
      width: 100%;
      object-fit: cover;
      height: auto;
      order: 0;
      @media screen and (max-width: 992px) {
        order: 1;
      }
    }

    .cls-content-message {
      order: 1;
      @media screen and (max-width: 992px) {
        order: 0;
      }

      .cls-title {
        font-weight: 700;
        line-height: 34.15px;
        text-align: center;
        color: #121212;
      }

      .cls-message {
        font-weight: 382;
        line-height: 25.61px;
        text-align: center;
        color: #6d6d6d;
      }
    }
  }

  .cls-video-coming-soon {
    height: calc(100vh - 230px);
    min-height: 450px;
    @media screen and (max-width: 992px) {
      height: calc(100vh - 200px);
      min-height: 350px;
    }
  }
}
</style>
