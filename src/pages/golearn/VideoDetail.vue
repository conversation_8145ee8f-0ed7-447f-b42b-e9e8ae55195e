<template>
  <div
    v-if="video"
    :class="showModal ? 'cls-video-detail-modal' : ''"
    class="cls-video-detail-container font-new-acumin"
  >
    <button
      v-if="!showModal"
      class="btn btn-secondary m-0 cls-btn-close"
      @click="closeDetail"
    >
      <img class="cls-next" src="@/assets/img/golearn/close.svg" />
    </button>
    <div
      class="cls-video-detail-wrapper d-flex flex-column align-items-start justify-content-start w-100"
    >
      <PerfectScrollbar :options="scrollOptions" class="w-100">
        <div class="cls-detail w-100">
          <h3 class="title font-24 mt-0">{{ video.title }}</h3>
          <article class="description pt-2 pb-2">
            <div v-html="formattedDescription" class="cls-video-content"></div>
            <b-tooltip
              v-for="(glossaryTooltip, idx) in glossaryTooltips"
              :key="idx"
              variant="secondary"
              :target="glossaryTooltip.id"
              triggers="hover"
              placement="top"
            >
              {{ glossaryTooltip.value }}
            </b-tooltip>
          </article>
        </div>
      </PerfectScrollbar>
      <div class="cls-footer mt-2 pt-2 w-100">
        <div
          class="cls-footer-wrapper pt-2 d-flex align-items-center justify-content-start w-100"
        >
          <p class="date font-12 mr-3">
            {{ videoCreated }}
          </p>
          <p class="duration font-12">
            {{ video.duration_format }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { PerfectScrollbar } from "vue3-perfect-scrollbar";
import "vue3-perfect-scrollbar/style.css";
import moment from "moment";
import { formattedVideoDescription } from "../../helpers/common";

export default {
  components: {
    PerfectScrollbar,
  },
  props: {
    video: Object,
    showModal: Boolean,
  },
  data() {
    return {
      scrollOptions: {
        suppressScrollX: true,
      },
      glossaryTooltips: [],
    };
  },
  methods: {
    closeDetail() {
      this.$emit("closeDetail");
    },
  },
  computed: {
    videoCreated() {
      if (!this.video.created_at) {
        return "";
      }
      const created = moment(this.video.created_at).format("DD MMM yyyy");
      return created;
    },
    formattedDescription() {
      let { glossary } = this.video;
      const { description, glossaryTooltips } = formattedVideoDescription(
        this.video.description,
        glossary,
        "glossary-detail-key"
      );
      this.glossaryTooltips = glossaryTooltips;
      return description;
    },
  },
};
</script>

<style lang="scss" scoped>
.cls-video-detail-container {
  width: 300px;
  padding: 30px 0;
  border: 1px solid #d9d9d9b2;
  border-radius: 0 21px 21px 0;
  height: 100%;
  box-shadow: 0px 0px 9.1px 0px #00000040;
  position: relative;
  &.cls-video-detail-modal {
    width: 100%;
    padding-top: 0;
    border: 0 !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }
  .cls-video-detail-wrapper {
    height: 100%;
    .ps {
      height: 100%;
      padding: 0 30px;
    }
  }
  .cls-detail {
    .title {
      font-weight: 600;
      line-height: 28.8px;
      color: #333333;
    }
    .description {
      font-size: 18px;
      font-weight: 382;
      line-height: 21.6px;
      color: #333333;
      text-align: left;
      white-space: pre-line;
      * {
        font-size: 18px;
        font-weight: 382;
        line-height: 21.6px;
        color: #333333;
      }
    }
  }
  .cls-footer {
    padding: 0 30px;
    .cls-footer-wrapper {
      border-top: 1px solid #000000;
      p {
        font-weight: 382;
        line-height: 14.4px;
        text-align: left;
        color: #898989;
      }
    }
  }
  .cls-btn-close {
    position: absolute;
    top: 4px;
    right: 4px;
    background-color: transparent;
    width: 32px;
    height: 32px;
    padding: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
    opacity: 0.8;
    cursor: pointer;
    z-index: 1;
    box-shadow: none;
    &:hover,
    &:focus,
    &active {
      box-shadow: none !important;
      background-color: transparent;
      opacity: 1;
    }
    img {
      width: 14px;
    }
  }
}
</style>
