<template>
  <div class="cls-register-form-wrapper cls-register-form-password-wrapper">
    <div class="cls-register-form-content">
      <h3 class="font-20 cls-title-step mt-3 mb-2">
        {{ $t("AUTH.TITLE_COMPLETE_ACCOUNT_SETUP") }}
      </h3>
      <RegisterStep :currentStep="3" :totalStep="totalStep"/>
      <b-row align-h="center" class="mt-4">
        <Form ref="observer" class="form col-md-12" v-slot="{ errors, handleSubmit }">
          <b-form ref="form" @submit.prevent="handleSubmit(createPassword)" class="cls-custom-form-group-field extend-invalid">
            <div class="form-group-parent">
              <Field :key="$i18n.locale" vid="password" :name="$t('AUTH.PASSWORD')"
                :rules="{ required: true, min: 8, strong_password_user_info: [{ ...form, ...paramsCheckStrongPassword }], strong_password_criteria: [{ ...form, ...paramsCheckStrongPassword }] }"
                v-slot="context" :model-value="form.password" @update:modelValue="form.password = $event">
                <b-form-group :label="$t('AUTH.PASSWORD')" label-for="AUTH_PASSWORD" label-class="custom-label" class="custom-form-group custom-form-group-passord mt-3">
                  <b-input-group>
                    <b-form-input id="AUTH_PASSWORD" v-bind="context.field" placeholder="••••••••••••"
                      :type="showPassword ? 'text' : 'password'" aria-describedby="input-password-feedback"
                      :state="getValidationState(context.meta)"></b-form-input>
                    <b-input-group-append>
                      <div class="show-hide d-flex justify-content-center align-items-center"
                        @click="showPassword = !showPassword">
                        <b-icon width="18" height="18" color="black"
                          :icon="showPassword ? 'eye' : 'eye-slash'"></b-icon>
                      </div>
                    </b-input-group-append>
                  </b-input-group>
                  <img width="24" class="cls-icon-lock" src="@/assets/img/icons/input-password.svg" alt=""/>
                </b-form-group>
                <b-form-invalid-feedback id="input-password-feedback">
                  {{ errors[$t('AUTH.PASSWORD')] }}
                </b-form-invalid-feedback>

                <p class="font-14 mt-3 cls-password-contain">
                  {{ $t('AUTH.PASSWORD_MUST_CONTAIN') }}
                </p>
                <ul class="goro-password-strength-checklist mt-0">
                  <li :class="getValidClass(passwordLeast1Upper1LowerCase)">
                    <p v-if="!passwordLeast1Upper1LowerCase" class="cls-rules-label font-20">
                      aA
                    </p>
                    <b-icon v-if="passwordLeast1Upper1LowerCase" :icon="getPasswordIcon(passwordLeast1Upper1LowerCase)" scale="1.4"></b-icon>
                    <span class="font-12 cls-label">{{ $t("AUTH.PASSWORD_RULES.LEAST1CHARCASE") }}</span>
                  </li>
                  <li :class="getValidClass(passwordLeast1Number)">
                    <p v-if="!passwordLeast1Number" class="cls-rules-label font-20">
                      123
                    </p>
                    <b-icon v-if="passwordLeast1Number" :icon="getPasswordIcon(passwordLeast1Number)" scale="1.4"></b-icon>
                    <span class="font-12 cls-label">{{ $t("AUTH.PASSWORD_RULES.LEAST1NUMBER") }}</span>
                  </li>
                  <li :class="getValidClass(passwordLeast1Special)">
                    <p v-if="!passwordLeast1Special" class="cls-rules-label font-20">
                      !#$%
                    </p>
                    <b-icon v-if="passwordLeast1Special" :icon="getPasswordIcon(passwordLeast1Special)" scale="1.4"></b-icon>
                    <span class="font-12 cls-label">{{ $t("AUTH.PASSWORD_RULES.LEAST1SPECIAL") }}</span>
                  </li>
                  <li :class="getValidClass(passwordLeast8Length)">
                    <p v-if="!passwordLeast8Length" class="cls-rules-label font-20">
                      8+
                    </p>
                    <b-icon v-if="passwordLeast8Length" :icon="getPasswordIcon(passwordLeast8Length)" scale="1.4"></b-icon>
                    <span class="font-12 cls-label">{{ $t("AUTH.PASSWORD_RULES.LEAST8CHAR") }}</span>
                  </li>
                </ul>
              </Field>
            </div>
            <!-- Recaptcha V2 checkbox fallback -->
            <div v-if="showRecaptchaV2.CREATE_PASSWORD" :ref="recaptchaV2Checkbox.CREATE_PASSWORD" class="d-flex justify-content-center mt-2"></div>
            <b-row align-h="center" class="mt-4">
              <b-button class="btn-main ml-3 mr-3 mt-2" type="submit" variant="none" style="width: 100%">
                {{ $t('AUTH.SUBMIT') }}
              </b-button>
            </b-row>
          </b-form>
        </Form>
      </b-row>
    </div>
  </div>
</template>

<script>
import { useRecaptcha } from "@/composables/useRecaptcha";
import {configure, defineRule, Field, Form} from "vee-validate"
import {email, min, regex, required} from "@vee-validate/rules"
import {localize} from "@vee-validate/i18n"
import {getDeviceUUID, getErrorMessage, notify,} from "@/helpers/common"
import {gtmTrackEvent} from "@/helpers/gtm"
import {GTM_EVENT_NAMES} from "@/constants/gtm"
import {FIREBASE_EVENTS, firebaseLogEvent} from "@/helpers/firebase"
import { ERROR_CODE } from "@/constants/constants";
import messErrors from "@/constants/errors"
import RegisterStep from "@/pages/auth/register/RegisterStep";
import preRegisteredService from "@/services/pre_registered.service"

defineRule("email", email)
defineRule("min", min)
defineRule("required", required)
defineRule('regex', regex)

/**
 * Not based on something that is easy to guess or uses information related to the User, for example: name, telephone number, date of birth
 */
defineRule('strong_password_user_info', (value, [otherValues]) => {
  let { nationalNumber, name, email } = otherValues

  if (!name) {
    name = ''
  }
  name = name.split(' ').join('').trim()

  if (name && value.toLowerCase().includes(name.toLowerCase())) {
    return false
  }

  if (nationalNumber && value.includes(nationalNumber)) {
    return false
  }

  if (email && value.includes(email)) {
    return false
  }

  return true
})

/**
 * Uses a combination of uppercase letters, lowercase letters and numbers, wherever possible using special characters (such as: !$%#*)
 */
defineRule('strong_password_criteria', (value, [otherValues]) => {
  // Check if the password contains a combination of uppercase, lowercase, numbers, and special characters
  const regexPattern = /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9])[A-Za-z0-9!@#$%^&*()_+{}\[\]:;<>,.?/~\-\\]+$/
  return regexPattern.test(value);
})

configure({
  generateMessage: localize({
    en: {
      messages: {
        required: 'The {field} is required',
        legal_name: 'Legal name only accepts alphabets, single quotes, commas, full stops, and single spaces.',
        strong_password_user_info: 'Password cannot contain user-related information.',
        strong_password_criteria: 'Password should contain a mix of uppercase letters, lowercase letters, numbers, and special characters.',
      },
    },
    id: {
      messages: {
        required: 'Kolom {field} wajib diisi',
        legal_name: 'Nama resmi hanya menerima alfabet, tanda kutip tunggal, koma, titik, dan satu spasi.',
        strong_password_user_info: 'Kata sandi tidak boleh mengandung informasi yang berhubungan dengan pengguna.',
        strong_password_criteria: 'Kata sandi harus mengandung kombinasi huruf kapital, huruf kecil, angka, dan karakter khusus.',
      },
    },
  }),
})

export default {
  components: {
    Field,
    Form,
    RegisterStep,
  },
  props: {
    totalStep: {
      type: Number,
      default: 1,
    },
  },
  emits: ['next-step', 'go-back', 'callback-data'],
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ CREATE_PASSWORD: 'createPassword' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      title: "Register",
      form: {
        platform: "web",
        device_id: getDeviceUUID(),
        password: '',
      },
      showPassword: false,
    }
  },
  methods: {
    getValidationState({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    async createPassword() {
      if (!this.validateRecaptchaV2.CREATE_PASSWORD()) return
      let action = "createPassword"
      try {
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_REGISTER,
          action: action,
        })
        const recaptchaTokenV3 = await this.recaptchaV3Exec.CREATE_PASSWORD()
        const recaptchaTokenV2 = this.recaptchaTokenV2.CREATE_PASSWORD
        const formData = {
          ...this.form,
          uuid: this.preRegistered?.uuid,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }
        const res = await preRegisteredService.createPassword(formData, false, true)
        if (res) {
          let resData = res
          resData.preRegistered = res.data
          this.$emit('callback-data', {
            ...resData
          })
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.CREATE_PASSWORD = true
        } else {
          const errorMessage = getErrorMessage(ex) || messErrors.INTERNAL;
          firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
            uuid: this.form.uuid,
            action: action,
            error: errorMessage
          });
          notify({ text: errorMessage, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.CREATE_PASSWORD()
      }
    },
    getValidClass(valid) {
      if (this.form.password) {
        if (valid) {
          return 'is-valid';
        }
        return 'is-invalid';
      }
      return '';
    },
    getPasswordIcon(valid) {
      if (this.form.password) {
        if (valid) {
          return 'check-circle-fill';
        } else {
          return 'x-circle-fill';
        }
      }
      return ''      
    },
  },
  computed: {
    preRegistered() {
      return this.$store.getters.preRegistered
    },
    accountName() {
      return this.preRegistered?.name
    },
    accountEmail() {
      return this.preRegistered?.email || this.preRegistered?.email_fb || this.preRegistered?.email_google || this.preRegistered?.email_apple
    },
    accountPhoneNationalNumber() {
      return this.preRegistered?.phone
    },
    passwordLeast8Length() {
      let isValid = /^(?=.*[^\s]).{8,}$/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Number() {
      let isValid = /\d/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Upper1LowerCase() {
      let isValid = /^(?=.*[a-z])(?=.*[A-Z]).+$/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Special() {
      const regex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;
      // Test if the password contains at least one special character
      return regex.test(this.form.password)
    },
    paramsCheckStrongPassword() {
      return {
        nationalNumber: this.accountPhoneNationalNumber,
        name: this.accountName,
        email: this.accountEmail,
      }
    },
  },
}
</script>

<style lang="scss">
.cls-register-form-password-wrapper{
  .input-group-append{
    .show-hide{
      background-color: transparent !important;
    }
  }
  .cls-password-contain{
    font-weight: 600;
    color: #616161;
  }
  .goro-password-strength-checklist{
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    li{
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 10px 7px;
      .cls-label{
        font-weight: 500;
        color: #6D6D6D;
        text-align: center;
      }
      .cls-rules-label{
        color: #1E90FF;
        font-weight: 600;
        text-align: center;
      }
      svg{
        margin-top: 8px;
        margin-bottom: 7px;
      }
      &.is-valid{
        color: #28a745;
        .cls-rules-label,
        svg{
          color: #28a745;
        }
      }
      &.is-invalid{
        color: #dc3545;
        .cls-rules-label,
        svg{
          color: #dc3545;
        }
      }
    }
  }
}
</style>