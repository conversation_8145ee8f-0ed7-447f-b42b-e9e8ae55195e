<template>
  <div v-if="form" class="cls-register-form-wrapper" :class="{ 'reverse-otp': isVerifyOtpStatus && isReverseOtp }">
    <div v-if="!isVerifyOtpStatus" class="cls-register-form-content cls-unique-form-content">
      <h3 class="font-20 cls-title-step mt-3 mb-2">
        {{ $t("AUTH.TITLE_COMPLETE_ACCOUNT_SETUP") }}
      </h3>
      <RegisterStep :currentStep="1" :totalStep="totalStep"/>
      <b-row align-h="center" class="mt-4">
        <Form class="form col-md-12" v-slot="{ errors, handleSubmit }">
          <b-form @submit.prevent="handleSubmit(requestOtp)" class="cls-custom-form-group-field extend-invalid">
            <div class="form-group-parent">
              <Field :key="$i18n.locale" :model-value="form.email" @update:modelValue="form.email = $event" :name="$t('AUTH.EMAIL')"
                     :rules="{ required: true, email: true }" v-slot="context">
                <b-form-group :label="$t('AUTH.EMAIL')" label-for="AUTH_EMAIL" label-class="custom-label" class="custom-form-group">
                  <b-form-input id="AUTH_EMAIL" v-bind="context.field" placeholder="<EMAIL>" type="email"
                                :disabled="disableEmailInput" :state="getValidationState(context.meta)" aria-describedby="input-email-feedback"></b-form-input>
                </b-form-group>
                <b-form-invalid-feedback id="input-email-feedback">
                  {{ errors[$t('AUTH.EMAIL')] }}
                </b-form-invalid-feedback>
                <div v-if="autocorrectEmail" class="suggested-email">
                  <p class="prompt">{{ $t("AUTH.EMAIL_CORRECTION") }} <a class="suggestion"
                                                                         @click="useEmailSuggestion">{{ autocorrectEmail }}</a>?</p>
                </div>
              </Field>
            </div>

            <div class="form-group-parent">
              <Field :key="$i18n.locale" :name="$t('AUTH.PHONE')" :rules="{}" v-slot="{ valid, errors, field }">
                <b-form-group :label="$t('AUTH.PHONE')" label-for="AUTH_PHONE" label-class="custom-label" class="custom-form-group">
                  <vue-tel-input id="AUTH_PHONE" v-bind="field" v-model="vuePhone.phone"
                                 :autoFormat="false"
                                 @on-input="updatePhoneNumber"
                                 :validCharactersOnly="false"
                                 :dropdownOptions="{ showDialCodeInList: true, showDialCodeInSelection: true, showFlags: true, showSearchBox: true, searchBoxPlaceholder: $t('AUTH.TEL_INPUT_SEARCH_PLACEHOLDER') }"
                                 :inputOptions="{ autocomplete: false, name: 'phone', placeholder: $t('AUTH.ENTER_YOUR_PHONE_NUMBER'), styleClasses: vuePhone.isValid ? 'form-control is-valid' : (!vuePhone.isValid && vuePhone.isValid !== null ? 'form-control is-invalid' : 'form-control')}"
                                 :defaultCountry="form.iso_country_code || 'ID'" :preferredCountries="['ID']">
                  </vue-tel-input>
                </b-form-group>
                <label class="invalid" v-if="!vuePhone.isValid">
                  {{
                    errors[$t('AUTH.PHONE')] || vuePhone.isEmpty ? $t('AUTH.THE_PHONE_IS_REQUIRED') :
                      $t('AUTH.INVALID_PHONE_NUMBER')
                  }}
                </label>
              </Field>
            </div>

            <div class="cls-otp-notice-block">
              <p class="font-14 text-center mt-1 mb-2 mt-md-2 mb-md-3 px-0 px-md-3 cls-otp-notice-input">
                {{ $t("AUTH.OTP_NOTICE") }}
              </p>
            </div>
            <!-- Recaptcha V2 checkbox fallback -->
            <div v-if="showRecaptchaV2.REQUEST_OTP" :ref="recaptchaV2Checkbox.REQUEST_OTP" class="d-flex justify-content-center"></div>
            <b-row align-h="center" class="mt-4">
              <div class="cls-action-btn d-flex align-items-center justify-content-center mt-4">
                <b-button class="btn-main px-4 white-normal" @click="$emit('go-back')" variant="none">
                  {{ $t('AUTH.BACK') }}
                </b-button>
                <b-button class="btn-main px-4" type="submit" variant="none">
                  {{ $t('AUTH.CONTINUE') }}
                </b-button>
              </div>
            </b-row>
          </b-form>
        </Form>
      </b-row>
    </div>
    <div v-if="isVerifyOtpStatus && isInputOtp" class="cls-register-form-content cls-otp-form-content">
      <h3 class="font-24 cls-title-step mt-3 mb-2">
        {{ $t("AUTH.TITLE_OTP_VERIFICATION") }}
      </h3>
      <b-row align-h="center" class="mt-4">
        <b-form class="form cls-otp-form text-center" @submit.prevent="verifyOtp">
          <p class="cls-enter-otp-text font-14">
            {{ $t("AUTH.ENTER_THE_OTP_TEXT") }}
          </p>
          <p class="font-14 cls-phone-number-line">
            <span class="font-14 cls-phone-number">{{ preRegistered.phone }}</span>
            <label class="hyperlink font-14 m-0 p-0 capitalize" @click="changePhoneNumber">
              {{ $t("AUTH.CHANGE_PHONE_NUMBER") }}
            </label>
          </p>

          <label class="invalid font-bold pb-2"
                 :style="{ visibility: !isOtpCodeValid && observeOtp ? 'visible' : 'hidden' }">
            {{ $t("AUTH.OTP_REQUIRED") }}
          </label>
          <div style="display: flex; justify-content: center; align-items: center;">
            <v-otp-input ref="otpInput" v-model:value="form.otp_code" input-classes="otp-input" separator=" "
                         :num-inputs="6" :should-auto-focus="true" input-type="numeric" @on-change="onOtpChange"/>
          </div>
          <b-col class="text-center pt-3" align-h="center">
            <b-row class="align-items-center" align-h="center" v-if="isSendOtpUsingRadist && otpStatus">
              <p class="font-bold font-14 color-gray-6d">{{ $t("AUTH.OTP_DELIVERY_STATUS") }}</p>
              <p class="font-bold font-14 text-white ml-2" :class="otpStatusClass">{{ otpStatusText }}</p>
            </b-row>
            <b-row class="otp-notice-background">
              <p class="font-14 cls-otp-notice-text" v-if="isSendOtpUsingRadist">
                {{ $t("AUTH.OTP_NOTICE") }}
              </p>
            </b-row>
            <!-- Recaptcha V2 checkbox fallback -->
            <div v-if="showRecaptchaV2.VERIFY_OTP" :ref="recaptchaV2Checkbox.VERIFY_OTP" class="d-flex justify-content-center mt-1"></div>
            <b-button id="btn_Register_VerifyOtp" class="btn-main mt-1 pt-2 pb-2" type="submit" variant="none" style="width: 350px">
              {{ $t("AUTH.VERIFY") }}
            </b-button>
          </b-col>
          <b-row class="flex text-center pt-2 pb-2" align-h="center">
            <p class="font-bold color-gray-6d font-14">{{ $t("AUTH.OTP_DID_NOT_RECEIVE") }}</p>
            <p class="font-bold font-14 ml-1" :class="otpResendClass" @click="resendOtp()">
              {{ resendOtpText }}
            </p>
          </b-row>
        </b-form>
      </b-row>
    </div>
    <div v-if="isVerifyOtpStatus && isReverseOtp">
      <b-row class="justify-content-center align-items-center">
        <b-col v-if="isReverseOtpStep1" class="text-center" cols="auto">
          <img width="300" class="mb-3"
               src="@/assets/img/registration/otp_reverse_send.svg" alt="" @click="onRequestInputOtpClicked"/>
          <b-form class="form reverse-otp-form text-center" @submit.prevent="openWhatsappToSendReverseOtp">
            <p class="font-bold" style="font-size:26px; color: var(--text-color-darker)">
              {{ $t("AUTH.VERIFY_ACCOUNT_VIA_WHATSAPP") }}
            </p>
            <p class="font-normal mt-2 mb-3" style="color: var(--text-color-darker)">
              {{ $t("AUTH.VERIFY_ACCOUNT_VIA_WHATSAPP_DESCRIPTION") }}
            </p>
            <div class="reverse-otp-warning d-flex align-items-center px-2 py-2 mt-3 mb-3">
              <img src="@/assets/img/registration/warning.svg" alt="Warning" class="ml-1 mr-2"/>
              <p class="font-bold text-center flex-grow-1" style="color: var(--text-color-warning); font-size: 14px">
                {{ $t("AUTH.VERIFY_ACCOUNT_VIA_WHATSAPP_WARNING") }}
              </p>
            </div>
            <b-button class="btn-main mt-1 pt-2 pb-2" type="submit" variant="none" style="font-size: 18px; width: 100%">
              {{ $t("AUTH.SEND_VERIFICATION_VIA_WHATSAPP") }}
            </b-button>
          </b-form>
        </b-col>

        <b-col v-if="isReverseOtpStep2" class="text-center p-0" cols="auto">
          <img width="300" class="mb-3"
               src="@/assets/img/registration/otp_reverse_verifying.svg" alt="" @click="onRequestInputOtpClicked"/>
          <b-form class="form reverse-otp-form text-center" @submit.prevent="checkReverseOtpStatus(true)">
            <p class="font-bold" style="font-size:26px; color: var(--text-color-darker)">
              {{ $t("AUTH.VERIFYING_YOUR_CODE") }}
            </p>
            <p class="font-normal mt-2 mb-3" style="color: var(--text-color-darker); white-space: pre-wrap;">
              {{ $t("AUTH.VERIFYING_YOUR_CODE_DESCRIPTION") }}
            </p>
            <div class="reverse-otp-warning d-flex align-items-center px-2 py-2 mt-3 mb-3">
              <img src="@/assets/img/registration/warning.svg" alt="Warning" class="ml-1 mr-2"/>
              <p class="font-bold text-center flex-grow-1" style="color: var(--text-color-warning); font-size: 14px">
                {{ $t("AUTH.VERIFY_ACCOUNT_VIA_WHATSAPP_WARNING") }}
              </p>
            </div>
            <!-- Recaptcha V2 checkbox fallback -->
            <div v-if="showRecaptchaV2.PHONE_VERIFICATION_STATUS" :ref="recaptchaV2Checkbox.PHONE_VERIFICATION_STATUS" class="d-flex justify-content-center"></div>
            <b-button class="btn-main mt-1 pt-2 pb-2" type="submit" variant="none" style="font-size: 18px; width: 100%">
              {{ $t("AUTH.CHECK_VERIFICATION_STATUS") }}
            </b-button>
            <p class="font-medium mt-3" style="color: var(--text-color-darker);">
              {{ $t("AUTH.VERIFYING_YOUR_CODE_WARNING") }}
            </p>
            <b-row class="flex text-center" align-h="center">
              <p class="font-normal" style="font-size: 15px; color: var(--text-color-darker);">
                {{ $t("AUTH.OTP_DID_NOT_RECEIVE") }}</p>
              <p class="font-normal ml-1" :class="otpResendClass" @click="resendOtp()">
                {{ resendOtpText }}
              </p>
            </b-row>
          </b-form>
          <b-row align-h="center">
            <p class="reverse-otp-warning text-center font-normal px-3 py-2 mt-4 mb-1"
               style="color: var(--text-color-darker); font-size: 16px" v-html="needHelpText"/>
          </b-row>
        </b-col>
      </b-row>
    </div>

    <!-- Recaptcha V2 checkbox fallback -->
    <div v-if="showRecaptchaV2.RESEND_OTP" :ref="recaptchaV2Checkbox.RESEND_OTP" class="d-flex justify-content-center"></div>
    <div v-if="showRecaptchaV2.REQUEST_INPUT_OTP_METHOD" :ref="recaptchaV2Checkbox.REQUEST_INPUT_OTP_METHOD" class="d-flex justify-content-center"></div>
  </div>
</template>

<script>
import { useRecaptcha } from "@/composables/useRecaptcha";
import { configure, defineRule, Field, Form } from "vee-validate"
import { email, min, regex, required } from "@vee-validate/rules"
import { localize, setLocale } from "@vee-validate/i18n"
import RegisterStep from "@/pages/auth/register/RegisterStep"
import { getDeviceUUID, getErrorMessage, notify, getDeviceInfo } from "@/helpers/common"
import { FOREIGNER, OTP_STATUS, OTP_TYPE, STATUS_CODE, PRE_REGISTERED_STATUSES } from "@/constants/constants"
import VOtpInput from "vue3-otp-input"
import { gtmTrackEvent } from "@/helpers/gtm"
import { GTM_EVENT_NAMES } from "@/constants/gtm"
import { FIREBASE_EVENTS, firebaseLogEvent } from "@/helpers/firebase";
import authService from "@/services/auth.service"
import preRegisteredService from "@/services/pre_registered.service"
import { OTP_METHOD } from "@/constants/constants"
import externalSites from "@/constants/externalSites";
import { ERROR_CODE } from "@/constants/constants";
import messErrors from "@/constants/errors";

let DEFAULT_RESEND_OTP_TIME_IN_SECONDS = 120
defineRule("email", email)
defineRule("min", min)
defineRule("required", required)
defineRule('regex', regex)

configure({
  generateMessage: localize({
    en: {
      messages: {
        required: 'The {field} is required',
      },
    },
    id: {
      messages: {
        required: 'Kolom {field} wajib diisi',
      },
    },
  }),
})

export default {
  components: {
    Field,
    Form,
    RegisterStep,
    VOtpInput,
  },
  props: {
    totalStep: {
      type: Number,
      default: 3,
    },
    formData: {
      type: Object,
      default: null,
    },
  },
  emits: ['next-step', 'go-back', 'callback-data'],
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({
      REQUEST_OTP: 'requestOtp',
      RESEND_OTP: 'resendOtp',
      VERIFY_OTP: 'verifyOtp',
      REQUEST_INPUT_OTP_METHOD: 'requestInputOtpMethod',
      PHONE_VERIFICATION_STATUS: 'phoneVerificationStatus',
    })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      form: null,
      vuePhone: {
        phone: "",
        isValid: null,
        isEmpty: true,
        props: {
          error: true,
          clearable: true,
          //fetchCountry: true, //Do not use it with defaultCountryCode
          defaultCountryCode: "ID",
          preferredCountries: ["ID"],
          translations: {
            countrySelectorLabel: this.$t("AUTH.COUNTRY_CODE"),
            phoneNumberLabel: this.$t("AUTH.PHONE_HINT"),
            example: this.$t("common.example"),
          },
          color: "#80BDFF",
          validColor: "#006666",
          errorColor: "#DC3445",
        }
      },
      isOtpCodeValid: false,
      observeOtp: false,
      resendOtpTimer: 0,
      getOtpStatusIntervalId: null,
      otpStatus: OTP_STATUS.QUEUED,
      showPassword: false,
      propertyImages: [],
      ownedProperties: true,
      autocorrectEmail: "",
      deviceInfo: getDeviceInfo(),
      isReverseOtpStep1: false,
      isReverseOtpStep2: false,
      isReversedOtpVerified: false,
      requestInputOtpClickCount: 0,
      checkReverseOtpStatusInterval: null,
    }
  },
  async beforeDestroy() {
    this.stopCheckReverseOtpStatus()
  },
  methods: {
    getValidationState({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    updatePhoneNumber(number, data) {
      if (number) {
        this.vuePhone.isValid = data.valid
        this.vuePhone.isEmpty = false
      } else {
        if (this.vuePhone.isValid !== null) {
          this.vuePhone.isValid = false
        }
        this.vuePhone.isEmpty = true
      }
      this.form.phone = data.number
      this.form.country_code = data.countryCallingCode
      this.form.iso_country_code = data.countryCode
    },
    async changePhoneNumber() {
      this.observeOtp = false
      this.resendOtpTimer = 0
      this.otpStatus = OTP_STATUS.QUEUED

      this.isReverseOtpStep1 = false
      this.isReverseOtpStep2 = false
      this.observeInputOtp = false
      this.stopCheckReverseOtpStatus()
      this.$emit('go-back')
    },
    async useEmailSuggestion() {
      this.form.email = this.autocorrectEmail
      this.autocorrectEmail = ""
      await this.requestOtp()
    },
    onOtpChange(value) {
      this.isOtpCodeValid = value.toString().trim().length === 6
    },
    async requestOtp(isResend = false) {
      if (this.preRegistered) {
        if (this.preRegistered.phone && (this.preRegistered.phone === this.form.phone)) {
          this.$emit('next-step')
        }
        return
      }
      if (isResend === true && this.resendOtpTimer > 0) {
        return
      }

      if (!this.vuePhone.isValid && this.vuePhone.isValid === null) {
        this.vuePhone.isValid = false
      }

      if (this.vuePhone.isValid) {
        if (!this.validateRecaptchaV2.REQUEST_OTP()) return
        try {
          gtmTrackEvent({ event: GTM_EVENT_NAMES.USER_REQUEST_OTP, })
          const recaptchaTokenV3 = await this.recaptchaV3Exec.REQUEST_OTP()
          const recaptchaTokenV2 = this.recaptchaTokenV2.REQUEST_OTP
          let payload = {
            recaptcha_token: recaptchaTokenV3,
            recaptcha_token_v2: recaptchaTokenV2,
            preferred_locale: this.$i18n.locale === FOREIGNER.LOCALE ? "en" : this.$i18n.locale,
            ...this.form
          }
          if (isResend) {
            payload.is_resend = true;
          }
          let data = await preRegisteredService.requestOtp(payload, false, true)
          if (data && data.extraData && data.extraData.autocorrect) {
            this.autocorrectEmail = data.extraData.autocorrect
          }
          if (data) {
            this.handleOtpResponse(data)
          }
        } catch (ex) {
          if (ex && ex.statusCode === STATUS_CODE.HTTP_TOO_EARLY) {
            if (isResend === true) {
              notify({ text: this.$t("AUTH.OTP_PLEASE_WAIT"), type: "error" })
            }
          } else if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.REQUEST_OTP = true
          } else {
            const errorMessage = getErrorMessage(ex) || messErrors.INTERNAL;
            notify({ text: errorMessage, type: "error" })
            firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
              email: this.form.email,
              phone: this.form.phone,
              method: "RequestOtp",
              error: errorMessage
            });
          }
        } finally {
          this.resetRecaptchaV2.REQUEST_OTP()
        }
      }
    },
    async resendOtp() {
      if (!this.validateRecaptchaV2.RESEND_OTP()) return
      if (this.resendOtpTimer > 0) {
        return
      }
      try {
        gtmTrackEvent({ event: GTM_EVENT_NAMES.USER_RESEND_OTP, })
        const recaptchaTokenV3 = await this.recaptchaV3Exec.RESEND_OTP()
        const recaptchaTokenV2 = this.recaptchaTokenV2.RESEND_OTP
        let data = await preRegisteredService.resendOtp({
          uuid: this.preRegistered.uuid,
          device_id: getDeviceUUID(),
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }, false, true)
        if (data && data.otp_method) {
          const resData = {
            preRegistered: data.data,
            otp_method: data.otp_method,
            message_content: data?.message_content,
            message_to: data?.message_to
          }
          this.$emit('callback-data', resData)
          this.displayOtpVerification()
        }
      } catch (ex) {
        if (ex && ex.statusCode === STATUS_CODE.HTTP_TOO_EARLY) {
          if (isResend === true) {
            notify({ text: this.$t("AUTH.OTP_PLEASE_WAIT"), type: "error" })
          } else {
            this.displayOtpVerification()
          }
        } else if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.RESEND_OTP = true
        } else {
          const errorMessage = getErrorMessage(ex) || messErrors.INTERNAL;
          notify({ text: errorMessage, type: "error" })
          firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
            phone: this.preRegistered.phone,
            method: "resendOtp",
            error: errorMessage
          });
        }
      } finally {
        this.resetRecaptchaV2.RESEND_OTP()
      }
    },
    async verifyOtp() {
      if (!this.validateRecaptchaV2.VERIFY_OTP()) return
      try {
        gtmTrackEvent({ event: GTM_EVENT_NAMES.USER_VERIFY_OTP, })
        const recaptchaTokenV3 = await this.recaptchaV3Exec.VERIFY_OTP()
        const recaptchaTokenV2 = this.recaptchaTokenV2.VERIFY_OTP
        let data = await preRegisteredService.verifyOtp({
          uuid: this.preRegistered.uuid,
          device_id: getDeviceUUID(),
          otp_code: this.form.otp_code,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }, false, true)
        if (data && data.data) {
          this.$emit('next-step', {
            preRegistered: data.data
          })
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.VERIFY_OTP = true
        } else {
          const errorMessage = getErrorMessage(ex) || messErrors.INTERNAL;
          notify({ text: errorMessage, type: "error" })
          firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
            email: this.preRegistered.email,
            phone: this.preRegistered.phone,
            method: "verifyOtp",
            error: errorMessage
          });

        }
      } finally {
        this.resetRecaptchaV2.VERIFY_OTP()
      }
    },
    openWhatsappToSendReverseOtp() {
      if (this.reverseOtpMessageTo && this.reverseOtpMessageContent) {
        const phone = this.reverseOtpMessageTo;
        const message = encodeURIComponent(this.reverseOtpMessageContent);
        const url = `https://wa.me/${phone}?text=${message}`;
        window.open(url, '_blank');
        this.displayReverseOtpStep2()
      } else {
        notify({
          text: "Missing reverse OTP info. Please try again <NAME_EMAIL> for assistance.",
          type: "error"
        })
      }
    },
    displayReverseOtpStep2 () {
      this.isReverseOtpStep1 = false
      this.isReverseOtpStep2 = true
      this.startCheckReverseOtpStatus()
      this.scrollToTop()
    },
    async checkReverseOtpStatus(isUserRequest = false) {
      if (!this.validateRecaptchaV2.PHONE_VERIFICATION_STATUS()) return
      try {
        const recaptchaTokenV3 = await this.recaptchaV3Exec.PHONE_VERIFICATION_STATUS()
        const recaptchaTokenV2 = this.recaptchaTokenV2.PHONE_VERIFICATION_STATUS
        let data = await preRegisteredService.getReverseOtpStatus({
          phone: this.form.phone,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }, false, true)
        if (data) {
          if (data) {
            this.isReversedOtpVerified = true
            this.stopCheckReverseOtpStatus();
            await this.getPreRegistered()
          } else {
            if (isUserRequest) {
              //Notify to user
            }
          }
        }
      } catch (ex) {
        if (isUserRequest) {
          if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.PHONE_VERIFICATION_STATUS = true
          } else {
            notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
          }
        }
      } finally {
        this.resetRecaptchaV2.PHONE_VERIFICATION_STATUS()
      }
    },
    async getPreRegistered() {
      const res = await preRegisteredService.getRegister({
        uuid: this.preRegistered?.uuid,
        device_id: getDeviceUUID()
      })

      if (res && res.data) {
        const resData = {
          preRegistered: res.data,
          otp_method: res.otp_method,
          message_content: res?.message_content,
          message_to: res?.message_to
        }
        this.$emit('next-step', resData)
      }
    },
    displayOtpVerification() {
      this.resendOtpTimer = DEFAULT_RESEND_OTP_TIME_IN_SECONDS
      this.getOtpStatusIntervalId = setInterval(this.getOtpStatus, 5000)
      if (this.$refs.title) {
        this.$refs.title.scrollIntoView({ behavior: "smooth" })
      }
    },
    startCheckReverseOtpStatus () {
      this.stopCheckReverseOtpStatus()
      this.checkReverseOtpStatusInterval = setInterval(this.checkReverseOtpStatus, 6000)
    },
    stopCheckReverseOtpStatus () {
      if (this.checkReverseOtpStatusInterval) {
        clearInterval(this.checkReverseOtpStatusInterval)
      }
    },
    async getOtpStatus() {
      if (this.otpStatus !== OTP_STATUS.DELIVERED && this.otpStatus !== OTP_STATUS.ERROR) {
        let data = await authService.getOtpStatus({
          email: this.preRegistered.email,
          phone: this.preRegistered.phone,
        })
        if (data && data.status) {
          this.otpStatus = data.status
        }
      } else {
        this.stopGetOtpStatusInterval()
      }
    },
    stopGetOtpStatusInterval() {
      if (this.getOtpStatusIntervalId) {
        clearInterval(this.getOtpStatusIntervalId)
      }
    },
    onRequestInputOtpClicked() {
      this.requestInputOtpClickCount++;
      if (this.requestInputOtpClickCount === 12) {
        this.requestInputOtpMethod();
        this.requestInputOtpClickCount = 0;
      }
    },
    async requestInputOtpMethod() {
      if (!this.validateRecaptchaV2.REQUEST_INPUT_OTP_METHOD()) return
      try {
        const recaptchaTokenV3 = await this.recaptchaV3Exec.REQUEST_INPUT_OTP_METHOD()
        const recaptchaTokenV2 = this.recaptchaTokenV2.REQUEST_INPUT_OTP_METHOD
        let data = await preRegisteredService.requestInputOtpMethod({
          phone: this.form.phone,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }, false, true)
        if (data) {
          this.handleOtpResponse(data)
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.REQUEST_INPUT_OTP_METHOD = true
        } else {
          notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.REQUEST_INPUT_OTP_METHOD()
      }
    },
    handleOtpResponse (data) {
      if (data) {
        const resData = {
          preRegistered: data.data,
          otp_method: data.otp_method,
          message_content: data?.message_content,
          message_to: data?.message_to
        }
        if ((data.data && data.data.status === PRE_REGISTERED_STATUSES.VERIFY_OTP) || data.message) {
          this.$emit('callback-data', resData)
          this.displayOtpVerification()
        } else {
          this.$emit('callback-data', resData)
        }

        if (data.otp_method) {
          this.oldPhone = this.form.phone;
          this.resendOtpTimer = store.state.configs?.otp_retry_time != null
            ? store.state.configs.otp_retry_time * 60 : DEFAULT_RESEND_OTP_TIME_IN_SECONDS
        }
      }
    },
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      })
    },
  },
  computed: {
    disableEmailInput() {
      return !!(this.form && (this.form.email_google || this.form.email_fb || this.form.email_apple));

    },
    preRegistered() {
      return this.$store.getters.preRegistered
    },
    preRegisteredOtpMethod() {
      return this.$store.getters.preRegisteredOtpMethod?.otp_method || OTP_METHOD.INPUT_OTP
    },
    isInputOtp() {
      return this.preRegisteredOtpMethod === OTP_METHOD.INPUT_OTP
    },
    isReverseOtp() {
      return this.preRegisteredOtpMethod === OTP_METHOD.REVERSE_OTP
    },
    isVerifyOtpStatus() {
      return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.VERIFY_OTP
    },
    reverseOtpMessageTo() {
      if (this.isReverseOtp) {
        return this.$store.getters.preRegisteredOtpMethod?.message_to || ""
      }
      return ""
    },
    reverseOtpMessageContent() {
      if (this.isReverseOtp) {
        return this.$store.getters.preRegisteredOtpMethod?.message_content || ""
      }
      return ""
    },
    isSendOtpUsingRadist() {
      if (store.state.configs && store.state.configs.otp_type) {
        return store.state.configs.otp_type === OTP_TYPE.RADIST || store.state.configs.otp_type === OTP_TYPE.TWILIO
      }
      return false
    },
    otpStatusText() {
      if ([OTP_STATUS.DELIVERED, OTP_STATUS.READ, OTP_STATUS.SENT].includes(this.otpStatus)) {
        return "Sent"
      }
      if ([OTP_STATUS.ERROR, OTP_STATUS.FAILED, OTP_STATUS.UNDELIVERED].includes(this.otpStatus)) {
        return "Error"
      }
      return 'Queued'
    },
    otpStatusClass() {
      const isSent = [OTP_STATUS.DELIVERED, OTP_STATUS.READ, OTP_STATUS.SENT].includes(this.otpStatus)
      const isError = [OTP_STATUS.ERROR, OTP_STATUS.FAILED, OTP_STATUS.UNDELIVERED].includes(this.otpStatus)
      return {
        'otp-status-queued': !isSent && !isError,
        'otp-status-sent': isSent,
        "otp-status-error": isError,
      };
    },
    otpResendClass() {
      return {
        "hyperlink": this.resendOtpTimer === 0,
        "hyperlink-disabled": this.resendOtpTimer > 0,
      }
    },
    resendOtpText () {
      if (this.resendOtpTimer > 0) {
        const minutes = Math.floor(this.resendOtpTimer / 60).toString().padStart(2, '0');
        const seconds = (this.resendOtpTimer % 60).toString().padStart(2, '0');
        return this.$t("AUTH.OTP_RESEND_AFTER") + " " + `${minutes}:${seconds}`;
      }
      return this.$t("AUTH.OTP_RESEND")
    },
    needHelpText () {
      let whatsapp = externalSites.WHATSAPP_SUPPORTS['id']
      if (this.$i18n.locale === FOREIGNER.LOCALE) {
        whatsapp = externalSites.WHATSAPP_SUPPORTS['en-UK']
      }
      return `${this.$t('AUTH.NEED_HELP_HERE', { whatsapp })}`
    },
  },
  watch: {
    formData: {
      handler(newVal) {
        if (newVal) {
          this.form = { ...this.form, ...newVal }
        }
      },
      immediate: true,
    },
    preRegistered: {
      handler(newVal) {
        if (newVal) {
          if (newVal.email) {
            this.form.email = newVal.email
          }
          if (newVal.phone) {
            this.vuePhone.phone = newVal.phone
            this.form.phone = newVal.phone
          }
        }
      },
      immediate: true
    },
    isReverseOtp: {
      handler(val) {
        if (val) {
          this.isReverseOtpStep1 = true
        }
      },
      immediate: true
    },
    resendOtpTimer(value) {
      if (value > 0) {
        setTimeout(() => { this.resendOtpTimer-- }, 1000)
      }
    },
    '$i18n.locale'(newVal, oldVal) {
      setLocale(newVal)
    },
  }
}
</script>

<style lang="scss">
.cls-register-form-wrapper{
  .cls-otp-form-content{
    //border: 1px solid #DDE2E5;
    .cls-otp-form{
      .cls-enter-otp-text{
        font-weight: 500;
        color: #6D6D6D;
        line-height: 19.6px;
      }
      .cls-phone-number-line{
        display: flex;
        align-items: center;
        justify-content: center;
        .cls-phone-number{
          color: #6D6D6D;
          font-weight: 700;
          line-height: 19.6px;
          margin-right: 5px;
        }
        .hyperlink{
          font-weight: 500;
          line-height: 19.6px;
          color: #1D3BFF !important;
          text-decoration-color: #1D3BFF !important;
          &:hover{
            opacity: .8;
          }
        }
      }
      .otp-notice-background{
        padding: 40px 55px 30px 120px;
      }
      .cls-otp-notice-text{
        color: #6D6D6D;
        font-weight: 500;
      }
      .otp-input{
        color: #6D6D6D;
        font-size: 32px;
        @media(max-width: 991px) {
          font-size: 18px;
        }
      }
    }
  }

  .cls-unique-form-content{
    .cls-otp-notice-block{
      .cls-otp-notice-input{
        font-weight: 500;
        color: #1E90FF;
      }
    }
  }

  .capitalize {
      text-transform: capitalize;
  }

  .form {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 28px;
    padding: 30px 30px !important;
  }

  .invalid {
    width: 100%;
    margin-top: .25rem;
    margin-bottom: 0;
    font-size: 80%;
    color: #dc3545
  }

  .prompt {
    margin-top: 10px;
    font-size: 14px;
    color: #dc3545;
  }

  .suggestion {
    color: #1E90FF;
    text-decoration: underline;
    cursor: pointer;
  }

  .name-note {
    font-size: 14px;
    color: rgb(255, 166, 0);
  }

  .reverse-otp-form {
    width: 100%;
    max-width: 644px;
  }

  .reverse-otp-warning {
    background-color: #FFEFB9;
    //border: 1px solid #ffd680;
    border-radius: 8px;
  }

  .reverse-otp-warning a {
    color: #1E90FF;
    text-decoration: underline;
  }
}
.cls-register-form-wrapper.reverse-otp {
  border: none !important;
  max-width: 628px !important;
}
</style>