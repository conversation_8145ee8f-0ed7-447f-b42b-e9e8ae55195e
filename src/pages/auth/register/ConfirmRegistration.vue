<template>
  <div>
    <div v-if="!isParentalKyc" class="cls-register-form-wrapper cls-register-account-confirmation-wrapper">
      <div class="cls-register-form-content">
        <h3 class="font-20 cls-title-step mt-3 mb-2">
          {{ $t("AUTH.ACCOUNT_CONFIRMATION") }}
        </h3>
        <p class="font-16 text-center mb-3">
          {{ $t("AUTH.ACCOUNT_CONFIRMATION_WARNING_TEXT") }}
        </p>
        <b-row align-h="center" class="mt-4">
          <Form ref="observer" class="form col-md-12" v-slot="{ errors, handleSubmit }">
            <b-form ref="form" @submit.prevent="handleSubmit(confirmRegister)" class="cls-custom-form-group-field extend-invalid">
              <div class="cls-account-confirm-detail mt-2 mb-4">
                <ul class="cls-account-detail-list">
                  <li>
                    <label>
                      {{ $t("AUTH.EMAIL") }}
                    </label>
                    <p class="cls-text">
                      {{ preRegistered?.email }}
                    </p>
                  </li>
                  <li>
                    <label>
                      {{ $t("AUTH.PHONE") }}
                    </label>
                    <p class="cls-text">
                      {{ preRegistered?.phone }}
                    </p>
                  </li>
                  <li>
                    <label>
                      {{ $t("AUTH.FULL_NAME") }}
                    </label>
                    <p class="cls-text">
                      {{ preRegistered?.name }}
                    </p>
                  </li>
                  <li>
                    <label>
                      {{ $t("AUTH.DATE_OF_BIRTH") }}
                    </label>
                    <p class="cls-text">
                      {{ getDataDOB }}
                    </p>
                  </li>
                  <li>
                    <label>
                      {{ $t("AUTH.GENDER") }}
                    </label>
                    <p class="cls-text">
                      {{ getDataGender }}
                    </p>
                  </li>
                  <li>
                    <label>
                      {{ $t("AUTH.MARITAL_STATUS") }}
                    </label>
                    <p class="cls-text">
                      {{ getDataMaritalStatus }}
                    </p>
                  </li>
                  <li>
                    <label>
                      {{ $t("AUTH.DISABILITY_QUESTION") }}
                    </label>
                    <p class="cls-text">
                      {{ getDataIsDisability }}
                    </p>
                  </li>
                  <li>
                    <label>
                      {{ $t("AUTH.REFERRAL_CODE") }}
                    </label>
                    <p class="cls-text">
                      {{ preRegistered?.referral_code }}
                    </p>
                  </li>
                  <li>
                    <label>
                      {{ $t("AUTH.MOTHER_MAIDEN_NAME") }}
                    </label>
                    <p class="cls-text">
                      {{ preRegistered?.mother_maiden_name }}
                    </p>
                  </li>
                </ul>
              </div>
              <Field :name="$t('AUTH.I_ACCEPT_TERMS_AND_PRIVACY')" :rules="{ required: { allowFalse: false } }"
                v-slot="{ valid, errors, field, meta, handleChange }" :model-value="form.accept_status"
                @update:modelValue="form.accept_status = $event">
                <div class="d-flex flex-row align-items-center">
                  <b-form-checkbox @change="handleChange" class="pt-1" name="accept_status" id="AUTH_I_ACCEPT_TERMS_AND_PRIVACY" 
                    style="font-weight: 400; font-size: 16px; z-index: 0" :state="getValidationState(meta)">
                  </b-form-checkbox>
                  <label for="AUTH_I_ACCEPT_TERMS_AND_PRIVACY" :style="getTCStyle(meta)" class="mb-0">{{
                    $t('AUTH.I_ACCEPT_THE') }} <span @click="openTC" class="font-bold" style="cursor: pointer;">{{
                      $t('FOOTER.TERMS_AND_CONDITIONS') }}</span> & <span @click="openPrivacyPolicy" class="font-bold"
                      style="cursor: pointer;">{{ $t('FOOTER.PRIVACY_POLICY')
                      }}</span></label>
                </div>
              </Field>
              <!-- Recaptcha V2 checkbox fallback -->
              <div v-if="showRecaptchaV2.CONFIRM_REGISTER" :ref="recaptchaV2Checkbox.CONFIRM_REGISTER" class="d-flex justify-content-center mt-2"></div>
              <b-row align-h="center" class="mt-4">
                <div class="cls-action-btn d-flex align-items-center justify-content-center mt-4">
                  <b-button class="btn-main px-4 white-normal" @click="$emit('go-back')" variant="none">
                    {{ $t('AUTH.BACK') }}
                  </b-button>
                  <b-button class="btn-main px-4" type="submit" variant="none">
                    {{ $t('AUTH.SUBMIT') }}
                  </b-button>
                </div>
              </b-row>
            </b-form>
          </Form>
        </b-row>
      </div>
    </div>

    <div v-if="isWaitingParentKycStatus" class="cls-register-form-wrapper cls-register-account-confirmation-wrapper cls-parental-kyc-wrapper">
      <div class="cls-register-form-content">
        <b-row align-h="center" class="cls-parent-kyc-message p-0 m-0">
          <img width="450" class="cls-image" src="@/assets/img/parental-kyc/pre-registered-parental-kyc.png" alt=""/>
          <div class="text-center cls-message-content mt-4 p-3">
            <p class="cls-message pl-2 pr-2" v-html="$t('AUTH.PARENT_KYC_MESSAGE', { whatsapp: waSupport })">
            </p>
            <b-row align-h="center" class="mt-4 p-0 m-0">
              <div class="w-100">
                <b-form ref="form" class="cls-custom-form-group-field extend-invalid">
                  <div class="mb-2 ml-2 mr-2">
                    <Field v-if="showConfirmContacted" :name="$t('AUTH.I_ACCEPT_TERMS_AND_PRIVACY')" :rules="{ required: { allowFalse: false } }"
                           v-slot="{ valid, errors, field, meta, handleChange }" :model-value="formContinue.contact_status"
                           @update:modelValue="formContinue.contact_status = $event">
                      <div class="d-flex flex-row align-items-center">
                        <b-form-checkbox @change="handleChange" name="contact_status" id="CONTACTED_PARENTAL_KYC"
                                         style="font-weight: 400; font-size: 14px; z-index: 0" :state="getValidationState(meta)">
                        </b-form-checkbox>
                        <label for="CONTACTED_PARENTAL_KYC" :style="getTCStyle(meta)" class="font-14 mb-0 cls-contacted-kyc-confirm">
                          {{ $t('AUTH.CONTACTED_PARENTAL_KYC') }}
                        </label>
                      </div>
                    </Field>
                  </div>
                  <div class="cls-action-btn d-flex align-items-center justify-content-center w-100">
                    <a v-if="!showConfirmContacted" :href="waSupport" target="_blank" class="btn-main py-2 w-100" @click="contactUsToContinue()">
                      {{ $t('AUTH.CONTACT_US') }}
                    </a>
                    <b-button v-if="showConfirmContacted" :disabled="!formContinue.contact_status" class="btn-main m-0 w-100" @click="goNextToContinue()" variant="none">
                      {{ $t('AUTH.CONTINUE') }}
                    </b-button>
                  </div>
                </b-form>
              </div>
            </b-row>
          </div>
        </b-row>
      </div>
    </div>

    <div v-if="isProcessedKyc" class="cls-register-form-wrapper cls-register-account-confirmation-wrapper cls-parental-kyc-wrapper">
      <div class="cls-register-form-content">
        <b-row align-h="center" class="cls-parent-kyc-message p-0 m-0">
          <ParentalKycStatus @on-refresh="handleRefresh" @go-next="goNextToContinue" ></ParentalKycStatus>
        </b-row>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment"
import { useRecaptcha } from "@/composables/useRecaptcha";
import { configure, defineRule, Field, Form } from "vee-validate"
import { min, required } from "@vee-validate/rules"
import { localize } from "@vee-validate/i18n"
import { getDeviceUUID, getErrorMessage, notify, } from "@/helpers/common"
import {MARITAL_STATUS, PRE_REGISTERED_STATUSES} from "@/constants/constants"
import { gtmTrackEvent } from "@/helpers/gtm"
import { GTM_EVENT_NAMES } from "@/constants/gtm"
import { FIREBASE_EVENTS, firebaseLogEvent } from "@/helpers/firebase"
import { ERROR_CODE } from "@/constants/constants";
import messErrors from "@/constants/errors";
import externalSites from '@/constants/externalSites'
import ParentalKycStatus from "./ParentalKycStatus.vue"
import preRegisteredService from "@/services/pre_registered.service"

defineRule("min", min)
defineRule("required", required)

configure({
  generateMessage: localize({
    en: {
      messages: {
        required: 'The {field} is required',
      },
    },
    id: {
      messages: {
        required: 'Kolom {field} wajib diisi',
      },
    },
  }),
})

export default {
  components: {
    Field,
    Form,
    ParentalKycStatus,
  },
  emits: ['next-step', 'go-back', 'go-next', 'on-refresh'],
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ CONFIRM_REGISTER: 'confirmRegister' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      title: "Register",
      form: {
        platform: "web",
        device_id: getDeviceUUID(),
        accept_status: false,
      },
      showConfirmContacted: false,
      formContinue: {
        contact_status: false,
      },
      waSupport: '',
    }
  },
  mounted() {
    const prefilledMessage = this.$t("AUTH.CONTACT_US_PREFILLED_MESSAGE")
    this.waSupport = `${externalSites.WHATSAPP_SUPPORTS[this.$i18n.locale]}?text=${encodeURIComponent(prefilledMessage)}`
  },
  methods: {
    getValidationState({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    async confirmRegister() {
      if (!this.validateRecaptchaV2.CONFIRM_REGISTER()) return
      let action = "confirmRegister"
      try {
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_REGISTER,
          action: action,
        })
        const recaptchaTokenV3 = await this.recaptchaV3Exec.CONFIRM_REGISTER()
        const recaptchaTokenV2 = this.recaptchaTokenV2.CONFIRM_REGISTER
        const formData = {
          ...this.form,
          uuid: this.preRegistered.uuid,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }
        const data = await preRegisteredService.confirmRegister(formData, false, true)
        if (data) {
          let resData = data
          resData.preRegistered = data.data || null
          this.$emit('callback-data', {
            ...resData
          })
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.CONFIRM_REGISTER = true
        } else {
          const errorMessage = getErrorMessage(ex) || messErrors.INTERNAL;
          firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
            uuid: this.preRegistered.uuid,
            action: action,
            error: errorMessage
          });
          notify({ text: errorMessage, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.CONFIRM_REGISTER()
      }
    },

    async handleRefresh() {
      this.$emit('on-refresh')
    },

    async goNextToContinue() {
      this.$emit('go-next')
    },

    contactUsToContinue() {
      this.showConfirmContacted = true
    },
    
    getTCStyle(meta) {
      const state = this.getValidationState(meta)
      return { color: state != null ? state ? '#28a745' : '#dc3545' : '#333333' }
    },
    openTC() {
      const url = this.$router.resolve({ name: "termsAndConditions" }).href
      window.open(url, "_blank")
    },
    openPrivacyPolicy() {
      const url = this.$router.resolve({ name: "privacyPolicy" }).href
      window.open(url, "_blank")
    },
  },
  computed: {
    preRegistered() {
      return this.$store.getters.preRegistered
    },
    isParentalKyc() {
      const parentalKycStatuses = [
        PRE_REGISTERED_STATUSES.PARENTAL_KYC,
        PRE_REGISTERED_STATUSES.PARENTAL_KYC_FAILED,
        PRE_REGISTERED_STATUSES.PARENTAL_KYC_REVIEWED,
        PRE_REGISTERED_STATUSES.PARENTAL_KYC_SUCCESS
      ]

      return this.preRegistered && parentalKycStatuses.includes(this.preRegistered.status)
    },
    isParentalKycReviewed() {
      return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC_REVIEWED
    },
    isParentalKycFailed() {
      return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC_FAILED
    },
    isParentalKycSuccess() {
      return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC_SUCCESS
    },
    isWaitingParentKycStatus() {
      return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC
    },
    isProcessedKyc() {
      return this.isParentalKycReviewed || this.isParentalKycSuccess || this.isParentalKycFailed
    },
    getDataDOB() {
      return moment(this.preRegistered?.dob).format('DD/MM/YYYY')
    },
    getDataGender() {
      const gender = this.preRegistered?.gender.toString()
      if (gender === '1') {
        return this.$t('AUTH.MALE')
      }
      return this.$t('AUTH.FEMALE')
    },
    getDataMaritalStatus() {
      if (this.preRegistered?.marital_status === MARITAL_STATUS.SINGLE) {
        return this.$t('AUTH.NOT_MARRIED')
      } else if (this.preRegistered?.marital_status === MARITAL_STATUS.MARRIED) {
        return this.$t('AUTH.MARRIED')
      } else if (this.preRegistered?.marital_status === MARITAL_STATUS.DIVORCED) {
        return this.$t('AUTH.DIVORCED')
      } else if (this.preRegistered?.marital_status === MARITAL_STATUS.WIDOWED) {
        return this.$t('AUTH.WIDOWED')
      }
      return ''
    },
    getDataIsDisability() {
      if (this.preRegistered?.is_disability === null) {
        return ''
      }
      if (this.preRegistered?.is_disability) {
        return this.$t('AUTH.HAVE_DISABILITY')
      }
      return this.$t('AUTH.NOT_HAVE_DISABILITY')
    },
  },
}
</script>

<style lang="scss">
.cls-register-account-confirmation-wrapper{
  .cls-account-confirm-detail{
    .cls-account-detail-list{
      list-style-type: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      li{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        margin-bottom: 8px;
        label{
          font-size: 15px;
          font-weight: 500;
          color: #616161;
          line-height: 24px;
          margin-bottom: 0;
        }
        p.cls-text{
          font-size: 14px;
          font-weight: 500;
          color: #A2A2A2;
          line-height: 24px;
        }
      }
    }
  }
  &.cls-parental-kyc-wrapper{
    &.cls-register-form-wrapper{
      border: 0;
      max-width: 500px;
      padding: 25px;
      @media screen and (max-width: 768px) {
        padding: 20px;
      }
      .cls-register-form-content{
        .cls-parent-kyc-message{
          .cls-image{
            border-radius: 22px;
            width: 100%;
          }
          .cls-message-content{
            box-shadow: 0 4px 4px 0 #00000040;
            border-radius: 0 0 22px 22px;
            .cls-message{
              color: #333;
              font-size: 18px;
            }
          }
          .cls-contacted-kyc-confirm{
            color: #333333;
            text-align: left;
            line-height: 20px;
          }
          .cls-action-btn{
            @media screen and (max-width: 768px) {
              margin-left: 0;
              margin-right: 0;
              justify-content: center !important;
            }
          }
        }
      }
    }
    
  }
}
</style>