<template>
  <b-container class="register-steps-container pl-0 pr-0">
    <div class="cls-step-progress-text d-flex align-items-center justify-content-end mb-3">
      <p class="font-12 cls-text">
        {{ $t("AUTH.REGISTER_STEP_PROGRESS", {currentStep, totalStep}) }}
      </p>
    </div>
    <div class="cls-step-progress-wrapper">
      <div class="cls-step-background"></div>
      <div class="cls-step-progress">
        <div class="step" v-for="step in totalStep" :key="`step-${step}`" :class="getActiveClass(step)"></div>
      </div>
    </div>
  </b-container>
</template>

<script>

export default {
    props: {
      totalStep: {
        type: Number,
        default: 3,
      },
      currentStep: {
        type: Number,
        default: 1,
      },
    },
    methods: {
      getActiveClass(step) {
        let cls = ""
        if (step <= this.currentStep) {
          cls = `${cls} active`
          if (step === this.currentStep) {
            cls = `${cls} last-active`
          }
        }
        return cls
      }
    },
    computed: {

    }
}
</script>

<style lang="scss">
.register-steps-container{
    .cls-step-progress-text{
      .cls-text{
        color: #A2A2A2;
        font-weight: 400;
        line-height: 100%;
      }

    }
    .cls-step-progress-wrapper{
      height: 4px;
      border-radius: 20px;
      position: relative;
      overflow: hidden;
      .cls-step-background{
        position: absolute;
        height: 2px;
        top: 1px;
        left: 0;
        width: 100%;
        background-color: #E2E6F9;
      }
      .cls-step-progress{
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .step{
          flex: 1;
          height: 4px;
          background-color: transparent;
          &.active{
            background-color: var(--layout2-primary-color);
          }
          &.last-active{
            border-radius: 0 20px 20px 0;
          }
        }
      }
    }
  }
</style>