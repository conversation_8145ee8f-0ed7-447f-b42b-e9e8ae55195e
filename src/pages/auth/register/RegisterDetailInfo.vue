<template>
  <div class="cls-register-form-wrapper">
    <div class="cls-register-form-content">
      <h3 class="font-20 cls-title-step mt-3 mb-2">
        {{ $t("AUTH.TITLE_COMPLETE_ACCOUNT_SETUP") }}
      </h3>
      <RegisterStep :currentStep="2" :totalStep="totalStep"/>
      <b-row align-h="center" class="mt-4">
        <Form ref="observer" class="form col-md-12" v-slot="{ errors, handleSubmit }">
          <b-form ref="form" @submit.prevent="handleSubmit(updateInfo)" class="cls-custom-form-group-field extend-invalid">
            <div class="form-group-parent">
              <Field :key="$i18n.locale" :model-value="form.name" @update:modelValue="updateField('name', $event)" :name="$t('AUTH.FULL_NAME_VALID')"
                     :rules="{ required: true, min: 2, legal_name: [{ ...form }] }" v-slot="validationContext">
                <b-form-group :label="$t('AUTH.FULL_NAME')" label-for="AUTH_FULL_NAME" label-class="custom-label" class="custom-form-group">
                  <b-form-input id="AUTH_FULL_NAME" v-bind="validationContext.field" placeholder="John Doe" aria-describedby="input-name-feedback"
                                :state="getValidationState(validationContext.meta)"></b-form-input>
                </b-form-group>
                <b-form-invalid-feedback id="input-name-feedback">
                  {{ errors[$t("AUTH.FULL_NAME_VALID")] }}
                </b-form-invalid-feedback>
                <p class="name-note">{{ $t("AUTH.WARNING_NAME_TEXT") }}</p>
              </Field>
            </div>

            <div class="form-group-parent">
              <Field :key="$i18n.locale" :model-value="form.dob" @update:modelValue="updateField('dob', $event)"
                     :name="$t('AUTH.DATE_OF_BIRTH')" :rules="{ required: true } "
                     v-slot="validationContext">
                <b-form-group :label="$t('AUTH.DATE_OF_BIRTH')" label-for="AUTH_DATE_OF_BIRTH" label-class="custom-label" class="custom-form-group mt-3">
                  <VueDatePicker id="AUTH_DATE_OF_BIRTH" v-model="form.dob" :max-date="today" :enable-time-picker="false" format="dd/MM/yyyy"
                                 :state="getValidationState(validationContext.meta)">
                    <template #input-icon>
                        <span class="p-2">
                          <img width="18" class="input-slot-image" src="@/assets/img/icons/calendar.svg" alt=""/>
                        </span>
                    </template>
                  </VueDatePicker>
                </b-form-group>
                <p class="invalid mt-1">
                  {{ getValidationState(validationContext.meta) === false ? $t("VALIDATION.THE_DOB_IS_REQUIRED") : '' }}
                </p>
              </Field>
            </div>

            <div class="form-group-parent">
              <Field
                :key="$i18n.locale"
                :model-value="form.gender"
                @update:modelValue="updateField('gender', $event)"
                :name="$t('AUTH.GENDER')"
                :rules="{ required: true }" v-slot="validationContext"
              >
                <b-form-group
                  :label="$t('AUTH.GENDER')"
                  label-for="AUTH_GENDER"
                  label-class="custom-label"
                  class="mt-2"
                >
                  <b-form-input hidden :model-value="form.gender" id="AUTH_GENDER" :state="getValidationState(validationContext.meta)"></b-form-input>
                  <b-button-group class="cls-custom-button-group" :state="getValidationState(validationContext.meta)">
                    <b-button
                      class="mr-2"
                      :class="{ active: form.gender === '0' }"
                      @click="validationContext.field.onChange('0')"
                    >
                      {{ $t('AUTH.FEMALE') }}
                    </b-button>
                    <b-button
                      :class="{ active: form.gender === '1' }"
                      @click="validationContext.field.onChange('1')"
                    >
                      {{ $t('AUTH.MALE') }}
                    </b-button>
                  </b-button-group>
                </b-form-group>
                <b-form-invalid-feedback>
                  {{ validationContext.errors[0] }}
                </b-form-invalid-feedback>
              </Field>
            </div>

            <div v-if="isShowMarriedStatus" class="form-group-parent">
              <Field :key="$i18n.locale" :model-value="form.marital_status"
                     @update:modelValue="updateField('marital_status', $event)"
                     :name="$t('AUTH.MARITAL_STATUS')"
                     :rules="{ required: true }" v-slot="validationContext">
                <b-form-group :label="$t('AUTH.MARITAL_STATUS')" label-for="AUTH_MARITAL_STATUS" label-class="custom-label" class="custom-form-group">
                  <b-form-select id="AUTH_MARITAL_STATUS" :value="form.marital_status" v-bind="validationContext.field"
                                 aria-describedby="input-marital-status-feedback"
                                 :state="getValidationState(validationContext.meta)">
                    <option :value="null" disabled>{{ $t('AUTH.MARITAL_STATUS') }}
                    </option>
                    <option v-for="maritalStatus in maritalStatusOptions" :value="maritalStatus.value" :key="maritalStatus.value">
                      {{ maritalStatus.text }}
                    </option>
                  </b-form-select>
                </b-form-group>
                <b-form-invalid-feedback id="input-marital-status-feedback">
                  {{ validationContext.errors[0] }}
                </b-form-invalid-feedback>
                <p v-if="isShowMaritalMessage" class="name-note">{{ $t("AUTH.UNDER_18_AND_NOT_MARRIED") }}</p>
              </Field>
            </div>

            <div class="form-group-parent">
              <Field :key="$i18n.locale" :model-value="form.is_disability"
                     @update:modelValue="updateField('is_disability', $event)"
                     :name="$t('AUTH.DISABILITY_QUESTION')"
                     :rules="{ required: true }" v-slot="validationContext">
                <b-form-group :label="$t('AUTH.DISABILITY_QUESTION')" label-for="AUTH_DISABILITY_QUESTION" label-class="custom-label" class="custom-form-group">
                  <b-form-select id="AUTH_DISABILITY_QUESTION" :value="form.is_disability" v-bind="validationContext.field"
                                 aria-describedby="input-is_disability-feedback"
                                 :state="getValidationState(validationContext.meta)">
                    <option :value="null" disabled>{{ $t('AUTH.DISABILITY_QUESTION') }}
                    </option>
                    <option v-for="disability in disabilityOptions" :value="disability.value" :key="disability.value">
                      {{ disability.text }}
                    </option>
                  </b-form-select>
                </b-form-group>
                <b-form-invalid-feedback>
                  {{ validationContext.errors[0] }}
                </b-form-invalid-feedback>
              </Field>
            </div>

            <div class="form-group-parent">
              <b-form-group :label="$t('REFERRAL.REFERRAL_CODE')" label-for="AUTH_REFERRAL_CODE" label-class="custom-label" class="custom-form-group">
                <b-form-input id="AUTH_REFERRAL_CODE" name="referral_code" placeholder="123456ABC" :disabled="disabledReferralInput"
                              v-model="form.referral_code"></b-form-input>
              </b-form-group>
            </div>

            <div class="form-group-parent">
              <Field :key="$i18n.locale" :model-value="form.mother_maiden_name" @update:modelValue="updateField('mother_maiden_name', $event)" :name="$t('AUTH.MOTHER_MAIDEN_NAME')"
                :rules="{ required: true }" v-slot="context">
                <b-form-group :label="$t('AUTH.MOTHER_MAIDEN_NAME')" label-for="AUTH_MOTHER_MAIDEN_NAME" label-class="custom-label" class="custom-form-group">
                  <b-form-input id="AUTH_MOTHER_MAIDEN_NAME" v-bind="context.field" placeholder="Siti" aria-describedby="input-name-feedback"
                    :state="getValidationState(context.meta)"></b-form-input>
                </b-form-group>
                <b-form-invalid-feedback>
                  {{ errors[$t("AUTH.MOTHER_MAIDEN_NAME")] }}
                </b-form-invalid-feedback>
              </Field>
            </div>
            <!-- Recaptcha V2 checkbox fallback -->
            <div v-if="showRecaptchaV2.UPDATE_ACCOUNT_INFO" :ref="recaptchaV2Checkbox.UPDATE_ACCOUNT_INFO" class="d-flex justify-content-center mt-2"></div>
            <b-row align-h="center" class="mt-4">
              <div class="cls-action-btn d-flex align-items-center justify-content-center mt-4">
                <b-button class="btn-main px-4 white-normal" @click="$emit('go-back')" variant="none">
                  {{ $t('AUTH.BACK') }}
                </b-button>
                <b-button class="btn-main px-4" type="submit" variant="none">
                  {{ $t('AUTH.CONTINUE') }}
                </b-button>
              </div>
            </b-row>
          </b-form>
        </Form>
      </b-row>
    </div>
  </div>
</template>

<script>
import moment from "moment"
import { useRecaptcha } from "@/composables/useRecaptcha";
import { configure, defineRule, Field, Form } from "vee-validate"
import { email, min, regex, required } from "@vee-validate/rules"
import { localize } from "@vee-validate/i18n"
import { convertStringBoolToBool, getCookie, getDeviceUUID, getErrorMessage, notify } from "@/helpers/common"
import { gtmTrackEvent } from "@/helpers/gtm"
import { MARITAL_STATUS, PRE_REGISTERED_STATUSES, REGEX } from "@/constants/constants"
import { GTM_EVENT_NAMES } from "@/constants/gtm"
import { FIREBASE_EVENTS, firebaseLogEvent } from "@/helpers/firebase"
import { ERROR_CODE } from "@/constants/constants";
import messErrors from "@/constants/errors";
import externalSites from '@/constants/externalSites'
import RegisterStep from "@/pages/auth/register/RegisterStep"
import preRegisteredService from "@/services/pre_registered.service"

defineRule("email", email)
defineRule("min", min)
defineRule("required", required)
defineRule('regex', regex)

defineRule("legal_name", (value, [otherValues]) => {
  // Check if the password contains a combination of uppercase, lowercase, numbers, and special characters
  return REGEX.LEGAL_NAME.test(value);
})

configure({
  generateMessage: localize({
    en: {
      messages: {
        required: 'The {field} is required',
        legal_name: 'Legal name only accepts alphabets, single quotes, commas, full stops, and single spaces.',
      },
    },
    id: {
      messages: {
        required: 'Kolom {field} wajib diisi',
        legal_name: 'Nama resmi hanya menerima alfabet, tanda kutip tunggal, koma, titik, dan satu spasi.',
      },
    },
  }),
})

export default {
  components: {
    Field,
    Form,
    RegisterStep,
  },
  props: {
    totalStep: {
      type: Number,
      default: 3,
    },
  },
  emits: ['next-step', 'go-back', 'callback-data'],
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ UPDATE_ACCOUNT_INFO: 'updateAccountInfo' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      title: "Register",
      form: {
        platform: "web",
        device_id: getDeviceUUID(),
        cookie_fbp: getCookie("_fbp"),
        name: null,
        dob: null,
        gender: null,
        marital_status: null,
        is_disability: null,
        referral_code: null,
        mother_maiden_name: null
      },
      isUnder18YearOld: null,
      referUser: "",
      refereeBonusPercent: null,
      disabilityOptions: [
        {
          value: 'true',
          text: this.$t('AUTH.HAVE_DISABILITY'),
        },
        {
          value: 'false',
          text: this.$t('AUTH.NOT_HAVE_DISABILITY')
        }
      ],
      maritalStatusOptions: [
        {
          value: MARITAL_STATUS.SINGLE,
          text: this.$t('AUTH.NOT_MARRIED')
        },
        {
          value: MARITAL_STATUS.MARRIED,
          text: this.$t('AUTH.MARRIED'),
        },
        {
          value: MARITAL_STATUS.DIVORCED,
          text: this.$t('AUTH.DIVORCED'),
        },
        {
          value: MARITAL_STATUS.WIDOWED,
          text: this.$t('AUTH.WIDOWED'),
        },
      ],
    }
  },
  methods: {
    getValidationState({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    updateField(field, value) {
      if (this.form[field] !== value) {
        this.form[field] = value;
      }
    },
    checkAge(dob) {
      if (!dob) {
        this.isUnder18YearOld = false
        return;
      }
      const today = new Date()
      const birthDate = new Date(dob)
      const age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      const dayDiff = today.getDate() - birthDate.getDate()

      this.isUnder18YearOld = (age < 18) || (age === 18 && (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)))
    },
    async updateInfo() {
      if (!this.validateRecaptchaV2.UPDATE_ACCOUNT_INFO()) return
      let action = "updateAccountInfo"
      try {
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_REGISTER,
          action: action,
        })
        const recaptchaTokenV3 = await this.recaptchaV3Exec.UPDATE_ACCOUNT_INFO()
        const recaptchaTokenV2 = this.recaptchaTokenV2.UPDATE_ACCOUNT_INFO
        const formData = {
          ...this.form,
          dob: moment(this.form.dob).format('DD/MM/YYYY'),
          is_married: this.form.is_married ? convertStringBoolToBool(this.form.is_married) : null,
          is_disability: convertStringBoolToBool(this.form.is_disability),
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }
        const res = await preRegisteredService.updateInfo(formData, false, true)
        if (res && res.data) {
          if (this.isUnder18YearOld && res.data.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC) {
            this.$emit('callback-data', {
              preRegistered: res.data
            })
          } else {
            this.$emit('next-step', {
              preRegistered: res.data
            })
          }
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.UPDATE_ACCOUNT_INFO = true
        } else {
          const errorMessage = getErrorMessage(ex) || messErrors.INTERNAL;
          firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
            uuid: this.form.uuid,
            action: action,
            error: errorMessage
          });
          notify({ text: errorMessage, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.UPDATE_ACCOUNT_INFO()
      }
    },
  },
  computed: {
    preRegistered() {
      return this.$store.getters.preRegistered
    },
    referralCode() {
      const savedReferralCode = localStorage.getItem('referral_code')
      const codeParam = this.$route.query.code
      if (codeParam && codeParam !== savedReferralCode) {
        localStorage.setItem('referral_code', codeParam)
      }
      return codeParam || savedReferralCode
    },
    disabledReferralInput() {
      return this.referUser != null && this.referUser !== ""
    },
    today() {
      return new Date()
    },
    isShowMarriedStatus() {
      return true //this.isUnder18YearOld
    },
    isShowMaritalMessage() {
      return this.isUnder18YearOld && this.form.marital_status === MARITAL_STATUS.SINGLE
    },
    waSupport() {
      return externalSites.WHATSAPP_SUPPORTS[this.$i18n.locale]
    }
  },
  watch: {
    referralCode: {
      handler(code) {
        this.form.referral_code = code
      },
      immediate: true
    },
    preRegistered: {
      handler(newVal) {
        if (newVal) {
          this.form.uuid = newVal.uuid || this.form.uuid
          this.form.name = newVal.name != null ? newVal.name.toString() : this.form.name
          this.form.dob = newVal.dob ? newVal.dob : this.form.dob
          this.form.gender = newVal.gender != null ? newVal.gender.toString() : this.form.gender
          this.form.is_disability = newVal.is_disability != null ? newVal.is_disability.toString() : this.form.is_disability
          this.form.marital_status = newVal.marital_status != null ? newVal.marital_status.toString() : this.form.marital_status
          this.form.referral_code = newVal.referral_code || this.form.referral_code
          this.form.mother_maiden_name = newVal.mother_maiden_name || this.form.mother_maiden_name
        }
      },
      immediate: true
    },
    'form.dob'(value) {
      this.checkAge(value)
    },
  }
}
</script>

<style lang="scss" scoped>
.cls-parent-kyc-message {
  .cls-image {
    @media(max-width: 991px) {
      width: 80%;
    }
  }

  .cls-message {
    line-height: 24px;
    color: #3E3E3E;
    font-weight: 500;
  }

  .cls-wa-support {
    color: #00918E;
    font-weight: 500;
    line-height: 24px;

    &:hover {
      text-decoration: underline;
    }
  }
}

.cls-custom-button-group {
  :deep(.btn:hover) {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    box-shadow: none !important;
  }

  :deep(.btn.active) {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    box-shadow: none !important;
  }

  :deep(.btn.active:hover) {
    background-color: var(--primary-darker-color) !important;
    border-color: var(--primary-darker-color) !important;
  }
}
</style>