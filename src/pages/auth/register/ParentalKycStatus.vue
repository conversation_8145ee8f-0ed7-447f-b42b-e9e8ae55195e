<template>
    <b-container class="kyc-status-container p-0 m-0">
      <div class="row justify-content-center p-0 m-0">
        <div class="col-12 p-0 m-0">
          <div
            class="d-flex flex-column align-items-start justify-content-start kyc-status-content"
          >
            <div class="cls-kyc-status-icon w-100">
              <img
                :src="require(`@/assets/img/parental-kyc/${kycImageByStatus}`)"
                alt=""
                class="icon-status w-100"
              />
            </div>
            <div class="cls-kyc-status w-100">
              <h3 class="cls-headline font-32 mb-3 mt-4" :class="getHeadlineClass()">
                {{ getHeadlineByStatus }}
              </h3>
              <div class="cls-verification-status d-flex flex-column align-items-center justify-content-start">
                <div class="cls-status-item w-100 step-1 d-flex flex-row align-items-center justify-content-start" :class="getStatusClassForStep(1)">
                  <div class="cls-status-step d-flex flex-column align-items-center justify-content-start" >
                    <div class="cls-line-timeline-status">
                      <p
                        class="cls-line-status hide-line"
                      >
                      </p>
                    </div>
                    <p class="cls-icon-status">
                      <img
                        width="24"
                        src="@/assets/img/icons/parental-kyc/kyc-process.svg"
                        alt=""
                      />
                    </p>
                    <div class="cls-line-timeline-status">
                      <p
                        class="cls-line-status"
                        :class="getStatusClassForStep(1)"
                      >
                      </p>
                    </div>
                  </div>
                  <div class="cls-status-step-text">
                    <p class="cls-step-text font-20">
                      {{$t("AUTH.PARENTAL_KYC.REVIEWED_TITLE")}}
                    </p>
                    <p v-if="isParentalKycReviewed"
                      class="cls-step-detail font-16"
                    >
                      {{$t("AUTH.PARENTAL_KYC.REVIEWED_TEXT")}}
                    </p>
                  </div>
                </div>
                <div class="cls-status-item w-100 step-2 d-flex flex-row align-items-center justify-content-start" :class="getStatusClassForStep(2)">
                  <div class="cls-status-step d-flex flex-column align-items-center justify-content-start">
                    <div class="cls-line-timeline-status">
                      <p
                        class="cls-line-status"
                        :class="getStatusClassForStep(2)"
                      >
                      </p>
                    </div>
                    <p class="cls-icon-status">
                      <img
                        v-if="isParentalKycReviewed || isParentalKycSuccess"
                        width="24"
                        height="24"
                        src="@/assets/img/icons/parental-kyc/kyc-success.svg"
                        alt=""
                      />
                      <img
                        v-if="isParentalKycFailed"
                        width="24"
                        height="24"
                        src="@/assets/img/icons/parental-kyc/kyc-failed.svg"
                        alt=""
                      />
                    </p>
                    <div class="cls-line-timeline-status">
                      <p
                        class="cls-line-status hide-line"
                      >
                      </p>
                    </div>
                  </div>
                  <div class="cls-status-step-text">
                    <p v-if="isParentalKycReviewed || isParentalKycSuccess" class="cls-step-text font-20">
                      {{$t("AUTH.PARENTAL_KYC.SUCCESS_TITLE")}}
                    </p>
                    <p
                      v-if="isParentalKycFailed"
                      class="cls-step-text font-20"
                    >
                      {{$t("AUTH.PARENTAL_KYC.FAILED_TITLE")}}
                    </p>
                    <p
                      v-if="isParentalKycFailed"
                      class="cls-step-detail"
                    >
                      {{ kycFailedText }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="cls-actions">
                <button
                  v-if="isParentalKycReviewed"
                  id="id_kyc_status_refresh_btn"
                  class="btn btn-secondary btn btn-none btn-main font-18 w-100 mt-3 mb-2"
                  @click="refreshKycStatus()"
                >
                  {{$t("AUTH.PARENTAL_KYC.REFRESH")}}
                </button>
                <button
                  v-if="isParentalKycSuccess"
                  id="id_kyc_status_done_btn"
                  class="btn btn-secondary btn btn-none btn-main font-18 w-100 mt-3 mb-2"
                  @click="goNext"
                >
                  {{ $t("AUTH.PARENTAL_KYC.CONTINUE") }}
                </button>
                <button
                  v-if="isParentalKycFailed"
                  id="id_kyc_status_done_btn"
                  class="btn btn-secondary btn btn-none btn-main font-18 w-100 mt-3 mb-2"
                  @click="goNext"
                >
                  {{ $t("AUTH.PARENTAL_KYC.DONE") }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </b-container>
  </template>
  
  <script>
  import { PRE_REGISTERED_STATUSES } from "@/constants/constants"
  
  export default {
    components: {
    },
    emits: ['go-next', 'on-refresh'],
    data() {
      return {
        statusClass: {
          inqueue: 'inqueue',
          reviewed: 'reviewed',
          success: 'success',
          failed: 'failed'
        },
      }
    },
  
    methods: {
      getHeadlineClass() {
        if (this.isParentalKycReviewed) {
          return this.statusClass.reviewed
        } else if (this.isParentalKycSuccess) {
          return this.statusClass.success
        } else if (this.isParentalKycFailed) {
          return this.statusClass.failed
        } else {
          return this.statusClass.reviewed
        }
      },
      getStatusClassForStep(step = 1) {
        if (step === 1) {
          if (this.isParentalKycReviewed) {
            return this.statusClass.reviewed
          }
          if (this.isParentalKycSuccess) {
            return this.statusClass.success
          }
          if (this.isParentalKycFailed) {
            return this.statusClass.success
          }
        }

        if (step === 2) {
          if (this.isParentalKycReviewed) {
            return this.statusClass.inqueue
          }
          if (this.isParentalKycSuccess) {
            return this.statusClass.success
          }
          if (this.isParentalKycFailed) {
            return this.statusClass.failed
          }
        }
        return this.statusClass.inqueue
      },
      async refreshKycStatus() {
        this.$emit('on-refresh')
      },
      async goNext() {
        this.$emit('go-next')
      },
    },
  
    computed: {
      preRegistered() {
        return this.$store.getters.preRegistered
      },
      isParentalKycReviewed() {
        return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC_REVIEWED
      },
      isParentalKycFailed() {
        return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC_FAILED
      },
      isParentalKycSuccess() {
        return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC_SUCCESS
      },
      kycImageByStatus() {
        if (this.isParentalKycReviewed) {
          return 'kyc-reviewed.png'
        } else if (this.isParentalKycSuccess) {
          return 'kyc-success.png'
        } else if (this.isParentalKycFailed) {
          return 'kyc-failed.png'
        } else {
          return 'pre-registered-parental-kyc.png'
        }
      },
      getHeadlineByStatus() {
        if (this.isParentalKycReviewed) {
          return this.$t("AUTH.PARENTAL_KYC.HEADLINE_REVIEWED")
        } else if (this.isParentalKycSuccess) {
          return this.$t("AUTH.PARENTAL_KYC.HEADLINE_SUCCESS")
        } else if (this.isParentalKycFailed) {
          return this.$t("AUTH.PARENTAL_KYC.HEADLINE_FAILED")
        } else {
          return this.$t("AUTH.PARENTAL_KYC.HEADLINE")
        }
      },
      kycFailedText() {
        const days = this.$store.state.configs ? this.$store.state.configs.pre_registered_users_kyc_failed_auto_delete_days : 180
        let value = ''
        if (days % 30 === 0) {
          const months = Math.floor(days / 30)
          value = `${months} ${months === 1 ? this.$t("common.MONTH") : this.$t("common.MONTHS")}`
        } else {
          value = `${days} ${days === 1 ? this.$t("common.DAY") : this.$t("common.DAYS")}`
        }
        return this.$t("AUTH.PARENTAL_KYC.FAILED_TEXT", { value })
      },
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .kyc-status-container {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    * {
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    }
    .kyc-status-content {
      @media (max-width: 991px) {
        flex-direction: column;
      }
      .cls-kyc-status-icon {
        width: 332px;
        text-align: center;
        .icon-status {
          width: 100%;
          height: auto;
        }
        @media (max-width: 991px) {
          width: 100%;
          margin-top: 0 !important;
          margin-right: 0 !important;
          .icon-status {
            width: 80% !important;
          }
        }
      }
      .cls-kyc-status {
        flex: 1;
        .cls-headline {
          font-weight: 700;
          line-height: 38px;
          letter-spacing: 0;
          text-align: center;
          color: #FF7A00;
          &.reviewed{
            color: #FF7A00;
          }
          &.success{
            color: #00B200;
          }
          &.failed{
            color: #FB234A;
          }
        }
        .cls-verification-status {
          .cls-status-item {
            border-radius: 8px;
            padding: 0 15px;
            &.reviewed{
              background-color: #FFF2E6;

            }
            &.failed{
              background-color: #FB234A1A;

            }
            &.success{
              &.step-2{
                background-color: #0068670F;
              }
            }
            .cls-status-step {
              margin-right: 20px;
              .cls-icon-status {
                width: 50px;
                height: 50px;
                background-color: #ff7c03;
                border-radius: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                img {
                  width: 24px;
                }
              }
              .cls-line-status {
                min-height: 35px;
                width: 2px;
                border-left: 2px solid #bababa;
                &.inqueue {
                  border-left: 2px solid #bababa;
                }
                &.reviewed {
                  border-left: 2px solid #ff7c03;
                }
                &.success {
                  border-left: 2px solid #006867;
                }
                &.failed {
                  border-left: 2px solid #fb234a;
                }
                &.hide-line{
                  border-left: 2px solid transparent;
                }
              }
            }
            .cls-status-step-text {
              padding: 8px 0;
              .cls-step-text {
                font-weight: 600;
                line-height: 28.06px;
                color: #ff7c03;
              }
              .cls-step-detail {
                font-weight: 382;
                line-height: 22.45px;
                color: #ff7c03;
                .cls-wa-support {
                  color: #006867;
                  text-decoration: underline;
                  &:hover {
                    opacity: 0.8;
                  }
                }
              }
            }
            &.inqueue {
              .cls-status-step {
                .cls-icon-status {
                  background-color: #bababa;
                }
              }
              .cls-status-step-text {
                .cls-step-text {
                  color: #bababa;
                }
                .cls-step-detail {
                  color: #bababa;
                }
              }
            }
            &.reviewed {
              .cls-status-step {
                .cls-icon-status {
                  background-color: #ff7c03;
                }
              }
              .cls-status-step-text {
                .cls-step-text {
                  color: #ff7c03;
                }
                .cls-step-detail {
                  color: #ff7c03;
                }
              }
            }
            &.success {
              .cls-status-step {
                .cls-icon-status {
                  background-color: #006867;
                }
              }
              .cls-status-step-text {
                .cls-step-text {
                  color: #006867;
                }
                .cls-step-detail {
                  color: #006867;
                }
              }
            }
            &.failed {
              .cls-status-step {
                .cls-icon-status {
                  background-color: #fb234a;
                }
              }
              .cls-status-step-text {
                .cls-step-text {
                  color: #fb234a;
                }
                .cls-step-detail {
                  color: #fb234a;
                }
              }
            }
          }
        }
      }
    }
  }
  </style>
  