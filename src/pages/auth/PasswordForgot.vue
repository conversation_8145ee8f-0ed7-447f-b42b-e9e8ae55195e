<template>
  <div class="forgot-password" style="background-color: var(--primary-background-color)">
    <b-container>
      <b-row class="text-center pt-4 pb-1" align-h="center">
        <b-col>
          <h2>{{ $t("AUTH.PASSWORD_FORGOT") }}</h2>
        </b-col>
      </b-row>
      <b-row align-h="center">
        <Form class="form col-md-6" ref="observer" v-slot="{ handleSubmit }">
          <b-form @submit.prevent="handleSubmit(onSubmit)">
            <h5 v-if="message">{{ message }}</h5>
            <Field
              v-if="!requestSent"
              :name="$t('AUTH.EMAIL')"
              :rules="{ required: true, email: true }"
              v-slot="validationContext"
              :model-value="form.email"  @update:modelValue="form.email = $event"
            >
              <b-form-group :label="$t('AUTH.EMAIL')">
                <b-form-input v-bind="validationContext.field"
                              placeholder="<EMAIL>"
                              type="email"
                              :state="getValidationState(validationContext.meta)"
                              aria-describedby="input-email-feedback"
                ></b-form-input>
                <b-form-invalid-feedback id="input-email-feedback">
                  {{ validationContext.errors[0] }}
                </b-form-invalid-feedback>
              </b-form-group>
            </Field>
            <!-- Recaptcha V2 checkbox fallback -->
            <b-row align-h="center">
              <b-col cols="auto">
                <div v-if="showRecaptchaV2.FORGOT_PASSWORD" :ref="recaptchaV2Checkbox.FORGOT_PASSWORD"></div>
              </b-col>
            </b-row>
            <b-row v-if="!requestSent" class="text-center pt-4" align-h="center">
              <b-button id="btn_ForgotPassword" class="btn-main pl-4 pr-4" type="submit" variant="none" :disabled="submitting">
                {{ $t("AUTH.PASSWORD_FORGOT_SEND_LINK") }}
              </b-button>
            </b-row>
          </b-form>
        </Form>
      </b-row>
    </b-container>
  </div>
</template>

<script>
import { useRecaptcha } from "@/composables/useRecaptcha";
import { email, required } from "@vee-validate/rules"
import { localize, setLocale } from "@vee-validate/i18n"
import { configure, defineRule, Field, Form } from "vee-validate"
import { getErrorMessage, notify } from "@/helpers/common";
import { ERROR_CODE } from "@/constants/constants";
import messErrors from "@/constants/errors";
import authService from "../../services/auth.service"

defineRule("email", email)
defineRule("required", required)
configure({
  generateMessage: localize({
    en: {
      messages: {
        required: 'The {field} is required',
      },
    },
    id: {
      messages: {
        required: 'Kolom {field} wajib diisi',
      },
    },
  }),
});

export default {
  components: {
    Field, Form
  },
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ FORGOT_PASSWORD: 'forgotPassword' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data () {
    return {
      title: "Forgot Password",
      operatingSystem: "",
      browserName: "",
      message: "",
      form: {},
      submitting: false,
      requestSent: false,
    }
  },
  mounted () {
    setLocale(this.$i18n.locale)
    this.browserName = this.detectBrowser.meta.name
    let userAgent = this.detectBrowser.meta.ua.toLocaleLowerCase()
    if (userAgent.includes("win")) this.operatingSystem = "Windows"
    else if (userAgent.includes("mac") && !this.detectBrowser.isIOS) this.operatingSystem = "MacOS"
    else if (userAgent.includes("ios") || userAgent.includes("iphone") || this.detectBrowser.isIOS) this.operatingSystem = "iOS"
    else if (userAgent.includes("android")) this.operatingSystem = "Android"
    else if (userAgent.includes("linux")) this.operatingSystem = "Linux"
    else this.operatingSystem = "Unknown os"
  },
  methods: {
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    async onSubmit() {
      if (this.submitting || !this.validateRecaptchaV2.FORGOT_PASSWORD()) return

      this.submitting = true
      try {
        this.message = ""
        this.form["browser_name"] = this.browserName
        this.form["operating_system"] = this.operatingSystem
        const recaptchaTokenV3 = await this.recaptchaV3Exec.FORGOT_PASSWORD()
        const recaptchaTokenV2 = this.recaptchaTokenV2.FORGOT_PASSWORD
        const data = await authService.forgotPassword({
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
          ...this.form
        }, false, true)
        if (data) {
          this.message = data.message
          this.requestSent = true
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.FORGOT_PASSWORD = true
        } else {
          notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
        }
      } finally {
        this.submitting = false
        this.resetRecaptchaV2.FORGOT_PASSWORD()
      }
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
  watch: {
    '$i18n.locale'(newVal, oldVal) {
      setLocale(newVal)
    },
  },
}
</script>
<style lang="scss">
.forgot-password {
  margin-top: -20px;
  padding-bottom: 60px;

  .form {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    margin: 10px;
    padding: 24px !important;
  }

  h5 {
    color: var(--primary-color);
    border: 2px var(--primary-background-darker-color);
    border-radius: 8px;
    background: var(--primary-background-color);
    padding: 10px 15px;
  }

  //b-form-group label
  legend {
    padding-top: 5px !important;
    font-style: normal;
    font-weight: bold;
    font-size: 15px;
    color: var(--primary-darker-color);
  }

  //b-form-input
  input {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    color: var(--primary-darker-color);

    &::placeholder {
      color: #ABB5BE !important;
      opacity: 1;
    }
  }

  //b-form-invalid-feedback
  .invalid-feedback {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
  }
}
</style>
