<template>
  <div class="login mt-1">
    <b-container>
      <b-row class="text-center pt-4 mb-4" align-h="center">
        <b-col>
          <img class="logo" src="@/assets/img/logo.png" alt="">
          <h2>{{ $t("common.WELCOME") }}</h2>
          <p>{{ $t("AUTH.LOGIN_TO_YOUR_ACCOUNT") }}</p>
        </b-col>
      </b-row>
      <b-row align-h="center">
        <Form class="form col-md-8 col-lg-6" v-slot="{ handleSubmit }" as="div">
          <b-form @submit="handleSubmit($event, onSubmit)">

            <div v-if="showLoginForm">
              <Field :name="$t('AUTH.EMAIL')" :rules="{ required: true, email: true }" v-slot="validationContext"
                :model-value="form.email" @update:modelValue="form.email = $event">
                <b-form-group :label="$t('AUTH.EMAIL')">
                  <b-form-input v-bind="validationContext.field" placeholder="<EMAIL>" type="email"
                    :state="getValidationState(validationContext.meta)"
                    aria-describedby="input-email-feedback"></b-form-input>
                  <b-form-invalid-feedback id="input-email-feedback">
                    {{ validationContext.errors[0] }}
                  </b-form-invalid-feedback>
                </b-form-group>
              </Field>

              <Field :model-value="form.password" @update:modelValue="form.password = $event" vid="password"
                :name="$t('AUTH.PASSWORD')" :rules="{ required: true, min: 8 }" v-slot="validationContext">
                <b-form-group :label="$t('AUTH.PASSWORD')">
                  <b-input-group>
                    <b-form-input v-bind="validationContext.field" placeholder="••••••••••••"
                      :type="showPassword ? 'text' : 'password'" :state="getValidationState(validationContext.meta)"
                      aria-describedby="input-password-feedback"></b-form-input>
                    <b-input-group-append>
                      <div class="show-hide d-flex justify-content-center align-items-center"
                        @click="showPassword = !showPassword">
                        <b-icon width="18" height="18" color="white" :icon="showPassword ? 'eye' : 'eye-slash'"></b-icon>
                      </div>
                    </b-input-group-append>
                    <b-form-invalid-feedback id="input-password-feedback">
                      {{ validationContext.errors[0] }}
                    </b-form-invalid-feedback>
                  </b-input-group>
                </b-form-group>
              </Field>
              <b-row align-h="between" class="m-0">
                <b-form-checkbox class="color-gray font-14" name="remember_me"
                  v-model="form.remember_me" @change="trackGtmEvent(gtmEvent.rememberMe)">
                  {{ $t("AUTH.REMEMBER_ME") }}
                </b-form-checkbox>
                <router-link :to="{ name: 'forgotPassword' }" cols="4">
                  <p class="text-center font-14" style="font-weight: 600">
                    <span style="color: var(--primary-color); font-weight: 600">{{ $t("AUTH.PASSWORD_FORGOT") }}?</span>
                  </p>
                </router-link>
              </b-row>
            </div>
            <div v-if="showLoginForm && maxLoginAttempts != null && remainingLoginAttempts != null"
                 class="login-failed-warning d-flex align-items-center px-2 py-2 mt-3">
              <img src="@/assets/img/warning.svg" alt="Warning"
                   :class="['ml-1', 'mr-2', { 'mt-1': remainingLoginAttempts > 0 }]" width="20" height="20"/>
              <div class="flex-grow-1">
                <p v-if="remainingLoginAttempts <= 0"
                   v-html="$t('AUTH.LOGIN_FAILED_BANNED', { whatsapp: whatsappSupport })">
                </p>

                <template v-else>
                  <p>{{ $t("AUTH.LOGIN_FAILED") }} {{ retryLoginText }}</p>
                  <p>{{ $t(loginWarningKey, { remainingAttempts: remainingLoginAttempts }) }}
                    <router-link v-if="remainingLoginAttempts <= 2"
                                 :to="{ name: 'forgotPassword' }" style="text-decoration: underline;">
                      {{ $t("AUTH.PASSWORD_FORGOT") }}?
                    </router-link>
                  </p>
                </template>
              </div>
            </div>
            <!-- Recaptcha V2 checkbox fallback -->
            <div v-if="showRecaptchaV2.LOGIN" :ref="recaptchaV2Checkbox.LOGIN" class="d-flex justify-content-center mt-2"></div>
            <b-button v-if="showLoginForm" :disabled="retryAfterSeconds > 0" id="btn_ManualLogin" class="btn-main w-100 py-2 mt-4" type="submit" variant="none">
              {{ $t("AUTH.LOGIN") }}
            </b-button>

            <b-button v-else id="btn_SelectManualLogin" class="btn-main w-100 py-2 mt-2" variant="none" @click="showLoginForm = true">
              <img width="18" height="18" class="mb-1 mr-1" src="@/assets/img/login.svg" alt="" />
              {{ $t("AUTH.LOGIN") }}
            </b-button>

            <div v-if="!showLoginForm">
              <b-row class="w-100 align-items-center p-0 m-0 my-3">
                <div class="flex-grow-1 h-line"></div>
                <p class="text-uppercase mx-2 p-0">{{ $t("common.OR") }}</p>
                <div class="flex-grow-1 h-line"></div>
              </b-row>
              <!-- Recaptcha V2 checkbox fallback -->
              <div v-if="showRecaptchaV2.LOGIN_WITH_GOOGLE" :ref="recaptchaV2Checkbox.LOGIN_WITH_GOOGLE" class="d-flex justify-content-center mt-1 mb-1"></div>
              <div v-if="showRecaptchaV2.LOGIN_WITH_FACEBOOK" :ref="recaptchaV2Checkbox.LOGIN_WITH_FACEBOOK" class="d-flex justify-content-center mt-1 mb-1"></div>
              <div v-if="showRecaptchaV2.LOGIN_WITH_APPLE" :ref="recaptchaV2Checkbox.LOGIN_WITH_APPLE" class="d-flex justify-content-center mt-1 mb-1"></div>
              <b-button id="btn_LoginWithGoogle" class="btn-outline-main col-12 my-1 py-2"
                variant="none" @click.prevent="loginWithGoogle">
                <img width="16" height="16" class="mb-1 mr-1" src="@/assets/img/socials/google_auth.svg" alt="" />
                {{ $t("AUTH.LOGIN_WITH_GOOGLE") }}
              </b-button>
              <b-button id="btn_LoginWithFacebook" class="btn-outline-main col-12 mt-2 mb-1 py-2"
                variant="none" @click.prevent="loginWithFacebook">
                <img width="16" height="16" class="mb-1 mr-1" src="@/assets/img/socials/facebook_auth.svg" alt="" />
                {{ $t("AUTH.LOGIN_WITH_FACEBOOK") }}
              </b-button>
              <b-button id="btn_LoginWithApple" class="btn-outline-main col-12 mt-2 py-2" variant="none"
                @click.prevent="loginWithApple">
                <img width="16" height="16" class="mb-1 mr-1" src="@/assets/img/socials/apple_auth.svg" alt="" />
                {{ $t("AUTH.LOGIN_WITH_APPLE") }}
              </b-button>
            </div>

            <router-link :to="{ name: 'register' }" cols="6">
              <p class="text-center mt-4" style="font-weight: 600">
                {{ $t("AUTH.DONT_HAVE_AN_ACCOUNT") }}
                <span style="color: var(--primary-color); font-weight: 600">{{ $t("AUTH.REGISTER_NOW") }}</span>
              </p>
            </router-link>
          </b-form>
        </Form>
      </b-row>
    </b-container>
  </div>
</template>

<script>
import { useRecaptcha } from "@/composables/useRecaptcha";
import { configure, defineRule, Field, Form } from "vee-validate"
import { email, min, required } from "@vee-validate/rules"
import { localize, setLocale } from "@vee-validate/i18n"
import { facebookAuth } from "@/helpers/facebookAuth"
import authService from "../../services/auth.service"
import accountService from "../../services/account.service"
import { getDeviceUUID, notify, clearStorageKeys, clearStorageActionKeys } from "@/helpers/common"
import { STATUS_CODE, STORAGE_KEYS } from "@/constants/constants"
import { useMeta } from "vue-meta"
import { GTM_EVENT_NAMES } from "@/constants/gtm"
import { gtmTrackEvent } from "@/helpers/gtm"
import { getCookie, getErrorMessage, getDeviceInfo } from "@/helpers/common"
import { ERROR_CODE } from "@/constants/constants";
import messErrors from "../../constants/errors"
import { requestSignIn } from "@/helpers/appleAuth"
import externalSites from "@/constants/externalSites";
import store from "@/store/store";

defineRule("email", email)
defineRule("min", min)
defineRule("required", required)

configure({
  generateMessage: localize({
    en: {
      messages: {
        required: 'The {field} is required',
      },
    },
    id: {
      messages: {
        required: 'Kolom {field} wajib diisi',
      },
    },
  }),
});

export default {
  components: {
    Field, Form, defineRule
  },
  setup() {
    const title = 'Login'
    useMeta({
      title: title,
      meta: [
        { property: 'og:title', content: title },
        { property: 'og:site_name', content: title },
      ],
    })
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ LOGIN: 'login', LOGIN_WITH_FACEBOOK: 'loginWithFacebook', LOGIN_WITH_GOOGLE: 'loginWithGoogle', LOGIN_WITH_APPLE: 'loginWithApple' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      title: "Login",
      form: {},
      showPassword: false,
      gtmEvent: {
        rememberMe: GTM_EVENT_NAMES.REMEMBER_ME,
      },
      showLoginForm: false,
      maxLoginAttempts: null,
      remainingLoginAttempts: null,
      retryAfterSeconds: null,
      whatsappSupports: externalSites.WHATSAPP_SUPPORTS,
      deviceInfo: getDeviceInfo(),
    }
  },
  computed: {
    loginWarningKey () {
      if (this.remainingLoginAttempts === 1) {
        return "AUTH.LOGIN_FAILED_WARNING_3";
      } else if (this.remainingLoginAttempts === 2) {
        return "AUTH.LOGIN_FAILED_WARNING_2";
      } else {
        return "AUTH.LOGIN_FAILED_WARNING";
      }
    },
    retryLoginText() {
      if (this.retryAfterSeconds > 0) {
        const minutes = Math.floor(this.retryAfterSeconds / 60).toString().padStart(2, '0');
        const seconds = (this.retryAfterSeconds % 60).toString().padStart(2, '0');
        return "(" + this.$t("common.RETRY_AFTER", { retryAfter: `${minutes}:${seconds}` }) + ")";
      }
      return ""
    },
    whatsappSupport () {
      if (this.whatsappSupports[this.$i18n.locale]) {
        return this.whatsappSupports[this.$i18n.locale]
      }
      return this.whatsappSupports.id
    },
  },
  async mounted() {
    await store.dispatch("setSocialLoginData", {})
    setLocale(this.$i18n.locale)
    await facebookAuth.init()
  },
  beforeUnmount() {
    if (this.loginRetryCooldownInterval) clearInterval(this.loginRetryCooldownInterval);
  },
  methods: {
    getValidationState({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    async loginWithFacebook() {
      if (!this.validateRecaptchaV2.LOGIN_WITH_FACEBOOK()) return
      let loginResponse = await facebookAuth.login(
        { scope: "email, public_profile", return_scopes: true, auth_type: "rerequest" })
      if (loginResponse && loginResponse.authResponse) {
        let authResponse = loginResponse.authResponse
        let userResponse = await facebookAuth.api("/me", { fields: "email, name" })
        if (userResponse) {
          try {
            const recaptchaTokenV3 = await this.recaptchaV3Exec.LOGIN_WITH_FACEBOOK()
            const recaptchaTokenV2 = this.recaptchaTokenV2.LOGIN_WITH_FACEBOOK
            const data = await authService.loginWithFacebook({
              "fb_id": authResponse.userID,
              "fb_token": authResponse.accessToken,
              "email_fb": userResponse.email,
              "uuid": getDeviceUUID(),
              "cookie_fbp": getCookie("_fbp"),
              "recaptcha_token": recaptchaTokenV3,
              "recaptcha_token_v2": recaptchaTokenV2,
              ...this.deviceInfo,
            }, false, true)
            if (data && data.user && data.access_token) {
              await this.onLoginSuccess(data.user, data.access_token)
            }
          } catch (ex) {
            if (ex.statusCode === STATUS_CODE.HTTP_NOT_FOUND) {
              await store.dispatch("setSocialLoginData", {
                "fb_id": authResponse.userID,
                "fb_token": authResponse.accessToken,
                "email_fb": userResponse.email,
                "email": userResponse.email,
                "name": userResponse.name ? userResponse.name : "",
              })
              await this.$router.push({ name: "register", })
            } else {
              if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
                this.showRecaptchaV2.LOGIN_WITH_FACEBOOK = true
              } else {
                notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
              }
            }
          } finally {
            this.resetRecaptchaV2.LOGIN_WITH_FACEBOOK()
          }
        } else {
          notify({ text: "Facebook: failed to get user info", type: "error" })
        }
      } else {
        notify({ text: "Facebook: login failed.", type: "error" })
      }
    },
    async loginWithGoogle() {
      if (!this.validateRecaptchaV2.LOGIN_WITH_GOOGLE()) return
      let googleUserInfo = await this.$gAuth.signIn().catch(error => {
        notify({ text: JSON.stringify(error), type: "error" })
      })
      //console.log(googleUserInfo)
      if (googleUserInfo) {
        try {
          const recaptchaTokenV3 = await this.recaptchaV3Exec.LOGIN_WITH_GOOGLE()
          const recaptchaTokenV2 = this.recaptchaTokenV2.LOGIN_WITH_GOOGLE
          const data = await authService.loginWithGoogle({
            "google_id": googleUserInfo.google_id,
            "google_token": googleUserInfo.google_token,
            "email_google": googleUserInfo.email,
            "uuid": getDeviceUUID(),
            "cookie_fbp": getCookie("_fbp"),
            "recaptcha_token": recaptchaTokenV3,
            "recaptcha_token_v2": recaptchaTokenV2,
            ...this.deviceInfo,
          }, false, true)
          if (data && data.user && data.access_token) {
            await this.onLoginSuccess(data.user, data.access_token)
          }
        } catch (ex) {
          if (ex.statusCode === STATUS_CODE.HTTP_NOT_FOUND) {
            await store.dispatch("setSocialLoginData", {
              "google_id": googleUserInfo.google_id,
              "google_token": googleUserInfo.google_token,
              "email_google": googleUserInfo.email,
              "email": googleUserInfo.email,
              "name": googleUserInfo.name ? googleUserInfo.name : "",
            })
            await this.$router.push({ name: "register", })
          } else {
            if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
              this.showRecaptchaV2.LOGIN_WITH_GOOGLE = true
            } else {
              notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
            }
          }
        } finally {
          this.resetRecaptchaV2.LOGIN_WITH_GOOGLE()
        }
      } else {
        notify({ text: "Google: failed to get user info", type: "error" })
      }
    },
    async loginWithApple() {
      if (!this.validateRecaptchaV2.LOGIN_WITH_APPLE()) return
      requestSignIn((event) => {
        if (event.data) {
          this.proceedAppleLogin(event.data)
        }
      })
    },
    startLoginRetryCountdown(seconds) {
      // Always clear existing timer first
      if (this.loginRetryCooldownInterval) {
        clearInterval(this.loginRetryCooldownInterval);
        this.loginRetryCooldownInterval = null;
      }
      // If no valid seconds passed → just reset & exit
      if (!Number.isFinite(Number(seconds)) || seconds <= 0){
        this.retryAfterSeconds = null;
        return;
      }
      // Otherwise start countdown
      this.retryAfterSeconds = seconds;
      this.loginRetryCooldownInterval = setInterval(() => {
        if (this.retryAfterSeconds > 0) {
          this.retryAfterSeconds -= 1;
        }
        if (this.retryAfterSeconds <= 0) {
          clearInterval(this.loginRetryCooldownInterval);
          this.loginRetryCooldownInterval = null;
        }
      }, 1000);
    },
    async onSubmit() {
      if (!this.validateRecaptchaV2.LOGIN()) return
      try {
        // clear login warning
        this.maxLoginAttempts = null;
        this.remainingLoginAttempts = null;
        this.retryAfterSeconds = null;
        // login
        const recaptchaTokenV3 = await this.recaptchaV3Exec.LOGIN()
        const recaptchaTokenV2 = this.recaptchaTokenV2.LOGIN
        const data = await authService.login({
          ...this.form,
          "uuid": getDeviceUUID(),
          "cookie_fbp": getCookie("_fbp"),
          "recaptcha_token": recaptchaTokenV3,
          "recaptcha_token_v2": recaptchaTokenV2,
          ...this.deviceInfo,
        }, false, true)
        if (data && data.user && data.access_token) {
          await this.onLoginSuccess(data.user, data.access_token)
          this.form = {}
        }
      } catch (ex) {
        if (ex.extraData
          && Object.prototype.hasOwnProperty.call(ex.extraData, 'max_attempts')
          && Object.prototype.hasOwnProperty.call(ex.extraData, 'remaining_attempts')) {
          this.maxLoginAttempts = ex.extraData.max_attempts
          this.remainingLoginAttempts = ex.extraData.remaining_attempts
          this.startLoginRetryCountdown(ex.extraData.retry_after_seconds)
          if (ex.extraData.too_many_attempts_error) {
            notify({ text: ex.extraData.too_many_attempts_error, type: "error" })
          }
        } else {
          if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.LOGIN = true
          } else {
            notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
          }
        }
      } finally {
        this.resetRecaptchaV2.LOGIN()
      }
    },
    async onLoginSuccess(user, accessToken) {
      if (user && accessToken) {
        clearStorageKeys()
        clearStorageActionKeys()
        localStorage.setItem(STORAGE_KEYS.AUTHORIZATION.key, accessToken)
        await store.dispatch("setUserProfile", user)
        await store.dispatch("getDocumentVersioning")

        store.dispatch("getPendingTaskCount")
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_LOGIN,
        })

        const res = await accountService.getAssetsCount()
        if (res && res.data) {
          await store.dispatch("setAssetsCount", res.data)
          await this.$router.push(this.$route.query.redirect || { name: "assetsOverview" })
        } else {
          await this.$router.push(this.$route.query.redirect || { name: "buyProperty" })
        }
      }
    },
    trackGtmEvent(event) {
      gtmTrackEvent({
        event: event
      })
    },

    async proceedAppleLogin(loginData) {
      if (loginData.code && loginData.id && loginData.name && loginData.email) {
        try {
          const recaptchaTokenV3 = await this.recaptchaV3Exec.LOGIN_WITH_APPLE()
          const recaptchaTokenV2 = this.recaptchaTokenV2.LOGIN_WITH_APPLE
          const data = await authService.loginWithApple({
            apple_id: loginData.id,
            apple_token: loginData.code,
            email_apple: loginData.email,
            uuid: getDeviceUUID(),
            recaptcha_token: recaptchaTokenV3,
            recaptcha_token_v2: recaptchaTokenV2,
            cookie_fbp: getCookie("_fbp"),
            ...this.deviceInfo,
          })
          if (data && data.user && data.access_token) {
            await this.onLoginSuccess(data.user, data.access_token)
          }
        } catch (ex) {
          if (ex.statusCode === STATUS_CODE.HTTP_NOT_FOUND) {
            if (ex.extraData && ex.extraData.refresh_token) {
              await store.dispatch("setSocialLoginData", {
                "apple_id": loginData.id,
                "apple_token": ex.extraData.refresh_token,
                "email": loginData.email,
                "email_apple": loginData.email,
                "name": loginData.name,
                "grant_type": "refresh_token",
              })
              await this.$router.push({
                name: "register",
              })
            }
          } else {
            if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
              this.showRecaptchaV2.LOGIN_WITH_APPLE = true
            } else {
              notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
            }
          }
        } finally {
          this.resetRecaptchaV2.LOGIN_WITH_APPLE()
        }
      }
    },
  },
  watch: {
    '$i18n.locale'(newVal, oldVal) {
      setLocale(newVal)
    },
  }
}
</script>
<style lang="scss">
.login {
  margin-top: -20px;
  padding-bottom: 50px;
  background-color: var(--primary-background-color);

  .logo {
    width: 90px;
    height: 90px;
  }

  h2 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 32px;
  }

  .form {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    margin: 10px;
    padding: 24px !important;
  }

  p {
    color: #6C757D;
    font-weight: 400;
    font-size: 16px;
  }

  .h-line {
    height: 1px;
    background-color: #dddddd;
  }

  //b-form-group label
  legend {
    padding-top: 5px !important;
    font-style: normal;
    font-weight: bold;
    font-size: 15px;
    color: var(--primary-darker-color);
  }

  //b-form-input
  input {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    color: var(--primary-darker-color);

    &::placeholder {
      color: #ABB5BE !important;
      opacity: 1;
    }
  }

  //b-form-invalid-feedback
  .invalid-feedback {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
  }

  span {
    color: var(--primary-color);
  }

  .show-hide {
    width: 40px;
    background-color: rgba(0, 102, 102, 0.9);
    cursor: pointer;
    border-radius: 0px 4px 4px 0px;
    transition: .5s;

    &:hover {
      background-color: rgba(0, 102, 102, 1);
    }
  }

  .login-failed-warning {
    background-color: #FFDBDB;
    border-radius: 10px;
  }

  .login-failed-warning p, router-link {
    font-size: 14px;
    color: var(--text-color-error);
  }

  .login-failed-warning a {
    color: #1E90FF;
    text-decoration: underline;
  }
}
</style>
