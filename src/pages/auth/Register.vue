<!--suppress HtmlUnknownTag -->
<template>
  <div class="cls-register-container mt-1">
    <b-container>
      <b-row v-if="!isRegistering" ref="title" class="text-center pt-0 pt-md-4 pb-1" align-h="center">
        <b-col>
          <img class="logo" src="../../assets/img/logo.png" alt="">
          <h2 class="font-32" v-if="!referUser">{{ $t("AUTH.GET_STARTED") }}</h2>
          <b-col v-if="referUser">
            <h2 class="font-32">{{ referUser }} {{ $t("REFERRAL.REFERRAL_JUST_GIFTED") }}</h2>
            <p class="font-22" style="margin-top: 10px; color: var(--primary-color);">{{
                ownedProperties ? $t("REFERRAL.REFERRAL_OWN_PROPERTIES", {
                    name: referUser,
                    bonus_text: referBonusText,
                    bonus_required_text: referBonusRequiredText,
                  }) :
                  $t("REFERRAL.JOIN_GORO_TO_START_INVESTING")
              }}</p>
          </b-col>
          <b-row v-if="referUser" align-h="center" class="mt-1 mb-2">
            <img class="property-image" v-for="image in propertyImages" :src="getUrlImage(image)" alt="">
          </b-row>
          <p>{{ $t("REFERRAL.REFERRAL_SIGN_UP_TO_INVEST") }}</p>
        </b-col>
      </b-row>

      <b-row v-if="!isRegistering" align-h="center">
        <Form class="custom-form col-md-8 col-lg-6" v-slot="{ errors, handleSubmit }">
          <b-form @submit.prevent="handleSubmit">
            <div class="cls-register-manual mt-2">
              <b-button id="btn_create_account_btn" class="btn-main w-100" variant="none" @click="isManualRegister = true">
                {{ $t("AUTH.CREATE_ACCOUNT") }}
              </b-button>
              <b-row class="w-100 align-items-center p-0 m-0 my-3">
                <div class="flex-grow-1 h-line"></div>
                <p class="text-uppercase mx-2 p-0">{{ $t("common.OR") }}</p>
                <div class="flex-grow-1 h-line"></div>
              </b-row>
            </div>
            <div>
              <!-- Recaptcha V2 checkbox fallback -->
              <div v-if="showRecaptchaV2.LOGIN_WITH_GOOGLE" :ref="recaptchaV2Checkbox.LOGIN_WITH_GOOGLE" class="d-flex justify-content-center mt-1 mb-1"></div>
              <div v-if="showRecaptchaV2.LOGIN_WITH_FACEBOOK" :ref="recaptchaV2Checkbox.LOGIN_WITH_FACEBOOK" class="d-flex justify-content-center mt-1 mb-1"></div>
              <div v-if="showRecaptchaV2.SYNC_APPLE_ACCOUNT" :ref="recaptchaV2Checkbox.SYNC_APPLE_ACCOUNT" class="d-flex justify-content-center mt-1 mb-1"></div>
              <b-button id="btn_RegisterWithGoogle" class="btn-outline-main col-12 my-1 py-2"
                        variant="none" @click.prevent="getGoogleUserInfo">
                <img width="16" height="16" class="mb-1 mr-1" src="@/assets/img/socials/google_auth.svg" alt=""/>
                {{ $t("AUTH.GOOGLE") }}
              </b-button>
              <b-button id="btn_RegisterWithFacebook" class="btn-outline-main col-12 mt-2 mb-1 py-2"
                        variant="none" @click.prevent="getFacebookUserInfo">
                <img width="16" height="16" class="mb-1 mr-1" src="@/assets/img/socials/facebook_auth.svg" alt=""/>
                {{ $t("AUTH.FACEBOOK") }}
              </b-button>
              <b-button id="btn_RegisterWithApple" class="btn-outline-main col-12 mt-2 py-2" variant="none"
                        @click.prevent="registerWithApple">
                <img width="16" height="16" class="mb-1 mr-1" src="@/assets/img/socials/apple_auth.svg" alt=""/>
                {{ $t("AUTH.APPLE") }}
              </b-button>
            </div>
            <b-row align-h="center mt-2">
              <router-link :to="{ name: 'login' }">
                <p class="text-center pt-3" style="font-weight: 600">
                  {{ $t("AUTH.ALREADY_HAVE_AN_ACCOUNT") }}
                  <span style="color: var(--primary-color); font-weight: 600">{{ $t("AUTH.LOGIN") }}</span>
                </p>
              </router-link>
            </b-row>
          </b-form>
        </Form>
      </b-row>

      <b-row v-if="isRegistering" align-h="center" class="">
        <div class="cls-register-form-container mt-4">
          <RegisterUniqueInfo v-if="isRegisteringUniqueInfo" :formData="form" :totalStep="totalRegistrationStep" @next-step="nextStep" @go-back="handleGoBack" @callback-data="callbackData"/>
          <RegisterDetailInfo v-if="isRegisteringDetailInfo" :totalStep="totalRegistrationStep" @next-step="nextStep" @go-back="handleGoBack" @callback-data="callbackData"/>
          <ConfirmRegistration v-if="isConfirmingRegistrationInfo || isReviewingParentKyc" @next-step="nextStep"
                               @go-back="handleGoBack" @callback-data="callbackData" @go-next="handleGoNext" @on-refresh="refreshPreRegistered"/>
          <RegisterPassword v-if="isCreatingPassword" :totalStep="totalRegistrationStep" @next-step="nextStep" @go-back="handleGoBack" @callback-data="callbackData"/>
          <div v-if="showRecaptchaV2.GO_BACK" :ref="recaptchaV2Checkbox.GO_BACK" class="d-flex justify-content-center mt-1 mb-1"></div>
          <div v-if="showRecaptchaV2.GO_NEXT" :ref="recaptchaV2Checkbox.GO_NEXT" class="d-flex justify-content-center mt-1 mb-1"></div>
        </div>
      </b-row>
    </b-container>
  </div>
</template>

<script>
import {setLocale} from "@vee-validate/i18n"
import { useRecaptcha } from "@/composables/useRecaptcha";
import {Field, Form} from "vee-validate"
import {facebookAuth} from "@/helpers/facebookAuth"
import authService from "../../services/auth.service"
import commonService from "../../services/common.service"
import RegisterStep from "@/pages/auth/register/RegisterStep";
import RegisterUniqueInfo from "@/pages/auth/register/RegisterUniqueInfo";
import RegisterDetailInfo from "@/pages/auth/register/RegisterDetailInfo";
import RegisterPassword from "@/pages/auth/register/RegisterPassword";
import ConfirmRegistration from "@/pages/auth/register/ConfirmRegistration";

import {
  clearStorageKeys,
  clearStorageActionKeys,
  exchange,
  getCookie,
  getDeviceInfo,
  getDeviceUUID,
  getErrorMessage,
  notify,
  urlImage
} from "@/helpers/common"
import { ERROR_CODE, STORAGE_KEYS } from "@/constants/constants";
import messErrors from "../../constants/errors"
import store from "../../store/store"
import {INDO, STATUS_CODE, PRE_REGISTERED_STATUSES} from "@/constants/constants"
import {gtmTrackEvent} from "@/helpers/gtm"
import {GTM_EVENT_NAMES} from "@/constants/gtm"
import {FIREBASE_EVENTS, firebaseLogEvent} from "@/helpers/firebase";
import {requestSignIn} from "@/helpers/appleAuth";
import preRegisteredService from "@/services/pre_registered.service";

export default {
  components: {
    Form,
    Field,
    RegisterStep,
    RegisterUniqueInfo,
    RegisterDetailInfo,
    RegisterPassword,
    ConfirmRegistration,
  },
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({
      GO_BACK: 'goBack',
      GO_NEXT: 'goNext',
      LOGIN_WITH_FACEBOOK: 'loginWithFacebook',
      LOGIN_WITH_GOOGLE: 'loginWithGoogle',
      SYNC_APPLE_ACCOUNT: 'syncAppleAccount'
    })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      title: "Register",
      form: {
        platform: "web",
        device_id: getDeviceUUID(),
        cookie_fbp: getCookie("_fbp"),
      },
      referUser: "",
      refereeBonusPercent: null,
      propertyImages: [],
      ownedProperties: true,
      deviceInfo: getDeviceInfo(),
      isManualRegister: false,
    }
  },
  async mounted() {
    await this.getPreRegistered()

    // Load socialLoginData from Login.vue and clear it from store
    this.form = {
      ...this.form,
      "google_id": this.$store.getters.socialLoginData.google_id,
      "google_token": this.$store.getters.socialLoginData.google_token,
      "email_google": this.$store.getters.socialLoginData.email_google,
      "fb_id": this.$store.getters.socialLoginData.fb_id,
      "fb_token": this.$store.getters.socialLoginData.fb_token,
      "email_fb": this.$store.getters.socialLoginData.email_fb,
      "email": this.$store.getters.socialLoginData.email,
      "apple_id": this.$store.getters.socialLoginData.apple_id,
      "apple_token": this.$store.getters.socialLoginData.apple_token,
      "email_apple": this.$store.getters.socialLoginData.email_apple,
      "grant_type": this.$store.getters.socialLoginData.grant_type,
    }

    setLocale(this.$i18n.locale)
    await facebookAuth.init()

    if (this.$route.query.code) {
      gtmTrackEvent({
        event: GTM_EVENT_NAMES.CLICK_REFERRAL,
        source: this.$route.query.source || '',
      })
    }
  },

  async created() {
    await this.getReferralInviter()
  },

  methods: {
    async getPreRegistered() {
      try {
        const preRegisteredUuid = localStorage.getItem(STORAGE_KEYS.PRE_REGISTERED_UUID.key)
        if (preRegisteredUuid) {
          const res = await preRegisteredService.getRegister({
            uuid: preRegisteredUuid,
            device_id: getDeviceUUID()
          })

          if (res && res.data) {
            await this.updatePreRegisteredData(res.data, res)
          } else {
            await this.resetPreRegistered()
          }
        }
      } catch (e) {
        await this.resetPreRegistered()
      }
    },

    async refreshPreRegistered() {
      await this.getPreRegistered()
      this.scrollToTop()
    },

    async callbackData(data = {}) {
      if (data.preRegistered) {
        await this.updatePreRegisteredData(data.preRegistered, data)
      }
      this.scrollToTop()
    },

    async nextStep(data = {}) {
      if (data.preRegistered) {
        await this.updatePreRegisteredData(data.preRegistered, data)
      }
      this.scrollToTop()
    },

    async resetPreRegistered() {
      localStorage.removeItem(STORAGE_KEYS.PRE_REGISTERED_UUID.key)
      await store.dispatch("setPreRegistered", null)
    },

    async updatePreRegisteredData(preRegistered, data) {
      if (preRegistered && preRegistered.uuid) {
        localStorage.setItem(STORAGE_KEYS.PRE_REGISTERED_UUID.key, preRegistered.uuid)
        await store.dispatch("setPreRegistered", preRegistered)
      } else {
        await this.resetPreRegistered()
      }
      if (data) {
        if (data.otp_method) {
          await store.dispatch("setPreRegisteredOtpMethod", {
            otp_method: data.otp_method,
            message_content: data ? data.message_content : null,
            message_to: data ? data.message_to : null
          })
        }
      }
      if (data && data.user && data.access_token) {
        // On register success
        await this.onAuthenticationSuccess(data.user, data.access_token, true)
        await this.resetPreRegistered()
      }
    },

    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      })
    },

    async handleGoBack() {
      if (this.isRegisteringUniqueInfo && !this.preRegistered) {
        this.isManualRegister = false
        await this.clearSocmedInfo()
        this.scrollToTop()
        return
      }

      if (!this.validateRecaptchaV2.GO_BACK()) return
      let action = "goBack"
      try {
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_REGISTER,
          action: action,
        })
        const recaptchaTokenV3 = await this.recaptchaV3Exec.GO_BACK()
        const recaptchaTokenV2 = this.recaptchaTokenV2.GO_BACK
        const formData = {
          device_id: getDeviceUUID(),
          uuid: this.preRegistered ? this.preRegistered.uuid : null,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }
        const data = await preRegisteredService.goBack(formData, false, true)
        if (data) {
          if (data.data) {
            await this.updatePreRegisteredData(data.data, data)
          } else {
            await this.resetPreRegistered()
          }
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.GO_BACK = true
        } else {
          const errorMessage = getErrorMessage(e) || messErrors.INTERNAL;
          firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
            uuid: this.form.uuid,
            action: action,
            error: errorMessage
          });
          notify({ text: errorMessage, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.GO_BACK()
      }
      this.scrollToTop()
    },

    async handleGoNext() {
      let action = "goNext"
      if (this.preRegistered.status === PRE_REGISTERED_STATUSES.PARENTAL_KYC_FAILED) {
        await this.resetPreRegistered()
      } else {
        if (!this.validateRecaptchaV2.GO_NEXT()) return
        try {
          gtmTrackEvent({
            event: GTM_EVENT_NAMES.USER_REGISTER,
            action: action,
          })
          const recaptchaTokenV3 = await this.recaptchaV3Exec.GO_NEXT()
          const recaptchaTokenV2 = this.recaptchaTokenV2.GO_NEXT
          let formData = {
            platform: "web",
            device_id: getDeviceUUID(),
            uuid: this.preRegistered.uuid,
            recaptcha_token: recaptchaTokenV3,
            recaptcha_token_v2: recaptchaTokenV2,
          }
          const data = await preRegisteredService.goNext(formData, false, true)
          if (data) {
            if (data.data && data.data.status === PRE_REGISTERED_STATUSES.CREATE_PASSWORD) {
              await this.nextStep({
                ...data,
                preRegistered: data.data
              })
            } else {
              await this.updatePreRegisteredData(data.data || null, data)
            }
          }
        } catch (ex) {
          if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.GO_NEXT = true
          } else {
            const errorMessage = getErrorMessage(e) || messErrors.INTERNAL;
            notify({ text: errorMessage, type: "error" })
            firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
              uuid: this.preRegistered.uuid,
              action: action,
              error: errorMessage
            });
          }
        } finally {
          this.resetRecaptchaV2.GO_NEXT()
        }
      }
    },

    async onAuthenticationSuccess(user, accessToken, isRegistrationFlow = false) {
      if (user && accessToken) {
        clearStorageKeys()
        clearStorageActionKeys()
        localStorage.setItem(STORAGE_KEYS.AUTHORIZATION.key, accessToken)
        await store.dispatch("setUserProfile", user)
        await store.dispatch("getDocumentVersioning")
        if (isRegistrationFlow) {
          await store.dispatch("setShowRegistrationComplete", true)
          gtmTrackEvent({ event: GTM_EVENT_NAMES.USER_REGISTER_SUCCESS })
          const query = this.$route.query.redirect ? { redirect: this.$route.query.redirect } : {};
          await this.$router.push({ name: "registrationComplete", query })
        } else {
          await this.$router.push(this.$route.query.redirect || { name: "assetsOverview" })
        }
      }
    },

    getValidationState({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },

    async getFacebookUserInfo() {
      if (!this.validateRecaptchaV2.LOGIN_WITH_FACEBOOK()) return
      let loginResponse = await facebookAuth.login(
        { scope: "email, public_profile", return_scopes: true, auth_type: "rerequest" })
      if (loginResponse && loginResponse.authResponse) {
        let authResponse = loginResponse.authResponse
        let userResponse = await facebookAuth.api("/me", { fields: "email, name" })
        if (userResponse) {
          try {
            const recaptchaTokenV3 = await this.recaptchaV3Exec.LOGIN_WITH_FACEBOOK()
            const recaptchaTokenV2 = this.recaptchaTokenV2.LOGIN_WITH_FACEBOOK
            const data = await authService.loginWithFacebook({
              "fb_id": authResponse.userID,
              "fb_token": authResponse.accessToken,
              "email_fb": userResponse.email,
              "uuid": getDeviceUUID(),
              "cookie_fbp": getCookie("_fbp"),
              "recaptcha_token": recaptchaTokenV3,
              "recaptcha_token_v2": recaptchaTokenV2,
              ...this.deviceInfo,
            }, false, true)
            if (data && data.user && data.access_token) {
              await this.onAuthenticationSuccess(data.user, data.access_token)
            }
          } catch (ex) {
            if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
              this.showRecaptchaV2.LOGIN_WITH_FACEBOOK = true
            } else {
              //Login failed -> continue register
              this.form = {
                ...this.form,
                "fb_id": authResponse.userID,
                "fb_token": authResponse.accessToken,
                "email_fb": userResponse.email,
                "email": userResponse.email,
                "platform": "web"
              }
              if (this.referUser != null && this.referUser !== "" && this.referralCode) {
                this.form.referral_code = this.referralCode
              }
            }
          } finally {
            this.resetRecaptchaV2.LOGIN_WITH_FACEBOOK()
          }
        } else {
          notify({ text: "Facebook: failed to get user info", type: "error" })
        }
      } else {
        notify({ text: "Facebook: login failed.", type: "error" })
      }
    },

    async getGoogleUserInfo() {
      if (!this.validateRecaptchaV2.LOGIN_WITH_GOOGLE()) return
      let googleUserInfo = await this.$gAuth.signIn().catch(error => {
        notify({ text: JSON.stringify(error), type: "error" })
      })
      if (googleUserInfo) {
        try {
          const recaptchaTokenV3 = await this.recaptchaV3Exec.LOGIN_WITH_GOOGLE()
          const recaptchaTokenV2 = this.recaptchaTokenV2.LOGIN_WITH_GOOGLE
          const data = await authService.loginWithGoogle({
            "google_id": googleUserInfo.google_id,
            "google_token": googleUserInfo.google_token,
            "email_google": googleUserInfo.email,
            "uuid": getDeviceUUID(),
            "cookie_fbp": getCookie("_fbp"),
            "recaptcha_token": recaptchaTokenV3,
            "recaptcha_token_v2": recaptchaTokenV2,
            ...this.deviceInfo,
          }, false, true)
          if (data && data.user && data.access_token) {
            await this.onAuthenticationSuccess(data.user, data.access_token)
          }
        } catch (ex) {
          if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.LOGIN_WITH_GOOGLE = true
          } else {
            this.form = {
              ...this.form,
              "google_id": googleUserInfo.google_id,
              "google_token": googleUserInfo.google_token,
              "email_google": googleUserInfo.email,
              "email": googleUserInfo.email,
              "platform": "web",
            }
            if (this.referUser != null && this.referUser !== "" && this.referralCode) {
              this.form.referral_code = this.referralCode
            }
          }
        } finally {
          this.resetRecaptchaV2.LOGIN_WITH_GOOGLE()
        }
      }
    },

    async registerWithApple() {
      if (!this.validateRecaptchaV2.SYNC_APPLE_ACCOUNT()) return
      requestSignIn((event) => {
        if (event.data) {
          this.syncAppleAccount(event.data)
        }
      })
    },

    async syncAppleAccount(loginData) {
      if (loginData.code && loginData.id && loginData.name && loginData.email) {
        try {
          const recaptchaTokenV3 = await this.recaptchaV3Exec.SYNC_APPLE_ACCOUNT()
          const recaptchaTokenV2 = this.recaptchaTokenV2.SYNC_APPLE_ACCOUNT
          const data = await authService.syncAppleAccount({
            apple_id: loginData.id,
            apple_token: loginData.code,
            email_apple: loginData.email,
            uuid: getDeviceUUID(),
            grant_type: "authorization_code",
            recaptcha_token: recaptchaTokenV3,
            recaptcha_token_v2: recaptchaTokenV2,
            ...this.deviceInfo,
          }, false, true)
          if (data && data.user && data.access_token) {
            await this.onAuthenticationSuccess(data.user, data.access_token)
          }
        } catch (ex) {
          if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.SYNC_APPLE_ACCOUNT = true
          } else if (ex.statusCode === STATUS_CODE.HTTP_NOT_FOUND) {
            if (ex.extraData && ex.extraData.refresh_token) {
              this.form = {
                ...this.form,
                "apple_id": loginData.id,
                "apple_token": ex.extraData.refresh_token,
                "email": loginData.email,
                "email_apple": loginData.email,
                "grant_type": "refresh_token",
                "platform": "web",
              }
            }
          } else {
            notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
          }
        } finally {
          this.resetRecaptchaV2.SYNC_APPLE_ACCOUNT()
        }
      }
    },

    async clearSocmedInfo() {
      await store.dispatch("setSocialLoginData", {})
      this.form = {
        platform: "web",
        device_id: getDeviceUUID(),
        cookie_fbp: getCookie("_fbp"),
      }
      if (this.referUser != null && this.referUser !== "" && this.referralCode) {
        this.form.referral_code = this.referralCode
      }
    },

    async getReferralInviter() {
      if (this.referralCode != null) {
        const res = await commonService.getReferralInviter(this.referralCode)
        if (res.name) {
          this.referUser = res.name
          this.refereeBonusPercent = res.referee_bonus_percent
          this.propertyImages = res.owned_images.length ? res.owned_images : res.images
          this.ownedProperties = !!res.owned_images.length
          this.form.referral_code = this.referralCode
        }
      }
    },

    getUrlImage(image) {
      return urlImage(image)
    },
  },

  computed: {
    isIndonesian() {
      if (this.$store.getters.userProfile) {
        return this.$store.getters.userProfile.iso_country_code === INDO.ISO_COUNTRY_CODE
      } else if (this.$store.getters.geoLocation) {
        return this.$store.getters.geoLocation.country_code === INDO.COUNTRY_CODE
          || this.$store.getters.geoLocation.iso_country_code === INDO.ISO_COUNTRY_CODE
      }
      return false
    },
    isEnableReferralTokenForReferee() {
      return store.state.configs.enable_referral_token_for_referee;
    },
    referralTokenAmountForReferee() {
      return store.state.configs.referral_token_amount_for_referee;
    },
    referralTokenMinimumPurchaseForReferee() {
      return store.state.configs.referral_token_minimum_purchase;
    },
    referralBonusIndoRefereePercent() {
      if (this.refereeBonusPercent) {
        return this.refereeBonusPercent
      } else if (store.state.configs && store.state.configs.referral_bonus_non_indo_referee_percent) {
        return store.state.configs.referral_bonus_non_indo_referee_percent
      }
      return 0
    },
    referBonusText() {
      if (this.isIndonesian && this.isEnableReferralTokenForReferee && this.referralTokenAmountForReferee) {
        const pricePerToken = 10000;
        return this.$t("REFERRAL.REFERRAL_OWN_PROPERTIES_FREE_TOKEN", {
          token_bonus: this.referralTokenAmountForReferee,
          token_label: this.referralTokenAmountForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase(),
          token_value: exchange(this.referralTokenAmountForReferee * pricePerToken, 100, false, "IDR"),
        })
      } else {
        return this.$t("REFERRAL.REFERRAL_OWN_PROPERTIES_CASHBACK", { cashback_percent: `${this.referralBonusIndoRefereePercent}%` })
      }
    },
    referBonusRequiredText() {
      if (this.isIndonesian && this.isEnableReferralTokenForReferee && this.referralTokenAmountForReferee && this.referralTokenMinimumPurchaseForReferee) {
        return this.$t("REFERRAL.REFERRAL_OWN_PROPERTIES_FREE_TOKEN_REQUIRED", {
          token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
          token_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
        })
      } else {
        return ""
      }
    },
    referralCode() {
      const savedReferralCode = localStorage.getItem(STORAGE_KEYS.REFERRAL_CODE.key)
      const codeParam = this.$route.query.code
      if (codeParam && codeParam !== savedReferralCode) {
        localStorage.setItem(STORAGE_KEYS.REFERRAL_CODE.key, codeParam)
      }
      return codeParam || savedReferralCode
    },
    isRegisterWithFacebook() {
      return this.form.hasOwnProperty("fb_id")
        && this.form.fb_id && this.form.hasOwnProperty("fb_token")
        && this.form.fb_token && this.form.hasOwnProperty("email_fb") && this.form.email_fb
    },
    isRegisterWithGoogle() {
      return this.form.hasOwnProperty("google_id") && this.form.google_id
        && this.form.hasOwnProperty("google_token") && this.form.google_token
        && this.form.hasOwnProperty("email_google") && this.form.email_google
    },
    isRegisterWithApple() {
      return this.form.hasOwnProperty("apple_id") && this.form.apple_id
        && this.form.hasOwnProperty("apple_token") && this.form.apple_token
        && this.form.hasOwnProperty("email_apple") && this.form.email_apple
    },
    isRegisterWithSocmed() {
      return this.isRegisterWithFacebook || this.isRegisterWithGoogle || this.isRegisterWithApple
    },
    isPreRegisteredWithSocmed() {
      return this.preRegistered &&
        ((this.preRegistered.fb_id && this.preRegistered.fb_token && this.preRegistered.email_fb)
          || (this.preRegistered.google_id && this.preRegistered.google_token && this.preRegistered.email_google)
          || (this.preRegistered.apple_id && this.preRegistered.apple_token && this.preRegistered.email_apple))
    },
    preRegistered() {
      return this.$store.getters.preRegistered
    },
    totalRegistrationStep() {
      if (this.isRegisterWithSocmed || this.isPreRegisteredWithSocmed) {
        return 2
      }
      return 3
    },
    isRegistering() {
      return this.isManualRegister || this.isRegisterWithSocmed || this.preRegistered
    },
    isRegisteringUniqueInfo() {
      if (this.preRegistered) {
        return this.preRegistered.status === PRE_REGISTERED_STATUSES.VERIFY_OTP
      } else {
        return this.isManualRegister || this.isRegisterWithSocmed
      }
    },
    isRegisteringDetailInfo() {
      return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.UPDATE_INFO
    },
    isConfirmingRegistrationInfo() {
      return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.REVIEW_INFO
    },
    isReviewingParentKyc() {
      const parentalKycStatuses = [
        PRE_REGISTERED_STATUSES.PARENTAL_KYC,
        PRE_REGISTERED_STATUSES.PARENTAL_KYC_FAILED,
        PRE_REGISTERED_STATUSES.PARENTAL_KYC_REVIEWED,
        PRE_REGISTERED_STATUSES.PARENTAL_KYC_SUCCESS
      ]
      return this.preRegistered && parentalKycStatuses.includes(this.preRegistered.status)
    },
    isCreatingPassword() {
      return this.preRegistered && this.preRegistered.status === PRE_REGISTERED_STATUSES.CREATE_PASSWORD
    },
  },

  metaInfo() {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },

  watch: {
    '$i18n.locale'(newVal, oldVal) {
      setLocale(newVal)
    },
  }
}
</script>
<style lang="scss">
.cls-register-container {
  min-height: calc(100vh - 100px);
  padding-bottom: 50px;
  background-color: #fff;
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;

  * {
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
  }

  .logo {
    width: 90px;
    height: 90px;
    @media screen and (max-width: 768px) {
      width: 45px;
      height: 45px;
    }
  }

  h2 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 32px;
  }

  .custom-form {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 28px;
    padding: 30px 30px !important;
  }

  .invalid {
    width: 100%;
    margin-top: .25rem;
    margin-bottom: 0;
    font-size: 80%;
    color: #dc3545
  }

  p {
    color: #6C757D;
    font-weight: 400;
    font-size: 16px;
  }

  hr {
    margin-top: 18px;
    margin-bottom: 18px;
    height: 2px;
    color: #E6EBEF;
    solid-color: #E6EBEF;
  }

  //b-form-group label
  legend {
    padding-top: 5px !important;
    font-style: normal;
    font-weight: bold;
    font-size: 15px;
    color: var(--primary-darker-color);
  }

  //b-form-input
  input {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    color: var(--primary-darker-color);

    &::placeholder {
      color: #ABB5BE !important;
      opacity: 1;
    }
  }

  //b-form-invalid-feedback
  .invalid-feedback {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
  }

  .show-hide {
    width: 40px;
    background-color: rgba(0, 102, 102, 0.9);;
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    transition: .5s;

    &:hover {
      background-color: rgba(0, 102, 102, 1);;
    }
  }

  .property-image {
    width: 150px;
    height: 150px;
    object-fit: cover;
    margin-left: 10px;
    margin-top: 10px;
    border: 1px solid rgb(219, 219, 219);
    border-radius: 10px;

    @media screen and (max-width: 992px) {
      width: 100px;
      height: 100px;
    }

    @media screen and (max-width: 768px) {
      width: 45px;
      height: 45px;
    }
  }

  .prompt {
    margin-top: 10px;
    font-size: 14px;
    color: #dc3545;
  }

  .suggestion {
    color: #1E90FF;
    text-decoration: underline;
    cursor: pointer;
  }

  .name-note {
    font-size: 14px;
    color: rgb(255, 166, 0);
  }

  .h-line {
    height: 1px;
    background-color: #dddddd;
  }
}

.cls-register-form-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  @media screen and (max-width: 991px) {
    width: 90%;
    margin-top: 0 !important;
  }
  @media screen and (max-width: 768px) {
    width: 100%;
  }

  .cls-register-form-wrapper {
    width: 100%;
    max-width: 550px;
    border: 1px solid #DDE2E5;
    padding: 35px;
    border-radius: 20px;
    background-color: #fff;
    @media screen and (max-width: 991px) {
      padding: 15px;
    }
    @media screen and (max-width: 768px) {
      border: 0;
      padding-top: 0;
    }

    .cls-register-form-content {
      .cls-title-step {
        font-weight: 600;
        color: #616161;
        line-height: 100%;
        text-align: center;
      }
    }

    .cls-action-btn {
      @media screen and (max-width: 768px) {
        display: flex;
        align-items: center !important;
        justify-content: space-between !important;
        width: 100%;
        margin-left: 15px;
        margin-right: 15px;
        margin-top: 0 !important;
      }

      button {
        min-width: 140px;
        margin-left: 10px;
        margin-right: 10px;
        @media screen and (max-width: 768px) {
          min-width: 90px;
          width: 100%;
          margin-bottom: 10px;
          max-width: 48%;
          margin-left: 0;
          margin-right: 0;
        }
      }
    }
  }
}
</style>
