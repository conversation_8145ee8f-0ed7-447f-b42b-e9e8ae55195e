<template>
  <div class="reset-password" style="background-color: var(--primary-background-color)">
    <b-container>
      <b-row class="text-center pt-4 pb-1" align-h="center">
        <b-col>
          <h2>{{ $t("AUTH.PASSWORD_RESET") }}</h2>
        </b-col>
      </b-row>
      <b-row align-h="center">
        <Form class="form col-md-6" v-slot="{ handleSubmit }">
          <b-form @submit.prevent="handleSubmit(onSubmit)">
            <h5 v-if="message">{{ message }}</h5>
            <Field
              :name="$t('AUTH.EMAIL')"
              :rules="{ required: true, email: true }"
              v-slot="validationContext"
              :model-value="form.email"  @update:modelValue="form.email = $event"
            >
              <b-form-group :label="$t('AUTH.EMAIL')">
                <b-form-input v-bind="validationContext.field"
                              placeholder="<EMAIL>"
                              type="email"
                              :state="getValidationState(validationContext.meta)"
                              aria-describedby="input-email-feedback"
                              :disabled="true"
                ></b-form-input>
                <b-form-invalid-feedback id="input-email-feedback">
                  {{ validationContext.errors[0] }}
                </b-form-invalid-feedback>
              </b-form-group>
            </Field>

            <Field
              vid="password"
              :name="$t('AUTH.PASSWORD')"
              :rules="{ required: true, min: 8, strong_password_user_info: [ { email: form.email } ], strong_password_criteria: [ { email: form.email } ]}"
              v-slot="validationContext"
              :model-value="form.password"  @update:modelValue="form.password = $event"
            >
              <b-form-group :label="$t('AUTH.PASSWORD')">
                <b-input-group>
                  <b-form-input
                    v-bind="validationContext.field"
                    placeholder="••••••••••••"
                    :type="showPassword ? 'text' : 'password'"
                    :state="getValidationState(validationContext.meta)"
                    aria-describedby="input-password-feedback"
                  ></b-form-input>
                  <b-input-group-append>
                    <div class="show-hide d-flex justify-content-center align-items-center" @click="showPassword=!showPassword">
                      <b-icon width="18" height="18" color="white" :icon="showPassword ? 'eye' : 'eye-slash'"></b-icon>
                    </div>
                  </b-input-group-append>
                  <b-form-invalid-feedback id="input-password-feedback">
                    {{ validationContext.errors[0] }}
                  </b-form-invalid-feedback>
                </b-input-group>
                <ul class="goro-password-strength-checklist">
                  <li :style="getPasswordIconStyle(passwordLeast8Length)">
                      <b-icon :icon="getPasswordIcon(passwordLeast8Length)"
                        :style="getPasswordIconStyle(passwordLeast8Length)" scale="1.4"></b-icon>
                      <span class="font-14">{{ $t("CHANGE_PASSWORD.CHECKLISTS.LEAST1CHAR") }}</span>
                    </li>
                    <li :style="getPasswordIconStyle(passwordLeast1Number)">
                      <b-icon :icon="getPasswordIcon(passwordLeast1Number)"
                        :style="getPasswordIconStyle(passwordLeast1Number)" scale="1.4"></b-icon>
                      <span class="font-14">{{ $t("CHANGE_PASSWORD.CHECKLISTS.LEAST1NUMBER") }}</span>
                    </li>
                    <li :style="getPasswordIconStyle(passwordLeast1Upper1LowerCase)">
                      <b-icon :icon="getPasswordIcon(passwordLeast1Upper1LowerCase)"
                        :style="getPasswordIconStyle(passwordLeast1Upper1LowerCase)" scale="1.4"></b-icon>
                      <span class="font-14">{{ $t("CHANGE_PASSWORD.CHECKLISTS.LEAST1CHARCASE") }}</span>
                    </li>
                    <li :style="getPasswordIconStyle(passwordLeast1Special)">
                      <b-icon :icon="getPasswordIcon(passwordLeast1Special)"
                        :style="getPasswordIconStyle(passwordLeast1Special)" scale="1.4"></b-icon>
                      <span class="font-14">{{ $t("CHANGE_PASSWORD.CHECKLISTS.LEAST1SPECIAL") }}</span>
                    </li>
                </ul>
              </b-form-group>
            </Field>

            <b-row class="text-center pt-4" align-h="center">
              <b-button id="btn_ResetPassword" class="btn-main pl-4 pr-4" type="submit" variant="none" :disabled="submitting">
                {{ $t("AUTH.CONFIRM_NEW_PASSWORD") }}
              </b-button>
            </b-row>
          </b-form>
        </Form>
      </b-row>
    </b-container>
  </div>
</template>

<script>
import { defineRule, Field, Form, configure } from "vee-validate"
import { email, min, required, regex } from "@vee-validate/rules"
import { setLocale, localize } from '@vee-validate/i18n'
import authService from "../../services/auth.service"
import { gtmTrackEvent } from "../../helpers/gtm"
import { GTM_EVENT_NAMES } from "../../constants/gtm"

defineRule("email", email)
defineRule("min", min)
defineRule("required", required)
defineRule('regex', regex)

configure({
  generateMessage: localize({
    en: {
      messages: {
        required: 'The {field} is required',
        strong_password_user_info: 'Password cannot contain user-related information.',
        strong_password_criteria: 'Password should contain a mix of uppercase letters, lowercase letters, numbers, and special characters.',

      },
    },
    id: {
      messages: {
        required: 'Kolom {field} wajib diisi',
        strong_password_user_info: 'Kata sandi tidak boleh mengandung informasi yang berhubungan dengan pengguna.',
        strong_password_criteria: 'Kata sandi harus mengandung kombinasi huruf kapital, huruf kecil, angka, dan karakter khusus.',
      },
    },
  }),
})

/**
 * Not based on something that is easy to guess or uses information related to the User, for example: name, telephone number, date of birth
 */
defineRule('strong_password_user_info', (value, [otherValues]) => {
  let { email } = otherValues

  if (email && value.includes(email)) {
    return false
  }
  return true
})

/**
 * Uses a combination of uppercase letters, lowercase letters and numbers, wherever possible using special characters (such as: !$%#*)
 */
defineRule('strong_password_criteria', (value, [otherValues]) => {
  // Check if the password contains a combination of uppercase, lowercase, numbers, and special characters
  const regexPattern = /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9])[A-Za-z0-9!@#$%^&*()_+{}\[\]:;<>,.?/~\-\\]+$/
  if (!regexPattern.test(value)) {
    return false
  }

  return true
})

export default {
  components: {
    Field, Form, defineRule
  },
  data () {
    return {
      title: "Reset Password",
      userAgent: "",
      userOs: "",
      token: "",
      form: {},
      message: "",
      showPassword: false,
      submitting: false,
    }
  },
  mounted() {
    setLocale(this.$i18n.locale)
  },
  created () {
    let queryParams = this.$route.query
    if (queryParams && queryParams.email && queryParams.token) {
      this.token = queryParams.token
      this.form.email = queryParams.email
    }
  },
  computed: {
    passwordLeast8Length() {
      let isValid = /^(?=.*[^\s]).{8,}$/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Number() {
      let isValid = /\d/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Upper1LowerCase() {
      let isValid = /^(?=.*[a-z])(?=.*[A-Z]).+$/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Special() {
      const regex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;
      // Test if the password contains at least one special character
      const isValid = regex.test(this.form.password)
      return isValid
    },
  },
  methods: {
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    async onSubmit () {
      if (!this.submitting) {
        this.submitting = true
        try {
          this.message = ""
          this.form["token"] = this.token
          this.form["password_confirmation"] = this.form["password"]
          const data = await authService.resetPassword(this.form)
          if (data && data.message) {
            gtmTrackEvent({
              event: GTM_EVENT_NAMES.USER_RESET_PASSWORD,
            })
            this.message = data.message
            await this.$router.push({ name: "login" })
          }
        } finally {
          this.submitting = false
        }
      }
    },
    getPasswordIconStyle(valid) {
      if (this.form.password) {
        if (valid) {
          return { color: '#28a745' };
        }
        return { color: '#dc3545' };
      }
      return { color: '#707a8a' };
    },
    getPasswordIcon(valid) {
      if (this.form.password) {
        if (!valid) {
          return 'x';
        }
      }
      return 'check';
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
  watch: {
    '$i18n.locale'(newVal, oldVal) {
      setLocale(newVal)
    },
  }
}
</script>
<style lang="scss">
.reset-password {
  margin-top: -20px;
  padding-bottom: 60px;

  .form {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    margin: 10px;
    padding: 24px !important;
  }

  h5 {
    color: var(--primary-color);
    border: 2px var(--primary-background-darker-color);
    border-radius: 8px;
    background: var(--primary-background-color);
    padding: 10px 15px;
  }

  //b-form-group label
  legend {
    padding-top: 5px !important;
    font-style: normal;
    font-weight: bold;
    font-size: 15px;
    color: var(--primary-darker-color);
  }

  //b-form-input
  input {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    color: var(--primary-darker-color);

    &::placeholder {
      color: #ABB5BE !important;
      opacity: 1;
    }
  }

  //b-form-invalid-feedback
  .invalid-feedback {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
  }

  .show-hide {
    width: 40px;
    background-color: rgba(0, 102, 102, 0.9);;
    cursor: pointer;
    border-radius: 0px 4px 4px 0px;
    transition: .5s;

    &:hover {
      background-color: rgba(0, 102, 102, 1);;
    }
  }
}
</style>
