<template>
  <b-container>
    <div class="verifyEmail pt-5 pb-5">
      <b-container>
        <b-row class="text-center" align-h="center">
          <b-col col-md-10>
            <img class="logo" src="@/assets/img/logo.png" alt="" width="90" height="90">
            <h1>{{ $t("AUTH.EMAIL_VERIFICATION") }}</h1>
            <h2 :style="{'color':messageColor}">{{ message }}</h2>
          </b-col>
        </b-row>
      </b-container>
    </div>
  </b-container>
</template>

<script>
import authService from "../../services/auth.service"

export default {
  data () {
    return {
      title: "Verify Email",
      message: "Verifying...",
      messageColor: "#6C757D",
    }
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
  async created () {
    let successColor = "#68CD86"
    let errorColor = "#B82E24"
    let queryParams = this.$route.query
    if (queryParams && queryParams.email && queryParams.code) {
      const data = await authService.verifyEmail({
        "email": queryParams.email,
        "code": queryParams.code
      })
      if (data && (data.message || data.error)) {
        if (data.message) {
          this.message = data.message
          this.messageColor = successColor
        } else {
          if (typeof data.error === "object") {
            let errorMessage = ""
            for (const p in data.error) {
              if (data.error.hasOwnProperty(p)) {
                errorMessage += `${data.error[p]} `
              }
            }
            this.message = errorMessage
          } else {
            this.message = data.error
          }
          this.messageColor = errorColor
        }
      } else {
        this.message = "ERROR: Something went wrong."
        this.messageColor = errorColor
      }
    } else {
      this.message = "ERROR: Invalid request."
      this.messageColor = errorColor
    }
  }
}
</script>
<style lang="scss">
.verifyEmail {
  min-height: 500px;

  h1 {
    font-weight: 700;
    font-size: 52px;
  }

  h2 {
    font-weight: 600;
    font-size: 20px;
  }
}
</style>
