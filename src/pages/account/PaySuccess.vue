<template>
  <b-container class="cls-single-page-container cls-payment-successs-container p-1 p-md-3 p-lg-3 mt-3 mb-3"
    v-if="((payment.id && payment.status === 'PAID') || transaction) && property && property.id">
    <b-row class="text-center" align-h="center">
      <b-col cols="12" lg="7" class="cls-page-wrapper">
        <div class="cls-page-content-wrapper no-boder">
          <div class="cls-property-purchased-container mb-4">
            <div class="cls-property-purchased-content">
              <order-card-success :property="property" :payment="payment" :transaction="transaction"></order-card-success>
            </div>
            <div class="cls-property-purchased-message">
              <p class="font-18 font-weight-bold cls-title-success mb-3">
                {{ $t("PAYMENT.SUCCESSFULLY_PURCHASED_TITLE") }}
              </p>
              <p class="font-16 mt-2 mb-0 cls-caption" v-html="$t('PAYMENT.SUCCESSFULLY_PURCHASED', { value: formatNumberIntl(payment.num_of_tokens), value2: (payment.num_of_tokens > 1 ? $t('common.TOKENS').toLowerCase() : $t('common.TOKEN').toLowerCase()) })"></p>
              <p class="font-16 cls-officially-token mt-1" v-html="ownedMessage"></p>
            </div>
          </div>

          <p class="cls-payment-external-id font-16">
            {{ payment.external_id }}
          </p>

          <div v-if="!isPresale" class="cls-payout-date-content d-flex justify-content-start align-items-center mt-4">
            <img class="cls-icon" width="24" height="24" :src="require(`@/assets/img/account/warning-message.svg`)"/>
            <p class="font-13 cls-message">
              {{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE_DESCRIPTION", payoutDateRules) }} <span class="underline">{{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE_DESCRIPTION_UNDERLINE", payoutDateRules) }}</span>
            </p>
          </div>

          <div class="cls-referral-message-content mt-4">
            <p class="cls-message">
              <span v-if="referralMessage && !transaction" class="font-18 mt-2" v-html="referralMessage"></span>
              <span v-if="referralMessage" class="ml-1 font-18 cls-copy-link">
                <u v-if="canCopy" @click="copyReferralLink"
                    style="color: var(--primary-lighter-color);">{{ $t("REFERRAL.COPY_REFERRAL_LINK") }}</u>
                <span v-else>{{ referralLink }}</span>
              </span>
            </p>
          </div>

          <b-button id="btn_paymentSuccess_ViewMyAssets" class="btn-main mt-4 cls-btn-view-assets" variant="none" @click="goToMyAssets">
            {{ $t("ASSETS.VIEW_MY_ASSETS") }}
          </b-button>
        </div>
      </b-col>
    </b-row>
  </b-container>
</template>

<script>
import paymentsService from "@/services/payments.service"
import accountService from "@/services/account.service"
import { exchange, formatNumberIntl, notify, urlImage } from "@/helpers/common"
import moment from "moment"
import store from "@/store/store"
import { gtmTrackEvent } from "../../helpers/gtm"
import { GTM_EVENT_NAMES } from "../../constants/gtm"
import { payoutDateRules } from '../../helpers/common'
import OrderCardSuccess from '../../components/Cards/OrderCardSuccess.vue'

export default {
  components: {
    OrderCardSuccess,
  },
  data() {
    return {
      title: "Pay Success",
      paymentId: this.$route.query.payment_id,
      paymentExternalId: this.$route.query.external_id,
      payment: {},
      property: {},
      trxId: this.$route.query.trx_id,
      transaction: null,
      totalOwningTokens: 0,
    }
  },
  async mounted() {
    if (this.trxId) {
      await this.getTransactionDetail()
    } else {
      await this.getPaymentDetail()
    }
  },
  methods: {
    formatNumberIntl,
    async getPaymentDetail() {
      let response = await paymentsService.getPaymentDetail({
        payment_id: this.paymentId,
        external_id: this.paymentExternalId
      })
      if (response && response.payment && response.property) {
        this.payment = response.payment
        this.property = response.property

        await this.getOwningTokens(this.property.id)

        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_PAY_SUCCESS,
          property_id: this.property.id,
          num_of_tokens: this.payment.num_of_tokens,
          balance_type: 'real',
          payment_method: this.payment.payment_method,
          customerID: this.$store.getters.userProfile.id,
          customerEmail: this.$store.getters.userProfile.email,
          customerPhoneNumber: this.$store.getters.userProfile.phone,
          value: this.payment.amount,
          totalPayment: this.payment.amount,
          currency: this.payment.currency,
          transactionId: this.paymentExternalId
        })

        this.startConfetti()
      }
    },

    async getTransactionDetail() {
      const response = await accountService.getTransactionBySecondaryId(this.trxId)
      if (response.data) {
        this.transaction = response.data
        this.property = response.data.property

        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_PAY_SUCCESS,
          property_id: this.property.id,
          num_of_tokens: this.transaction.num_of_tokens,
          balance_type: this.transaction.is_virtual ? 'virtual' : 'real',
          payment_method: 'virtual_balance',
          customerID: this.$store.getters.userProfile.id,
          customerEmail: this.$store.getters.userProfile.email,
          customerPhoneNumber: this.$store.getters.userProfile.phone,
          value: this.transaction.amount,
          totalPayment: this.transaction.amount,
          currency: this.transaction.currency,
          transactionId: this.trxId
        })

        this.startConfetti()
      }
    },
    async getOwningTokens(propertyId) {
      const response = await accountService.getOwningTokensOfProperty({
        property_id: propertyId,
      })
      if (response.data) {
        this.totalOwningTokens = response.data
      }
    },
    startConfetti() {
      this.$confetti.start({
        particles: [
          { type: "heart", },
          { type: "circle", },
          { type: "rect", },
        ],
        particlesPerFrame: 3
      })
      setTimeout(() => this.$confetti.stop(), 3000)
    },
    async goToMyAssets() {
      await this.$router.push({ name: "assetsOverview" })
    },
    copyReferralLink() {
      if (this.referralLink) {
        navigator.clipboard.writeText(this.referralLink)
        notify({ text: this.$t("common.COPIED") })
      }
    },
  },
  metaInfo() {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
  computed: {
    referralBonusPercentForReferrer () {
      if (this.$store.getters.referralCode && this.$store.getters.referralCode.referrer_bonus_percent) {
        return this.$store.getters.referralCode.referrer_bonus_percent
      } else if (store.state.configs && store.state.configs.referral_bonus_non_indo_referrer_percent) {
        return store.state.configs.referral_bonus_non_indo_referrer_percent
      }
      return 0
    },
    referralTokenAmountForReferrer () {
      if (store.state.configs && store.state.configs.referral_token_amount_for_referrer) {
        return store.state.configs.referral_token_amount_for_referrer
      }
      return 0
    },
    referralTokenMinimumPurchaseForReferee() {
      if (store.state.configs && store.state.configs.referral_token_minimum_purchase) {
        return store.state.configs.referral_token_minimum_purchase;
      }
      return 0
    },
    isEnableReferralTokenForIndo () {
      if (store.state.configs && store.state.configs.enable_referral_token_for_indo) {
        return store.state.configs.enable_referral_token_for_indo
      }
      return false
    },
    isIndonesian () {
      if (this.$store.getters.userProfile && this.$store.getters.userProfile.iso_country_code) {
        return this.$store.getters.userProfile.iso_country_code === "ID"
      }
      return false
    },
    referralMessage() {
      if (this.isIndonesian && this.isEnableReferralTokenForIndo && this.referralTokenAmountForReferrer > 0) {
        const pricePerToken = 10000;
        const tokenBonusRequired = this.referralTokenMinimumPurchaseForReferee > 0
          ? this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_REQUIRED_INFO", {
            token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
            token_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
          }) : ""
        return this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_INFO", {
          token_bonus: this.referralTokenAmountForReferrer,
          token_label: this.referralTokenAmountForReferrer > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase(),
          token_value: exchange(this.referralTokenAmountForReferrer * pricePerToken, 100, false, "IDR"),
          token_bonus_required: tokenBonusRequired
        })
      } else if (this.referralBonusPercentForReferrer > 0) {
        return this.$t("REFERRAL.REFERRAL_BONUS_INFO", { percent: this.referralBonusPercentForReferrer })
      }
      return null
    },
    referralLink() {
      return this.$store.getters.referralCode &&
        `${window.location.origin}/invite/${this.$store.getters.referralCode.code}`
    },
    ownedMessage() {
      if (this.transaction) {
        const tokens = this.transaction && this.transaction.num_of_tokens || 0
        return this.$t('VIRTUAL_BALANCE.YOU_ARE_NOW_OWNED_TOKENS', { value: formatNumberIntl(tokens), value2: (tokens > 1 ? this.$t("common.TOKENS").toLowerCase() : this.$t("common.TOKEN").toLowerCase()), value3: `${this.property.name} - ${this.property.metadata.address}` })
      } else {
        return this.$t('PAYMENT.YOU_ARE_NOW_OWNER_OF', { numOfTokens: formatNumberIntl(this.totalOwningTokens), tokenLabel: (this.totalOwningTokens > 1 ? this.$t("common.TOKENS").toLowerCase() : this.$t("common.TOKEN").toLowerCase()), propertyName: `${this.property.name} - ${this.property.metadata.address}` })
      }
    },
    expirationTime() {
      return this.transaction && moment(this.transaction.expiration_time).format('DD/MM/YYYY HH:mm') || '';
    },
    canCopy() {
      return window.isSecureContext && navigator.clipboard
    },
    isPresale() {
      return this.property && this.property.status === 'presale'
    },
    payoutDateRules() {
      const payoutDate = payoutDateRules()
      // The label depend on the totalDaysReceiveRent
      const totalDaysReceiveRent = payoutDate?.totalDaysReceiveRent || 0
      const totalDaysReceiveRentLabel = (totalDaysReceiveRent > 1) ? this.$t('common.DAYS') : this.$t('common.DAY')
      return {
        ...payoutDate,
        totalDaysReceiveRentLabel
      }
    },
  },
}
</script>

<style lang="scss">
.cls-payment-successs-container{
  *{
    font-family: "AcuminVariableConcept", Helvetica, sans-serif;
  }
  .cls-page-wrapper{
    max-width: 503px;
  }
  .cls-page-header-wrapper{
    h1.page-heading{
      font-weight: 600;
      line-height: 28.8px;
      text-align: center;
      color: #616161;
      text-transform: capitalize;
    }
    .cls-caption{
      font-weight: 500;
      line-height: 19.2px;
      text-align: center;
      color: #A5A4A4;
    }
  }
  .cls-page-content-wrapper{
    padding-top: 0 !important;
    margin-top: 0 !important;
    .cls-property-purchased-container{
      // background-color: #00AAA9;
      border-radius: 13px;
      box-shadow: 0px 8px 22.3px 0px #00000033;

      .cls-property-purchased-message{
        padding: 20px;
        padding-top: 0;
        .cls-title-success{
          color: #616161;
          font-weight: 600;
          line-height: 19.2px;
        }
        .cls-caption,
        .cls-officially-token{
          font-weight: 500;
          line-height: 18px;
          color: #6D6D6D;
        }
      }
    }
    .cls-payment-external-id{
      font-weight: 382;
      line-height: 16.8px;
      color: #6D6D6D;
    }
    .cls-payout-date-content{
      background-color: #FFEED9;
      border: 1px solid #FFA705;
      border-radius: 10px;
      padding: 15px;
      .cls-icon{
        width: 25px;
        margin-right: 15px;
      }
      .cls-message{
        font-weight: 500;
        line-height: 16px;
        letter-spacing: -0.03em;
        text-align: left;
        color: #5F5F5F;
        .underline{
          font-weight: 600;
        }
      }
    }
    .cls-referral-message-content{
      .cls-message{
        font-weight: 382;
        line-height: 24px;
        letter-spacing: -0.01em;
        text-align: center;
        color: #6D6D6D;
        padding: 10px;
        max-width: 544px;
        text-align: center;
        margin: 0 auto;
        .cls-copy-link{
          font-weight: 600;
          color: #00B7B6;
          cursor: pointer;
        }
      }
    }
    button{
      &.cls-btn-view-assets{
        width: 200px;
        padding-top: 12px;
        padding-bottom: 8px;
      }
    }
  }
}
</style>
