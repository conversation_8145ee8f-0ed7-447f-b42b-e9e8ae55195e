<template>
    <div class="pin-container">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-7 mt-2">
                <p class="font-28 font-weight-bold mb-3">{{ $t('PIN_SECURITY.CHANGE_PIN') }}</p>
                <div class="content">
                    <div v-if="isBanned != null && isBanned"
                        class="popup-content d-flex flex-column align-items-center pt-4 pb-5">
                        <div class="pb-5">
                            <img class="lock-img" src="@/assets/img/pin-blocked.png" alt="" />
                            <p class="font-weight-bold font-20 mt-3">{{ $t('PIN_SECURITY.ACCOUNT_BLOCKED') }}</p>
                            <p class="pre-formatted font-14 mt-2">{{ $t('PIN_SECURITY.ACCOUNT_BLOCKED_MESSAGE') }}</p>
                            <p class="mt-3 contact-message">{{ contactMessage }}</p>
                        </div>
                        <b-button id="btn_pinSecurity_Okay" class="btn-main col-8 col-lg-6 mt-4" variant="none" @click="close(true)">
                            {{ $t("PIN_SECURITY.OKAY") }}
                        </b-button>
                    </div>
                    <div v-else-if="isBanned != null && !pinCreatedSuccess"
                        class="popup-content d-flex flex-column align-items-center">
                        <img class="lock-img" src="@/assets/img/pin-lock-1.png" alt="" />
                        <p class="font-weight-bold font-20 mt-2 mb-1">{{ title }}</p>
                        <p class="pre-formatted font-14" :class="{ 'mb-4': isReEnter }">{{ message }}</p>
                        <div class="d-flex flex-row justity-content-center mt-5" :class="{ shake: pinDoesNotMatchShake }">
                            <div class="pin" :class="{ 'filled': pinLength >= 1 }"></div>
                            <div class="pin ml-4" :class="{ 'filled': pinLength >= 2 }"></div>
                            <div class="pin ml-4" :class="{ 'filled': pinLength >= 3 }"></div>
                            <div class="pin ml-4" :class="{ 'filled': pinLength >= 4 }"></div>
                            <div class="pin ml-4" :class="{ 'filled': pinLength >= 5 }"></div>
                            <div class="pin ml-4" :class="{ 'filled': pinLength >= 6 }"></div>
                        </div>
                        <p v-if="error" class="not-match mt-4">{{ error }}</p>
                        <p v-if="isValidatePin" class="mt-5 font-14 forgot-pin mb-4" @click="forgotPin">{{
                    $t('PIN_SECURITY.FORGOT_PIN')
                }}
                        </p>
                        <div class="d-flex flex-column mb-5" :class="{ 'mt-5': !isValidatePin }">
                            <div class="d-flex flex-row">
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(1)">1</div>
                                <div class="v-line"></div>
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(2)">2</div>
                                <div class="v-line"></div>
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(3)">3</div>
                            </div>
                            <div class="h-line"></div>
                            <div class="d-flex flex-row">
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(4)">4</div>
                                <div class="v-line"></div>
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(5)">5</div>
                                <div class="v-line"></div>
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(6)">6</div>
                            </div>
                            <div class="h-line"></div>
                            <div class="d-flex flex-row">
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(7)">7</div>
                                <div class="v-line"></div>
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(8)">8</div>
                                <div class="v-line"></div>
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(9)">9</div>
                            </div>
                            <div class="h-line"></div>
                            <div class="d-flex flex-row">
                                <div class="key"></div>
                                <div class="v-line"></div>
                                <div class="key d-flex flex-column justify-content-center" @click="clickPin(0)">0</div>
                                <div class="v-line"></div>
                                <div class="key d-flex flex-column justify-content-center align-items-center" @click="clearPin">
                                    <img class="clear-img" src="@/assets/img/ic-clear.png" alt="" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="isBanned != null && pinCreatedSuccess"
                        class="popup-content d-flex flex-column align-items-center pt-5 pb-5">
                        <div class="mt-5 mb-5">
                            <img class="lock-img" src="@/assets/img/pin-lock-2.png" alt="" />
                            <p class="font-18 font-weight-bold mt-3">{{ $t('PIN_SECURITY.PIN_UPDATED_SUCCESSFULLY') }}</p>
                        </div>
                        <b-button id="btn_pinCreatedSuccess_Okay" class="btn-main col-8 col-lg-6 mt-5" variant="none" @click="close()">
                            {{ $t("PIN_SECURITY.OKAY") }}
                        </b-button>
                    </div>
                </div>
                <PopupSecurityPin ref="popUpPinSecurity"></PopupSecurityPin>
            </div>
        </div>
    </div>
</template>

<script>

import pinService from "../../services/pin.service"
import { isLoggedIn } from "../../constants/userStatus"
import externalSites from "../../constants/externalSites"
import PopupSecurityPin from "@/components/PopupSecurityPin.vue"

export default {
    components: {
        PopupSecurityPin,
    },
    data() {
        return {
            oldPin: '',
            pin: '',
            confirmingPin: '',
            isReEnter: false,
            error: null,
            pinCreatedSuccess: false,
            isValidatePin: !this.$route.query.token,
            pinDoesNotMatchShake: false,
            attemptsLeft: null,
            isBanned: null,
            resetToken: this.$route.query.token,
        };
    },

    emits: [],
    async mounted() {
        await this.getAttemptsLeft()

        const self = this
        window.addEventListener('keyup', function (ev) {
            const key = ev.key
            if (key == 'Backspace') {
                self.clearPin()
            } else {
                const reg = /^\d+$/;
                if (reg.test(key)) {
                    self.clickPin(key)
                }
            }
        });
    },

    watch: {
        async pin(value) {
            if (value.length === 6) {
                if (this.isValidatePin) {
                    try {
                        const res = await pinService.validatePin({ pin: value })
                        if (res && res.data) {
                            this.isValidatePin = false
                            this.oldPin = value
                            this.pin = ''
                            this.confirmingPin = ''
                        }
                    } catch (e) {
                        this.attemptsLeft = e.extraData.attempts_left
                        this.isBanned = e.extraData.is_banned
                        if (!this.isBanned && this.attemptsLeft != null) {
                            const wrongAttempts = 5 - this.attemptsLeft
                            this.error = this.$t('PIN_SECURITY.WRONG_PIN_ATTEMPTS', { value: wrongAttempts })
                        } else {
                            this.error = e.extraData.error
                        }
                        this.startShaking()
                        setTimeout(() => { this.pin = '' }, 1500)
                    }
                } else {
                    setTimeout(() => { this.isReEnter = true }, 200)
                }
            }
        },
        confirmingPin(value) {
            if (value.length === 6) {
                if (value !== this.pin) {
                    this.error = this.$t('PIN_SECURITY.PIN_DOES_NOT_MATCH')
                    this.startShaking()
                    setTimeout(() => { this.confirmingPin = '' }, 1500)
                } else {
                    if (this.resetToken) {
                        this.resetPin()
                    } else {
                        this.changePin()
                    }
                }
            }
        },
    },
    methods: {
        async changePin() {
            try {
                const res = await pinService.changePin({ current_pin: this.oldPin, new_pin: this.pin })
                if (res && res.message) {
                    this.pinCreatedSuccess = true
                }
            } catch (e) {
                if (e.extraData.attempts_left) {
                    this.attemptsLeft = e.extraData.attempts_left
                    this.isBanned = e.extraData.is_banned
                }
                this.error = e.extraData.error
            }
        },
        async resetPin() {
            try {
                const res = await pinService.resetPin({ new_pin: this.pin, token: this.resetToken })
                if (res && res.message) {
                    this.pinCreatedSuccess = true
                }
            } catch (e) {
                this.error = e.extraData.error
                this.pin = ''
                this.confirmingPin = ''
                this.isReEnter = false
                
            }
        },
        async getAttemptsLeft() {
            if (isLoggedIn()) {
                const res = await pinService.attemptsLeft()
                if (res && res.attempts_left >= 0) {
                    this.attemptsLeft = res.attempts_left
                    this.isBanned = res.is_banned
                }
            }
        },

        clickPin(num) {
            if (this.pinLength < 6) {
                this.error = null
                if (this.isReEnter) {
                    this.confirmingPin = `${this.confirmingPin}${num}`
                } else {
                    this.pin = `${this.pin}${num}`
                }
            }
        },

        clearPin() {
            if (this.pinLength > 0) {
                this.error = null
                if (this.isReEnter) {
                    this.confirmingPin = this.confirmingPin.substring(0, this.pinLength - 1)
                } else {
                    this.pin = this.pin.substring(0, this.pinLength - 1)
                }
            }
        },

        forgotPin() {
            this.$refs.popUpPinSecurity.openPopup(false, true)
        },

        async close(logout = false) {
            this.isValidatePin = true
            this.oldPin = ''
            this.pin = ''
            this.confirmingPin = ''
            this.pinCreatedSuccess = false
            this.isReEnter = false
            if (logout) {
                await this.$store.dispatch("doLogout")
                await this.$router.push({ path: "/login", })
            }
            if (this.resetToken) {
                this.resetToken = null
                await this.$router.push({ name: "pin", })
            }
        },

        startShaking() {
            this.pinDoesNotMatchShake = true
            setTimeout(() => {
                this.pinDoesNotMatchShake = false
            }, 1500)
        },
    },

    computed: {
        pinLength() {
            return this.isReEnter ? this.confirmingPin.length : this.pin.length
        },

        title() {
            return this.isValidatePin ? this.$t('PIN_SECURITY.ENTER_YOUR_CURRENT_PIN') :
                this.isReEnter ?
                    this.$t('PIN_SECURITY.RE_ENTER_PIN') :
                    this.$t('PIN_SECURITY.CREATE_NEW_PIN')
        },

        message() {
            return this.isValidatePin ?
                this.$t('PIN_SECURITY.ENTER_6_DIGITS') : this.isReEnter ?
                    this.$t('PIN_SECURITY.RE_ENTER_6_DIGITS') :
                    this.$t('PIN_SECURITY.AVOID_USING')
        },

        contactMessage() {
            return this.$t("PIN_SECURITY.PLEASE_CONTACT", {
                email: externalSites.CUSTOMER_EMAIL,
                phone: externalSites.PHONE_SUPPORTS[this.$i18n.locale]
            })
        },
    },
}
</script>

<style lang="scss">
.pin-container {
    width: 100%;
    margin-top: 20px;
    padding-bottom: 20px;

    .content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding: 20px;
        margin: auto;

        .popup-content {
            background-color: #fff;
            padding: 20px;
            border-radius: 20px;
            text-align: center;
            min-width: 60%;
            min-height: 90%;

            @media (max-width: 767.98px) {
                min-width: 90%;
            }

            .lock-img {
                width: 150px;
                height: 150px;
                object-fit: contain;
            }

            .pre-formatted {
                white-space: pre;
            }

            .pin {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                border: var(--primary-darker-color) solid 1px;

                &.filled {
                    background-color: var(--primary-darker-color);
                }
            }

            .key {
                width: 100px;
                height: 50px;
                cursor: pointer;
                font-weight: bold;
                font-size: 18px;
            }

            .v-line {
                height: 50px;
                width: 0.5px;
                background-color: #0f9a9a;
            }

            .h-line {
                width: 300px;
                height: 0.5px;
                background-color: #0f9a9a;
            }

            .clear-img {
                width: 25px;
                height: 25px;
                object-fit: contain;
            }

            .not-match {
                padding: 4px 16px 4px 16px;
                background-color: #9C1E1F;
                border-radius: 16px;
                font-size: 15px;
                color: white;
                white-space: pre;
            }

            .shake {
                animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
                transform: translate3d(0, 0, 0);
            }

            @keyframes shake {

                10%,
                90% {
                    transform: translate3d(-1px, 0, 0);
                }

                20%,
                80% {
                    transform: translate3d(2px, 0, 0);
                }

                30%,
                50%,
                70% {
                    transform: translate3d(-4px, 0, 0);
                }

                40%,
                60% {
                    transform: translate3d(4px, 0, 0);
                }
            }

            .contact-message {
                font-size: 15px;
                color: white;
                background-color: #EA983E;
                padding: 4px 20px 4px 20px;
                border-radius: 8px;
                white-space: pre;
            }

            .forgot-pin {
                background-color: var(--primary-color);
                color: white;
                padding: 4px 12px 4px 12px;
                margin-bottom: 4px;
                border-radius: 4px;
                cursor: pointer;
                font-weight: bold;

                &:hover {
                    background-color: var(--primary-darker-color);
                }
            }
        }
    }
}
</style>
