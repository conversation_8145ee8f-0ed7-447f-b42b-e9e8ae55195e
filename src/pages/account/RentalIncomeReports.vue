<!--suppress HtmlUnknownTag -->
<template>
  <div class="ts-container">
    <p class="font-28 font-weight-bold mb-2">{{ $t("RENTAL_INCOME.RENTAL_INCOME_REPORTS") }}</p>
    <div class="transaction-content d-flex flex-column align-items-center">
      <div class="rental-income-top-actions w-100 d-flex align-items-center justify-content-between mb-4">
        <div class="d-flex align-items-center justify-content-start w-100">
          <v-select class="goro-select select mr-3" v-model="selectedType" :options="filterOptions" :clearable="false"
            label="name">
            <template v-slot:option="option">
              {{ option.name }}
            </template>
            <template #selected-option="option">
              {{ option.name }}
            </template>
            <template #open-indicator="{ attributes }">
              <img v-bind="attributes" src="@/assets/img/icons/arrow-select.svg" class="v-open-indicator"/>
            </template>
          </v-select>
          <b-button id="btn_incom_report_select_month_and_year" class="btn-main cls-button-filter font-16 d-flex align-items-center justify-content-between" @click="showModalSelectMonthYear=true" variant="none">
              <span class="font-16">{{ $t("FILTER.FILTER_MONTH_AND_YEAR") }}</span>
              <img v-bind="attributes" src="@/assets/img/icons/arrow-select.svg" class="v-open-indicator"/>
          </b-button>
        </div>
      </div>
      <div v-if="!rows && showNoRentalIncome" class="cls-no-rental-income-wrapper text-center mt-4 mb-4">
        <img
          class="cls-img-no-rental-income mb-4"
          width="213"
          :src="require(`@/assets/img/account/no-rental-income-yet.png`)"
        />
        <p class="no-transactions mb-4">
          {{ $t("RENTAL_INCOME.THERE_ARE_NO_RENTAL_INCOME", payoutDateRules) }}
        </p>
        <router-link :to="{ name: 'marketplace' }">
          <b-button id="btn_noIncome_GoToMarketplace" class="btn-main" type="submit" variant="none">
            {{ $t("account.marketplace") }}
          </b-button>
        </router-link>
      </div>
      <b-table v-if="totalItems" responsive hover head-variant="light" id="my-table" :fields="fields" :items="items"
        :per-page="0" @row-clicked="onRowClicked" :current-page="currentPage" small>
        <template v-slot:cell(trx_id)="{ value }">
          <span style="color: var(--primary-color); font-weight: bold; text-decoration:underline">
            {{ value }}
          </span>
        </template>
        <template v-slot:cell(value)="data">
          <p :id="`item-${data.item.item.id}`">{{ exchangeValue(data.value, data.item?.item?.exchange_rates?.rates) }}</p>
          <CurrencyTooltip :tooltipId="`item-${data.item.item.id}`" :value="data.value"></CurrencyTooltip>
        </template>
      </b-table>
      <b-pagination v-if="totalItems" class="align-self-end" v-model="currentPage" :total-rows="totalItems" :per-page="perPage"
        aria-controls="my-table">
      </b-pagination>
    </div>
    <PopupRentalDistributionDetail ref="popupRentalDistributionDetail"/>
    <ModalSelectMonthYear
      :show="showModalSelectMonthYear"
      :type="monthYear"
      @on-close="showModalSelectMonthYear = false"
      @on-apply="onApplySelectMonthYear"
    ></ModalSelectMonthYear>
  </div>
</template>

<script>

import accountService from "../../services/account.service"
import propertiesService from "../../services/properties.service"
import { exchange } from "@/helpers/common"
import moment from "moment"
import CurrencyTooltip from "../../components/CurrencyTooltip.vue"
import PopupRentalDistributionDetail from "../../components/PopupRentalDistributionDetail.vue"
import { PAGINATION_DEFAULT } from "../../constants/pagination"
import ModalSelectMonthYear from "../../modals/ModalSelectMonthYear.vue"

export default {
  components: {
    CurrencyTooltip,
    PopupRentalDistributionDetail,
    ModalSelectMonthYear,
  },
  data() {
    return {
      perPage: PAGINATION_DEFAULT.perPage,
      currentPage: 1,
      items: [],
      totalItems: 0,
      showNoRentalIncome: false,
      property: null,
      transaction: null,
      rentalDistribution: null,
      totalDeductions: 0,
      rentalDeductions: [],
      rentalIncomeDetails: [],
      rentalIncomeSummaryDetails: [],
      fields: this.getTableHeaderFields(),
      summaryReportFields: this.getSummaryReportFields(),
      deductionFields: this.getDeductionFields(),
      selectedType: {
        name: this.$t('LANDING.SELECT_PROPERTY'),
				value: 'ALL',
      },
      filterOptions: [
        {
          name: this.$t('FILTER.ALL_PROPERTIES'),
				  value: 'ALL',
        }
      ],
      showModalSelectMonthYear: false,
      filters: {
        date_and_month: null
      },
    }
  },

  async mounted() {
    await this.getRentalIncomesFilter()
    await this.getRentalIncomes()
  },

  watch: {
    '$i18n.locale'(newVal, oldVal) {
      this.items = this.items.map(e => {
        e.status = this.$t('RENTAL_INCOME.RECEIVED')
        return e
      })
      this.fields = this.getTableHeaderFields()
      this.summaryReportFields = this.getSummaryReportFields()
      this.deductionFields = this.getDeductionFields()
      if (this.selectedType.value === 'ALL') {
        this.selectedType.name = this.$t('LANDING.SELECT_PROPERTY')
      }
    },
    async 'selectedType.value'(value) {
			this.fields = this.getTableHeaderFields()
      if (this.currentPage != 1) {
        this.currentPage = 1
      } else {
        this.getRentalIncomes()
      }
		},
    currentPage: {
      handler: async function(value) {
        await this.getRentalIncomes()
      }
    },
    async 'filters.date_and_month'(value) {
      await this.getRentalIncomes()
    },
  },

  methods: {
    async getRentalIncomes() {
      let filters = {
        ...this.filters,
        page: this.currentPage,
        per_page: this.perPage,
      }
      if (this.selectedType.value !== 'ALL') {
        filters['property_id'] = this.selectedType.value
      }
      const res = await accountService.getRentalIncomes(filters)
      const data = res && res.data ? res.data : []
      this.showNoRentalIncome = true
      this.totalItems = res && res.total ? parseInt(res.total) : 0
      this.items = data && data.map(e => {
        return {
          trx_id: e.secondary_id,
          property: e.property && e.property.name || "--",
          value: e.amount,
          status: this.$t('RENTAL_INCOME.RECEIVED'),
          month: moment(e.rental_distribution.date).format("MM/YYYY"),
          item: e,
        }
      }) || []
    },

    async getRentalIncomesFilter() {
      const res = await accountService.getRentalIncomesFilter()
      if (res && res.data && this.filterOptions.length === 1) {
        this.filterOptions = [...this.filterOptions, ...res.data.map(e => {
          return {
            name: e.name,
            value: `${e.id}`
          }
        })]
      }
    },

    async onRowClicked(item, index, evt) {
      this.totalDeductions = 0
      this.transaction = item.item
      const res = await propertiesService.getByUuid(this.transaction.property.uuid)
      this.property = res.data
      const reportRes = await accountService.getRentalIncomeDetailsReport({
        property_id: this.property.id,
        date: this.transaction.rental_distribution.date,
      })
      const reportData = reportRes.data
      if (reportData && reportData.rental_distribution
        && reportData.rental_deductions && reportData.details && reportData.summary_details) {
        this.rentalDistribution = reportData.rental_distribution
        this.rentalDeductions = reportData.rental_deductions.map(rentalDeduction => {
          this.totalDeductions = this.totalDeductions + rentalDeduction.amount
          return {
            deduction_type: rentalDeduction.rental_deduction_type.deduction_type,
            description: JSON.parse(rentalDeduction.rental_deduction_type.description)[this.$i18n.locale],
            amount: rentalDeduction.amount,
            item: rentalDeduction,
          }
        })
        this.rentalIncomeDetails = reportData.details
        this.rentalIncomeSummaryDetails = reportData.summary_details.map(item => {
          return {
            number_of_tokens: item.num_of_tokens,
            ownership_days: item.days,
            subtotal: item.subtotal,
          }
        })
        const distributionDate = reportData.distribution_date
        this.$refs['popupRentalDistributionDetail'].openPopup(
          this.property,
          this.transaction,
          this.rentalIncomeDetails,
          this.rentalDistribution,
          this.deductionFields,
          this.rentalDeductions,
          this.totalDeductions,
          this.summaryReportFields,
          this.rentalIncomeSummaryDetails,
          distributionDate,
          )
      }
    },

    getTableHeaderFields() {
      return [
        { key: 'trx_id', label: this.$t('RENTAL_INCOME_TABLE_HEADER.TRANSACTION_ID') },
        { key: 'property', label: this.$t('RENTAL_INCOME_TABLE_HEADER.PROPERTY') },
        { key: 'value', label: this.$t('RENTAL_INCOME_TABLE_HEADER.VALUE') },
        { key: 'status', label: this.$t('RENTAL_INCOME_TABLE_HEADER.STATUS') },
        { key: 'month', label: this.$t('RENTAL_INCOME_TABLE_HEADER.RENTAL_MONTH') },
      ]
    },

    getSummaryReportFields() {
      return [
        { key: 'number_of_tokens', label: this.$t('RENTAL_INCOME_SUMMARY_HEADER.NUM_OF_TOKENS') },
        { key: 'ownership_days', label: this.$t('RENTAL_INCOME_SUMMARY_HEADER.OWNERSHIP_DAYS') },
        { key: 'subtotal', label: this.$t('RENTAL_INCOME_SUMMARY_HEADER.SUB_TOTAL') },
      ]
    },

    getDeductionFields() {
      return [
        { key: 'deduction_type', label: this.$t('RENTAL_INCOME_DEDUCTION_HEADER.DEDUCTION_TYPE') },
        { key: 'description', label: this.$t('RENTAL_INCOME_DEDUCTION_HEADER.DESCRIPTION') },
        { key: 'amount', label: this.$t('RENTAL_INCOME_DEDUCTION_HEADER.AMOUNT') },
      ]
    },

    exchangeValue(value, exchangeRates = null) {
      return exchange(value, 1000, false, null, false, exchangeRates)
    },

    onApplySelectMonthYear(value) {
      this.filters.date_and_month = value
    },
  },
  computed: {
    rows() {
      return this.items.length
    },

    value() {
      return this.transaction && this.transaction.amount || 0
    },

    status() {
      return this.$t('RENTAL_INCOME.RECEIVED')
    },

    isIndonesian() {
      return this.$i18n.locale.toLowerCase() === 'id';
    },

    currentLocale() {
      return this.$i18n.locale
    },

    payoutDateRules() {
      moment.locale(this.currentLocale)

      // The month of the buy token date
      const currentMonth = moment().format('MMMM')

      /**
       * Here's the mean of the value to get and show
       *
       * For the [Current Month & Year] rent distribution,
       * which will be paid by [Next 21, Month Year], you will only receive rent distribution
       * for [Total days] ([Date range of Buy Tokens starting from buy date until end of the month])
       * if you do not sell tokens in [Current Month].
       */
      const expectedPayoutDate = process.env.VUE_APP_EXPECTED_PAYOUT_DATE

      let nextPayoutDate
      if (this.isIndonesian) {
        nextPayoutDate = moment().set("date", expectedPayoutDate).add(1, "M").format("DD MMMM YYYY")
      } else {
        nextPayoutDate = moment().set("date", expectedPayoutDate).add(1, "M").format("MMMM DD, YYYY")
      }

      return {
        currentMonth,
        nextPayoutDate
      }
    },
  }
}
</script>

<style lang="scss">

.ts-container {
  width: 100%;
  margin-top: 20px;

  .transaction-content {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding: 20px;
    margin-top: 15px;

    .no-transactions {
      font-size: 18px;
      font-weight: 600;
      max-width: 60%;
      margin: 0 auto;
      color: #121212;
      @media (max-width: 768px) {
        max-width: 100%;
      }
    }

    .b-table>tbody>tr {
      cursor: pointer;
    }
  }
}

.cls-no-rental-income-wrapper{
  .cls-img-no-rental-income{
    @media(max-width: 992px) {
      width: 100%
    }
  }
}

.rental-income-top-actions {
  .cls-button-filter{
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    font-weight: 382;
    line-height: 24px;
    color: #303030;
    height: 56px;
    box-shadow: 0px 3px 18.5px 0px #0000001A !important;
    background-color: #fff;
    padding-left: 25px;
    padding-right: 25px;
    border: none;
    border-radius: 8px;
    min-width: 250px;
    &:hover{
      background-color: #fff !important;
      color: var(--primary-menu-active-color) !important;
    }
    &:focus,
    &:active{
      background-color: #fff !important;
      color: #303030 !important;
    }
    @media (max-width: 768px) {
      flex: 1;
      min-width: auto;
      padding-left: 15px;
      padding-right: 15px;
    }
  }
  @media (max-width: 991px) {
    flex-direction: column;
  }
  .select {
    min-width: 250px;
    max-width: 250px;
    width: fit-content;
    height: 56px;
    border-radius: 8px;
    box-shadow: 0px 3px 18.5px 0px #0000001A;
    border: none;
    @media (max-width: 768px) {
      flex: 1;
      width: 50%;
      min-width: 50%;
      max-width: unset;
    }

    .vs__dropdown-toggle{
      padding: 4px 0 5px;
      border: none;
    }

    .vs__selected-options {
      width: 82%;
    }

    .vs__selected {
      white-space: nowrap;
      width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
      color: #303030;
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;
    }

    .vs__open-indicator{
      fill: #303030;
    }

    .vs__dropdown-menu .vs__dropdown-option {
      width: 100%;
      white-space: initial;
      padding-right: 30px;
    }
  }
}
</style>
