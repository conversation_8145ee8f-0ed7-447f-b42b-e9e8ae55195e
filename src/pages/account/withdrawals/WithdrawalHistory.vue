<template>
  <div class="wr-container">
    <p class="font-28 font-weight-bold">{{ $t('WITHDRAWALS.WITHDRAWAL_HISTORY') }}</p>
    <div class="content d-flex flex-column align-items-center">
      <div v-if="!rows && showNoWithdrawals" class="text-center">
        <p class="no-withdrawals mb-5">{{ $t('WITHDRAWALS.THERE_ARE_NO_WITHDRAWAL_HISTORY') }}</p>
        <b-button id="btn_withdrawalHistory_createWithdrawal" @click="createWithdrawal" class="bg-main-color color-white mr-1" type="submit"
                  style="padding: 7px 10px;">
          {{ $t("WITHDRAWALS.MAKE_WITHDRAWAL") }}
        </b-button>
      </div>
      <b-table v-if="totalItems" responsive hover head-variant="light" id="my-table" :fields="fields" :items="items"
               :per-page="0" :tbody-tr-class="rowClass" @row-clicked="onRowClicked" :current-page="currentPage"
               small>
        <template v-slot:cell(amount)="data">
          <p :id="`amount-item-${data.item.item.id}`">{{ exchangeValue(data.value, data.item?.item?.exchange_rates?.rates) }}</p>
          <CurrencyTooltip :tooltipId="`amount-item-${data.item.item.id}`" :value="data.value"></CurrencyTooltip>
        </template>
        <template v-slot:cell(fee)="data">
          <p :id="`fee-item-${data.item.item.id}`">{{ exchangeValue(data.value, data.item?.item?.exchange_rates?.rates) }}</p>
          <CurrencyTooltip :tooltipId="`fee-item-${data.item.item.id}`" :value="data.value"></CurrencyTooltip>
        </template>
        <template v-slot:cell(voucherAmount)="data">
          <p :id="`fee-item-${data.item.item.id}`">{{ exchangeValue(data.value, data.item?.item?.exchange_rates?.rates) }}</p>
          <CurrencyTooltip :tooltipId="`fee-item-${data.item.item.id}`" :value="data.value"></CurrencyTooltip>
        </template>
      </b-table>
      <b-pagination v-if="totalItems" class="align-self-end" v-model="currentPage" :total-rows="totalItems" :per-page="perPage"
                    aria-controls="my-table">
      </b-pagination>
    </div>
    <b-modal v-model="showModal">
      <template #modal-header>
        <div class="w-100 d-flex flex-row align-items-center justify-content-between">
          <div class="font-24 font-weight-bold">{{ $t('WITHDRAWALS.WITHDRAWAL_DETAILS') }}</div>
          <div class="font-14">{{ withdrawalDate }}</div>
        </div>
      </template>
      <div class="text-center">
        <p :class="statusClass">{{ withdrawalStatus }}</p>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.AMOUNT') }}</p>
          <p id="withdrawal-amount" class="value">{{ exchangeValue(withdrawalAmount, this.selectedItem?.exchange_rates?.rates) }}</p>
          <CurrencyTooltip tooltipId="withdrawal-amount" :value="withdrawalAmount"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.FEE') }}</p>
          <p id="withdrawal-fee" class="value">-{{ exchangeValue(withdrawalFee, this.selectedItem?.exchange_rates?.rates) }}</p>
          <CurrencyTooltip tooltipId="withdrawal-fee" :value="withdrawalFee"></CurrencyTooltip>
        </div>
        <div v-if="withdrawalVoucherReward" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('VOUCHER.PROMO_DISCOUNT') }}</p>
          <p id="withdrawal-fee" class="value">{{ exchangeValue(withdrawalVoucherReward, this.selectedItem?.exchange_rates?.rates) }}</p>
          <CurrencyTooltip tooltipId="withdrawal-fee" :value="withdrawalVoucherReward"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.BALANCE_DEDUCTED') }}</p>
          <p id="withdrawal-amount" class="value">{{ exchangeValue(withdrawalRequiredAmount, this.selectedItem?.exchange_rates?.rates) }}</p>
          <CurrencyTooltip tooltipId="withdrawal-amount" :value="withdrawalRequiredAmount"></CurrencyTooltip>
        </div>
        <div v-if="isPayPal" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.PAYPAL_ACCOUNT_EMAIL') }}</p>
          <p class="value">{{ withdrawalPayPalEmail }}</p>
        </div>
        <div v-if="!isPayPal" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.BANK_ACCOUNT_HOLDER_NAME') }}</p>
          <p class="value">{{ withdrawalAccountHolder }}</p>
        </div>
        <div v-if="!isPayPal" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.BANK_ACCOUNT_NUMBER') }}</p>
          <p class="value">{{ withdrawalAccountNumber }}</p>
        </div>
        <div v-if="!isPayPal" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.BANK_NAME') }}</p>
          <p class="value">{{ withdrawalBankName }}</p>
        </div>
        <div v-if="isBankAccount" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.BANK_COUNTRY') }}</p>
          <p class="value">{{ bankCountry }}</p>
        </div>
        <div v-if="isBankAccount" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.BANK_ADDRESS') }}</p>
          <p class="value">{{ bankAddress }}</p>
        </div>
        <div v-if="isBankAccount" class="d-flex flex-row justify-content-between item">
          <p class="title">SWIFT Code</p>
          <p class="value">{{ swiftCode }}</p>
        </div>
        <div v-if="isBankAccount" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.NOTES') }}</p>
          <p class="value">{{ notes }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.WITHDRAWAL_DESCRIPTION') }}</p>
          <p class="value">{{ withdrawalDescription }}</p>
        </div>
        <div v-if="isRejected || isFailed" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t('WITHDRAWALS.REASON') }}</p>
          <p class="value">{{ withdrawalReason }}</p>
        </div>
        <div v-if="isCompleted || isFailed" class="d-flex flex-row justify-content-between item">
          <p class="title">External ID</p>
          <p class="value">{{ withdrawalExternalId }}</p>
        </div>
      </div>
      <template #modal-footer="{ ok, cancel, hide }">
        <b-button id="btn_showWithdrawalHistory_cancelRequest" v-if="isPending" size="sm" variant="danger" @click="onCancelRequest">
          {{ $t('WITHDRAWALS.CANCEL_REQUEST') }}
        </b-button>
        <b-button id="btn_showWithdrawalHistory_close" size="sm" variant="primary" @click="ok()">
          {{ $t('common.CLOSE') }}
        </b-button>
      </template>
    </b-modal>
  </div>
</template>

<script>

import moment from 'moment';
import {exchange} from '@/helpers/common';
import withdrawalsService from '../../../services/withdrawals.service';
import CurrencyTooltip from "../../../components/CurrencyTooltip.vue"
import {WITHDRAWAL_METHOD} from "@/constants/constants"
import {PAGINATION_DEFAULT} from "@/constants/pagination"

export default {
  components: {
    CurrencyTooltip,
  },
  data () {
    return {
      perPage: PAGINATION_DEFAULT.perPage,
      currentPage: 1,
      showNoWithdrawals: false,
      items: [],
      totalItems: 0,
      showModal: false,
      selectedItem: null,
      fields: this.getTableHeaderFields(),
    };
  },

  async mounted () {
    await this.getWithdrawalRequests();
  },

  watch: {
    '$i18n.locale' (newVal, oldVal) {
      this.items = this.items.map(e => {
        e.displayStatus = this.$t(`WITHDRAWAL_STATUSES.${e.status}`)
        return e
      });
      this.fields = this.getTableHeaderFields()
    },
    currentPage: {
      handler: async function (value) {
        await this.getWithdrawalRequests()
      }
    }
  },

  methods: {
    async getWithdrawalRequests () {
      const filters = {
        page: this.currentPage,
        per_page: this.perPage,
      }
      const res = await withdrawalsService.getWithdrawalHistory(filters);
      this.showNoWithdrawals = true;
      if (res && res.data) {
        this.totalItems = res && res.total ? parseInt(res.total) : 0
        this.items = res.data.map(e => {
          return {
            status: e.status,
            displayStatus: this.$t(`WITHDRAWAL_STATUSES.${e.status}`),
            amount: e.amount,
            bank: this.getBankName(e),
            accountNumber: this.getAccountNumber(e),
            description: e.description,
            date: moment(e.created_at).format('DD/MM/YYYY HH:mm'),
            item: e,
          };
        });
      }
    },
    getBankName (e) {
      return e.withdrawal_method === WITHDRAWAL_METHOD.PAYPAL ? `PayPal` : e.withdrawal_method === WITHDRAWAL_METHOD.XENDIT ? `${e.channel_code}` : `${e.bank_name}`
    },
    getAccountNumber (e) {
      return e.withdrawal_method === WITHDRAWAL_METHOD.PAYPAL ? `${e.email}` : `${e.account_number}`
    },
    getTableHeaderFields () {
      return [
        { key: 'displayStatus', label: this.$t('WITHDRAWAL_HISTORY_TABLE_HEADER.STATUS') },
        { key: 'amount', label: this.$t('WITHDRAWAL_HISTORY_TABLE_HEADER.AMOUNT') },
        { key: 'bank', label: this.$t('WITHDRAWAL_HISTORY_TABLE_HEADER.BANK') },
        { key: 'accountNumber', label: this.$t('WITHDRAWAL_HISTORY_TABLE_HEADER.ACCOUNT_NUMBER') },
        { key: 'description', label: this.$t('WITHDRAWAL_HISTORY_TABLE_HEADER.NOTE') },
        { key: 'date', label: this.$t('WITHDRAWAL_HISTORY_TABLE_HEADER.DATE') }
      ]
    },
    rowClass (item, type) {
      if (item && type === 'row') {
        if (item.status === 'PENDING') {
          return 'text-pending';
        } else if (item.status === 'CANCELLED' || item.status === 'REJECTED' || item.status === 'FAILED') {
          return 'text-cancelled';
        } else if (item.status === 'APPROVED') {
          return 'text-approved';
        } else {
          return 'text-completed';
        }
      } else {
        return null;
      }
    },
    async onRowClicked (item, index, evt) {
      this.selectedItem = item.item;
      this.showModal = true;
    },
    async onCancelRequest () {
      if (this.selectedItem) {
        const res = await withdrawalsService.cancelWithdrawalRequest({
          request_id: this.selectedItem.id,
        });
        if (res && res.data) {
          this.showModal = false;
          this.getWithdrawalRequests();
        }
      }
    },
    async createWithdrawal () {
      if (this.$store.getters.userProfile && this.$store.getters.userProfile.email_verified_at !== null) {
        await this.$router.push({ name: 'createWithdrawal' }).catch(() => {
        })
      } else {
        await this.$router.push({ path: '/account/my-account/basic-info?status=email_not_verified' })
      }
    },
    exchangeValue (value, exchangeRates = null) {
      return exchange(value, 100, false, null, false, exchangeRates)
    }
  },

  computed: {
    rows () {
      return this.items.length;
    },
    isBankAccount () {
      if (this.selectedItem) {
        return this.selectedItem.withdrawal_method === WITHDRAWAL_METHOD.BANK_ACCOUNT
      }
      return false
    },
    isPayPal () {
      if (this.selectedItem) {
        return this.selectedItem.withdrawal_method === WITHDRAWAL_METHOD.PAYPAL
      }
      return false
    },
    withdrawalAmount () {
      if (!this.selectedItem) {
        return 0;
      }
      return this.selectedItem.amount;
    },
    withdrawalRequiredAmount () {
      if (!this.selectedItem) {
        return 0;
      }
      return this.selectedItem.required_amount;
    },
    withdrawalFee () {
      if (!this.selectedItem) {
        return 0;
      }
      return this.selectedItem.fee + this.selectedItem.fee_from_voucher;
    },
    withdrawalVoucherReward () {
      if (!this.selectedItem) {
        return 0;
      }
      return this.selectedItem.transaction_fee_from_voucher + this.selectedItem.fee_from_voucher;
    },
    withdrawalBankName () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.bank_name;
    },
    bankCountry () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.bank_country;
    },
    bankAddress () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.bank_address;
    },
    swiftCode () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.swift_code;
    },
    notes () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.note;
    },
    withdrawalPayPalEmail () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.email
    },
    withdrawalAccountHolder () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.account_holder_name;
    },
    withdrawalAccountNumber () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.account_number;
    },
    withdrawalDescription () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.description;
    },
    withdrawalDate () {
      if (!this.selectedItem) {
        return '';
      }
      return moment(this.selectedItem.created_at).format('DD/MM/YYYY HH:mm');
    },
    withdrawalReason () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.reason;
    },
    withdrawalExternalId () {
      if (!this.selectedItem) {
        return '';
      }
      return this.selectedItem.external_id;
    },
    statusClass () {
      if (!this.selectedItem) {
        return '';
      }
      if (this.selectedItem.status === 'PENDING') {
        return 'status-pending';
      } else if (this.selectedItem.status === 'CANCELLED' || this.selectedItem.status === 'REJECTED' || this.selectedItem.status === 'FAILED') {
        return 'status-cancelled';
      } else if (this.selectedItem.status === 'APPROVED') {
        return 'status-approved';
      }
      return 'status-completed';
    },
    withdrawalStatus () {
      if (!this.selectedItem) {
        return '';
      }
      return this.$t(`WITHDRAWAL_STATUSES.${this.selectedItem.status}`);
    },
    isPending () {
      return this.selectedItem && this.selectedItem.status === 'PENDING';
    },
    isRejected () {
      return this.selectedItem && this.selectedItem.status === 'REJECTED';
    },
    isFailed () {
      return this.selectedItem && this.selectedItem.status === 'FAILED';
    },
    isCompleted () {
      return this.selectedItem && this.selectedItem.status === 'COMPLETED';
    },
  },
}
</script>

<style lang="scss">
.wr-container {
  width: 100%;
  margin-top: 20px;

  .content {
    width: 100%;
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 5px;
    padding-right: 5px;
    margin-top: 15px;

    .no-withdrawals {
      font-size: 18px;
      font-weight: 600;
    }

    .b-table > tbody > tr {
      cursor: pointer;
    }

    .text-pending {
      color: gray;
    }

    .text-cancelled {
      color: red;
    }

    .text-approved {
      color: #CE7F2D;
    }

    .text-completed {
      color: green;
    }
  }
}

.item {
  background-color: rgb(241, 241, 241);
  border-radius: 5px;
  padding: 15px 10px 0px 10px;
  margin-top: 5px;
}

.title {
  font-size: 18px;
  text-align: start;
  color: gray;
}

.value {
  font-size: 18px;
  font-weight: 500;
  text-align: end;
}

.status-pending {
  background-color: gray;
  border-radius: 5px;
  padding: 10px;
  color: white;
  font-weight: 700;
  font-size: 20px;
}

.status-cancelled {
  background-color: red;
  border-radius: 5px;
  padding: 10px;
  color: white;
  font-weight: 700;
  font-size: 20px;
}

.status-approved {
  background-color: #CE7F2D;
  border-radius: 5px;
  padding: 10px;
  color: white;
  font-weight: 700;
  font-size: 20px;
}

.status-completed {
  background-color: green;
  border-radius: 5px;
  padding: 10px;
  color: white;
  font-weight: 700;
  font-size: 20px;
}
</style>
