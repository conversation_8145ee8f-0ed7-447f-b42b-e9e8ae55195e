<template>
    <div class="container ba-container">
        <p class="font-28 font-weight-bold">{{ $t('WITHDRAWALS.BANK_ACCOUNT') }}</p>
        <div v-if="!changing" class="content text-center d-flex flex-column align-items-center">
            <p v-if="bankAccount" class="font-24 font-weight-bold">{{ $t('WITHDRAWALS.CURRENT_BANK_ACCOUNT') }}</p>
            <div v-if="pendingBankAccount && !isForeignerBank" class="col-12 col-md-12 col-lg-8 col-xl-6 d-flex flex-row justify-content-start align-items-center confirm-bank-container mt-4">
                <img src="@/assets/img/info-circle.svg" alt="" width="36" height="36">
                <p class="font-18 text-left ml-1 ml-md-3 flex-grow-1">{{ needConfirmName ? $t('WITHDRAWALS.HAVE_PENDING_BANK_ACCOUNT_NAME') : $t('WITHDRAWALS.HAVE_PENDING_BANK_ACCOUNT') }}
                </p>
                <p v-if="needConfirmName" class="confirm-bank font-12 ml-3" @click="scrollToPending">{{
                    $t('WITHDRAWALS.CONFIRM_NOW') }}</p>
            </div>
            <div v-if="pendingBankAccount && isForeignerBank" class="confirm-foreigner-bank-container col-12 col-lg-8 col-xl-6 mt-2">
                <p class="font-weight-bold">{{ $t('WITHDRAWALS.YOUR_BANK_ACCOUNT_IS_BEING_VERIFIED') }}</p>
                <p>{{ $t('WITHDRAWALS.PLEASE_WAIT_FOR_A_MAXIMUM') }}</p>
            </div>
            <div v-if="bankAccount" class="col-12 col-md-12 col-lg-8 col-xl-6 card-container">
                <p class="bank-account-title">{{ $t('WITHDRAWALS.BANK_NAME') }}</p>
                <p class="bank-account-value">{{ currentBankName }}</p>
                <p class="bank-account-title">{{ $t('WITHDRAWALS.BANK_ACCOUNT_HOLDER') }}</p>
                <p class="bank-account-value">{{ currentAccountHolder }}</p>
                <p class="bank-account-title">{{ $t('WITHDRAWALS.BANK_ACCOUNT_NUMBER') }}</p>
                <p class="bank-account-value">{{ currentAccountNumber }}</p>
            </div>
            <p v-if="bankAccount" class="last-changed common-contain-tooltips">
                {{ $t('WITHDRAWALS.LAST_CHANGED') }}: {{ lastChanged }}
                <span class="common-tooltips">
                    <img v-b-tooltip.hover="{ variant: 'secondary' }" :title="$t('WITHDRAWALS.NOTICE_CHANGE_BANK_ACCOUNT')" class="tooltip-icon"
                     :src="require('@/assets/img/info-circle-fill.png')" id="tooltip-change-bank-account" alt=""/>
                </span>
            </p>
            <b-button id="btn_bankAccount_changeBankAccount" v-if="!pendingBankAccount && bankAccount && !isLoading" class="bg-main-color color-white mt-3"
                @click="showChangeBankAccount" variant="light" style="padding: 7px 10px;">
                {{ $t("WITHDRAWALS.CHANGE_BANK_ACCOUNT") }}
            </b-button>

            <p v-if="!bankAccount && !pendingBankAccount && !isLoading" class="no-history">{{
                $t('WITHDRAWALS.BANK_ACCOUNT_NOT_CONFIGURED')
            }}</p>
            <b-button id="btn_bankAccount_addBankAccount" v-if="!bankAccount && !pendingBankAccount && !isLoading" class="bg-main-color color-white mt-5"
                @click="addBankAccount" variant="light" style="padding: 7px 10px;">
                {{ $t("WITHDRAWALS.ADD_BANK_ACCOUNT") }}
            </b-button>
        </div>
        <div v-else class="content d-flex flex-column align-items-center">
            <p class="align-self-end mr-3 cancel-text" @click="cancelChanging">{{ $t('WITHDRAWALS.CANCEL') }}</p>
            <div class="col-12 col-md-12 col-lg-10 p-0">
                <AddForeignerBankAccount v-if="isForeigner" :isAdd="false" @on-success="onChangeBankSuccess"></AddForeignerBankAccount>
                <div v-else class="col-12 d-flex flex-column align-items-center p-0">
                    <p class="font-24 font-weight-bold">{{ $t('WITHDRAWALS.CHANGE_BANK_ACCOUNT') }}</p>
                    <div class="col-12 col-lg-8 text-center">
                        <div class="col-12 text-center warning-card">
                            <p class="title font-bold">{{ $t('WITHDRAWALS.BANK_WARNING_MESSAGE') }}</p>
                        </div>
                    </div>
                    <div class="col-12 col-lg-8 d-flex text-center flex-column align-items-center">
                        <div class="col-12 text-left card-container">
                            <p class="title">{{ $t('WITHDRAWALS.SELECT_BANK_NAME') }}</p>
                            <v-select class="goro-select" v-model="bank" :options="banks" :clearable="false" label="name" :style="{height: '60px'}">
                                <template v-slot:option="option">
                                    {{ option.name }}
                                </template>
                                <template #selected-option="option">
                                    {{ option.name }}
                                </template>
                            </v-select>
                            <p class="title mt-3">{{ $t('WITHDRAWALS.BANK_ACCOUNT_NUMBER') }}</p>
                            <input class="account-number" minlength="8" maxlength="16" v-model="accountNumber"
                                @keypress="isNumber($event)" @paste="onPaste" placeholder="1111-2222-3333-4444" />
                            <p v-if="accountNumberError" class="error">{{ accountNumberError }}</p>
                        </div>
                    </div>
                    <div class="col-12 col-lg-8 text-center mb-5">
                        <b-button id="btn_bankAccount_submitChangeBankAccount" class="bg-main-color color-white mt-5" @click="changeBankAccount(false)" variant="light"
                            style="padding: 10px">
                            {{ $t("WITHDRAWALS.CHANGE_BANK_ACCOUNT") }}</b-button>
                    </div>
                </div>
            </div>
        </div>
        <PopupConfirmAddBank ref="popupConfirmBank" @on-confirmed="changeBankAccount(true)"></PopupConfirmAddBank>
    </div>
</template>

<script>

import moment from 'moment';
import withdrawalsService from "../../../services/withdrawals.service";
import { gtmTrackEvent } from '../../../helpers/gtm';
import { GTM_EVENT_NAMES } from '../../../constants/gtm';
import { PAYMENT_METHOD, BANK_TYPE } from '../../../constants/constants';
import PopupConfirmAddBank from '../../../components/PopupConfirmAddBank'
import AddForeignerBankAccount from '../../../components/AddForeignerBankAccount';

export default {
    components: {
        PopupConfirmAddBank,
        AddForeignerBankAccount,
    },
    emits: ['on-success', 'on-pending-bank', 'scroll-to-pending'],
    data() {
        return {
            changing: false,
            banks: this.$store.getters.availableBanks,
            bank: this.$store.getters.availableBanks[0],
            accountNumber: '',
            accountNumberError: null,
            pendingBankAccount: null,
        };
    },

    async mounted() {
        await this.getPendingBankAccount();
    },

    methods: {
        showChangeBankAccount() {
            this.changing = true;
        },
        cancelChanging() {
            this.changing = false;
        },
        async addBankAccount() {
            await this.$router.push({ name: 'withdrawals', query: { action: 'add' } })
        },
        async getPendingBankAccount() {
            const res = await withdrawalsService.getPendingBankAccount();
            if (res) {
                this.pendingBankAccount = res.data;
                if (res.data) {
                    this.$emit('on-pending-bank', res.data)
                }
            }
        },
        async changeBankAccount(confirmed) {
            this.validate();
            if (this.accountNumberError) {
                return
            }
            if (confirmed) {
                if (!this.accountNumberError && this.bank) {
                    const res = await withdrawalsService.changeBankAccount({
                        bank_name: this.bank.name,
                        channel_code: this.bank.code,
                        account_number: this.accountNumber,
                    });
                    if (res && !res.error) {
                        this.changing = false;
                        await this.getPendingBankAccount();
                        await this.$store.dispatch('refreshUserProfile');
                        this.$emit('on-success');
                    }
                    gtmTrackEvent({
                        event: GTM_EVENT_NAMES.CHANGE_BANK_ACCOUNT
                    })
                }
            } else {
                this.$refs.popupConfirmBank.openPopup(this.accountNumber, this.bank.name)
            }
        },
        isNumber(evt) {
            evt = (evt) ? evt : window.event;
            const charCode = (evt.which) ? evt.which : evt.keyCode;
            if (charCode > 31 && (charCode < 48 || charCode > 57)) {
                evt.preventDefault();;
            } else {
                return true;
            }
        },

        onPaste(evt) {
            let number = evt.clipboardData.getData('text').replace(/\D/g, '')
            if (number.length > 16) {
                number = number.substring(0, 16)
            }
            setTimeout(() => {
                this.accountNumber = number
            }, 50)
        },

        validate() {
            this.checkAccountNumber(this.accountNumber);
        },

        checkAccountNumber(value) {
            if (value.length < 8) {
                this.accountNumberError = this.$t('WITHDRAWALS.ACCOUNT_NUMBER_MUST_AT_LEAST_8_DIGITS');
            } else {
                this.accountNumberError = null;
            }
        },

        scrollToPending() {
            this.$emit('scroll-to-pending')
        },

        onChangeBankSuccess() {
            this.changing = false;
            this.getPendingBankAccount()
            this.$emit('on-success')
        },
    },

    watch: {
        accountNumber(value) {
            this.checkAccountNumber(value);
        },
    },

    computed: {
        currentBankName() {
            return this.bankAccount && this.bankAccount.bank_name;
        },

        currentAccountHolder() {
            return this.bankAccount && this.bankAccount.account_holder_name;
        },

        currentAccountNumber() {
            return this.bankAccount && this.bankAccount.account_number;
        },

        lastChanged() {
            return this.bankAccount && moment(this.bankAccount.updated_at).format('DD/MM/YYYY HH:mm');
        },

        bankAccount() {
            return this.$store.getters.userProfile && this.$store.getters.userProfile.bank_account;
        },

        isLoading() {
            return this.$store.getters.isLoading;
        },

        userProfile() {
            return this.$store.getters.userProfile;
        },

        isForeigner() {
            if (this.userProfile && this.userProfile.payment_method === PAYMENT_METHOD.STRIPE) {
                return true;
            }
            return false;
        },

        isForeignerBank() {
            return this.pendingBankAccount && this.pendingBankAccount.bank_type === BANK_TYPE.FOREIGNER
        },

        needConfirmName() {
            return this.pendingBankAccount && this.pendingBankAccount.status === 'NEED_CONFIRM_NAME'
        },
    },
}
</script>

<style lang="scss" scoped>
.ba-container {
    // width: 100%;
    margin-top: 20px;

    .content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding-top: 20px;
        padding-bottom: 20px;
        padding-left: 5px;
        padding-right: 5px;
        margin-top: 15px;

        .warning-card {
            background-color: #FF9324;
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);

            .title {
                font-size: 18px;
                color: white;
                text-transform: uppercase;
                text-align: center;
            }
        }

        .card-container {
            background-color: var(--primary-darker-color);
            padding-bottom: 30px;
            padding-top: 30px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);

            &.pending {
                background-color: #607ae2;
            }

            .bank-account-title {
                font-size: 18px;
                color: rgb(206, 206, 206);
                text-transform: uppercase;
                margin-top: 10px;
                text-align: center;
            }

            .bank-account-value {
                font-size: 23px;
                color: white;
                font-weight: 700;
            }

            .account-number {
                width: 100%;
                background-color: #ffffff;
                border: none;
                border-radius: 8px;
                padding-left: 1rem;
                padding-right: 1rem;
                color: var(--primary-color);
                min-height: 60px;
                font-size: 32px;
                font-weight: 700;
                letter-spacing: 0.07em;
                text-transform: uppercase;

                &::placeholder {
                    color: rgb(221, 221, 221);
                    opacity: 1;
                    /* Firefox */
                }

                &:-ms-input-placeholder {
                    color: rgb(221, 221, 221);
                }

                &::-ms-input-placeholder {
                    color: rgb(221, 221, 221);
                }
            }

            .title {
                font-size: 18px;
                margin-bottom: 10px;
                color: white;
                text-transform: uppercase;
            }

            .account-holder {
                font-size: 35px;
                color: white;
                font-weight: bold;
                text-align: center;
            }

            .error {
                font-size: 15px;
                color: red;
            }
        }

        .last-changed {
            font-size: 15px;
            color: #afafaf;
            margin-top: 20px;
        }

        .cancel-text {
            font-size: 18px;
            font-weight: 700;
            color: red;
            cursor: pointer;
        }

        .no-history {
            font-size: 18px;
            font-weight: 600;
        }

        .confirm-bank-container {
            padding: 16px;
            background-color: #FFEED9;
            border: #FFA705 solid 1px;
            border-radius: 8px;
            color: #3F3F3F;

            .confirm-bank {
                cursor: pointer;
                font-weight: bold;
                color: #fff;
                background-color: var(--primary-color);
                padding: 8px 16px;
                border-radius: 5px;
                white-space: nowrap;
            }
        }

        .confirm-foreigner-bank-container {
            background-color: #FF952C;
            padding: 16px;
            border-radius: 10px;
            color: white;
        }
    }
}
</style>
