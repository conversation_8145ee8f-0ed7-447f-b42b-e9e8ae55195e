<template>
    <div v-if="hasOwnTokens">
        <BankAccount ref="bankAccount" @on-success="onSuccess" @on-pending-bank="onPendingBank" @scroll-to-pending="scollToPending" />
        <div class="container bh-container">
            <p class="font-28 font-weight-bold">{{ $t('WITHDRAWALS.HISTORY') }}</p>
            <div class="content d-flex flex-column align-items-center">
                <div v-if="!rows && showNoHistory" class="text-center">
                    <p class="no-history mb-5">{{ $t('WITHDRAWALS.THERE_ARE_NO_CHANGES_HISTORY_YET') }}</p>
                </div>
                <b-table ref="pending" v-if="totalItems" responsive hover head-variant="light" id="my-table" :fields="fields"
                    :items="items" :per-page="0" :current-page="currentPage" small :tbody-tr-class="rowClass"
                    @row-clicked="onRowClicked">
                    <template v-slot:cell(status)="data">
                        <span v-if="needConfirmName(data.item.originStatus)" class="font-weight-bold">
                            {{ data.value }} <span style="padding-left:5px" id="tooltip-click-confirm">
                                <b-icon icon="info-circle" variant="dark" scale="1"></b-icon></span>
                        </span>
                        <span v-else>{{ data.value }}</span>
                        <b-tooltip variant="secondary" target="tooltip-click-confirm" triggers="hover" placement="top">
                            {{ $t("account.CLICK_TO_CONFIRM") }}
                        </b-tooltip>
                    </template>
                </b-table>
                <b-pagination v-if="totalItems" class="align-self-end" v-model="currentPage" :total-rows="totalItems"
                    :per-page="perPage" aria-controls="my-table">
                </b-pagination>
            </div>
        </div>
        <ModalBankAccountDetail ref="modalBankAccountDetail" @on-success="onVerified"></ModalBankAccountDetail>
    </div>
    <div v-else class="bh-container">
        <p v-if="!loadingOwningTokens" class="font-28 font-weight-bold">{{ $t('WITHDRAWALS.BANK_ACCOUNT') }}</p>
        <div v-if="!loadingOwningTokens" class="content text-center">
            <p class="no-transactions mb-4">{{ $t("WITHDRAWALS.MESSAGE_CAN_NOT_ADD_BANK_ACCOUNT") }}</p>
            <router-link :to="{ name: 'buyProperty' }">
                <b-button id="btn_canNotAddBackAccount_GoToMarketplace" class="btn-main" type="submit" variant="none">
                    {{ $t("account.marketplace") }}
                </b-button>
            </router-link>
        </div>
    </div>
</template>

<script>

import withdrawalsService from '../../../services/withdrawals.service';
import accountService from '../../../services/account.service';
import moment from 'moment';
import BankAccount from './BankAccount';
import { PAGINATION_DEFAULT } from "../../../constants/pagination"
import ModalBankAccountDetail from '../../../modals/ModalBankAccountDetail.vue';

export default {
    components: {
        BankAccount,
        ModalBankAccountDetail,
    },
    data() {
        return {
            perPage: PAGINATION_DEFAULT.perPage,
            currentPage: 1,
            originalItems: [],
            items: [],
            totalItems: 0,
            showNoHistory: false,
            fields: this.getTableHeaderFields(),
            isPending: false,
            owningTokens: 0,
            loadingOwningTokens: true,
        };
    },

    async mounted() {
        await Promise.all([
            this.getBankAccountHistory(),
            this.getOwningTokens()
        ])
    },

    watch: {
        '$i18n.locale'(newVal, oldVal) {
            this.items = this.originalItems.map(e => {
                return {
                    status: this.getStatus(e.status),
                    bank: e.bank_name,
                    account_number: e.account_number,
                    account_holder: e.account_holder_name,
                    date: moment(e.created_at).format('DD/MM/YYYY HH:mm'),
                    originStatus: e.status,
                    item: e,
                };
            });
            this.fields = this.getTableHeaderFields()
        },
        currentPage: {
            handler: async function(value) {
                await this.getBankAccountHistory()
            }
        }
    },

    methods: {
        async getBankAccountHistory() {
            const filters = {
                page: this.currentPage,
                per_page: this.perPage,
            }
            const res = await withdrawalsService.getBankAccountHistory(filters);
            if (res && res.data) {
                this.totalItems = res && res.total ? parseInt(res.total) : 0
                this.showNoHistory = true;
                this.originalItems = res.data;
                this.items = res.data.map(e => {
                    return {
                        status: this.getStatus(e.status),
                        bank: e.bank_name,
                        account_number: e.account_number,
                        account_holder: e.account_holder_name,
                        date: moment(e.created_at).format('DD/MM/YYYY HH:mm'),
                        originStatus: e.status,
                        item: e,
                    };
                });
            }
        },

        async onVerified() {
            await this.getBankAccountHistory();
            await store.dispatch('refreshUserProfile');
            await this.$refs.bankAccount.getPendingBankAccount()
        },

        rowClass(item, type) {
            if (item && type === 'row') {
                if (item.status === this.getStatus('PENDING')) {
                    return 'text-pending';
                } else if (item.status === this.getStatus('CONFIRMED')) {
                    return 'text-confirmed';
                } else if (item.status === this.getStatus('NEED_CONFIRM_NAME') || item.status === this.getStatus('NEED_CONFIRM_EMAIL')) {
                    return 'text-waiting';
                } else if (item.status === this.getStatus('DEACTIVATED')) {
                    return 'text-deactivated';
                } else {
                    return 'text-failed';
                }
            } else {
                return null;
            }
        },

        async onRowClicked(item, index, evt) {
            this.$refs.modalBankAccountDetail.openPopup(item.item)
        },

        async onSuccess() {
            await this.getBankAccountHistory();
        },

        getStatus(key) {
            return this.$t(`BANK_STATUS.${key}`)
        },

        needConfirmName(status) {
            return status === 'NEED_CONFIRM_NAME'
        },

        getTableHeaderFields() {
            return [
                { key: 'status', label: this.$t('BANK_ACCOUNT_HISTORY_TABLE_HEADER.STATUS') },
                { key: 'bank', label: this.$t('BANK_ACCOUNT_HISTORY_TABLE_HEADER.BANK') },
                { key: 'account_number', label: this.$t('BANK_ACCOUNT_HISTORY_TABLE_HEADER.ACCOUNT_NUMBER') },
                { key: 'account_holder', label: this.$t('BANK_ACCOUNT_HISTORY_TABLE_HEADER.ACCOUNT_HOLDER') },
                { key: 'date', label: this.$t('BANK_ACCOUNT_HISTORY_TABLE_HEADER.DATE') }
            ]
        },

        onPendingBank(pendingBank) {
            this.isPending = true
        },

        scollToPending() {
            document.getElementById('my-table').scrollIntoView()
        },

        async getOwningTokens() {
            const res = await accountService.getOwningTokens()
            if (res) {
                this.owningTokens = res.data
            }
            this.loadingOwningTokens = false
        },
    },

    computed: {
        rows() {
            return this.items.length;
        },

        isIndonesian() {
            return this.$i18n.locale.toLowerCase() === 'id';
        },

        hasOwnTokens() {
            return this.owningTokens > 0
        },
    },
}
</script>

<style lang="scss">
.bh-container {
    // width: 100%;
    margin-top: 20px;

    .content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding-top: 20px;
        padding-bottom: 20px;
        padding-left: 5px;
        padding-right: 5px;
        margin-top: 15px;

        .no-history {
            font-size: 18px;
            font-weight: 600;
        }

        .text-pending {
            color: red;
        }

        .text-waiting {
            background-color: #ffff8d;
            color: black;
        }

        .text-failed {
            color: red;
        }

        .text-completed {
            color: val(--primary-color);
        }

        .text-confirmed {
            color: green;
        }

        .text-deactivated {
            color: black;
        }

        .b-table>tbody>tr {
            cursor: pointer;
        }
    }
}

.bank-history-item {
    border-bottom: #ebecf0 1px solid;
    margin-top: 10px;
}

.title {
    font-size: 18px;
    text-align: start;
    color: gray;
}

.value {
    font-size: 18px;
    font-weight: 500;
    text-align: end;
}

.account-holder-update {
    width: 100%;
    background-color: #ebecf0;
    border: none;
    border-radius: 8px;
    padding-left: 1rem;
    padding-right: 1rem;
    color: var(--primary-color);
    min-height: 45px;
    font-size: 28px;
    font-weight: 600;
    letter-spacing: 0.07em;
    text-transform: uppercase;
    margin-top: 5px;
    margin-bottom: 15px;
    text-align: right;

    &::placeholder {
        color: rgb(221, 221, 221);
        opacity: 1;
        /* Firefox */
    }

    &:-ms-input-placeholder {
        color: rgb(221, 221, 221);
    }

    &::-ms-input-placeholder {
        color: rgb(221, 221, 221);
    }
}

.account-holder {
    cursor: pointer;
}

.click-update-msg {
    padding: 5px 0px 5px 5px;
    color: var(--primary-color);
    font-size: 14px;
}

.click-update {
    cursor: pointer;
    background-color: #ffff8d;
}
</style>
