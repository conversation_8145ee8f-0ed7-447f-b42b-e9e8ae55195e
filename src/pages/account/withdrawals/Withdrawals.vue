<template>
    <router-view v-if="completedFirstPurchase && !isAddBank" />
    <div v-else class="container">
        <p class="font-28 font-weight-bold">{{ isAddBank ? $t('WITHDRAWALS.BANK_ACCOUNT') : $t('WITHDRAWALS.WITHDRAWALS') }}
        </p>
        <div v-if="!completedFirstPurchase" class="content text-center">
            <p class="no-transactions mb-5 text-center">{{
                $t('WITHDRAWALS.MAKE_YOUR_FIRST_TRANSACTION_TO_WITHDRAWAL_MONEY')
            }}
            </p>
            <router-link :to="{ name: 'marketplace' }">
                <b-button id="btn_withdrawals_noWithdrawal_GoToMarketplace" class="bg-main-color color-white" variant="light" style="padding: 10px">
                    {{ $t("account.marketplace") }}</b-button>
            </router-link>
        </div>
        <div v-else class="content d-flex flex-column align-items-center">
            <div v-if="hasOwnTokens" class="col-12 col-md-12 col-lg-10 p-0">
                <AddForeignerBankAccount v-if="isForeigner"></AddForeignerBankAccount>
                <div v-else class="col-12 d-flex flex-column align-items-center p-0">
                    <div class="col-12 col-lg-8 d-flex text-center flex-column align-items-center">
                        <p class="font-24 font-weight-bold text-center mb-3">{{ $t('WITHDRAWALS.ADD_BANK_ACCOUNT') }}</p>
                        <div class="col-12 text-center warning-card">
                            <p class="title font-bold">{{ $t('WITHDRAWALS.BANK_WARNING_MESSAGE') }}</p>
                        </div>
                        <div class="col-12 text-left card-container">
                            <p class="title">{{ $t('WITHDRAWALS.SELECT_BANK_NAME') }}</p>
                            <v-select class="goro-select" v-model="bank" :options="banks" :clearable="false" label="name" :style="{height: '60px'}">
                                <template v-slot:option="option">
                                    {{ option.name }}
                                </template>
                                <template #selected-option="option">
                                    {{ option.name }}
                                </template>
                            </v-select>
                            <p class="title mt-3">{{ $t('WITHDRAWALS.BANK_ACCOUNT_NUMBER') }}</p>
                            <input class="account-number" minlength="8" maxlength="16" v-model="accountNumber"
                                @keypress="isNumber($event)" @paste="onPaste" placeholder="1111-2222-3333-4444" />
                            <p v-if="accountNumberError" class="error">{{ accountNumberError }}</p>
                        </div>
                        <div class="text-center">
                            <b-button id="btn_withdrawals_addBankAccount" class="bg-main-color color-white mt-5" @click="addBankAccount(false)" variant="light"
                                style="padding: 10px">
                                {{ $t("WITHDRAWALS.ADD_BANK_ACCOUNT") }}</b-button>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else>
                <p v-if="!loadingOwningTokens" class="no-transactions mb-4">{{
                    $t("WITHDRAWALS.MESSAGE_CAN_NOT_ADD_BANK_ACCOUNT") }}</p>
                <router-link v-if="!loadingOwningTokens" :to="{ name: 'buyProperty' }">
                    <b-button id="btn_withdrawals_cannotAddBankAccount_GoToMarketplace" class="btn-main" type="submit" variant="none">
                        {{ $t("account.marketplace") }}
                    </b-button>
                </router-link>
            </div>
        </div>
        <PopupConfirmAddBank ref="popupConfirmBank" @on-confirmed="addBankAccount(true)"></PopupConfirmAddBank>
    </div>
</template>

<script>

import withdrawalsService from "../../../services/withdrawals.service"
import accountService from "../../../services/account.service"
import { gtmTrackEvent } from "../../../helpers/gtm"
import { GTM_EVENT_NAMES } from "../../../constants/gtm"
import { PAYMENT_METHOD } from '../../../constants/constants';
import PopupConfirmAddBank from "../../../components/PopupConfirmAddBank.vue"
import AddForeignerBankAccount from '../../../components/AddForeignerBankAccount'

export default {
    components: {
        PopupConfirmAddBank,
        AddForeignerBankAccount,
    },
    data() {
        return {
            banks: this.$store.getters.availableBanks,
            bank: this.$store.getters.availableBanks[0],
            accountNumber: '',
            accountNumberError: null,
            owningTokens: 0,
            loadingOwningTokens: true,
        };
    },

    async mounted() {
        await this.getOwningTokens()
    },

    methods: {
        isNumber(evt) {
            evt = (evt) ? evt : window.event;
            const charCode = (evt.which) ? evt.which : evt.keyCode;
            if (charCode > 31 && (charCode < 48 || charCode > 57)) {
                evt.preventDefault();
            } else {
                return true;
            }
        },

        onPaste(evt) {
            let number = evt.clipboardData.getData('text').replace(/\D/g, '')
            if (number.length > 16) {
                number = number.substring(0, 16)
            }
            setTimeout(() => {
                this.accountNumber = number
            }, 50)
        },

        async addBankAccount(confirmed) {
            this.validate();
            if (this.accountNumberError) {
                return
            }
            if (confirmed) {
                if (!this.accountNumberError && this.bank) {
                    const res = await withdrawalsService.addBankAccount({
                        bank_name: this.bank.name,
                        channel_code: this.bank.code,
                        account_number: this.accountNumber,
                    });
                    if (res) {
                        await this.$store.dispatch('refreshUserProfile');
                        await this.$router.push({ name: 'bankAccountHistory' })
                    }
                    gtmTrackEvent({
                        event: GTM_EVENT_NAMES.ADD_BANK_ACCOUNT
                    })
                }
            } else {
                this.$refs.popupConfirmBank.openPopup(this.accountNumber, this.bank.name)
            }
        },

        validate() {
            this.checkAccountNumber(this.accountNumber);
        },

        checkAccountNumber(value) {
            if (value.length < 8) {
                this.accountNumberError = this.$t('WITHDRAWALS.ACCOUNT_NUMBER_MUST_AT_LEAST_8_DIGITS');
            } else {
                this.accountNumberError = null;
            }
        },

        async getOwningTokens() {
            const res = await accountService.getOwningTokens()
            if (res) {
                this.owningTokens = res.data
            }
            this.loadingOwningTokens = false
        },
    },

    watch: {
        accountNumber(value) {
            this.checkAccountNumber(value);
        },
    },

    computed: {

        completedFirstPurchase() {
            return this.$store.getters.userProfile && this.$store.getters.userProfile.completed_first_purchase;
        },

        isAddBank() {
            return this.$route.query.action === 'add';
        },

        hasOwnTokens() {
            return this.owningTokens > 0
        },

        userProfile() {
            return this.$store.getters.userProfile;
        },

        isForeigner() {
            if (this.userProfile && this.userProfile.payment_method === PAYMENT_METHOD.STRIPE) {
                return true;
            }
            return false;
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    margin-top: 20px;

    .content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding-top: 20px;
        padding-bottom: 20px;
        padding-left: 5px;
        padding-right: 5px;
        margin-top: 15px;

        .warning-card {
            background-color: #FF9324;
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);

            .title {
                font-size: 18px;
                color: white;
                text-transform: uppercase;
                text-align: center;
            }
        }

        .card-container {
            background-color: var(--primary-darker-color);
            padding-bottom: 30px;
            padding-top: 30px;
            border-radius: 15px;
            margin-top: 30px;
            box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);

            .account-number {
                width: 100%;
                background-color: #ebecf0;
                border: none;
                border-radius: 8px;
                padding-left: 1rem;
                padding-right: 1rem;
                color: var(--primary-color);
                min-height: 60px;
                font-size: 32px;
                font-weight: 700;
                letter-spacing: 0.07em;
                text-transform: uppercase;

                &::placeholder {
                    color: rgb(221, 221, 221);
                    opacity: 1;
                    /* Firefox */
                }

                &:-ms-input-placeholder {
                    color: rgb(221, 221, 221);
                }

                &::-ms-input-placeholder {
                    color: rgb(221, 221, 221);
                }
            }

            .title {
                font-size: 18px;
                margin-bottom: 10px;
                color: white;
                text-transform: uppercase;
            }

            .account-holder {
                font-size: 35px;
                color: white;
                font-weight: bold;
                text-align: center;
            }

            .error {
                font-size: 15px;
                color: red;
            }
        }
    }
}
</style>
