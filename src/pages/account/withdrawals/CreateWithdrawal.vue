<template>
  <div class="container">
    <p class="font-28 font-weight-bold">{{ $t('WITHDRAWALS.CREATE_WITHDRAWAL') }}</p>
    <div class="d-flex flex-column align-items-center">
      <p v-if="confirming" class="font-32 font-weight-bold text-center">{{ $t('WITHDRAWALS.WITHDRAWAL_CONFIRMATION') }}</p>
      <div v-if="confirming" class="warning-note pr-0 pl-0">
        <div class="notice-important-block medium-notice mt-4 text-center">
          <h3 class="title font-23 text-uppercase mb-0 text-center">
            {{ $t("WITHDRAWALS.NOTICE_WITHDRAW_TAKE_TIME", { take_time: 72 }) }}
          </h3>
        </div>
      </div>
      <div class="d-flex flex-column content">
        <div v-if="!confirming">
          <div class="d-flex flex-row justify-content-between align-items-center">
            <div class="d-flex flex-column align-items-start">
              <p class="font-28 font-weight-bold">{{ $t('WITHDRAWALS.WITHDRAWAL_FORM') }}</p>
              <p class="mandatory-note mb-3">{{ $t('WITHDRAWALS.MANDATORY') }}</p>
            </div>
            <div class="d-flex flex-column align-items-end">
              <p class="title">{{ $t('WITHDRAWALS.CURRENT_BALANCE') }}</p>
              <p id="current-balance"><strong class="amount">{{ exchangeValue(userBalanceAmount, 'IDR') }}</strong></p>
              <CurrencyTooltip tooltipId="current-balance" :value="userBalanceAmount" :show-others="true">
              </CurrencyTooltip>
            </div>
          </div>
          <div class="divider mb-3 mt-2"></div>
          <p class="title">{{ $t('WITHDRAWALS.INPUT_AMOUNT_TO_WITHDRAWAL') }}(IDR)<span style="color: red;">*</span>
          </p>
          <input class="amount-input" v-model="amount"
                 :placeholder="minWithdrawalAmount" @keypress="isNumber($event)" type="number" :disabled="confirming"/>
          <p class="withdrawal-min">{{ $t('WITHDRAWALS.MINIMUM_WITHDRAWAL_AMOUNT') }}:
            <span id="min-withdrawal-value">{{ exchangeValue(minWithdrawalAmount, 'IDR') }}</span>
            <CurrencyTooltip tooltipId="min-withdrawal-value" :value="minWithdrawalAmount" :show-others="true"></CurrencyTooltip>
          </p>
          <div class="d-flex align-items-center mt-3">
            <p class="title">{{ $t('WITHDRAWALS.WITHDRAWAL_DESCRIPTION') }}<span style="color: red;">*</span></p>
            <img id="withdrawal-description-info" class="img-info ml-1" src="@/assets/img/info-circle-fill.png" alt="">
            <b-tooltip variant="secondary" target="withdrawal-description-info" triggers="hover" placement="right">
              {{ $t('WITHDRAWALS.WITHDRAWAL_DESCRIPTION_TOOLTIP') }}
            </b-tooltip>
          </div>
          <textarea v-model="description" :placeholder="$t('WITHDRAWALS.WANT_TO_ENJOY_RETURNS')" :disabled="confirming">
          </textarea>
          <promo-code
              v-if="enableVoucherCodeInput"
              :voucherCodeInput="voucherCodeInput"
              @update:modelValue="$event => {
                  voucherCodeInput = $event
                  voucherCodeError = null
                }"
              :error="voucherCodeError"
              @on-apply="validateVoucherCode"
              @on-clear-voucher="clearVoucherCodeInfo"
              :voucher-code="voucherCode"
              :reward-note="voucherRewardNote"
              :showIcon="voucherCode"
              titleColor="#8f8f8f"
              :boldTitle="voucherCode"
              class="mt-2">
          </promo-code>
          <div class="divider my-4"></div>
          <b-row v-if="isForeigner && configuredBankAccount" align-h="center" class="mb-3">
            <input id="method-bank-account" type="radio" v-model="method" :value="1" @click="method = 1"/>
            <label style="color:var(--primary-color)" for="method-bank-account">
              {{ $t('WITHDRAWALS.BANK_ACCOUNT') }}
            </label>
            <div class="ml-3"></div>
            <input id="method-paypal" type="radio" v-model="method" :value="2" @click="method = 2"/>
            <label style="color:var(--primary-color)" for="method-paypal">
              PayPal
            </label>
          </b-row>
          <div v-if="!isPayPal" class="bank-account p-0 m-0">
            <div class="d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.BANK_ACCOUNT_HOLDER_NAME') }}:</p>
              <p class="value col-12 col-lg-6 font-semibold">{{ accountHolderName }}</p>
            </div>
            <div class="mt-2 d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.BANK_ACCOUNT_NUMBER') }}:</p>
              <p class="value col-12 col-lg-6 font-semibold">{{ bankAccountNumber }}</p>
            </div>
            <div class="mt-2 d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.BANK_NAME') }}:</p>
              <p class="value col-12 col-lg-6 font-semibold">{{ bankName }}</p>
            </div>
            <div v-if="isForeignerBank" class="mt-2 d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.BANK_COUNTRY') }}:</p>
              <p class="value col-12 col-lg-6 font-semibold">{{ bankCountry }}</p>
            </div>
            <div v-if="isForeignerBank" class="mt-2 d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.BANK_ADDRESS') }}:</p>
              <p class="value col-12 col-lg-6 font-semibold">{{ bankAddress }}</p>
            </div>
            <div v-if="isForeignerBank" class="mt-2 d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">SWIFT Code:</p>
              <p class="value col-12 col-lg-6 font-semibold">{{ swiftCode }}</p>
            </div>
            <div v-if="isForeignerBank" class="mt-2 d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.NOTES') }}:</p>
              <p class="value col-12 col-lg-6 font-semibold">{{ notes }}</p>
            </div>
          </div>
          <div v-if="isPayPal" class="bank-account p-0">
            <div class="d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.PAYPAL_ACCOUNT_EMAIL') }}:</p>
              <p class="value col-12 col-lg-6 font-semibold">{{ email }}</p>
            </div>
          </div>
          <div class="warning d-flex align-items-center justify-content-center mt-3">
            <img class="mr-3" src="@/assets/img/info-circle.svg" alt="" width="36" height="36">
            <p>{{ $t('WITHDRAWALS.PLEASE_ENSURE_YOUR_BANK_CORRECT') }}</p>
          </div>
          <div class="d-flex flex-row justify-content-between fee">
            <p class="mt-3 title">{{ $t('WITHDRAWALS.WITHDRAWAL_FEE') }}{{ processingFeePercent }}</p>
            <p v-if="withdrawalDetails.processingFee <= 0" id="withdrawal-fee" class="mt-3 d-flex flex-column align-items-end">
              <strong class="amount">
                {{ $t("common.FREE") }}
              </strong>
            </p>
            <p v-if="withdrawalDetails.processingFee > 0" id="withdrawal-fee" class="mt-3 d-flex flex-column align-items-end">
              <strong class="amount">
                -{{ exchangeValue(withdrawalDetails.processingFee) }}
              </strong>
            </p>
            <CurrencyTooltip v-if="withdrawalDetails.processingFee > 0" tooltipId="withdrawal-fee" :value="withdrawalDetails.processingFee"></CurrencyTooltip>
          </div>
          <div v-if="voucherCode" class="d-flex flex-row justify-content-between">
            <div>
              <p class="mt-3 title">{{ $t('VOUCHER.PROMO_CODE') }}</p>
              <p class="voucher-note">{{ voucherRewardNote }}</p>
            </div>
            <p id="voucher-reward" class="mt-3 ml-3 d-flex flex-column align-items-end">
              <strong class="amount">{{ exchangeValue(withdrawalDetails.voucherRewardAmount) }}</strong>
            </p>
            <CurrencyTooltip tooltipId="voucher-reward" :value="withdrawalDetails.voucherRewardAmount"></CurrencyTooltip>
          </div>
          <div v-if="showExchanged" class="d-flex flex-row justify-content-between fee">
            <p class="mt-3 title">{{ $t('WITHDRAWALS.EXCHANGE_RATES') }}</p>
            <p id="withdrawal-fee" class="mt-3"><strong class="rates">
              {{ userInfo.preferred_currency }}1 = IDR{{ exchangeRates }}</strong>
            </p>
          </div>
          <p v-if="showExchanged" class="font-14 mt-1">{{ $t('WITHDRAWALS.EXCHANGE_RATES_NOTE') }}</p>
        </div>
        <div v-else>
          <p class="font-28 font-weight-bold text-center mb-2">{{ $t('WITHDRAWALS.WITHDRAWAL_SUMMARY') }}</p>
          <div class="divider my-3"></div>
          <div class="bank-account">
            <div class="d-flex flex-column flex-lg-row">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.WITHDRAWAL_AMOUNT') }}:</p>
              <p id="withdrawal-amount" class="value col-12 col-lg-6 font-weight-bold font-22">{{ exchangeValue(withdrawalAmount) }}</p>
              <CurrencyTooltip tooltipId="withdrawal-amount" :value="withdrawalAmount"></CurrencyTooltip>
            </div>
            <div class="d-flex flex-column flex-lg-row mt-2">
              <p class="title col-12 col-lg-6">{{ $t('WITHDRAWALS.WITHDRAWAL_FEE') }}</p>
              <p v-if="withdrawalDetails.processingFee <= 0" id="withdrawal-fee-2" class="value col-12 col-lg-6 font-weight-bold font-22">
                <span>{{ $t("common.FREE") }}</span>
              </p>
              <p v-if="withdrawalDetails.processingFee > 0" id="withdrawal-fee-2" class="value col-12 col-lg-6 font-weight-bold font-22">
                <span>-{{ exchangeValue(withdrawalDetails.processingFee) }}</span>
              </p>
              <CurrencyTooltip v-if="withdrawalDetails.processingFee > 0" tooltipId="withdrawal-fee-2" :value="withdrawalDetails.processingFee"></CurrencyTooltip>
            </div>
            <div v-if="voucherCode" class="d-flex flex-row justify-content-between">
              <div>
                <p class="mt-2 title">{{ $t('VOUCHER.PROMO_CODE') }}</p>
                <p class="voucher-note">{{ voucherRewardNote }}</p>
              </div>
              <p id="voucher-reward" class="mt-3 ml-3 d-flex flex-column align-items-end">
                <strong class="amount">{{ exchangeValue(withdrawalDetails.voucherRewardAmount) }}</strong>
              </p>
              <CurrencyTooltip tooltipId="voucher-reward" :value="withdrawalDetails.voucherRewardAmount"></CurrencyTooltip>
            </div>
          </div>
          <div class="divider my-3"></div>
          <div class="bank-account">
            <div class="d-flex align-items-center">
              <p class="flex-grow-1 font-weight-bold p-0">{{ $t('WITHDRAWALS.RECEIVE_AMOUNT') }}:</p>
              <p id="withdrawal-receive" class="value font-weight-bold font-24">{{ exchangeValue(withdrawalDetails.receiveAmount) }}</p>
              <CurrencyTooltip tooltipId="withdrawal-receive" :value="withdrawalDetails.receiveAmount"></CurrencyTooltip>
            </div>
          </div>
          <div class="divider my-3"></div>
          <div class="bank-account">
            <p class="title">{{ $t('WITHDRAWALS.WITHDRAWAL_DESCRIPTION') }}:</p>
            <p>{{ description }}</p>
          </div>
          <div class="bank-account mt-2" v-if="voucherCode">
            <p class="title">{{ $t('VOUCHER.PROMO_CODE') }}:</p>
            <p>{{ voucherRewardNote }}</p>
          </div>
          <div class="divider my-3"></div>
          <div class="bank-account">
            <p class="title">{{ $t('WITHDRAWALS.ACCOUNT_BALANCE_LEFT') }}:</p>
            <p id="balance-left" class="font-weight-bold font-22">{{ exchangeValue(withdrawalDetails.balanceLeftAmount) }}</p>
            <CurrencyTooltip tooltipId="balance-left" :value="withdrawalDetails.balanceLeftAmount"></CurrencyTooltip>
          </div>
        </div>
        <div class="d-flex flex-row justify-content-end mt-5">
          <b-button v-if="confirming" class="btn-outline-white btn-next mr-3" @click="onBack">{{ $t('common.PREVIOUS') }}</b-button>
          <b-button v-if="!confirming" class="bg-main-color color-white btn-next" @click="onNext" variant="light" :disabled="!withdrawalDetails.enableWithdrawal">
            {{ $t('common.NEXT') }}
          </b-button>
          <b-button v-else class="bg-main-color color-white btn-next" @click="onNext" variant="light">
            {{ $t('common.PROCEED') }}
          </b-button>
        </div>
      </div>
    </div>
    <PopupSecurityPin ref="popUpPinSecurity" @on-success="onVerified"></PopupSecurityPin>
  </div>
</template>

<script>

import {exchange, getErrorMessage, notify, numberWithCommas} from '@/helpers/common'
import withdrawalsService from '../../../services/withdrawals.service';
import voucherService from "@/services/voucher.service";
import CurrencyTooltip from "../../../components/CurrencyTooltip.vue"
import PopupSecurityPin from '@/components/PopupSecurityPin.vue'
import store from '../../../store/store';
import {BANK_TYPE, PAYMENT_METHOD, VOUCHER} from "@/constants/constants"
import i18n from "../../../i18n"
import PromoCode from '../../../components/PromoCode.vue';
import {calculateFee} from "@/helpers/fee";
import {calculateVoucherRewardAmount, getVoucherRewardNote} from "@/helpers/voucher";
import authService from "@/services/auth.service";

export default {
  components: {
    CurrencyTooltip,
    PopupSecurityPin,
    PromoCode,
  },
  data () {
    return {
      amount: '',
      description: '',
      voucherCodeInput: "",
      voucherCode: null,
      voucherCodeError: null,
      withdrawalAmount: 0,
      confirming: false,
      email: store.state.userProfile.email,
      method: store.state.userProfile.payment_method !== PAYMENT_METHOD.STRIPE || store.state.userProfile.bank_account ? 1 : 2,
    };
  },
  async mounted() {
    await Promise.all([this.getUserProfile()])
  },
  methods: {
    isNumber (evt) {
      evt = (evt) ? evt : window.event;
      const charCode = (evt.which) ? evt.which : evt.keyCode;
      if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        evt.preventDefault();
      } else {
        return true;
      }
    },
    exchangeValue (value, currency = null) {
      return exchange(value, 100, false, currency)
    },
    async getUserProfile () {
      const res = await authService.getUserProfile(false)
      if (res && res.data) {
        await store.dispatch('setUserProfile', res.data);
      }
    },
    clearVoucherCodeInfo () {
      this.voucherCodeInput = "";
      this.voucherCode = null;
      this.voucherCodeError = null;
    },
    async validateVoucherCode () {
      try {
        const response = await voucherService.validateVoucherCode({ voucher_code: this.voucherCodeInput, action: VOUCHER.REDEEM_ACTION_WITHDRAWAL }, true);
        if (response.voucher_code) {
          this.voucherCode = response.voucher_code
          this.voucherCodeError = null
        }
      } catch (error) {
        this.voucherCode = null
        this.voucherCodeError = getErrorMessage(error)
      }
    },
    isValidWithdrawal () {
      if (this.withdrawalDetails.receiveAmount < 0) {
        notify({ text: "Your withdrawal result is negative. Please adjust the withdrawal amount or any fees before proceeding.", type: "error" })
        return false
      } else if (this.withdrawalDetails.balanceLeftAmount < 0) {
        notify({ text: "You do not have enough balance to cover this withdrawal. Please enter a smaller amount.", type: "error" })
        return false
      } else if (this.voucherCodeError) {
        return false
      } else if (this.voucherCode && this.withdrawalDetails.voucherRewardAmount <= 0) {
        notify({ text: this.$t("VOUCHER.WARNING_NO_DISCOUNT"), type: "error" })
        return false
      }
      return true
    },
    async onNext () {
      if (!this.confirming) {
        this.confirming = true;
      } else {
        if (this.isValidWithdrawal()) {
          await this.$refs.popUpPinSecurity.openPopup(true)
        }
      }
    },
    onBack () {
      this.confirming = false;
    },
    async onVerified (pin) {
      const res = await withdrawalsService.createWithdrawal({
        amount: this.withdrawalAmount,
        description: this.description,
        voucher_code: this.voucherCodeInput,
        pin,
        is_paypal: this.isPayPal,
        can_input_all: true,
      });
      if (res && res.data) {
        this.$router.push({ name: 'withdrawalHistory' });
      }
    },
  },

  watch: {
    amount (value) {
      if (value !== '') {
        value = parseInt(value);
        if (value > this.userBalanceAmount) {
          value = this.userBalanceAmount;
        }
        if (value < 0) {
          value = 0;
        }
        this.amount = `${value}`;
        this.withdrawalAmount = value;
      } else {
        this.withdrawalAmount = 0;
      }
    },
  },

  computed: {
    userInfo () {
      return store.state.userProfile
    },
    userBalanceAmount () {
      return parseInt(store.state.userProfile && store.state.userProfile.balance || '0');
    },
    minWithdrawalAmount () {
      return parseInt(store.state.configs && store.state.configs.minimum_withdrawal_amount || '0');
    },
    isForeigner () {
      return store.state.userProfile.payment_method === PAYMENT_METHOD.STRIPE
    },
    isForeignerBank () {
      return this.configuredBankAccount && store.state.userProfile.bank_account.bank_type === BANK_TYPE.FOREIGNER
    },
    configuredBankAccount () {
      return store.state.userProfile.bank_account
    },
    bankAccount () {
      return store.state.userProfile && store.state.userProfile.bank_account
    },
    bankName () {
      return this.bankAccount && this.bankAccount.bank_name;
    },
    bankAccountNumber () {
      return this.bankAccount && this.bankAccount.account_number;
    },
    accountHolderName () {
      return this.bankAccount && this.bankAccount.account_holder_name;
    },
    bankCountry () {
      return this.bankAccount && this.bankAccount.bank_country;
    },
    bankAddress () {
      return this.bankAccount && this.bankAccount.bank_address;
    },
    swiftCode () {
      return this.bankAccount && this.bankAccount.swift_code;
    },
    notes () {
      return this.bankAccount && this.bankAccount.note;
    },
    showExchanged () {
      return this.userInfo.preferred_currency !== 'IDR'
    },
    exchangeRates () {
      const rates = store.state.exchangeRates;
      const currency = this.userInfo.preferred_currency;
      const rate = rates[currency];
      if (!rate) {
        return 0
      }
      if (currency !== 'IDR') {
        const locale = i18n.global.locale.value
        const separator = locale === 'id' ? '.' : ','
        return numberWithCommas(Math.round(1 / rate), separator)
      }
      return 1
    },
    isPayPal () {
      return this.method === 2
    },
    payPalFeePercent () {
      return store.state.configs && store.state.configs.paypal_withdrawal_fee_percent || 0
    },
    processingFeePercent () {
      if (!this.isPayPal) {
        return ''
      }
      return ` (${this.payPalFeePercent}%)`
    },
    enableVoucherCodeInput () {
      return store.state.configs.enable_voucher_code_input
    },
    withdrawalDetails () {
      const baseAmount = this.withdrawalAmount
      const userBalanceAmount = this.userBalanceAmount
      const balanceLeftAmount = userBalanceAmount - baseAmount;
      const displayTransactionFees = false // NOT apply yet (this.$store.getters.configs.display_transaction_fees)
      const transactionFeeConfig = '' // NOT apply yet (this.$store.getters.configs.withdrawal_transaction_fees)
      const transactionFee = displayTransactionFees ? calculateFee(transactionFeeConfig, baseAmount) : 0
      let voucherRewardAmount = 0
      let voucherRewardType = this.voucherCode?.voucher?.reward_type
      let voucherRequiredTransactionAmount = this.voucherCode?.voucher?.required_transaction_amount || 0;
      if (baseAmount >= voucherRequiredTransactionAmount) {
        switch (voucherRewardType) {
          case VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES:
          //case VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_WITHDRAWAL:
              voucherRewardAmount = Math.min(calculateVoucherRewardAmount(this.voucherCode, transactionFee), transactionFee)
              break;
        }
      }
      // External gateway processing fees
      let processingFee = 0
      if (!this.isPayPal) {
        if (this.isForeignerBank) {
          processingFee = parseInt(store.state.configs && store.state.configs.foreigner_bank_account_withdrawal_fee || '0');
        } else {
          processingFee = parseInt(store.state.configs && store.state.configs.withdrawal_fee || '0');
        }
      } else {
        processingFee = Math.round(this.withdrawalAmount * (this.payPalFeePercent / 100))
      }
      // Apply Voucher to reduce external fee
      if (baseAmount >= voucherRequiredTransactionAmount) {
        if (voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_FEE_WITHDRAWAL) {
          voucherRewardAmount = Math.min(calculateVoucherRewardAmount(this.voucherCode, processingFee), processingFee)
        } else if (voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES) {
          const usedRewardAmount = voucherRewardAmount // previously used for transaction fee
          voucherRewardAmount += Math.min(calculateVoucherRewardAmount(this.voucherCode, processingFee, usedRewardAmount), processingFee)
        }
      }

      const receiveAmount = Math.max(0, baseAmount - transactionFee - processingFee + voucherRewardAmount);
      const enableWithdrawal = balanceLeftAmount >= 0 && this.withdrawalAmount >= this.minWithdrawalAmount
          && !this.voucherCodeError && (!this.voucherCode || voucherRewardAmount > 0) && this.description !== ''

      return { baseAmount, receiveAmount, processingFee, voucherRewardAmount, balanceLeftAmount, enableWithdrawal }
    },
    // transactionFeeNote () {
    //   return getTransactionFeeNote(this.withdrawalDetails.displayTransactionFees,
    //       this.withdrawalDetails.transactionFeeConfig, this.withdrawalDetails.transactionFee, this.$t, this.exchangeValue)
    // },
    voucherRewardNote () {
      return getVoucherRewardNote(this.voucherCode, VOUCHER.REDEEM_ACTION_WITHDRAWAL, this.$t, this.exchangeValue)
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  margin-top: 20px;
  max-width: 100% !important;

  .warning-note {
    width: 98%;
    @media screen and (min-width: 600px) {
      width: 80%;
    }

    @media screen and (min-width: 800px) {
      width: 70%;
    }

    @media screen and (min-width: 1100px) {
      width: 50%;
    }

    @media screen and (min-width: 1300px) {
      width: 40%;
    }
  }

  .content {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding: 30px;
    margin-top: 15px;
    margin-bottom: 15px;
    color: var(--primary-color);
    width: 98%;

    @media screen and (min-width: 600px) {
      width: 98%;
    }

    @media screen and (min-width: 800px) {
      width: 75%;
    }

    @media screen and (min-width: 1100px) {
      width: 65%;
    }

    @media screen and (min-width: 1300px) {
      width: 50%;
    }

    p {
      font-size: 18px;
      font-weight: 500;
    }

    .title {
      color: #8f8f8f;
      padding: 0;
    }

    .mandatory-note {
      color: red;
      font-size: 14px;
    }

    .amount {
      font-size: 23px;
      color: #006666;
    }

    .rates {
      font-size: 18px;
      color: #006666;
    }

    .fee {
      border-top: 1px solid #C6C6C6;
      margin-top: 20px;
      padding-top: 4px;
    }

    .amount-input {
      width: 100%;
      background-color: #ebecf0;
      border: none;
      border-radius: 8px;
      padding-left: 1rem;
      padding-right: 1rem;
      color: var(--primary-color);
      min-height: 60px;
      font-size: 36px;
      font-weight: 700;
      letter-spacing: 0.07em;
      text-align: end;

      &.email-input {
        font-size: 20px;
      }

      &::placeholder {
        color: rgb(202, 202, 202);
        opacity: 1;
        /* Firefox */
      }

      &:-ms-input-placeholder {
        color: rgb(202, 202, 202);
      }

      &::-ms-input-placeholder {
        color: rgb(202, 202, 202);
      }

      &.confirming {
        background-color: #f7f7f8b6;
      }
    }

    textarea {
      width: 100%;
      background-color: #ebecf0;
      border: none;
      border-radius: 8px;
      padding: 0.5rem;
      color: var(--primary-color);
      min-height: 100px;
      font-size: 16px;

      &::placeholder {
        color: rgb(202, 202, 202);
        opacity: 1;
        /* Firefox */
      }

      &:-ms-input-placeholder {
        color: rgb(202, 202, 202);
      }

      &::-ms-input-placeholder {
        color: rgb(202, 202, 202);
      }

      &:focus {
        outline: none !important;
        border: none;
      }

      &.confirming {
        background-color: #f7f7f8b6;
      }
    }

    /* Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Firefox */
    input[type=number] {
      -moz-appearance: textfield;
    }

    .withdrawal-min {
      font-size: 13px;
      color: #a0a0a0;
      text-align: end;
    }

    .bank-account {
      .title {
        color: #8f8f8f;
      }

      .value {
        color: var(--primary-color);
        text-align: end;
        overflow-wrap: break-word;
        padding: 0;
        margin: 0;
      }

      .value-child-invalid {
        color: gray;
        text-decoration: line-through;
      }
    }

    .voucher-note {
      font-size: 13px;
      color: #a0a0a0;
    }

    .warning {
      padding: 16px;
      background-color: #FFEED9;
      border: #FFA705 solid 1px;
      border-radius: 8px;
      color: #3F3F3F;
    }

    .btn-next {
      padding: 10px 30px 10px 30px;
      width: 100%;
      border-radius: 10px;
    }

    .divider {
      background-color: #C6C6C6;
      height: 1px;
      width: 100%;
    }

    .img-info {
      width: 20px;
      height: 20px;
      background-color: transparent;
    }
  }
}

.text-error {
  color: red;
  font-size: 0.8rem;
}

[type="radio"]:checked,
[type="radio"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="radio"]:checked + label,
[type="radio"]:not(:checked) + label {
  position: relative;
  padding-left: 28px;
  cursor: pointer;
  line-height: 20px;
  display: inline-block;
  color: #666;
}

[type="radio"]:checked + label:before,
[type="radio"]:not(:checked) + label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid var(--primary-color);
  border-radius: 100%;
  background: #fff;
}

[type="radio"]:checked + label:after,
[type="radio"]:not(:checked) + label:after {
  content: '';
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  position: absolute;
  top: 3px;
  left: 3px;
  border-radius: 100%;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="radio"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="radio"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.strikethrough {
  text-decoration: line-through;
}

.compound-tooltip-icon {
  margin-bottom: 2px;
  width: 15px;
  height: 15px;
  z-index: 1;
}
</style>
