<template>
  <b-container class="cls-single-page-container cls-registration-complete-container p-1 p-md-3 p-lg-3 mt-3 mb-3">
    <b-row class="text-center" align-h="center">
      <b-col cols="12" lg="7" class="cls-page-wrapper">
        <div class="cls-page-content-wrapper no-boder">
          <img class="icon" width="298" src="@/assets/img/account/registration-complete.png" alt="Registration complete"/>
          <div class="cls-content">
            <h1 class="title font-24 mt-4 mt-md-5 mb-3">
              {{ $t("AUTH.REGISTRATION_COMPLETE.HEADLINE") }}
            </h1>
            <p class="text font-20">
              {{ $t("AUTH.REGISTRATION_COMPLETE.TEXT") }}
            </p>
          </div>
          <div class="redirect mt-4 mt-md-5">
            <p class="text font-20" v-html="redirectContent()"></p>
          </div>
        </div>
      </b-col>
    </b-row>
  </b-container>
</template>

<script>
  import { gtmTrackEvent } from "@/helpers/gtm"
  import { GTM_EVENT_NAMES } from "@/constants/gtm"

  export default {
    data() {
      return {
        redirectInSeconds: 3,
        redirectTo: null
      }
    },
    async mounted() {
      this.redirectTo = this.$router.resolve({ name: 'assetsOverview' }).href
      if (this.$route.query && this.$route.query.redirect) {
        this.redirectTo = this.$router.resolve(this.$route.query.redirect).href
      }

      if (!this.showRegistrationComplete) {
        await this.$router.push(this.redirectTo)
      } else {
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_REGISTRATION_COMPLETE,
        })

        this.startCountdown()
      }
    },
    methods: {
      redirectContent() {
        return this.$t("AUTH.REGISTRATION_COMPLETE.REDIRECT_TEXT", {
          seconds: this.redirectInSeconds,
          link: `<a href="${this.redirectTo}" style="color:#0066FF;">${this.$t("AUTH.REGISTRATION_COMPLETE.CLICK_HERE")}</a>` 
        })
      },
      startCountdown() {
        const countdown = setInterval(async () => {
          if (this.redirectInSeconds > 1) {
            this.redirectInSeconds--
          } else {
            clearInterval(countdown)
            await this.$router.push(this.redirectTo)
            await this.$store.dispatch("setShowRegistrationComplete", false)
          }
        }, 1000)
      },
    },
    computed: {
      showRegistrationComplete() {
        return this.$store.getters.showRegistrationComplete
      },
    },
  }
</script>

<style lang="scss" scoped>
  .cls-registration-complete-container{
    *{
      font-family: "NewAcuminVariableConcept", Helvetica, sans-serif;
    }
    .cls-page-wrapper{
      .cls-page-content-wrapper{
        @media(max-width: 991px) {
          padding-top: 0 !important;
          margin-top: 5px !important;
        }
        img.icon{
          width: 298px;
          @media(max-width: 991px) {
            width: 240px;
          }
        }
        .cls-content{
          .title{
            font-weight: 500;
            line-height: 24px;
            text-align: center;
            color: #000000;
          }
          .text{
            font-weight: 382;
            line-height: 28px;
            text-align: center;
            color: #000000;
          }
        }
        .redirect{
          .text{
            font-weight: 300;
            font-style: italic;
            line-height: 28px;
            text-align: center;
            color: #000000;
            .a{
              color: #0066FF !important;
            }
          }
        }
      }
    }

  }
</style>
