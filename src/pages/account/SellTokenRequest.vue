<template>
  <div class="cls-order-form-page sell-form mt-0 mb-4" v-if="property.id">
    <b-container fluid v-if="!isConfirmSellRequest" class="cls-order-form-container">
      <b-row class="cls-order-form-content">
        <b-col cols="12" class="pl-0 pr-0">
          <h1 class="font-28 font-weight-bold mb-4 page-heading">
            {{ $t("SELL_TOKEN.REQUEST_SELL_TOKEN") }}
          </h1>
        </b-col>
        <div class="content-left">
          <div class="cls-block-small">
            <div class="cls-token-order-wrapper mb-4">
              <div class="cls-property-order-item image-container" style="width: 100%">
                <b-img :src="getPropertyImage(property.images)" fluid blank-src="property image" class="img-property"/>
                <b-row align-h="between" class="bottom-info">
                  <b-col cols="12">
                    <p class="font-24 font-weight-bold priority-name">{{ property.name }}</p>
                    <p class="priority-address font-14" @click="openGoogleMap(property)">{{ property.metadata.address }}</p>
                  </b-col>
                </b-row>
              </div>
              <b-row align-h="between" align-v="center" class="order-token-quantity-row pt-4 pb-0">
                <b-col cols="6">
                  <div class="cls-token-quantity">
                    <p class="order-label-main-1">{{ $t("SELL_TOKEN.OWNING_TOKENS_LABEL") }}</p>
                  </div>
                </b-col>
                <b-col cols="6">
                  <div class="d-flex flex-column align-items-end justify-content-start">
                    <div class="cls-token-available-for-sell">
                      <b-button class="d-flex flex-row" id="sellable-status" @click="showSellableStatus = true" style="background:transparent; border:none; padding:0;">
                        <span class="font-16 cls-text">
                          {{ formatNumberIntl(availableTokens) }} {{ availableTokens > 0 ? $t("common.TOKENS").toLowerCase() : $t("common.TOKEN").toLowerCase() }} {{ $t("common.AVAILABLE") }}
                        </span>
                        <img class="img ml-1" style="width: 15px; height: 15px" src="../../assets/img/info-circle-fill.png" alt="">
                      </b-button>
                      <b-tooltip class="mb-1" variant="secondary" target="sellable-status" triggers="hover" placement="bottom">
                        {{ $t("ASSETS.CLICK_TO_SEE_DETAILS") }}
                      </b-tooltip>
                    </div>
                    <div v-if="lockedTokens" class="cls-tokens-locked">
                      <p class="font-15 mt-1">{{ $t("SELL_TOKEN.LOCKED_TOKENS_LABEL", { value: formatNumberIntl(lockedTokens) }) }}</p>
                    </div>
                  </div>
                </b-col>
              </b-row>
              <b-row class="ml-0 mr-0">
                <b-col cols="12">
                  <hr class="solid">
                </b-col>
              </b-row>
              <b-row align-h="between" align-v="center" class="order-token-quantity-row pb-4 pt-0">
                <b-col cols="12" sm="6">
                  <div class="cls-token-quantity">
                    <p class="order-label-main-1">{{ $t("PAYMENT.TOKEN_QUANTITY") }}</p>
                    <p class="order-label-sub-1">
                      {{ exchangeValue(property.price_per_token) }}/{{ $t("PAYMENT.TOKEN").toLowerCase() }}
                    </p>
                  </div>
                </b-col>
                <b-col cols="12" sm="6" class="pt-2 pt-sm-0">
                  <div class="purchase-amount-action full-width">
                    <div class="btn-numberic-custom has-border has-max-button full-width">
                      <VueNumericInput class="custom" align="center" size="normal"
                                       :min="minimumSellAmount" :max="availableTokens" :step="1"
                                       :value="quantity" :value-formated="formatNumberIntl(quantity)"
                                       @change="onTokenQuantityChanged" @input="onTokenQuantityChanged"/>
                      <b-button id="btn_sellToken_sellMax" class="pl-2 pr-2 font-11 btn-main btn-max"
                                variant="none" @click="sellMax">{{ $t("common.MAX") }}
                      </b-button>
                    </div>
                  </div>
                </b-col>
              </b-row>
            </div>
          </div>
          <promo-code
            v-if="enableVoucherCodeInput"
            :voucherCodeInput="voucherCodeInput"
            @update:modelValue="$event => {
                  voucherCodeInput = $event
                  voucherCodeError = null
                }"
            :error="voucherCodeError"
            @on-apply="validateVoucherCode"
            @on-clear-voucher="clearVoucherCodeInfo"
            :voucher-code="voucherCode"
            :reward-note="voucherRewardNote"
            class="pt-3">
          </promo-code>
        </div>
        <div class="content-right">
          <div class="content-right-wrapper">
            <div class="order-summary-block order-summary-items mt-2">
              <h3 class="font-20 font-weight-bold order-sub-heading-level-1">
                {{ $t("SELL_TOKEN.SUMMARY") }}
              </h3>
              <b-row align-h="between" class="order-summary-item">
                <b-col cols="7" class="order-label">
                  <p>{{ $t("SELL_TOKEN.TOKEN_QUANTITY") }}</p>
                </b-col>
                <div class="mr-3 order-value">
                  <p class="text-right fit-content">
                    {{ formatNumberIntl(quantity) }} {{ quantity > 1 ? $t("common.TOKENS") : $t("common.TOKEN") }}
                  </p>
                </div>
              </b-row>
              <b-row align-h="between" class="order-summary-item">
                <b-col cols="7" class="order-label">
                  <p>{{ $t("SELL_TOKEN.PRICE_PER_TOKEN") }}</p>
                </b-col>
                <div class="mr-3 order-value">
                  <p class="text-right fit-content" id="price-per-token">{{ exchangeValue(pricePerToken) }}</p>
                  <CurrencyTooltip tooltipId="price-per-token" :value="pricePerToken"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row align-h="between" class="order-summary-item">
                <b-col cols="7" class="order-label">
                  <p>{{ $t("SELL_TOKEN.SUBTOTAL") }}</p>
                </b-col>
                <div class="mr-3 order-value">
                  <p class="text-right fit-content" id="receive-amount-before-fee">{{ exchangeValue(sellTokenDetails.baseAmount) }}</p>
                  <CurrencyTooltip tooltipId="receive-amount-before-fee" :value="sellTokenDetails.baseAmount"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="sellTokenDetails.displayTransactionFees" align-h="between" class="order-summary-item">
                <b-col cols="7 order-label">
                  <p>{{ $t("TRANSACTION_FEE.TITLE") }}
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-transaction-fee" alt="">
                  </p>
                  <b-tooltip variant="secondary" target="tooltip-transaction-fee" triggers="hover" placement="top">
                    {{ $t("TRANSACTION_FEE.TOOLTIP") }}
                  </b-tooltip>
                  <p><span class="sub-text"> {{ transactionFeeNote }}</span></p>
                </b-col>
                <div class="mr-3 order-value">
                  <p v-if="sellTokenDetails.transactionFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                  <p v-if="sellTokenDetails.transactionFee > 0" class="text-right" id="transaction-fee">-{{ exchangeValue(sellTokenDetails.transactionFee) }}</p>
                  <CurrencyTooltip v-if="sellTokenDetails.transactionFee > 0" tooltipId="transaction-fee" :value="sellTokenDetails.transactionFee"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="voucherCode" align-h="between" class="order-summary-item">
                <b-col cols="7 order-label">
                  <p>{{ $t("VOUCHER.PROMO_CODE") }}</p>
                  <p class="sub-text">{{ voucherRewardNote }}</p>
                </b-col>
                <div class="mr-3 order-value">
                  <p class="text-right fit-content" id="order-voucher-code">{{ exchangeValue(sellTokenDetails.voucherRewardAmount) }}</p>
                  <CurrencyTooltip tooltipId="order-voucher-code" :value="sellTokenDetails.voucherRewardAmount"></CurrencyTooltip>
                </div>
              </b-row>
            </div>
            <hr class="solid">
            <b-row class="mt-0 pt-1" align-h="between" align-v="center">
              <b-col cols="6">
                <h3 class="font-18 font-weight-bold order-sub-heading-level-2 mb-0">
                  {{ $t("SELL_TOKEN.TOTAL_RECEIVE") }}
                </h3>
              </b-col>
              <div class="mr-3">
                <h3 class="text-right font-24 font-weight-bold mt-0 cls-total-payment-value mb-0" id="receive-amount">
                  {{ exchangeValue(sellTokenDetails.receiveAmount) }}
                </h3>
                <CurrencyTooltip tooltipId="receive-amount" :value="sellTokenDetails.receiveAmount"></CurrencyTooltip>
              </div>
            </b-row>
            <b-button id="btn_confirmSellRequest" class="btn-main col-12 mt-4 mb-2 pt-2" variant="none" @click="confirmSellRequest">
              {{ $t("SELL_TOKEN.CONFIRM_SELL_REQUEST") }}
            </b-button>
          </div>
        </div>
      </b-row>
    </b-container>

    <b-container v-if="isConfirmSellRequest" class="cls-order-form-container cls-single-page-container">
      <div class="row justify-content-center cls-order-form-content">
        <div class="col-12 col-lg-7 mt-2 p-0">
          <b-row align-content="center" class="cls-page-header-wrapper">
            <b-button id="btn_confirmSellRequest_Back" style="border: none; background-color: transparent; color: black" @click.prevent="back">
              <b-icon icon="arrow-left" class="pr-1 pt-1" style="color: black;" scale="0.8"></b-icon>
              {{ $t("common.BACK") }}
            </b-button>
            <h1 class="font-28 font-weight-bold text-center cls-heading" style="width:80%;">
              {{ $t("SELL_TOKEN.SELL_REQUEST_CONFIRMATION") }}
            </h1>
          </b-row>
          <div class="cls-page-content-wrapper d-flex flex-column align-items-center">
            <b-container class="cls-order-form-container">
              <div class="order-summary-block mt-3 order-summary-items">
                <h3 class="font-20 font-weight-bold order-sub-heading-level-1">
                  {{ $t("SELL_TOKEN.SUMMARY") }}
                </h3>
                <hr class="solid">
                <b-row align-h="between" class="order-summary-item">
                  <b-col cols="5" sm="7" class="order-label">
                    <p>{{ property.name }}</p>
                    <p class="sub-text">{{ formatNumberIntl(quantity) }} {{ quantity > 1 ? $t("common.TOKENS") : $t("common.TOKEN") }}</p>
                  </b-col>
                  <div class="mr-3 order-value">
                    <p class="text-right" id="price-per-token-2">{{ exchangeValue(property.price_per_token) }}/{{ $t("PAYMENT.EACH") }}</p>
                    <CurrencyTooltip tooltipId="price-per-token-2" :value="property.price_per_token"></CurrencyTooltip>
                  </div>
                </b-row>
                <hr class="solid">
                <b-row align-h="between" class="order-summary-item">
                  <b-col cols="6" class="order-label">
                    <h3 class="font-18 font-weight-bold order-sub-heading-level-1">{{ $t("SELL_TOKEN.SUBTOTAL") }}</h3>
                  </b-col>
                  <div class="mr-3 order-value">
                    <p class="text-right font-16" id="receive-amount-before-fee-2">{{ exchangeValue(sellTokenDetails.baseAmount) }}</p>
                    <CurrencyTooltip tooltipId="receive-amount-before-fee-2" :value="sellTokenDetails.baseAmount"></CurrencyTooltip>
                  </div>
                </b-row>
                <b-row v-if="sellTokenDetails.displayTransactionFees" align-h="between" class="order-summary-item mt-2">
                  <b-col cols="6" class="order-label">
                    <p class="font-16">{{ $t("TRANSACTION_FEE.TITLE") }}
                      <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-transaction-fee-2" alt="">
                    </p>
                    <b-tooltip variant="secondary" target="tooltip-transaction-fee-2" triggers="hover" placement="top">
                      {{ $t("TRANSACTION_FEE.TOOLTIP") }}
                    </b-tooltip>
                    <p v-if="transactionFeeNote"><span class="sub-text"> {{ transactionFeeNote }}</span></p>
                  </b-col>
                  <div class="mr-3 order-value">
                    <p v-if="sellTokenDetails.transactionFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                    <p v-if="sellTokenDetails.transactionFee > 0" class="text-right" id="transaction-fee-2">-{{ exchangeValue(sellTokenDetails.transactionFee) }}</p>
                    <CurrencyTooltip v-if="sellTokenDetails.transactionFee > 0" tooltipId="transaction-fee-2" :value="sellTokenDetails.transactionFee"></CurrencyTooltip>
                  </div>
                </b-row>
                <b-row v-if="voucherCode" align-h="between" class="order-summary-item">
                  <b-col cols="6 order-label">
                    <p>{{ $t("VOUCHER.PROMO_CODE") }}</p>
                    <p class="sub-text">{{ voucherRewardNote }}</p>
                  </b-col>
                  <div class="mr-3 order-value">
                    <p class="text-right fit-content" id="order-voucher-code-2">{{ exchangeValue(sellTokenDetails.voucherRewardAmount) }}</p>
                    <CurrencyTooltip tooltipId="order-voucher-code-2" :value="sellTokenDetails.voucherRewardAmount"></CurrencyTooltip>
                  </div>
                </b-row>
                <hr class="solid">
                <b-row align-h="between" class="order-summary-item">
                  <b-col cols="6">
                    <h3 class="font-18 font-weight-bold order-sub-heading-level-2 mb-0">{{ $t("SELL_TOKEN.TOTAL_RECEIVE") }}</h3>
                  </b-col>
                  <b-col cols="6">
                    <h3 class="text-right font-24 font-weight-bold mt-0 cls-total-payment-value mb-0" id="receive-amount-2">
                      {{ exchangeValue(sellTokenDetails.receiveAmount) }}
                    </h3>
                    <CurrencyTooltip tooltipId="receive-amount-2" :value="sellTokenDetails.receiveAmount"></CurrencyTooltip>
                  </b-col>
                </b-row>
                <b-row align-h="center" class="mt-3">
                  <b-col cols="12">
                    <!-- Recaptcha V2 checkbox fallback -->
                    <div v-if="showRecaptchaV2.CREATE_SELL_TOKEN_REQUEST" :ref="recaptchaV2Checkbox.CREATE_SELL_TOKEN_REQUEST" class="d-flex justify-content-center"></div>
                    <b-button id="btn_btn_confirmSellRequest_Submit" class="btn-main col-12 mt-4 pt-2" variant="none" @click.prevent="submitNow">
                      {{ $t("SELL_TOKEN.SUBMIT_SELL_REQUEST") }}
                    </b-button>
                  </b-col>
                </b-row>
              </div>
            </b-container>
          </div>
        </div>
      </div>
    </b-container>

    <popup ref="popupPendingTask" @on-positive-clicked="openContractAgreement"></popup>
    <modal-contract-agreement ref="contractAgreement" @on-agreed-to-contract="onAgreedToContract"/>
    <modal-sellable-status :property-name="this.property.name" :tokens-status="tokensStatus"
                           :show="showSellableStatus" @on-close="showSellableStatus = false"></modal-sellable-status>
    <popup ref="popupTransactionFeeConfirmation" @on-positive-clicked="isConfirmSellRequest = true"></popup>
    <PopupSecurityPin ref="popUpPinSecurity" @on-success="onVerified"></PopupSecurityPin>
  </div>
</template>

<script>
import { useRecaptcha } from "@/composables/useRecaptcha";
import { ERROR_CODE, STORAGE_KEYS, VOUCHER } from "@/constants/constants";
import messErrors from "@/constants/errors";
import VueNumericInput from "../../components/VueNumericInput"
import accountService from "../../services/account.service"
import propertiesService from "../../services/properties.service"
import sellTokensService from "../../services/sellTokens.service"
import { exchange, formatNumberIntl, getErrorMessage, notify, urlImage } from "@/helpers/common"
import CurrencyTooltip from "../../components/CurrencyTooltip.vue"
import ModalContractAgreement from "../../modals/ModalContractAgreement"
import ModalSellableStatus from "../../modals/ModalSellableStatus.vue"
import store from "../../store/store"
import { gtmTrackEvent } from "@/helpers/gtm"
import { GTM_EVENT_NAMES } from "@/constants/gtm"
import contractsService from "../../services/contracts.service"
import PopupSecurityPin from "@/components/PopupSecurityPin.vue"
import Popup from "../../components/Popup"
import voucherService from "@/services/voucher.service";
import PromoCode from "../../components/PromoCode.vue"
import { calculateFee, getTransactionFeeNote } from "@/helpers/fee";
import { calculateVoucherRewardAmount, getVoucherRewardNote } from '@/helpers/voucher'

export default {
  components: {
    VueNumericInput,
    CurrencyTooltip,
    ModalSellableStatus,
    ModalContractAgreement,
    PopupSecurityPin,
    Popup,
    PromoCode,
  },
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ CREATE_SELL_TOKEN_REQUEST: 'createSellTokenRequest' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      title: "Selling Tokens",
      propertyUuid: this.$route.query.uuid,
      property: {},
      tokensStatus: {},
      agreeToContractPendingTask: null,
      quantity: 1,
      showSellableStatus: false,
      isConfirmSellRequest: false,
      voucherCodeInput: "",
      voucherCode: null,
      voucherCodeError: null,
    }
  },
  async mounted() {
    await Promise.all([
      this.getProperty(),
      this.getTokensStatus(),
      this.getContractStatus(),
    ])
  },
  watch: {
    useVirtualBalance(value) {
      this.quantity = 1
      if (value) {
        this.useBalance = false
      }
    },
    useBalance(value) {
      this.quantity = 1
      if (value) {
        this.useVirtualBalance = false
      }
    },
  },
  methods: {
    formatNumberIntl,
    async getProperty() {
      const res = await propertiesService.getByUuid(this.propertyUuid)
      this.property = res.data
    },
    async getTokensStatus() {
      if (this.$store.getters.userProfile && localStorage.getItem(STORAGE_KEYS.AUTHORIZATION.key)) {
        const res = await accountService.getTokensStatus(this.propertyUuid)
        if (res && res.data) {
          this.tokensStatus = res.data
        }
      }
    },
    async getContractStatus() {
      if (this.$store.getters.userProfile && localStorage.getItem(STORAGE_KEYS.AUTHORIZATION.key) && this.propertyUuid) {
        this.agreeToContractPendingTask = null
        const response = await contractsService.getContractStatus(this.propertyUuid)
        if (response) {
          this.agreeToContractPendingTask = response.agree_to_contract_pending_task
        }
      }
    },
    exchangeValue(value) {
      return exchange(value)
    },
    getPropertyImage(images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    openGoogleMap(property) {
      if (property.map_link) {
        window.open(property.map_link, "_blank")
      }
    },
    onTokenQuantityChanged(newValue) {
      this.quantity = newValue
    },
    sellMax() {
      this.onTokenQuantityChanged(this.availableTokens)
    },
    clearVoucherCodeInfo() {
      this.voucherCodeInput = "";
      this.voucherCode = null;
      this.voucherCodeError = null;
    },
    async validateVoucherCode() {
      try {
        const response = await voucherService.validateVoucherCode({ voucher_code: this.voucherCodeInput, action: VOUCHER.REDEEM_ACTION_SELL }, true);
        if (response.voucher_code) {
          this.voucherCode = response.voucher_code
          this.voucherCodeError = null
        }
      } catch (error) {
        this.voucherCode = null
        this.voucherCodeError = getErrorMessage(error)
      }
    },
    async openContractAgreement() {
      const contractPreviewUrl = await contractsService.getContractPreview({ property_uuid: this.propertyUuid })
      if (contractPreviewUrl) {
        this.$refs.contractAgreement.showModal(contractPreviewUrl, this.propertyUuid)
      }
    },
    async onAgreedToContract(data) {
      if (data && data.propertyUuid === this.propertyUuid) {
        await this.getContractStatus()
      }
    },
    isValidSellRequest() {
      if (this.sellTokenDetails.receiveAmount < 0) {
        notify({ text: "Total receive is negative. Please review before proceeding to the next step", type: "error" })
        return false
      } else if (this.voucherCodeError) {
        return false
      } else if (this.voucherCode && this.sellTokenDetails.voucherRewardAmount <= 0) {
        notify({ text: this.$t("VOUCHER.WARNING_NO_DISCOUNT"), type: "error" })
        return false
      }
      return true
    },
    async confirmSellRequest() {
      if (this.agreeToContractPendingTask) {
        this.$refs.popupPendingTask.openPopup({
          title: this.$t("PENDING_TASKS.COMPLETE_PENDING_TASK"),
          message: this.$t("PENDING_TASKS.PLEASE_READ_AND_AGREE_TO_AGREEMENT"),
          positiveButton: this.$t("common.OK")
        })
      } else if (this.isValidSellRequest()) {
        if (this.sellTokenDetails.receiveAmount < this.sellTokenDetails.baseAmount) {
          const storageKey = STORAGE_KEYS.SELL_TOKEN_TRANSACTION_FEE_CONFIRMATION.key
          const dontShowAgainTimestamp = Number(localStorage.getItem(storageKey) || 0)
          const shouldShowConfirmation = dontShowAgainTimestamp < Date.now()
          if (shouldShowConfirmation) {
            this.$refs.popupTransactionFeeConfirmation.openPopup({
              title: this.$t("common.NOTIFICATION"),
              message: this.$t("TRANSACTION_FEE.SELL_TOKEN_CONFIRMATION_MESSAGE"),
              warningMessage: this.$t("TRANSACTION_FEE.SELL_TOKEN_CONFIRMATION_WARNING"),
              dontShowAgainKey: storageKey,
              positiveButton: this.$t("MODALS.COMMON.CONTINUE"),
              warningMessageClass: "warning-message",
              btnClass: "col-4",
            })
            return
          }
        }
        this.isConfirmSellRequest = true
      }
    },
    async back() {
      this.isConfirmSellRequest = false
    },
    async submitNow() {
      if (this.isValidSellRequest()) {
        await this.$refs.popUpPinSecurity.openPopup(true)
      }
    },
    async onVerified(pin) {
      if (!this.validateRecaptchaV2.CREATE_SELL_TOKEN_REQUEST()) return
      try {
        const recaptchaTokenV3 = await this.recaptchaV3Exec.CREATE_SELL_TOKEN_REQUEST()
        const recaptchaTokenV2 = this.recaptchaTokenV2.CREATE_SELL_TOKEN_REQUEST
        let response = await sellTokensService.createSellTokenRequest({
          property_uuid: this.property.uuid,
          num_of_tokens: this.quantity,
          description: `Request sell ${this.quantity} ${this.quantity > 1 ? "tokens" : "token"} of property ${this.property.name}`,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
          voucher_code: this.voucherCodeInput,
          pin,
        }, false, true)
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.SELL_PROPERTY,
          property_uuid: this.property.uuid,
          property_name: this.property.name,
          num_of_tokens: this.quantity,
        })
        if (response && response.sell_token_request) {
          gtmTrackEvent({
            event: GTM_EVENT_NAMES.SELL_PROPERTY_SUCCESS,
            property_uuid: this.property.uuid,
            property_name: this.property.name,
            num_of_tokens: this.quantity,
          })
          await this.$router.push({
            name: "sellTokenRequestSuccess",
            query: { request_uuid: String(response.sell_token_request.uuid) }
          })
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.CREATE_SELL_TOKEN_REQUEST = true
        } else {
          notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.CREATE_SELL_TOKEN_REQUEST()
      }
    },
  },
  computed: {
    pricePerToken() {
      return this.property.price_per_token || 10000
    },
    minimumSellAmount() {
      return store.state.configs && parseInt(store.state.configs.sell_token_minimum_amount) || 1
    },
    maximumSellAmount() {
      return store.state.configs && parseInt(store.state.configs.sell_token_maximum_amount) || 1000
    },
    availableTokens() {
      let sellableTokens = this.tokensStatus
        && this.tokensStatus.asset_sellable_tokens - this.tokensStatus.pending_sell_tokens || 0
      return Math.min(sellableTokens, this.maximumSellAmount)
    },
    lockedTokens() {
      return this.tokensStatus && parseInt(this.tokensStatus.asset_locked_tokens) || 0
    },
    enableVoucherCodeInput() {
      return store.state.configs.enable_voucher_code_input
    },
    sellTokenDetails() {
      const baseAmount = this.property.price_per_token * this.quantity
      const displayTransactionFees = this.$store.getters.configs.display_transaction_fees
      const transactionFeeConfig = this.$store.getters.configs.sell_token_transaction_fees
      const transactionFee = displayTransactionFees ? calculateFee(transactionFeeConfig, baseAmount) : 0

      let voucherRewardAmount = 0
      let voucherRewardType = this.voucherCode?.voucher?.reward_type
      let voucherRequiredTransactionAmount = this.voucherCode?.voucher?.required_transaction_amount || 0;
      if (baseAmount >= voucherRequiredTransactionAmount) {
        switch (voucherRewardType) {
          case VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES:
          case VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_SELL:
            voucherRewardAmount = Math.min(calculateVoucherRewardAmount(this.voucherCode, transactionFee), transactionFee)
            break;
        }
      }
      // External gateway processing fees (NOT apply yet)
      //let processingFee = 0
      //let processingFeePercent = 0
      const receiveAmount = Math.max(0, baseAmount - transactionFee + voucherRewardAmount);

      return { baseAmount, receiveAmount, displayTransactionFees, transactionFeeConfig, transactionFee, voucherRewardAmount }
    },
    transactionFeeNote() {
      return getTransactionFeeNote(this.sellTokenDetails.displayTransactionFees, this.sellTokenDetails.transactionFeeConfig, this.$t, this.exchangeValue)
    },
    voucherRewardNote() {
      return getVoucherRewardNote(this.voucherCode, VOUCHER.REDEEM_ACTION_SELL, this.$t, this.exchangeValue)
    },
  },
  metaInfo() {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
}
</script>

<style lang="scss">
.sell-form {
  background-color: white;
  box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
  border-radius: 16px;

  .content-left {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 40px 20px 0;
  }

  .content-right {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    border: 0 !important;
    box-shadow: none !important;
    padding: 0 !important;

    .content-right-wrapper {
      box-shadow: 0 0 26.4px 0 rgba(0, 0, 0, 0.0509803922) !important;
      border: 1px solid #E9E9E9 !important;
      border-radius: 13px !important;
      padding: 30px !important;
      @media(max-width: 400px) {
        padding: 20px 15px !important;
      }
    }
  }

  @media(max-width: 1366px) {
    .content-left {
      -ms-flex: 0 0 50%;
      flex: 0 0 50%;
      max-width: 50%;
    }

    .content-right {
      -ms-flex: 0 0 50%;
      flex: 0 0 50%;
      max-width: 50%;
    }
  }

  @media(max-width: 1024px) {
    .content-left {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
      padding: 0 0 20px 0 !important;

      .cls-token-order-wrapper {
        margin-bottom: 10px !important;
      }
    }

    .content-right {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
  @media(max-width: 991.98px) {
    .content-left {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }

    .content-right {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  p {
    margin: 0;
    padding: 0;
  }

  .purchase-amount-action {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .btn-numberic-custom {
      max-width: 220px;
      width: 175px !important;
      @media(max-width: 400px) {
        // width: 135px !important;
      }
    }

    .btn-numberic-custom {
      .vue-numeric-input {
        &.custom {
          @media(max-width: 400px) {
            width: 130px !important;
          }
        }
      }

      button.btn-max {
        padding: 3px 6px;
        border-radius: 6px;
      }

      input.numeric-input {
        font-family: "AcuminVariableConcept", Helvetica, sans-serif;
        font-size: 16px;
        font-weight: 500;
        line-height: 17px;
        color: var(--primary-darker-color);
        padding-top: 7px !important;
        @media(max-width: 400px) {
          font-size: 13px;
        }
      }
    }

    .recommended-token-selections {
      margin-top: 6px;
      display: flex;

      button {
        padding: 4px 14px;
        flex: 1;
        margin-right: 5px;
        max-width: 70px;

        &.last-item {
          margin-right: 0;
        }

        @media(max-width: 400px) {
          padding: 3px 8px;
        }
      }
    }

    &.full-width {
      .btn-numberic-custom {
        flex: 1;
        max-width: 100% !important;
        width: 100% !important;
        @media(max-width: 400px) {
          // width: 135px !important;
        }

        .vue-numeric-input {
          &.custom {
            @media(max-width: 400px) {
              width: 130px !important;
            }
          }
        }

        button.btn-max {
          padding: 6px 20px 3px 20px !important;
          border-radius: 6px;
        }
      }
    }

    @media(max-width: 400px) {
      flex-direction: column !important;
      justify-content: center;
      align-items: center;
      .btn-numberic-custom {
        max-width: 100%;
      }
      .recommended-token-selections {
        justify-content: center;
      }
    }
  }

  .cls-token-available-for-sell {
    button {
      span.cls-text {
        font-weight: 382;
        line-height: 19.2px;
        letter-spacing: -0.03em;
        text-align: left;
        color: #484848;
      }
    }
  }

  .cls-tokens-locked {
    font-weight: 382;
    line-height: 15.6px;
    letter-spacing: -0.03em;
    text-align: left;
    color: #CD1400;
  }

  .voucher-gray {
    color: #c2c1c1 !important;
  }

  .compound-tooltip-icon {
    margin-bottom: 2px;
    width: 15px;
    height: 15px;
    z-index: 1;
  }
}
</style>
