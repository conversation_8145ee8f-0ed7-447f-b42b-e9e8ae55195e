<template>
	<div class="setting-account-page">
		<div class="row justify-content-center">
			<div class="col-12 col-lg-7 mt-2">
				<p class="font-28 font-weight-bold">{{ $t('account.BASIC_INFO') }}</p>
				<div class="d-flex flex-column align-items-center">
				<div class="content col-12 col-lg-12 col-xl-12">
					<h2 class="session-title mb-2">{{ $t('account.MY_PROFILE') }}</h2>
					<div class="d-flex align-items-center">
						<b-dropdown id="dropdown-1" class="mb-2" dropright no-caret>
							<template #button-content>
								<div class="avatar">
									<img v-if="avatarUrl" class="img-avatar" :src="avatarUrl" />
									<default-avatar v-else :width="80" :height="80"></default-avatar>
									<div class="edit-avatar">
										<img src="@/assets/img/ic-edit.svg" alt="" />
									</div>
								</div>
							</template>
							<b-dropdown-item v-if="avatarUrl" @click="$refs.viewPhotoPopup.openPopup(avatarUrl)">{{ $t('account.VIEW_PHOTO') }}</b-dropdown-item>
							<b-dropdown-item @click="$refs.takePhotoPopup.openPopup()">{{ $t('account.TAKE_A_PHOTO') }}</b-dropdown-item>
							<b-dropdown-item @click="$refs.fileSelectInput.click()">{{ $t('account.CHOOSE_A_PHOTO') }}</b-dropdown-item>
						</b-dropdown>
						<div class="ml-4 flex-grow-1">
							<p class="color-gray">{{ $t('account.USERNAME') }}</p>
							<div class="d-flex flex-column flex-md-row align-items-stretch align-items-md-start">
								<p class="font-22 font-weight-bold flex-grow-1 d-flex align-items-center">
									{{ username }}
									<icon-checker v-if="isCompletedKyc" :type="'blue'" class="ml-1"/>
								</p>
								<div class="ml-0 ml-md-2 mt-1 mt-md-0">
									<b-button id="btn_changeUsername" v-if="!hasChangedUsername" class="btn-outline-main btn-edit" style="padding: 4px 12px;" @click.prevent="onClickEdit">
										{{ $t('common.EDIT') }}
									</b-button>
								</div>
							</div>
						</div>
					</div>
					<input type="file" ref="fileSelectInput" style="display: none" accept="image/png, image/jpeg, image/bmp"
						v-on:change="onSelectedPhoto" />
					<p class="text-center error-text my-3" v-if="isEmailNotVerified">
						{{ $t('account.PLEASE_VERIFY_YOUR_EMAIL_ADDRESS') }}</p>
					<div class="divider my-3"></div>
					<div class="d-flex flex-column flex-md-column flex-lg-row flex-wrap">
						<div class="col-12 d-flex flex-row mt-2 p-0 m-0">
							<p class="my-account-title col-4 col-md-3 p-0">{{ $t('GORO_ID') }}</p>
							<p class="my-account-value col-8 col-md-9 p-0">{{ uuid }}</p>
						</div>
						<div class="col-12 d-flex flex-row mt-2 p-0">
							<p class="my-account-title col-4 col-md-3 p-0">{{ $t('AUTH.NAME') }}</p>
							<p class="my-account-value col-8 col-md-9 p-0">{{ name }}</p>
						</div>
						<div class="col-12 d-flex flex-row mt-2 p-0">
							<p class="my-account-title col-4 col-md-3 p-0">{{ $t('AUTH.EMAIL') }}</p>
							<div class="col-8 col-md-9 d-flex flex-row align-items-center p-0">
								<p class="my-account-value">{{ email }}</p>
								<b-icon icon="patch-check-fill" :variant="isVerifiedEmail ? 'success' : 'secondary'"
									class="mx-1"></b-icon>
								<b-button id="btn_myAccount_resendEmailVerification" v-if="!isVerifiedEmail" class="btn-main ml-2 pt-1 pb-1 pl-3 pr-3" type="submit"
									variant="none" @click="resendEmailVerification">
									{{ $t("account.RESEND") }}
								</b-button>
							</div>
						</div>
						<div class="col-12 d-flex flex-row mt-2 p-0">
							<p class="my-account-title col-4 col-md-3 p-0">{{ $t('AUTH.PHONE') }}</p>
							<p class="col-8 col-md-9 d-flex flex-row align-items-center p-0">
								<p class="my-account-value">{{ phone }}</p>
								<b-icon icon="patch-check-fill" variant="success" class="ml-1"></b-icon>
							</p>
						</div>
						<div v-if="dob" class="col-12 d-flex flex-row mt-2 p-0">
							<p class="my-account-title col-4 col-md-3 p-0">{{ $t('account.dob') }}</p>
							<p class="my-account-value col-8 col-md-9 p-0">{{ dob }}</p>
						</div>
						<div v-if="showAddress" class="col-12 d-flex flex-row mt-2 p-0">
							<p class="my-account-title col-4 col-md-3 p-0">{{ $t('AUTH.ADDRESS') }}</p>
							<p class="my-account-value col-8 col-md-9 p-0">{{ address }}</p>
						</div>
					</div>
					<p class="mt-4">{{ $t('account.TO_UPDATE_PROFILE_V2.WA') }} <a :href="getWaSupportByLang">{{ getWaSupportByLang
							}}</a> {{
				$t('account.TO_UPDATE_PROFILE_V2.EMAIL') }} <a :href="contactMailTo">{{ contact }}</a></p>
				</div>

      <div v-if="userWalletAddress" class="content col-12 col-lg-12 col-xl-12">
        <h2 class="session-title" @click="onWalletClicked">
          {{ $t('BLOCKCHAIN.TITLE') }}
          <b-icon :icon='collapses.wallet ? "chevron-down" : "chevron-right"' style="margin-top: 6px"></b-icon>
        </h2>
        <b-collapse class="blockchain-info" v-model="collapses.wallet" id="collapse-wallet">
          <div class="d-flex align-items-center mt-3 mb-2">
            <img class="mr-4" src="~@/assets/img/blockchain/wallet.svg" alt=""/>
            <div>
              <p class="my-account-title">{{ $t('BLOCKCHAIN.WALLET_ADDRESS') }}</p>
              <a class="wallet-uri font-bold" :href="userWalletAddressLink" target="_blank">
                {{ userWalletAddress }}
              </a>
            </div>
          </div>
        </b-collapse>
      </div>

				<div class="content col-12 col-lg-12 col-xl-12">
					<h2 class="session-title">{{ $t('account.SETTINGS') }}</h2>
					<div class="d-flex flex-column flex-lg-row mt-2 align-items-start align-items-lg-center">
						<p>{{ $t('account.DISPLAY_CURRENCY') }}</p>
						<div class="ml-0 ml-lg-3 d-flex flex-row mt-2 mt-lg-0">
							<p @click="updateCurrency('IDR')" class="currency text-center"
								:class="{ selected: userInfo.preferred_currency == 'IDR' }">IDR</p>
							<p @click="updateCurrency('EUR')" class="currency text-center"
								:class="{ selected: userInfo.preferred_currency == 'EUR' }">EUR</p>
							<p @click="updateCurrency('USD')" class="currency text-center"
								:class="{ selected: userInfo.preferred_currency == 'USD' }">USD</p>
							<p @click="updateCurrency('SGD')" class="currency text-center"
								:class="{ selected: userInfo.preferred_currency == 'SGD' }">SGD</p>
							<p @click="updateCurrency('AUD')" class="currency text-center"
								:class="{ selected: userInfo.preferred_currency == 'AUD' }">AUD</p>
						</div>
					</div>
					<p v-if="showExchanged" class="mt-2 font-weight-bold" style="text-decoration: underline;">{{
				userInfo.preferred_currency }}1 = IDR{{ exchangedValue }}</p>
					<p v-if="showExchanged" class="currency-note font-16 mt-3">
						<span>{{ $t('account.CURRENCY.NOTE_LABEL') }}: </span>{{ $t('account.CURRENCY.NOTES') }}
					</p>
				</div>
				</div>
			</div>
		</div>
		<ImageCropperPopup ref="imageCropperPopup" @on-done="onCroppedPhoto" @on-cancel="onCropCancelled">
		</ImageCropperPopup>
		<TakePhotoPopup ref="takePhotoPopup" @on-done="onPhotoTaken"></TakePhotoPopup>
		<ViewPhotoPopup ref="viewPhotoPopup"></ViewPhotoPopup>
		<Popup ref="popupUsername"></Popup>
		<PopupChangeUsername ref="popupChangeUsername" @on-success="checkCanChangeUsername"/>
    <ModalWalletWarning :show="showWalletWarning" @on-close="onWalletWarningClosed"/>
	</div>
</template>

<script>
import externalSites from "../../constants/externalSites";
import store from "../../store/store";
import authService from "../../services/auth.service";
import accountService from "../../services/account.service";
import userService from "../../services/user.service";
import {
  numberWithCommas,
  obfuscateBlockchainInfo,
  urlImage,
} from "../../helpers/common";
import ImageCropperPopup from "../../components/ImageCropperPopup.vue";
import TakePhotoPopup from "../../components/TakePhotoPopup.vue";
import ViewPhotoPopup from "../../components/ViewPhotoPopup.vue";
import Popup from "../../components/Popup.vue";
import DefaultAvatar from "../../components/DefaultAvatar.vue";
import PopupChangeUsername from "../../components/PopupChangeUsername.vue";
import ModalWalletWarning from "../../modals/ModalWalletWarning";
import { BLOCKCHAIN } from "../../constants/constants";
import i18n from "../../i18n";
import IconChecker from "../../components/IconChecker.vue";

export default {
  components: {
    ImageCropperPopup,
    TakePhotoPopup,
    ViewPhotoPopup,
    DefaultAvatar,
    Popup,
    PopupChangeUsername,
    ModalWalletWarning,
    IconChecker,
  },
  data() {
    return {
      contact: externalSites.CUSTOMER_EMAIL,
      contactMailTo: externalSites.MAIL_TO.CUSTOMER_SUPPORT,
      whatsAppSupports: externalSites.WHATSAPP_SUPPORTS,
      canChangeUsername: false,
      hasChangedUsername: true,
      showWalletWarning: false,
      collapses: { wallet: false },
    };
  },

  mounted() {
    this.checkCanChangeUsername();
  },

  methods: {
    async resendEmailVerification() {
      await authService.resendVerifyEmail();
    },

    async updateCurrency(value) {
      if (this.userInfo.preferred_currency != value) {
        const res = await accountService.updatePreferredCurrency({
          preferred_currency: value,
        });
        if (res) {
          await this.$store.dispatch("refreshUserProfile");
        }
      }
    },

    async checkCanChangeUsername() {
      const res = await accountService.canChangeUsername();
      if (res) {
        this.canChangeUsername = res.data;
        this.hasChangedUsername = res.has_changed;
      }
    },

    onSelectedPhoto(e) {
      const files = e.target.files || e.dataTransfer.files;
      if (!files.length) {
        return;
      }
      const file = files[0];
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.$refs.imageCropperPopup.openPopup(String(reader.result));
      };
    },

    async onCroppedPhoto(base64) {
      this.$refs.fileSelectInput.value = null;
      const data = await userService.uploadAvatar({ image: base64 });
      if (data.user) {
        await store.dispatch("setUserProfile", data.user);
      }
    },

    onCropCancelled() {
      this.$refs.fileSelectInput.value = null;
    },

    onPhotoTaken(file) {
      this.$refs.imageCropperPopup.openPopup(file);
    },

    onClickEdit() {
      if (this.canChangeUsername) {
        this.$refs.popupChangeUsername.openPopup(this.userInfo.username);
      } else {
        this.$refs.popupUsername.openPopup({
            message: this.$t("USERNAME.USERNAME_CHANGE_NOTE"),
            positiveButton: this.$t("common.OK")
          }
        );
      }
    },

    onWalletClicked() {
      if (this.collapses["wallet"] === false) {
        //show warning before expand wallet info
        this.showWalletWarning = true;
      } else {
        this.toggleCollapse("wallet");
      }
    },

    onWalletWarningClosed(showWalletDetails) {
      this.showWalletWarning = false;
      if (showWalletDetails === true && this.collapses["wallet"] === false) {
        this.toggleCollapse("wallet");
      }
    },

    toggleCollapse(collapseId) {
      // Close the other collapse
      for (const id in this.collapses) {
        if (id !== collapseId) {
          this.collapses[id] = false;
        }
      }
      // Toggle the clicked collapse
      this.collapses[collapseId] = !this.collapses[collapseId];
    },
  },

  computed: {
    isVerifiedEmail() {
      return this.$store.getters.userProfile.email_verified_at !== null;
    },

    isEmailNotVerified() {
      return this.$route.query.status === "email_not_verified";
    },

    userInfo() {
      return this.$store.getters.userProfile;
    },
    showExchanged() {
      return this.userInfo.preferred_currency !== "IDR";
    },
    exchangedValue() {
      const rates = this.$store.getters.exchangeRates;
      const currency = this.userInfo.preferred_currency;
      const rate = rates[currency];
      if (!rate) {
        return 0;
      }
      if (currency !== "IDR") {
        const locale = i18n.global.locale.value;
        const separator = locale === "id" ? "." : ",";
        return numberWithCommas(Math.round(1 / rate), separator);
      }
      return 1;
    },
    avatarUrl() {
      const url =
        this.$store.getters.userProfile &&
        this.$store.getters.userProfile.avatar_url;
      return urlImage({ image: url });
    },
    getWaSupportByLang() {
      if (this.whatsAppSupports[this.$i18n.locale]) {
        return this.whatsAppSupports[this.$i18n.locale];
      }
      return this.whatsAppSupports.id;
    },

    uuid() {
      return this.userInfo.uuid || "";
    },

    name() {
      return this.userInfo.name || "";
    },

    email() {
      return (
        this.userInfo.email ||
        this.userInfo.email_fb ||
        this.userInfo.email_google ||
        this.userInfo.email_apple ||
        ""
      );
    },

    phone() {
      return this.userInfo.phone || "";
    },

    dob() {
      return this.userInfo.dob || "";
    },

    country() {
      return this.userInfo.country || {};
    },

    state() {
      return this.userInfo.state || {};
    },

    address() {
      return `${this.userInfo.street_address || ""}, ${
        this.userInfo.city || ""
      }, ${this.state.name || ""}, ${this.country.name || ""}, ${
        this.userInfo.zip_code || ""
      }`;
    },

    showAddress() {
      return (
        this.userInfo.street_address ||
        this.userInfo.city ||
        this.state.name ||
        this.country.name ||
        this.userInfo.zip_code
      );
    },

    username() {
      return this.$store.getters.userProfile.username;
    },

    userWalletAddress() {
      if (
        this.$store.getters.userProfile.gas_pump_address &&
        this.$store.getters.userProfile.gas_pump_address.address
      ) {
        return (
          obfuscateBlockchainInfo(
            this.$store.getters.userProfile.gas_pump_address.address
          ) || "-"
        );
      }
      return "-";
    },

    userWalletAddressLink() {
      if (
        this.$store.getters.userProfile.gas_pump_address &&
        this.$store.getters.userProfile.gas_pump_address.address
      ) {
        return (
          BLOCKCHAIN.POLYGON_SCAN_ADDRESS_URL +
          this.$store.getters.userProfile.gas_pump_address.address
        );
      }
      return "";
    },
    isCompletedKyc() {
      const isCompletedVerification = this.userInfo && this.userInfo.kyc_status && this.userInfo.kyc_status.completedVerification
      return isCompletedVerification
    },
  },
};
</script>

<style lang="scss">
.setting-account-page {
  width: 100%;
  margin-top: 20px;

  .content {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding: 24px;
    margin-top: 15px;
    margin-bottom: 15px;

    .session-title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-size: 23px;
      font-weight: 600;
      color: rgb(51, 51, 51);
      margin: 0;
      padding: 0;
      line-height: 1.4em;
    }

    .my-account-title {
      font-size: 16px;
      color: gray;
    }

    .my-account-value {
      font-size: 16px;
      white-space: pre-wrap;
      word-break: break-word;
      color: var(--primary-color);
      font-weight: bold;
    }

    .error-text {
      font-size: 18px;
      color: red;
    }

    .currency {
      padding: 5px;
      margin-right: 10px;
      border: 1px solid var(--primary-color);
      border-radius: 10px;
      min-width: 50px;
      cursor: pointer;
      font-weight: bold;

      &.selected {
        color: white;
        background-color: var(--primary-color);
      }
    }

    .avatar {
      position: relative;

      .img-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 0.5px solid var(--primary-color);
        cursor: pointer;
        object-fit: cover;
      }

      .edit-avatar {
        position: absolute;
        right: 2px;
        bottom: 2px;
        width: 24px;
        height: 24px;
        background-color: var(--primary-color);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;

        img {
          width: 14px;
          height: 14px;
        }
      }
    }

    #dropdown-1__BV_toggle_ {
      background-color: transparent;
      padding: 0px;
      border-radius: 50%;
      transition: all 0.45s ease;
      overflow: visible !important;

      &:hover {
        background-color: rgb(215, 215, 215);
        opacity: 0.8;
      }
    }

    .divider {
      width: 100%;
      height: 1px;
      background-color: rgb(233, 233, 233);
    }

    .btn-edit {
      border-radius: 6px;
      font-weight: bold;
      white-space: nowrap;
    }

    .blockchain-info {
      .wallet-uri {
        color: var(--primary-color);
        text-decoration: underline;
        overflow-wrap: break-word;
        word-break: break-all;
      }

      .wallet-warning {
        color: red;
        white-space: pre-wrap;
        overflow-wrap: break-word;
      }
    }
  }
}
</style>
