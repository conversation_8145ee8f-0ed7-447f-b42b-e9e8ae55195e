<template>
  <div class="swap-form p-2 p-md-2 p-lg-4 mt-4 mb-4" v-if="currentProperty.id">
    <b-container v-if="!isConfirmSwapRequest">
      <p class="font-weight-bold" style="font-size: 24px">{{ $t("SWAP_TOKEN.SWAP_TOKEN") }}</p>
      <b-row v-if="innerWidth >= 1100" class="mt-4" align-h="center">
        <b-col :cols="innerWidth > 1350 ? 10 : innerWidth > 1250 ? 11 : 12">
          <b-row class="mb-1">
            <b-col cols="5">
              <h5>{{ $t("SWAP_TOKEN.CURRENT_PROPERTY") }}</h5>
            </b-col>
            <b-col cols="2"></b-col>
            <b-col cols="5">
              <h5>{{ $t("SWAP_TOKEN.NEW_PROPERTY") }}</h5>
            </b-col>
          </b-row>
          <b-row align-v="center">
            <b-col cols="5">
              <p class="current-property-info">{{ currentPropertyName }}</p>
            </b-col>
            <b-col cols="2">
              <b-img :src="require('../../assets/img/arrow_swap_right.png')" style="max-width: 90%; height: auto"/>
            </b-col>
            <b-col cols="5">
              <v-select class="goro-select" v-model="newProperty" :options="availableProperties"
                        :clearable="false" label="name">
                <template v-slot:option="option">
                  {{ option.name }}
                </template>
                <template #selected-option="option">
                  {{ option.name }}
                </template>
              </v-select>
            </b-col>
          </b-row>
          <b-row class="mt-3" style="margin-top: -8px">
            <b-col cols="5" class="pb-0 mb-0">
              <h5 style="font-size: 14px; color: #169A97;">{{ $t("SWAP_TOKEN.YOUR_TOKEN") }}</h5>
            </b-col>
            <b-col cols="2"></b-col>
            <b-col cols="5">
              <h5 style="font-size: 14px; color: #169A97">{{ $t("SWAP_TOKEN.PROPERTY_TOKEN") }}</h5>
            </b-col>
          </b-row>
          <b-row style="margin-top: -8px">
            <b-col cols="5">
              <h5>{{
                  $t("SWAP_TOKEN.TOKEN_AVAILABLE", {
                    value: formatNumberIntl(currentPropertySwappableTokens),
                    token_label: currentPropertySwappableTokens > 1 ? this.$t('common.TOKENS') : this.$t('common.TOKEN')
                  })
                }}</h5>
            </b-col>
            <b-col cols="2"></b-col>
            <b-col cols="5">
              <h5>{{
                  $t("SWAP_TOKEN.TOKEN_AVAILABLE", {
                    value: formatNumberIntl(newPropertyAvailableTokens),
                    token_label: newPropertyAvailableTokens > 1 ? this.$t('common.TOKENS') : this.$t('common.TOKEN')
                  })
                }}</h5>
            </b-col>
          </b-row>
          <b-row align-v="center">
            <b-col cols="5">
              <p class="receive-token-quantity text-center" >{{ formatNumberIntl(currentPropertySwappableTokens) }}</p>
            </b-col>
            <b-col cols="2">
              <b-img :src="require('../../assets/img/arrow_swap_right.png')" style="max-width: 90%; height: auto"/>
            </b-col>
            <b-col cols="5">
              <div class="swap-token-quantity d-flex flex-row align-items-center">
                <VueNumericInput class="custom" align="center" size="large"
                                 :min="1" :max="swappableTokens" :step="1"
                                 :value="quantity" :value-formated="formatNumberIntl(quantity)"
                                 @change="onTokenQuantityChanged" @input="onTokenQuantityChanged"/>
                <b-button id="btn_swapToken_swapMax" class="btn-main pl-3 pr-3 ml-2" variant="none"
                          style="font-size: 12px; border-radius: 6px" @click="swapMax">
                  {{ $t("common.MAX") }}
                </b-button>
              </div>
            </b-col>
          </b-row>
          <b-row align-v="center">
            <b-col cols="7"/>
            <b-col cols="5">
              <p class="swap-token-quantity-warning" :class="{ invisible: swapTokenDetails.tokensBought > 0 }">{{
                  $t("SWAP_TOKEN.SWAP_TOKEN_WARNING", {
                    token: swapTokenDetails.tokensForFeeCover,
                    token_label: swapTokenDetails.tokensForFeeCover > 1 ? this.$t('common.TOKENS').toLocaleLowerCase() : this.$t('common.TOKEN').toLocaleLowerCase()
                  })
                }}</p>
            </b-col>
          </b-row>
        </b-col>
      </b-row>
      <b-row v-if="innerWidth < 1100" class="mt-4" align-h="center">
        <b-col cols="12">
          <h5 style="font-weight: bold">{{ $t("SWAP_TOKEN.CURRENT_PROPERTY") }}</h5>
          <p class="current-property-info mt-1">{{ currentPropertyName }}</p>
          <b-row class="mt-3">
            <b-col cols="6">
              <h5 style="font-weight: bold;">{{ $t("SWAP_TOKEN.YOUR_TOKEN") }}</h5>
            </b-col>
            <b-col cols="6">
              <h5 class="text-right">
                {{ $t("SWAP_TOKEN.TOKEN_AVAILABLE", { value: formatNumberIntl(currentPropertySwappableTokens) }) }}
              </h5>
            </b-col>
          </b-row>
          <p class="receive-token-quantity mt-1 text-center">{{ formatNumberIntl(currentPropertySwappableTokens) }}</p>
          <hr class="solid mb-3" style="margin-top: 25px">
          <h5 style="font-weight: bold">{{ $t("SWAP_TOKEN.NEW_PROPERTY") }}</h5>
          <v-select class="goro-select mt-2" v-model="newProperty" :options="availableProperties"
                    :clearable="false" label="name">
            <template v-slot:option="option">
              {{ option.name }}
            </template>
            <template #selected-option="option">
              {{ option.name }}
            </template>
          </v-select>
          <b-row class="mt-3">
            <b-col cols="6">
              <h5 style="font-weight: bold;">{{ $t("SWAP_TOKEN.PROPERTY_TOKEN") }}</h5>
            </b-col>
            <b-col cols="6">
              <h5 class="text-right">
                {{ $t("SWAP_TOKEN.TOKEN_AVAILABLE", { value: formatNumberIntl(newPropertyAvailableTokens) }) }}
              </h5>
            </b-col>
          </b-row>
          <div class="swap-token-quantity d-flex flex-row align-items-center mt-2">
            <VueNumericInput class="custom" align="center" size="large"
                             :value="quantity" :min="1" :max="swappableTokens" :step="1"
                             @change="onTokenQuantityChanged" @input="onTokenQuantityChanged"/>
            <b-button id="btn_swapToken_swapMax2" class="btn-main pl-3 pr-3 ml-2" style="font-size: 12px; border-radius: 6px"
                      variant="none" @click="swapMax">{{ $t("common.MAX") }}
            </b-button>
          </div>
          <p class="swap-token-quantity-warning" :class="{ invisible: swapTokenDetails.tokensBought > 0 }">{{
              $t("SWAP_TOKEN.SWAP_TOKEN_WARNING", {
                token: swapTokenDetails.tokensForFeeCover,
                token_label: swapTokenDetails.tokensForFeeCover > 1 ? this.$t('common.TOKENS').toLocaleLowerCase() : this.$t('common.TOKEN').toLocaleLowerCase()
              })
            }}</p>
        </b-col>
      </b-row>
      <b-row class="mt-2" align-h="center">
        <b-col :cols="innerWidth > 1350 ? 10 : innerWidth > 1250 ? 11 : 12">
          <div class="swap-details" style="border-radius: 8px">
            <div v-if="swapTokenDetails.displayTransactionFees">
              <b-row align-v="center">
                <b-col cols="12">
                  <h5 class="value-big">{{ $t("SWAP_TOKEN.SWAP_DETAILS") }}</h5>
                </b-col>
              </b-row>
              <hr style="border-top: 0.15rem dashed #D4D4D4; background: none"/>
              <b-row align-v="center">
                <b-col :cols="isExtraSmallScreen ? 12 : 6">
                  <h5 class="title">{{ $t("SWAP_TOKEN.YOUR_BALANCE") }}</h5>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 6" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value color-gray" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ exchangeValue(this.userBalance) }}
                  </h5>
                </b-col>
              </b-row>
              <b-row align-v="center" v-if="swapTokenDetails.displayTransactionFees">
                <b-col :cols="isExtraSmallScreen ? 12 : 7" class="pr-2">
                  <h5 class="title">{{ $t("TRANSACTION_FEE.TITLE") }}
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-transaction-fee" alt="">
                  </h5>
                  <b-tooltip variant="secondary" target="tooltip-transaction-fee" triggers="hover" placement="top">
                    {{ $t("TRANSACTION_FEE.NOTE_FOR_SWAP") }}
                  </b-tooltip>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 5" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value" :class="!isExtraSmallScreen ? `text-right` : ''">
                  <span v-if="swapTokenDetails.transactionFee > 0" class="red">
                  {{ exchangeValue(swapTokenDetails.transactionFee) }}
                  </span>
                    <span v-else class="color-cyan">{{ $t("common.FREE") }}</span>
                  </h5>
                </b-col>
              </b-row>
              <b-row align-v="center" v-if="swapTokenDetails.voucherRewardAmount">
                <b-col :cols="isExtraSmallScreen ? 12 : 7" class="pr-2">
                  <h5 class="title">{{ $t("VOUCHER.PROMO_DISCOUNT") }}
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-voucher-reward" alt="">
                  </h5>
                  <b-tooltip variant="secondary" target="tooltip-voucher-reward" triggers="hover" placement="top">
                    {{ voucherRewardNote }}
                  </b-tooltip>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 5" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ exchangeValue(swapTokenDetails.voucherRewardAmount) }}
                  </h5>
                </b-col>
              </b-row>
              <b-row align-v="center" v-if="swapTokenDetails.refundedAmount">
                <b-col :cols="isExtraSmallScreen ? 12 : 6" class="pr-2">
                  <h5 class="title">{{ $t("SWAP_TOKEN.CREDITED_TO_BALANCE") }} </h5>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 6" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ exchangeValue(swapTokenDetails.refundedAmount) }}
                  </h5>
                </b-col>
              </b-row>
              <b-row align-v="center">
                <b-col :cols="isExtraSmallScreen ? 12 : 6" class="pr-2">
                  <h5 class="title">{{ $t("SWAP_TOKEN.BALANCE_AFTER_SWAP") }} </h5>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 6" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value color-gray" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ exchangeValue(swapTokenDetails.balanceAfterSwap) }}
                  </h5>
                </b-col>
              </b-row>
              <b-row align-v="center">
                <b-col :cols="isExtraSmallScreen ? 12 : 5" class="pr-2">
                  <h5 class="title">{{ $t("SWAP_TOKEN.DEDUCTED_METHOD") }} </h5>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 7" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value color-gray" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ swapTokenDetails.canCoverFeeWithBalance ? $t("SWAP_TOKEN.DEDUCTED_FROM_BALANCE") : $t("SWAP_TOKEN.DEDUCTED_FROM_PROCEEDS") }}
                  </h5>
                </b-col>
              </b-row>
              <hr style="border-top: 0.15rem dashed #D4D4D4; background: none"/>
            </div>
            <b-row align-v="center">
              <b-col :cols="isExtraSmallScreen ? 12 : 7">
                <h5 class="value-big font-semibold">{{ $t("SWAP_TOKEN.TOTAL_RECEIVED_TOKEN") }}</h5>
              </b-col>
              <b-col :cols="isExtraSmallScreen ? 12 : 5" :style="isExtraSmallScreen ? `margin-top: -13px` : ''">
                <h5 class="value-big" :class="!isExtraSmallScreen ? `text-right` : ''">
                  {{ formatNumberIntl(swapTokenDetails.tokensBought) }} {{ swapTokenDetails.tokensBought > 1 ? $t("common.TOKENS") : $t("common.TOKEN") }}
                </h5>
              </b-col>
            </b-row>
          </div>
        </b-col>
      </b-row>
      <b-row class="mt-3" align-h="center">
        <b-col :cols="innerWidth > 1350 ? 10 : innerWidth > 1250 ? 11 : 12">
          <b-row v-if="swapTokenDetails.displayTransactionFees">
            <b-col cols="12">
              <promo-code
                v-if="enableVoucherCodeInput"
                :voucherCodeInput="voucherCodeInput"
                @update:modelValue="$event => {
                  voucherCodeInput = $event
                  voucherCodeError = null
                }"
                :error="voucherCodeError"
                @on-apply="validateVoucherCode"
                @on-clear-voucher="clearVoucherCodeInfo"
                :voucher-code="voucherCode"
                :reward-note="voucherRewardNote"
                titleColor="#006666"
                class="mt-2">
              </promo-code>
            </b-col>
          </b-row>
        </b-col>
      </b-row>

      <b-row align-h="center" style="margin-top: 35px">
        <b-col :cols="innerWidth > 1350 ? 10 : innerWidth > 1250 ? 11 : 12">
          <Form v-slot="{ errors, handleSubmit }" @invalid-submit="onInvalidSwapSubmit">
            <b-form @submit.prevent="handleSubmit(continueToSwap)">
              <Field :name="$t('PAYMENT.I_HAVE_READ_AND_AGREE')"
                     :rules="{ required: {allowFalse: false} }"
                     v-slot="{ valid, errors, field, meta, handleChange }"
                     :model-value="newPropertyAgreeToContract" @update:modelValue="newPropertyAgreeToContract = $event">
                <b-form-group :class="{ shake: this.newPropertyAgreeToContractShaking }">
                  <b-form-checkbox v-model="newPropertyAgreeToContract" name="accept_status"
                                   @change="handleChange" style="font-weight: 400; font-size: 14px; z-index: 0;">
                  <span class="font-14">
                    {{ $t("PAYMENT.I_HAVE_READ_AND_AGREE") }}
                    <u class="click-able token-transaction-agreement" @click.prevent="openNewPropertyContractAgreement">
                      {{ $t("PAYMENT.TOKEN_TRANSACTION_AGREEMENT") }}
                    </u>
                    {{ $t("PAYMENT.I_AGREE_TO_BE_BOUND") }}
                  </span>
                  </b-form-checkbox>
                  <p v-if="errors && errors.length > 0"
                     class="color-error font-medium ml-4 mt-1" style="font-size: 13px">
                    {{ $t("PAYMENT.PLEASE_AGREE_TO_AGREEMENT") }}
                  </p>
                </b-form-group>
              </Field>
              <p v-if="!isNewPropertyPresale" class="next-expected-payout-date mt-3 mb-1">
                {{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE") }} {{ nextExpectedPayoutDate }}
              </p>
              <p v-if="isNewPropertyPresale" class="next-expected-payout-date mt-3 mb-1">
                {{
                  $t("SWAP_TOKEN.PRESALE_EXPECTED_PAYOUT_DESCRIPTION", {
                    propertyName: newPropertyName,
                    firstLiveOn: newPropertyFirstLiveOn
                  })
                }}
              </p>
              <b-button id="btn_swapToken_continueToSwap" class="btn-main col-12 mt-2 mb-1 pt-2 pb-2"
                        type="submit" variant="none" :disabled="swapTokenDetails.tokensBought <= 0">
                {{ $t("SWAP_TOKEN.CONTINUE_TO_SWAP") }}
              </b-button>
            </b-form>
          </Form>
        </b-col>
      </b-row>
    </b-container>

    <b-container v-if="isConfirmSwapRequest">
      <b-row align-content="center">
        <b-col cols="auto" class="pl-0 pr-0">
          <b-button id="btn_swapToken_backConfirm" style="border: none; background-color: transparent; color: black" @click="back">
            <b-icon icon="arrow-left" class="pt-1 pb-1" style="color: black;" scale="0.8"></b-icon>
            {{ $t("common.BACK") }}
          </b-button>
        </b-col>
        <b-col class="pl-0 pr-0">
          <p class="font-weight-bold text-center" style="font-size: 24px;">
            {{ $t("SWAP_TOKEN.SWAP_TOKEN_SUMMARY") }}
          </p>
        </b-col>
      </b-row>
      <b-row class="mt-3" align-h="center">
        <b-col :cols="innerWidth > 1350 ? 10 : innerWidth > 1250 ? 11 : 12">
          <hr class="solid">
        </b-col>
      </b-row>

      <b-row v-if="innerWidth >= 1100" class="mt-3" align-h="center">
        <b-col :cols="innerWidth > 1350 ? 10 : innerWidth > 1250 ? 11 : 12">
          <b-row class="mb-1">
            <b-col cols="5">
              <h5 style="color: #169A97;">{{ $t("SWAP_TOKEN.SWAP_TOKEN") }}</h5>
            </b-col>
            <b-col cols="2"></b-col>
            <b-col cols="5"></b-col>
          </b-row>
          <b-row class="mb-2" align-v="center">
            <b-col cols="5">
              <p class="current-property-info">{{ currentPropertyName }}</p>
            </b-col>
            <b-col cols="2">
              <b-img :src="require('../../assets/img/arrow_swap_right.png')" style="max-width: 90%; height: auto"/>
            </b-col>
            <b-col cols="5">
              <p class="current-property-info">{{ newPropertyName }}</p>
            </b-col>
          </b-row>
        </b-col>
      </b-row>
      <b-row v-if="innerWidth < 1100" class="mt-3" align-h="center">
        <b-col>
          <h5 style="color: #169A97;">{{ $t("SWAP_TOKEN.SWAP_TOKEN") }}</h5>
          <p class="current-property-info">{{ currentPropertyName }}</p>
          <b-img class="mt-2 mb-2" :src="require('../../assets/img/arrow_swap_down.png')"
                 style="max-height: 40px; width: auto; display: block; margin-left: auto; margin-right: auto;"/>
          <p class="current-property-info">{{ newPropertyName }}</p>
        </b-col>
      </b-row>
      <b-row class="mt-4" align-h="center">
        <b-col :cols="innerWidth > 1350 ? 10 : innerWidth > 1250 ? 11 : 12">
          <div class="swap-details">
            <div v-if="swapTokenDetails.displayTransactionFees">
              <b-row class="summary-row" align-v="center">
                <b-col :cols="isExtraSmallScreen ? 12 : 6">
                  <h5 class="title">{{ $t("SWAP_TOKEN.TOTAL_TOKEN") }}</h5>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 6" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value-big" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ formatNumberIntl(swapTokenDetails.tokensSold) }} {{ swapTokenDetails.tokensSold > 1 ? $t("common.TOKENS") : $t("common.TOKEN") }}
                  </h5>
                  <p v-if="newProperty" class="title sub-text font-semibold" :class="!isExtraSmallScreen ? `text-right` : ''" style="font-size: 13px; margin-top: -5px">
                    {{ exchangeValue(pricePerToken) }} / {{ $t("PAYMENT.TOKEN") }}
                  </p>
                </b-col>
              </b-row>
              <b-row class="summary-row" align-v="center">
                <b-col :cols="isExtraSmallScreen ? 12 : 6">
                  <h5 class="title">{{ $t("SWAP_TOKEN.YOUR_BALANCE") }}</h5>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 6" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value-big color-gray" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ exchangeValue(this.userBalance) }}
                  </h5>
                </b-col>
              </b-row>
              <b-row class="summary-row" align-v="center" v-if="swapTokenDetails.displayTransactionFees">
                <b-col :cols="isExtraSmallScreen ? 12 : 7" class="pr-2">
                  <h5 class="title">{{ $t("TRANSACTION_FEE.TITLE") }}
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-transaction-fee-2" alt="">
                  </h5>
                  <b-tooltip variant="secondary" target="tooltip-transaction-fee-2" triggers="hover" placement="top">
                    {{ $t("TRANSACTION_FEE.NOTE_FOR_SWAP") }}
                  </b-tooltip>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 5" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value-big" :class="!isExtraSmallScreen ? `text-right` : ''">
                  <span v-if="swapTokenDetails.transactionFee > 0" class="red">
                  {{ exchangeValue(swapTokenDetails.transactionFee) }}
                  </span>
                    <span v-else class="color-cyan">{{ $t("common.FREE") }}</span>
                    <p v-if="swapTokenDetails.tokensForFeeCover" class="title sub-text" :class="!isExtraSmallScreen ? `text-right` : ''" style="font-size: 13px;">
                      (-{{ formatNumberIntl(swapTokenDetails.tokensForFeeCover) }} {{ swapTokenDetails.tokensForFeeCover > 1 ? $t("common.TOKENS") : $t("common.TOKEN") }})
                    </p>
                  </h5>
                </b-col>
              </b-row>
              <b-row class="summary-row" align-v="center" v-if="swapTokenDetails.voucherRewardAmount">
                <b-col :cols="isExtraSmallScreen ? 12 : 7" class="pr-2">
                  <h5 class="title">{{ $t("VOUCHER.PROMO_DISCOUNT") }}
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-voucher-reward-summary" alt="">
                  </h5>
                  <b-tooltip variant="secondary" target="tooltip-voucher-reward-summary" triggers="hover" placement="top">
                    {{ voucherRewardNote }}
                  </b-tooltip>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 5" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value-big" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ exchangeValue(swapTokenDetails.voucherRewardAmount) }}
                  </h5>
                </b-col>
              </b-row>
              <b-row class="summary-row" align-v="center" v-if="swapTokenDetails.refundedAmount">
                <b-col :cols="isExtraSmallScreen ? 12 : 6" class="pr-2">
                  <h5 class="title">{{ $t("SWAP_TOKEN.CREDITED_TO_BALANCE") }} </h5>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 6" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value-big" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ exchangeValue(swapTokenDetails.refundedAmount) }}
                  </h5>
                </b-col>
              </b-row>
              <hr style="border-top: 0.15rem dashed #D4D4D4; background: none"/>
              <b-row class="summary-row" align-v="center">
                <b-col :cols="isExtraSmallScreen ? 12 : 6" class="pr-2">
                  <h5 class="title">{{ $t("SWAP_TOKEN.BALANCE_AFTER_SWAP") }} </h5>
                </b-col>
                <b-col :cols="isExtraSmallScreen ? 12 : 6" :style="isExtraSmallScreen ? `margin-top: -10px` : ''">
                  <h5 class="value-big color-gray" :class="!isExtraSmallScreen ? `text-right` : ''">
                    {{ exchangeValue(swapTokenDetails.balanceAfterSwap) }}
                  </h5>
                </b-col>
              </b-row>
              <hr style="border-top: 0.15rem dashed #D4D4D4; background: none"/>
            </div>
            <b-row class="summary-row" align-v="center">
              <b-col :cols="isExtraSmallScreen ? 12 : 6">
                <h5 class="value-big font-semibold">{{ $t("SWAP_TOKEN.TOTAL_RECEIVED_TOKEN") }}</h5>
              </b-col>
              <b-col :cols="isExtraSmallScreen ? 12 : 6" :style="isExtraSmallScreen ? `margin-top: -13px` : ''">
                <h5 class="value-big" :class="!isExtraSmallScreen ? `text-right` : ''">
                  {{ formatNumberIntl(swapTokenDetails.tokensBought) }} {{ swapTokenDetails.tokensBought > 1 ? $t("common.TOKENS") : $t("common.TOKEN") }}
                </h5>
              </b-col>
            </b-row>
          </div>
        </b-col>
      </b-row>
      <b-row class="mt-1" align-h="center">
        <b-col :cols="innerWidth > 1350 ? 10 : innerWidth > 1250 ? 11 : 12">
          <div class="next-expected-payout-date medium-notice mt-4 text-center">
            <h3>
              {{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE_TITLE") }}
            </h3>
            <div class="paragraph-with-dot">
              <span class="dot">•</span>
              <p v-if="!isCurrentPropertyPresale" class="text-left mb-3">
                {{ $t("SWAP_TOKEN.CURRENT_EXPECTED_PAYOUT_DESCRIPTION", payoutDescriptionRules) }}
              </p>
              <p v-if="isCurrentPropertyPresale" class="text-left mb-3">
                {{
                  $t("SWAP_TOKEN.PRESALE_EXPECTED_PAYOUT_DESCRIPTION", {
                    propertyName: currentPropertyName,
                    firstLiveOn: currentPropertyFirstLiveOn
                  })
                }}
              </p>
            </div>
            <div class="paragraph-with-dot">
              <span class="dot">•</span>
              <p v-if="!isNewPropertyPresale" class="text-left mb-2">
                {{ $t("SWAP_TOKEN.NEXT_EXPECTED_PAYOUT_DESCRIPTION", payoutDescriptionRules) }} <span class="underline">{{
                  $t("SWAP_TOKEN.NEXT_EXPECTED_PAYOUT_DESCRIPTION_UNDERLINE", payoutDescriptionRules)
                }}</span>
              </p>
              <p v-if="isNewPropertyPresale" class="text-left mb-3">
                {{
                  $t("SWAP_TOKEN.PRESALE_EXPECTED_PAYOUT_DESCRIPTION", {
                    propertyName: newPropertyName,
                    firstLiveOn: newPropertyFirstLiveOn
                  })
                }}
              </p>
            </div>
          </div>
          <!-- Recaptcha V2 checkbox fallback -->
          <div v-if="showRecaptchaV2.CREATE_SWAP_TOKEN_TRANSACTION" :ref="recaptchaV2Checkbox.CREATE_SWAP_TOKEN_TRANSACTION" class="d-flex justify-content-center mt-2"></div>
          <b-button id="btn_swapToken_swapNow" class="btn-main col-12 mt-3 pt-2 pb-2"
                    variant="none" :disabled="swapTokenDetails.tokensBought <= 0" @click="swapNow">
            {{ $t("SWAP_TOKEN.SWAP_NOW") }}
          </b-button>
        </b-col>
      </b-row>
    </b-container>
    <div ref="bottomDiv"></div>

    <popup ref="currentPropertyPopupPendingTask" @on-positive-clicked="openCurrentPropertyContractAgreement"></popup>
    <modal-contract-agreement ref="currentPropertyContractAgreement" @on-agreed-to-contract="onCurrentPropertyAgreedToContract"/>

    <popup ref="newPropertyPopupPendingTask" @on-positive-clicked="openNewPropertyContractAgreement"></popup>
    <modal-contract-agreement ref="newPropertyContractAgreement" @on-agreed-to-contract="onNewPropertyAgreedToContract"/>

    <popup ref="popupTransactionFeeConfirmation" @on-positive-clicked="isConfirmSwapRequest = true"></popup>
    <PopupSecurityPin ref="popUpPinSecurity" @on-success="onPinVerified"></PopupSecurityPin>
  </div>
</template>

<script>
import { useRecaptcha } from "@/composables/useRecaptcha";
import moment from "moment"
import { exchange, formatNumberIntl, getErrorMessage, notify } from "@/helpers/common"
import { gtmTrackEvent } from "@/helpers/gtm"
import { GTM_EVENT_NAMES } from "@/constants/gtm"
import { ERROR_CODE, STORAGE_KEYS, VOUCHER } from "@/constants/constants";
import { Field, Form } from "vee-validate"
import VueNumericInput from "../../components/VueNumericInput"
import accountService from "../../services/account.service"
import contractsService from "../../services/contracts.service"
import propertiesService from "../../services/properties.service"
import swapTokensService from "../../services/swapTokens.service"
import CurrencyTooltip from "../../components/CurrencyTooltip.vue"
import ModalContractAgreement from "../../modals/ModalContractAgreement"
import PopupSecurityPin from "../../components/PopupSecurityPin.vue"
import Popup from "../../components/Popup"
import voucherService from "@/services/voucher.service";
import PromoCode from "../../components/PromoCode.vue"
import { calculateFee } from "@/helpers/fee";
import { calculateVoucherRewardAmount, getVoucherRewardNote } from "@/helpers/voucher";
import store from "@/store/store";
import authService from "@/services/auth.service";

export default {
  components: {
    Form,
    Field,
    VueNumericInput,
    CurrencyTooltip,
    ModalContractAgreement,
    PopupSecurityPin,
    Popup,
    PromoCode
  },
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ CREATE_SWAP_TOKEN_TRANSACTION: 'createSwapTokenTransaction' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data () {
    return {
      title: "Swap Tokens",
      innerWidth: 0,
      currentPropertyUuid: this.$route.query.uuid,
      currentProperty: {},
      currentPropertyTokensStatus: {},
      currentPropertyAgreeToContractPendingTask: null,
      currentPropertyLatestContractTransaction: null,
      newPropertyAgreeToContractPendingTask: null,
      availableProperties: [],
      newProperty: null,
      newPropertyAgreeToContract: false,
      newPropertyAgreeToContractShaking: false,
      quantity: 1,
      voucherCodeInput: "",
      voucherCode: null,
      voucherCodeError: null,
      isConfirmSwapRequest: false,
    }
  },
  watch: {
    newProperty (value) {
      this.getNewPropertyContractStatus()
    }
  },
  async mounted () {
    this.handleWindowResize()
    window.addEventListener("resize", this.handleWindowResize)
    await Promise.all([
      this.getProperty(),
      this.getUserProfile(),
      this.getTokensStatus(),
      this.getAvailableProperties(),
      this.getCurrentPropertyContractStatus(),
    ])
  },
  async beforeDestroy () {
    window.removeEventListener("resize", this.handleWindowResize)
  },
  methods: {
    formatNumberIntl,
    handleWindowResize () {
      this.innerWidth = window.innerWidth
    },
    async getUserProfile () {
      const res = await authService.getUserProfile(false)
      if (res && res.data) {
        await store.dispatch('setUserProfile', res.data);
      }
    },
    async getProperty () {
      const res = await propertiesService.getByUuid(this.currentPropertyUuid)
      this.currentProperty = res.data
    },
    async getTokensStatus () {
      if (this.$store.getters.userProfile && localStorage.getItem(STORAGE_KEYS.AUTHORIZATION.key) && this.currentPropertyUuid) {
        const res = await accountService.getTokensStatus(this.currentPropertyUuid)
        if (res && res.data) {
          this.currentPropertyTokensStatus = res.data
        }
      }
    },
    async getAvailableProperties () {
      const result = await propertiesService.getAvailableProperties()
      if (result) {
        this.availableProperties = result.filter(property => property.uuid !== this.currentPropertyUuid)
        if (this.availableProperties.length > 0) {
          this.newProperty = this.availableProperties[0]
        }
      }
    },
    async getCurrentPropertyContractStatus () {
      if (this.$store.getters.userProfile && localStorage.getItem(STORAGE_KEYS.AUTHORIZATION.key) && this.currentPropertyUuid) {
        this.currentPropertyAgreeToContractPendingTask = null
        const response = await contractsService.getContractStatus(this.currentPropertyUuid)
        if (response) {
          this.currentPropertyAgreeToContractPendingTask = response.agree_to_contract_pending_task
        }
      }
    },
    async getNewPropertyContractStatus () {
      if (this.$store.getters.userProfile && localStorage.getItem(STORAGE_KEYS.AUTHORIZATION.key) && this.newProperty) {
        this.newPropertyAgreeToContractPendingTask = null
        const response = await contractsService.getContractStatus(this.newProperty.uuid)
        if (response) {
          this.newPropertyAgreeToContractPendingTask = response.agree_to_contract_pending_task
        }
      }
    },
    onTokenQuantityChanged (newValue) {
      this.quantity = newValue
    },
    exchangeValue (value) {
      return exchange(value)
    },
    swapMax () {
      this.onTokenQuantityChanged(this.swappableTokens)
    },
    clearVoucherCodeInfo () {
      this.voucherCodeInput = "";
      this.voucherCode = null;
      this.voucherCodeError = null;
    },
    async validateVoucherCode () {
      try {
        const response = await voucherService.validateVoucherCode({ voucher_code: this.voucherCodeInput, action: VOUCHER.REDEEM_ACTION_SWAP }, true);
        if (response.voucher_code) {
          this.voucherCode = response.voucher_code
          this.voucherCodeError = null
        }
      } catch (error) {
        this.voucherCode = null
        this.voucherCodeError = getErrorMessage(error)
      }
    },
    isValidSwapRequest () {
      if (this.swapTokenDetails.tokensBought <= 0) {
        notify({
          text: this.$t("SWAP_TOKEN.NUMBER_OF_TOKEN_MUST_BE_POSITIVE"),
          type: "error"
        })
        return false
      }
      return true
    },
    async openCurrentPropertyContractAgreement () {
      const contractPreviewUrl = await contractsService.getContractPreviewForExisting({ property_uuid: this.currentPropertyUuid })
      if (contractPreviewUrl) {
        this.$refs.currentPropertyContractAgreement.showModal(contractPreviewUrl, this.currentPropertyUuid)
      }
    },
    async onCurrentPropertyAgreedToContract (data) {
      if (data && data.propertyUuid === this.currentPropertyUuid) {
        await this.getCurrentPropertyContractStatus()
      }
    },
    async openNewPropertyContractAgreement () {
      if (this.newProperty) {
        const contractPreviewUrl = await contractsService.getContractPreviewForExisting({ property_uuid: this.newProperty.uuid })
        if (contractPreviewUrl) {
          this.$refs.newPropertyContractAgreement.showModal(contractPreviewUrl, this.newProperty.uuid)
        }
      }
    },
    async onNewPropertyAgreedToContract (data) {
      if (data && data.propertyUuid === this.newProperty.uuid) {
        await this.getNewPropertyContractStatus()
      }
    },
    async onInvalidSwapSubmit ({ values, errors, results }) {
      if (!this.newPropertyAgreeToContract) {
        this.newPropertyAgreeToContractShaking = true
        this.$refs.bottomDiv.scrollIntoView({ behavior: 'smooth' })
        setTimeout(() => {
          this.newPropertyAgreeToContractShaking = false
        }, 1500)
      }
    },
    async continueToSwap () {
      if (this.currentPropertyAgreeToContractPendingTask) {
        this.$refs.currentPropertyPopupPendingTask.openPopup({
          title: this.$t("PENDING_TASKS.COMPLETE_PENDING_TASK"),
          message: this.$t("SWAP_TOKEN.PLEASE_READ_AND_AGREE_TO_AGREEMENT_OF_CURRENT_PROPERTY"),
          positiveButton: this.$t("common.OK")
        })
      } else if (this.newPropertyAgreeToContractPendingTask) {
        this.$refs.newPropertyPopupPendingTask.openPopup({
          title: this.$t("PENDING_TASKS.COMPLETE_PENDING_TASK"),
          message: this.$t("PENDING_TASKS.PLEASE_READ_AND_AGREE_TO_AGREEMENT"),
          positiveButton: this.$t("common.OK")
        })
      } else if (this.isValidSwapRequest()) {
        if (this.swapTokenDetails.tokensForFeeCover > 0) {
          const storageKey = STORAGE_KEYS.SWAP_TOKEN_TRANSACTION_FEE_CONFIRMATION.key
          const dontShowAgainTimestamp = Number(localStorage.getItem(storageKey) || 0)
          const shouldShowConfirmation = dontShowAgainTimestamp < Date.now()
          if (shouldShowConfirmation) {
            this.$refs.popupTransactionFeeConfirmation.openPopup({
              title: this.$t("common.NOTIFICATION"),
              message: this.$t("TRANSACTION_FEE.SWAP_TOKEN_CONFIRMATION_MESSAGE"),
              warningMessage: this.$t("TRANSACTION_FEE.SWAP_TOKEN_CONFIRMATION_WARNING"),
              dontShowAgainKey: storageKey,
              positiveButton: this.$t("MODALS.COMMON.CONTINUE"),
              warningMessageClass: "warning-message"
            })
          } else {
            this.isConfirmSwapRequest = true
          }
        } else {
          this.isConfirmSwapRequest = true
        }
      }
    },
    async back () {
      this.isConfirmSwapRequest = false
    },
    async swapNow () {
      if (this.isValidSwapRequest()) {
        await this.$refs.popUpPinSecurity.openPopup(true)
      }
    },
    async onPinVerified(pin) {
      if (!this.validateRecaptchaV2.CREATE_SWAP_TOKEN_TRANSACTION()) return
      try {
        const recaptchaTokenV3 = await this.recaptchaV3Exec.CREATE_SWAP_TOKEN_TRANSACTION()
        const recaptchaTokenV2 = this.recaptchaTokenV2.CREATE_SWAP_TOKEN_TRANSACTION
        let response = await swapTokensService.createSwapTokenTransaction({
          current_property_uuid: this.currentProperty.uuid,
          new_property_uuid: this.newProperty.uuid,
          num_of_tokens: this.quantity,
          use_balance: this.swapTokenDetails.canCoverFeeWithBalance,
          voucher_code: this.voucherCodeInput,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
          pin,
        }, false, true)
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.SWAP_PROPERTY,
          current_property_uuid: this.currentProperty.uuid,
          current_property_name: this.currentProperty.name,
          new_property_uuid: this.newProperty.uuid,
          new_property_name: this.newProperty.name,
          num_of_tokens: this.quantity,
        })
        if (response && response.swap_token_transaction) {
          gtmTrackEvent({
            event: GTM_EVENT_NAMES.SWAP_PROPERTY_SUCCESS,
            current_property_uuid: this.currentProperty.uuid,
            current_property_name: this.currentProperty.name,
            new_property_uuid: this.newProperty.uuid,
            new_property_name: this.newProperty.name,
            num_of_tokens: this.quantity,
          })
          await this.$router.push({
            name: "swapTokenSuccess",
            query: { request_uuid: String(response.swap_token_transaction.uuid) }
          })
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.CREATE_SWAP_TOKEN_TRANSACTION = true
        } else {
          notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.CREATE_SWAP_TOKEN_TRANSACTION()
      }
    },
  },
  computed: {
    isExtraSmallScreen () {
      return this.innerWidth < 576
    },
    userProfile() {
      return this.$store.getters.userProfile
    },
    userBalance() {
      return this.userProfile && this.userProfile.balance || 0
    },
    pricePerToken () {
      return this.newProperty && this.newProperty.price_per_token || 10000
    },
    currentPropertyName () {
      return this.currentProperty && this.currentProperty.name || ""
    },
    isCurrentPropertyPresale () {
      return this.currentProperty && this.currentProperty.status === "presale"
    },
    currentPropertyFirstLiveOn () {
      return this.currentProperty ? moment(this.currentProperty.first_live_on).format("DD/MM/YYYY") : ""
    },
    currentPropertySwappableTokens () {
      return this.currentPropertyTokensStatus
          && this.currentPropertyTokensStatus.asset_sellable_tokens - this.currentPropertyTokensStatus.pending_sell_tokens || 0
    },
    newPropertyName () {
      return this.newProperty && this.newProperty.name || ""
    },
    isNewPropertyPresale () {
      return this.newProperty && this.newProperty.status === "presale"
    },
    newPropertyFirstLiveOn () {
      return this.newProperty ? moment(this.newProperty.first_live_on).format("DD/MM/YYYY") : ""
    },
    newPropertyAvailableTokens () {
      if (this.newProperty) {
        return Math.min(this.newProperty.total_tokens - this.newProperty.display_sold_tokens, this.newProperty.total_tokens - this.newProperty.sold_tokens)
      }
      return 0
    },
    swappableTokens () {
      return Math.min(this.currentPropertySwappableTokens, this.newPropertyAvailableTokens)
    },
    enableVoucherCodeInput () {
      return store.state.configs.enable_voucher_code_input
    },
    swapTokenDetails () {
      let tokensSold = this.quantity;
      let tokensForFeeCover = 0;
      let tokensBought = this.quantity;
      let refundedAmount = 0;
      let balanceAfterSwap = 0;

      const baseAmount = this.pricePerToken * this.quantity
      const displayTransactionFees = this.$store.getters.configs.display_transaction_fees
      const transactionFeeConfig = this.$store.getters.configs.swap_token_transaction_fees
      const transactionFee = displayTransactionFees ? calculateFee(transactionFeeConfig, baseAmount) : 0

      let voucherRewardAmount = 0
      let voucherRewardType = this.voucherCode?.voucher?.reward_type
      let voucherRequiredTransactionAmount = this.voucherCode?.voucher?.required_transaction_amount || 0;
      if (baseAmount >= voucherRequiredTransactionAmount) {
        switch (voucherRewardType) {
          case VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES:
          case VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_SWAP:
            voucherRewardAmount = Math.min(calculateVoucherRewardAmount(this.voucherCode, transactionFee), transactionFee)
            break;
        }
      }

      const remainingFeeToCover = transactionFee - voucherRewardAmount;
      const canCoverFeeWithBalance = this.userBalance >= remainingFeeToCover;
      if (!canCoverFeeWithBalance) {
        tokensForFeeCover = Math.ceil(remainingFeeToCover / this.pricePerToken);
        refundedAmount = tokensForFeeCover * this.pricePerToken - remainingFeeToCover;
        tokensBought = tokensSold - tokensForFeeCover;
        balanceAfterSwap = this.userBalance + refundedAmount;
      } else {
        balanceAfterSwap = this.userBalance - remainingFeeToCover;
      }

      return {
        baseAmount, tokensSold, tokensBought, tokensForFeeCover, refundedAmount, canCoverFeeWithBalance,
        displayTransactionFees, transactionFeeConfig, transactionFee, voucherRewardAmount, remainingFeeToCover, balanceAfterSwap
      }
    },
    voucherRewardNote () {
      return getVoucherRewardNote(this.voucherCode, VOUCHER.REDEEM_ACTION_SWAP, this.$t, this.exchangeValue)
    },
    nextExpectedPayoutDate () {
      let expectedPayoutDate = process.env.VUE_APP_EXPECTED_PAYOUT_DATE
      return moment().set("date", expectedPayoutDate).add(1, "M").format("MMM DD, YYYY")
    },
    payoutDescriptionRules () {
      /**
       * Here's the mean of the value to get and show
       *
       * Your rental income from {currentPropertyName} until {yesterday} will be distributed at the next rental distribution.
       *
       * For {newPropertyName}, the next rental income will be distributed before {nextPayoutDate}.
       * Your rental income will be pro-rata adjusted based on {receiveRentFrom} to {receiveRentTo} ({totalDaysReceiveRent}).
       * This only applies if you don’t sell or swap your tokens before the end of {currentMonth}.
       *
       *
       * Example:
       * Your rental income from Villa Bae until 01 February 2024 will be distributed at the next rental distribution.
       *
       * For Villa Kulibul, the next rental income will be distributed before 21 March 2024.
       * Your rental income will be pro-rata adjusted based on 02 to 29 February 2024 (27 days).
       * This only applies if you don’t sell or swap your tokens before the end of February 2024.
       */
      const currentPropertyName = this.currentProperty.name
      const yesterday = moment().subtract(1, "d").format("DD MMMM YYYY")

      const newPropertyName = this.newProperty.name
      const nextPayoutDate = moment().set("date",
          process.env.VUE_APP_EXPECTED_PAYOUT_DATE).add(1, "M").format("DD MMMM YYYY")
      // Get the receive from. This is the buy token date
      const receiveRentFromDate = moment().startOf("day")
      const receiveRentFrom = receiveRentFromDate.clone().format("DD")
      const receiveRentTo = receiveRentFromDate.clone().endOf("month").format("DD MMMM YYYY")
      // Total days: Date range of Buy Tokens starting from buy date until end of the month
      const totalDaysReceiveRent = moment(receiveRentTo).diff(receiveRentFromDate, "days") + 1 // Include the start day
      const totalDaysReceiveRentLabel = (totalDaysReceiveRent > 1) ? this.$t("common.DAYS") : this.$t("common.DAY")
      // The month of the buy token date
      const currentMonth = moment().format("MMMM YYYY")

      return {
        currentPropertyName,
        yesterday,
        newPropertyName,
        nextPayoutDate,
        receiveRentFrom,
        receiveRentTo,
        totalDaysReceiveRent,
        totalDaysReceiveRentLabel,
        currentMonth
      }
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
}
</script>

<style lang="scss">
.swap-form {
  background-color: white;
  box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
  border-radius: 16px;
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;

  p {
    margin: 0;
    padding: 0;
  }

  h5 {
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 16px;
    color: var(--primary-color);
  }

  hr {
    //margin-top: 15px;
    //margin-bottom: 15px;
    height: 1px;
    color: #E8E8E8;
    background-color: #E8E8E8;
  }

  .current-property-info {
    background-color: #EBEBEB;
    color: #828282;
    font-weight: 600;
    font-size: 17px;
    border-radius: 10px;
    padding: 15px 22px;
  }

  .swap-token-quantity {
    background-color: white;
    padding: 5px 5px;
    border: 2px solid var(--primary-color);
    border-radius: 10px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
  }

  .swap-token-quantity-warning {
    margin-top: 5px;
    font-size: 14px;
    color: red;
  }

  .receive-token-quantity {
    background-color: #EBEBEB;
    font-weight: 600;
    font-size: 17px;
    border: 1px solid var(--color-in-progress);
    border-radius: 10px;
    padding: 15px 22px;
  }

  .swap-details {
    padding: 16px 20px;
    border: 1.5px solid #CECECE;
    border-radius: 12px;
    //box-shadow: 0 1px 6px rgba(7, 55, 99, 0.16);

    .title {
      font-weight: normal;
      font-size: 16px;
      color: var(--text-color-secondary);
    }

    .value {
      font-weight: bold;
      font-size: 16px;
      color: var(--primary-color);
    }

    .value-big {
      font-weight: bold;
      font-size: 20px;
      color: var(--primary-color);
    }

    .compound-tooltip-icon {
      margin-bottom: 2px;
      width: 15px;
      height: 15px;
      z-index: 1;
    }

    .summary-row {
      min-height: 2.8rem;
    }
  }

  /* Start: customize goro-select css*/
  .goro-select .vs__dropdown-toggle {
    background-color: white;
    color: var(--primary-color);
    font-weight: 500;
    font-size: 17px;
    padding: 8px 0 14px 10px;
    border-radius: 10px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
  }

  /* End: customize goro-select css*/

  .next-expected-payout-date {
    padding: 6px 6px;
    color: white;
    background: darkorange;
    border-radius: 16px;
    font-size: 13px;
    text-align: center;

    &.medium-notice {
      padding: 20px 55px;

      h3 {
        margin-top: 0;
      }

      p {
        .underline {
          text-decoration: underline;
          text-underline-offset: 3px;
        }
      }
    }
  }

  .token-transaction-agreement {
    color: #00B7B6;
  }

  .paragraph-with-dot {
    display: flex;
    align-items: flex-start;
  }

  .dot {
    margin-top: -4px;
    margin-right: 6px;
    font-size: 20px;
  }

  .custom.vue-numeric-input {
    width: 100% !important;
    height: 40px;
    font-size: 18px;
    font-weight: 500;
  }

  .custom.vue-numeric-input .numeric-input {
    height: 100%;
    padding: 2px;
    color: var(--primary-color);
    border-radius: 0;
    border-color: transparent;
  }

  .custom.vue-numeric-input .numeric-input:focus {
    color: var(--primary-color);
    border-color: transparent;
  }

  .custom.vue-numeric-input button {
    border-radius: 50%;
    width: 22px;
    height: 22px;
    margin-top: 8px;
    margin-left: 10px;
    margin-right: 10px;
    color: var(--primary-color);
    background: transparent;
    border: transparent;
    box-shadow: none;
  }

  .custom.vue-numeric-input button:hover {
    color: var(--primary-color);
    opacity: 0.5;
    background: transparent;
    box-shadow: none;
  }

  .custom.vue-numeric-input button:active {
    color: var(--primary-color);
    opacity: 0.5;
    background: transparent;
    box-shadow: none;
  }

  .vue-numeric-input .btn-increment .btn-icon:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23006666' viewBox='0 0 16 16'%3E%3Cpath d='M8 0a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2H9v6a1 1 0 1 1-2 0V9H1a1 1 0 0 1 0-2h6V1a1 1 0 0 1 1-1z'/%3E%3C/svg%3E");
  }

  .vue-numeric-input .btn-decrement .btn-icon:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23006666' viewBox='0 0 16 16'%3E%3Cpath d='M0 8a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H1a1 1 0 0 1-1-1z'/%3E%3C/svg%3E");
  }
}

.shake {
  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  transform: translate3d(0, 0, 0);
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

.with-button {
  padding-right: 10px; /* Space for the button inside the textbox */
  height: 55px;
}

.custom-text {
  width: 100%;
  border-color: transparent;
  flex-wrap: wrap;
}

.error-text {
  margin-top: 20px;
  font-size: 14px;
  text-align: left;
  color: red;
  white-space: pre;
}

.strikethrough {
  text-decoration: line-through;
  color: var(--text-color-secondary);
}

.bigger {
  font-size: 1.3rem; /* adjust as needed */
}

</style>
