<template>
  <div class="ts-container">
    <p class="font-28 font-weight-bold">{{ $t("PENDING_TASKS.PENDING_TASKS") }}</p>
    <div class="task-content d-flex flex-column">
      <!--Tabs content-->
      <div class="tab-content mt-2">
        <b-tabs content-class="mt-3" class="tab__primary">
          <b-tab :title="$t('TAB_CONTENT_HEADER.PENDING')" active>
            <div class="tab__details">
              <div v-if="!rows && showNoPendingTasks" class="text-center">
                <p class="no-transactions mb-4">{{ $t("PENDING_TASKS.THERE_ARE_NO_PENDING_TASKS") }}</p>
                <router-link :to="{ name: 'marketplace' }">
                  <b-button id="btn_pendingTasks_marketplace" class="btn-main" type="submit" variant="none">
                    {{ $t("account.marketplace") }}
                  </b-button>
                </router-link>
              </div>
              <b-table v-if="totalItems" responsive hover head-variant="light" id="my-table" :fields="fields" :items="items"
                      :per-page="0" :current-page="currentPage" small>
                <template v-slot:cell(task)="data">
                  <p v-html="data.item.task"></p>
                </template>
                <template v-slot:cell(action)="data">
                  <b-button v-if="showAction(data.item)" class="btn-main single-line" type="submit" variant="none"
                            @click="onRowClicked(data.item)">
                    {{ data.item.action }}
                  </b-button>
                </template>
              </b-table>
              <b-pagination v-if="totalItems" class="align-self-end justify-content-end" v-model="currentPage" :total-rows="totalItems" :per-page="perPage" 
                            aria-controls="my-table">
              </b-pagination>
            </div>
          </b-tab>
          <b-tab :title="$t('TAB_CONTENT_HEADER.DONE')">
            <div v-if="!totalItemsPageCompleted" class="text-center">
              <p class="no-transactions mb-4">{{ $t("COMPLETED_TASKS.THERE_ARE_NO_COMPLETED_TASKS") }}</p>
              <router-link :to="{ name: 'marketplace' }">
                <b-button id="btn_noCompletedTasks_backToMarketplace" class="btn-main" type="submit" variant="none">
                  {{ $t("account.marketplace") }}
                </b-button>
              </router-link>
            </div>
            <b-table v-if="totalItemsPageCompleted" responsive hover head-variant="light" id="my-table" :fields="completedFields" :items="completedItems"
                    :per-page="0" :current-page="currentPageCompleted" small>
              <template v-slot:cell(task)="data">
                <b-button v-if="isCompletedTask(data.item)" class="font-14 cls-complete-task-status mb-2 single-line" variant="success">
                  {{ $t("COMPLETED_TASKS.STATUS_COMPLETED") }}
                </b-button>
                <b-button v-if="isExpiredTask(data.item)" class="font-14 cls-complete-task-status mb-2 single-line" variant="danger">
                  {{ $t("COMPLETED_TASKS.STATUS_EXPIRED") }}
                </b-button>
                <p v-html="data.item.task"></p>
              </template>
            </b-table>
            <b-pagination v-if="totalItemsPageCompleted" class="align-self-end justify-content-end" v-model="currentPageCompleted" :total-rows="totalItemsPageCompleted" :per-page="perPage"
                          aria-controls="my-table">
            </b-pagination>
          </b-tab>
        </b-tabs>
      </div>

      <!--End tab-->
    </div>
    <modal-contract-agreement ref="contractAgreement" @on-agreed-to-contract="onAgreedToContract"/>
  </div>
</template>

<script>

import accountService from "../../services/account.service"
import notificationsService from "../../services/notifications.service"
import { PENDING_TASK_TYPE, TASK_STATUSES } from "../../constants/constants"
import contractsService from "../../services/contracts.service"
import ModalContractAgreement from "../../modals/ModalContractAgreement"
import Popup from "../../components/Popup"
import moment from "moment"
import { exchange, numberWithCommas } from "../../helpers/common"
import { PAGINATION_DEFAULT } from "../../constants/pagination"

export default {
  components: { Popup, ModalContractAgreement },
  data () {
    return {
      perPage: PAGINATION_DEFAULT.perPage,
      currentPage: 1,
      currentPageCompleted: 1,
      totalItems: 0,
      totalItemsPageCompleted: 0,
      items: [],
      completedItems: [],
      showNoPendingTasks: false,
      fields: this.getTableHeaderFields(),
      completedFields: this.getTableHeaderCompletedFields(),
    }
  },
  async mounted () {
    await this.getPendingTasks()
    await this.getCompletedTasks()
  },

  watch: {
    "$i18n.locale" (newVal, oldVal) {
      // Translating the text for pending tasks
      this.items = this.items.map(e => {
        return this.formatPendingTaskItem(e.item)
      })
      this.fields = this.getTableHeaderFields()

      // Translating the text for completed tasks
      this.completedItems = this.completedItems.map(e => {
        return this.formatCompletedTaskItem(e.item)
      })
      this.completedFields = this.getTableHeaderCompletedFields()
    },
    currentPage: {
      handler: async function(value) {
        await this.getPendingTasks()
      }
    },
    currentPageCompleted: {
      handler: async function(value) {
        await this.getCompletedTasks()
      }
    }
  },

  methods: {
    formatPendingTaskItem(e) {
      if (!e) return null
      let task = ""
      let action = ""
      if (PENDING_TASK_TYPE.AGREE_TO_CONTRACT.toLowerCase() === e.type.toLowerCase()) {
        task = this.$t("PENDING_TASKS.READ_AND_AGREE_TOKEN_TRANSACTION_AGREEMENT", { value: e.property.name })
        action = this.$t("PENDING_TASKS.ACTION_SIGN_NOW")
      } else if (PENDING_TASK_TYPE.CLAIM_VIRTUAL_RENTAL_INCOME.toLowerCase() === e.type.toLowerCase()) {
        task = this.$t("PENDING_TASKS.CLAIM_RENTAL_INCOME", {
          value: e.property.name,
          month: e.notification.attachment.date,
          amount: exchange(e.notification.attachment.amount, 100, false, 'IDR')
        })
        action = this.$t("PENDING_TASKS.CLAIM")
      } else if (PENDING_TASK_TYPE.CLAIM_REFERRAL_BONUS.toLowerCase() === e.type.toLowerCase()) {
        task = this.$t("PENDING_TASKS.CLAIM_REFERRAL_BONUS", { amount: exchange(e.notification.attachment.amount, 100, false, 'IDR') })
        action = this.$t("PENDING_TASKS.CLAIM")
      }

      let expires_on = null
      if (e.notification && e.notification.expires_on) {
        expires_on = e.notification.expires_on
      }

      return {
        task: task,
        type: e.type,
        status: e.status,
        action: action,
        item: e,
        property: e.property,
        notification: e.notification,
        expires_on: expires_on ? moment(expires_on).format("DD/MM/YYYY HH:mm") : ""
      }
    },

    formatCompletedTaskItem(e) {
      if (!e) return null

      let task = ""
      let date = e.updated_at || null

      if (PENDING_TASK_TYPE.AGREE_TO_CONTRACT.toLowerCase() === e.type.toLowerCase()) {
        task = this.$t("COMPLETED_TASKS.CONTRACT", { property_name: e.property.name })
      } else if (PENDING_TASK_TYPE.CLAIM_VIRTUAL_RENTAL_INCOME.toLowerCase() === e.type.toLowerCase()) {
        task = this.$t("COMPLETED_TASKS.CASHBACK", {
          amount: `${e.notification.attachment.currency}${numberWithCommas(e.notification.attachment.amount)}`,
        })
      } else if (PENDING_TASK_TYPE.CLAIM_REFERRAL_BONUS.toLowerCase() === e.type.toLowerCase()) {
        if  (e.notification && (e.status.toLowerCase() === TASK_STATUSES.EXPIRED.toLowerCase())) {
          if (e.notification.attachment) {
            task = this.$t("COMPLETED_TASKS.EXPIRED", {
              amount: `${e.notification.attachment.currency}${numberWithCommas(e.notification.attachment.amount)}` 
            })
          }

          if (e.notification.expires_on) {
            date = e.notification.expires_on
          }
        } else if (e.notification && (e.status.toLowerCase() === TASK_STATUSES.COMPLETED.toLowerCase())) {
          if (e.notification.attachment) {
            task = this.$t("COMPLETED_TASKS.CASHBACK", {
              amount: `${e.notification.attachment.currency}${numberWithCommas(e.notification.attachment.amount)}` 
            })
          }

          if (e.notification.claimed_on) {
            date = e.notification.claimed_on
          }
        }
      }

      return {
        item: e,
        status: e.status,
        task: task,
        date: date ? moment(date).format("DD/MM/YYYY HH:mm") : "",
      }
    },

    async getPendingTasks () {
      const filters = {
        page: this.currentPage,
        per_page: this.perPage,
      }
      const response = await accountService.getPendingTasks(filters)
      if (response && response.data) {
        this.totalItems = response && response.total ? parseInt(response.total) : 0
        this.items = response.data.map(e => {
          return this.formatPendingTaskItem(e)
        })
      }
      this.showNoPendingTasks = true
    },

    async getCompletedTasks () {
      const filters = {
				page: this.currentPageCompleted,
				per_page: this.perPage,
			}
      const response = await accountService.getCompletedTasks(filters)
      if (response && response.data) {
        this.totalItemsPageCompleted = response && response.total ? parseInt(response.total) : 0
        this.completedItems = response.data.map(e => {
          return this.formatCompletedTaskItem(e)
        })
      }
    },

    getTableHeaderFields () {
      return [
        { key: "task", label: this.$t("PENDING_TASKS.TABLE_HEADER_TASK") },
        //{ key: "type", label: this.$t("PENDING_TASKS.TABLE_HEADER_TYPE") },
        { key: "status", label: this.$t("PENDING_TASKS.TABLE_HEADER_STATUS") },
        { key: "expires_on", label: this.$t("PENDING_TASKS.TABLE_HEADER_EXPIRES_ON") },
        { key: "action", label: this.$t("PENDING_TASKS.TABLE_HEADER_ACTION") },
      ]
    },

    getTableHeaderCompletedFields () {
      return [
        { key: "task", label: this.$t("PENDING_TASKS.TABLE_HEADER_TASK") },
        { key: "date", label: this.$t("PENDING_TASKS.TABLE_HEADER_DATE") },
      ]
    },

    async onRowClicked(item, index, evt) {
      if (PENDING_TASK_TYPE.CLAIM_VIRTUAL_RENTAL_INCOME.toLowerCase() === item.type.toLowerCase()
        || PENDING_TASK_TYPE.CLAIM_REFERRAL_BONUS.toLowerCase() === item.type.toLowerCase()) {
        await this.claimRentalIncome(item.notification.id)
      } else if (PENDING_TASK_TYPE.AGREE_TO_CONTRACT.toLowerCase() === item.type.toLowerCase()) {
        await this.openContractAgreement(item.property.uuid)
      }
    },

    async claimRentalIncome (notificationId) {
      const res = await notificationsService.claimNotification({ id: notificationId })
      if (res && res.task) {
        this.items = this.items.filter(e =>
          (PENDING_TASK_TYPE.CLAIM_VIRTUAL_RENTAL_INCOME.toLowerCase() !== e.type.toLowerCase()
            && PENDING_TASK_TYPE.CLAIM_REFERRAL_BONUS.toLowerCase() !== e.type.toLowerCase())
          || e.notification.id !== notificationId)
        // noinspection ES6MissingAwait
        this.getPendingTasksCount()
      }
    },

    async openContractAgreement (propertyUuid) {
      const contractPreviewUrl = await contractsService.getContractPreviewForExisting({ property_uuid: propertyUuid })
      this.$refs.contractAgreement.showModal(contractPreviewUrl, propertyUuid)
    },

    async onAgreedToContract (data) {
      if (data && data.propertyUuid) {
        this.items = this.items.filter(e => PENDING_TASK_TYPE.AGREE_TO_CONTRACT.toLowerCase() !== e.type.toLowerCase()
          || e.property.uuid !== data.propertyUuid)
      }
    },

    async getPendingTasksCount () {
      await store.dispatch("getPendingTaskCount")
    },

    showAction (item) {
      if (PENDING_TASK_TYPE.CLAIM_VIRTUAL_RENTAL_INCOME.toLowerCase() === item.type.toLowerCase()) {
        return moment() <= moment(item.notification.expires_on)
      }
      return true
    },
    isCompletedTask(item) {
      return item && (item.status.toLowerCase() === TASK_STATUSES.COMPLETED.toLowerCase())
    },
    isExpiredTask(item) {
      return item && (item.status.toLowerCase() === TASK_STATUSES.EXPIRED.toLowerCase())
    },
  },
  computed: {
    rows () {
      return this.items.length
    },
    completedRows () {
      return this.completedItems.length
    },
  }
}
</script>

<style lang="scss">
.ts-container {
  width: 100%;
  margin-top: 20px;

  .task-content {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding: 20px;
    margin-top: 15px;

    .no-tasks {
      font-size: 18px;
      font-weight: 600;
    }

    .b-table > tbody > tr {
      cursor: pointer;
    }
  }
  .cls-complete-task-status{
    padding: 3px 15px;
  }
}
</style>
