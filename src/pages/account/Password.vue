<template>
    <div class="pw-container">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-7 mt-2">
                <p class="font-28 font-weight-bold">{{ $t('ACCOUNT_SECURITY.PASSWORD.TITLE') }}</p>
                <div class="content">
                    <div class="goro-row">
                        <div class="d-flex flex-row mt-2 align-items-center">
                            <div class="goro-heading">
                                <span class="goro-title font-weight-bold">{{ $t('ACCOUNT_SECURITY.PASSWORD.TITLE') }}</span>
                                <span class="show-desktop section-item-text-description">{{
                    $t('ACCOUNT_SECURITY.PASSWORD.DESCRIPTION') }}</span>
                            </div>
                            <span class="goro-hint">********</span>
                            <router-link :to="{ name: 'changePassword' }" class="btn btn-secondary btn-main white-normal">
                                {{ $t('ACCOUNT_SECURITY.MODIFY') }}
                            </router-link>
                        </div>
                        <span class="show-mobile section-item-text-description">{{ $t('ACCOUNT_SECURITY.PASSWORD.DESCRIPTION')
                            }}</span>
                        <div class="goro-warning-block">
                            <span class="warning font-weight-bold">{{ $t('ACCOUNT_SECURITY.PASSWORD.WARNING.TITLE') }}:</span>
                            <ul class="goro-warning-lists">
                                <li>
                                    {{ $t('ACCOUNT_SECURITY.PASSWORD.WARNING.WARNING_1') }}
                                </li>
                                <li>
                                    {{ $t('ACCOUNT_SECURITY.PASSWORD.WARNING.WARNING_2') }}
                                </li>
                                <li>
                                    {{ $t('ACCOUNT_SECURITY.PASSWORD.WARNING.WARNING_3') }}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>

</script>
<style lang="scss" scoped>
.pw-container {
    width: 100%;
    margin-top: 20px;

    .content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding: 20px;
        margin-top: 15px;

        .goro-row {
            .goro-heading {
                flex: 1;
                display: flex;
                flex-direction: column;

                span {
                    font-size: 14px;
                    color: gray;
                }

                .goro-title {
                    font-size: 18px;
                    color: var(--primary-color);
                }
            }

            .section-item-text-description {
                font-size: 14px;
                color: gray;
            }

            .goro-hint {
                opacity: .7;
                font-size: 15px;
                padding: 0px 20px;
                line-height: 10px;
            }
        }

        .goro-warning-block {
            background: #fef6d8;
            border-radius: 16px;
            padding: 20px;
            margin-top: 15px;

            .warning {
                font-size: 15px;
                color: rgb(51, 51, 51);
                margin-bottom: 7px;
                display: block;
            }

            .goro-warning-lists {
                padding-left: 20px;
                margin: 0;

                li {
                    padding-bottom: 5px;
                    font-size: 14px;
                    color: rgb(51, 51, 51);
                    opacity: .7;
                }
            }
        }
    }
}
</style>