<template>
  <div class="pb-4">
    <div class="cls-order-form-page pay-form new-pay-form mt-0 mb-4" v-if="property.id">
      <b-container fluid v-if="!confirmPayment" class="cls-order-form-container">
        <b-row class="cls-order-form-content">
          <b-col cols="12" class="pl-0 pr-0">
            <h1 class="font-28 font-weight-bold mb-4 page-heading">
              {{ $t("PAYMENT.INVESTMENT_SUMMARY") }}
            </h1>
          </b-col>
          <div class="content-left">
            <div class="cls-block-small">
              <div class="cls-token-order-wrapper mb-4">
                <div class="cls-property-order-item image-container" style="width: 100%">
                  <b-img :src="getAvatar(property.images)" blank-src="property image" class="img-property"/>
                  <b-row align-h="between" class="bottom-info">
                    <b-col cols="12">
                      <p class="font-24 font-weight-bold priority-name">
                        {{ property.name }}
                      </p>
                      <p class="priority-address font-14" @click="openGoogleMap(property)">{{ property.metadata.address }}</p>
                    </b-col>
                  </b-row>
                </div>

                <b-row align-h="between" align-v="center" class="order-token-quantity-row">
                  <b-col cols="5">
                    <div class="cls-token-quantity">
                      <p class="order-label-main-1">
                        {{ $t("PAYMENT.TOKEN_QUANTITY") }}
                      </p>
                      <p class="order-label-sub-1">
                        {{ exchangeValue(property.price_per_token) }}/{{ $t("PAYMENT.TOKEN").toLowerCase() }}
                      </p>
                    </div>
                    <div class="cls-token-available-block mt-2">
                      <p class="font-13 token-available-text">
                        {{ formatNumberIntl(availableTokens) }} {{ availableTokens > 0 ? $t("common.TOKENS").toLowerCase() : $t("common.TOKEN").toLowerCase() }} {{ $t("common.AVAILABLE") }}
                      </p>
                    </div>
                  </b-col>
                  <b-col cols="7">
                    <div class="purchase-amount-action">
                      <div class="d-flex flex-column">
                        <div class="d-flex flex-row">
                          <div class="btn-numberic-custom has-border full-width">
                            <VueNumericInput class="custom" align="center" size="normal"
                                             @change="onTokenQuantityChanged" @input="onTokenQuantityChanged" @blur="onBlurQuantityInput"
                                             @keypress="isNumber($event)" :value="quantity" :value-formated="formatNumberIntl(quantity)"
                                             :max="maxCheckoutTokens" :min="property?.min_purchase_token >= 1 ? property.min_purchase_token : 1" :step="1"/>
                          </div>
                        </div>
                        <div class="recommended-token-selections">
                          <button v-for="(value, index) in recommendedTokenSelection" :key="index" @click="selectTokenRecommendation(value)" class="btn btn-none btn-outline-main font-12" :class="(index+1)===recommendedTokenSelection.length ? 'last-item' : ''">
                            {{value}}
                          </button>
                        </div>
                      </div>
                    </div>
                  </b-col>
                </b-row>
              </div>
            </div>
            <div class="cls-use-balance-block pt-3">
              <div v-if="isEnableVirtualBalance" class="d-flex d-flex-column cls-block-icon">
                <img class="cls-icon" width="24" height="23" :src="require(`@/assets/img/icons/use-balance.svg`)" alt=""/>
                <div class="cls-inner-block">
                  <b-row align-h="between" class="ml-0">
                    <div class="d-flex flex-column">
                      <div class="mr-1 order-label-main-1">
                        {{ $t("PAYMENT.USE_VIRTUAL_BALANCE") }}
                        <CurrencyTooltip tooltipId="use-virtual-balance" :value="virtualBalance"></CurrencyTooltip>
                      </div>
                      <div class="order-label-sub-1" id="use-virtual-balance">{{ exchangeValue(virtualBalance) }} {{ $t("common.AVAILABLE") }}
                      </div>
                    </div>
                    <b-form-checkbox :disabled="!virtualBalance" switch class="custom-checkbox-control" v-model="useVirtualBalance"
                      @change="amountFromBalance = 0">
                    </b-form-checkbox>
                  </b-row>
                </div>
              </div>
              <div v-if="userProfile.balance > 0 && !useVirtualBalance">
                <hr v-if="isEnableVirtualBalance" class="solid">
                <div class="d-flex d-flex-column cls-block-icon">
                  <img class="cls-icon" width="24" height="23" :src="require(`@/assets/img/icons/use-balance.svg`)" alt=""/>
                  <div class="cls-inner-block">
                    <b-row align-h="between" class="ml-0">
                      <div class="d-flex flex-column">
                        <div class="mr-1 order-label-main-1">
                          {{ $t("PAYMENT.USE_BALANCE") }}
                          <CurrencyTooltip tooltipId="use-balance" :value="userProfile.balance" :show-others="true">
                          </CurrencyTooltip>
                        </div>
                        <div class="order-label-sub-1" id="use-balance">
                          IDR{{ formatNumberIntl(userProfile.balance) }} <span v-if="showExchanged">({{ exchangeValue(userProfile.balance) }})</span> {{ $t("common.AVAILABLE") }}
                        </div>
                      </div>
                      <b-form-checkbox switch class="custom-checkbox-control" v-model="useBalance" @change="amountFromBalance = 0">
                      </b-form-checkbox>
                    </b-row>
                    <b-row v-if="useBalance" class="justify-content-start align-items-center">
                      <b-col cols="12">
                        <div class="mt-2 purchase-amount-action full-width">
                          <div class="btn-numberic-custom has-border has-max-button full-width">
                            <VueNumericInput class="custom" :className="!hasError ? '' : 'balance-input-wrong'" :step="1000" align="center" width="190px"
                                             :min="1" :max="paymentDetails.maxBalanceAllowed" :currency="'IDR'" v-model="amountFromBalance" :value-formated="formatNumberIntl(amountFromBalance)"/>
                            <b-button class="pl-2 pr-2 font-11 btn-main btn-max" variant="none" @click="useMaxBalance">{{ $t('common.MAX') }}</b-button>
                          </div>
                        </div>
                        <p v-if="showExchanged && amountFromBalance > 0" class="cls-currency-exchange font-12 mt-2">
                          IDR{{ formatNumberIntl(amountFromBalance) }} = {{ exchangeValue(amountFromBalance) }}
                        </p>
                      </b-col>
                      <b-col cols="12">
                        <p v-if="hasError" class="error-text mt-1">{{ $t("PAYMENT.PLEASE_FILL_THE_BALANCE_OR") }}</p>
                      </b-col>
                    </b-row>
                  </div>
                </div>
              </div>
              <div v-if="enableVoucherCodeInput" class="divider mt-4"></div>
              <promo-code
                  v-if="enableVoucherCodeInput"
                  :voucherCodeInput="voucherCodeInput"
                  @update:modelValue="$event => {
                  voucherCodeInput = $event
                  voucherCodeError = null
                }"
                  :error="voucherCodeError"
                  @on-apply="validateVoucherCode"
                  @on-clear-voucher="clearVoucherCodeInfo"
                  :voucher-code="voucherCode"
                  :reward-note="voucherRewardNote"
                  class="mt-4">
              </promo-code>
            </div>
          </div>
          <div class="content-right">
            <div v-if="isForeigner && !useVirtualBalance" class="order-summary-block">
              <h3 class="font-20 font-weight-bold order-sub-heading-level-1">
                {{ $t("PAYMENT.PAYMENT_METHOD") }}
              </h3>
              <b-row align-h="start" class="mt-3 mb-2 order-payment-methods">
                <div v-if="enabledStripe" class="method-item-block mr-3">
                  <input id="method-card" type="radio" v-model="selectedPayMethod" value="creditCard"/>
                  <label class="font-16 method-item" for="method-card">
                    {{ $t("PAYMENT.CREDIT_CARD") }} ({{ stripeFeePercent }}%)
                  </label>
                </div>
                <div v-if="enabledStripe" class="method-item-block mr-3">
                  <input id="method-paynow" type="radio" v-model="selectedPayMethod" value="payNow"/>
                  <label class="font-16 method-item" for="method-paynow">
                    PayNow ({{ payNowFeePercent }}%)
                  </label>
                </div>
                <div class="method-item-block mr-0">
                  <input id="method-transfer" type="radio" v-model="selectedPayMethod" value="bankTransfer"/>
                  <label class="font-16 method-item" for="method-transfer">
                    {{ $t("PAYMENT.BANK_TRANSFER") }} ({{ bankTransferFeePercent }}%)
                  </label>
                </div>
              </b-row>
            </div>
            <hr v-if="isForeigner && !useVirtualBalance" class="solid">
            <div class="order-summary-block mt-2 order-summary-items">
              <h3 class="font-20 font-weight-bold order-sub-heading-level-1">
                {{ $t("PAYMENT.ORDER_SUMMARY") }}
              </h3>
              <b-row align-h="between" class="order-summary-item">
                <b-col cols="7 order-label">
                  <p>
                    {{ $t("PAYMENT.ORDER_TOTAL") }} <span class="order-quantity">({{formatNumberIntl(quantity)}} {{quantity > 1 ? $t("common.TOKENS") : $t("common.TOKEN")}})</span>
                  </p>
                </b-col>
                <div class="mr-3 order-value">
                  <p class="text-right fit-content" id="order-total-1">
                    {{ exchangeValue(paymentDetails.baseAmount) }}
                  </p>
                  <CurrencyTooltip tooltipId="order-total-1" :value="paymentDetails.baseAmount"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="useVirtualBalance" align-h="between" class="order-summary-item">
                <b-col cols="8 order-label">
                  <p>{{ $t("PAYMENT.USE_VIRTUAL_BALANCE") }}</p>
                </b-col>
                <div class="mr-3">
                  <div class="fit-content order-value text-right" id="amount-from-balance">-IDR{{ formatNumberIntl(paymentDetails.baseAmount) }}</div>
                  <CurrencyTooltip tooltipId="order-total" :value="paymentDetails.baseAmount" :show-others="true"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="useBalance && amountFromBalance > 0" align-h="between" class="order-summary-item">
                <b-col cols="8 order-label">
                  <p>{{ $t("PAYMENT.USE_BALANCE") }}</p>
                </b-col>
                <div class="mr-3">
                  <div class="fit-content order-value text-right" id="amount-from-balance">
                    -IDR{{ formatNumberIntl(amountFromBalance) }}<br><span v-if="showExchanged" class="d-inline-block mt-2 text-right">(-{{ exchangeValue(amountFromBalance) }})</span>
                  </div>
                  <CurrencyTooltip tooltipId="amount-from-balance" :value="amountFromBalance" :show-others="true">
                  </CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="paymentDetails.displayTransactionFees" align-h="between" class="order-summary-item">
                <b-col cols="8 order-label">
                  <p>{{ $t("TRANSACTION_FEE.TITLE") }}
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-transaction-fee" alt="">
                  </p>
                  <b-tooltip variant="secondary" target="tooltip-transaction-fee" triggers="hover" placement="top">
                    {{ $t("TRANSACTION_FEE.TOOLTIP") }}
                  </b-tooltip>
                  <p><span class="sub-text"> {{ transactionFeeNote }}</span></p>
                </b-col>
                <div class="mr-3 order-value">
                  <p v-if="paymentDetails.transactionFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                  <p v-if="paymentDetails.transactionFee > 0" class="text-right">{{ exchangeValue(paymentDetails.transactionFee) }}</p>
                  <CurrencyTooltip v-if="paymentDetails.transactionFee > 0" tooltipId="transaction-fee" :value="paymentDetails.transactionFee"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="displayXenditProcessingFee && !useVirtualBalance" align-h="between" class="order-summary-item">
                <b-col cols="8 order-label">
                  <p>{{ $t("PAYMENT.PAYMENT_PROCESSING_FEE") }}
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-processing-fee" alt="">
                  </p>
                  <b-tooltip variant="secondary" target="tooltip-processing-fee" triggers="hover" placement="top">
                    {{ $t("PROCESSING_FEE.TOOLTIP") }}
                  </b-tooltip>
                  <p v-if="processingFeeNoteForXendit"><span class="sub-text"> {{ processingFeeNoteForXendit }}</span></p>
                </b-col>
                <div class="mr-3 order-value">
                  <p v-if="paymentDetails.processingFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                  <p v-if="paymentDetails.processingFee > 0" class="text-right" id="stripe-fee">{{ exchangeValue(paymentDetails.processingFee) }}</p>
                  <CurrencyTooltip v-if="paymentDetails.processingFee > 0" tooltipId="stripe-fee" :value="paymentDetails.processingFee"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="isUsingCreditCard && !useVirtualBalance" align-h="between" class="order-summary-item">
                <b-col cols="8 order-label">
                  <p>{{ $t("PAYMENT.PAYMENT_PROCESSING_FEE") }} ({{ stripeFeePercent }}%)
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-processing-fee-2" alt="">
                  </p>
                  <b-tooltip variant="secondary" target="tooltip-processing-fee-2" triggers="hover" placement="top">
                    {{ $t("PROCESSING_FEE.TOOLTIP") }}
                  </b-tooltip>
                </b-col>
                <div class="mr-3 order-value">
                  <p v-if="paymentDetails.processingFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                  <p v-if="paymentDetails.processingFee > 0" class="text-right" id="stripe-fee">{{ exchangeValue(paymentDetails.processingFee) }}</p>
                  <CurrencyTooltip v-if="paymentDetails.processingFee > 0" tooltipId="stripe-fee" :value="paymentDetails.processingFee"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="isUsingPayNow && !useVirtualBalance" align-h="between" class="order-summary-item">
                <b-col cols="8 order-label">
                  <p>{{ $t("PAYMENT.PAY_NOW_PROCESSING_FEE") }} ({{ payNowFeePercent }}%)
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-processing-fee-3" alt="">
                  </p>
                  <b-tooltip variant="secondary" target="tooltip-processing-fee-3" triggers="hover" placement="top">
                    {{ $t("PROCESSING_FEE.TOOLTIP") }}
                  </b-tooltip>
                </b-col>
                <div class="mr-3 order-value">
                  <p v-if="paymentDetails.processingFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                  <p v-if="paymentDetails.processingFee > 0" class="text-right" id="pay-now-fee">{{ exchangeValue(paymentDetails.processingFee) }}</p>
                  <CurrencyTooltip v-if="paymentDetails.processingFee > 0" tooltipId="pay-now-fee" :value="paymentDetails.processingFee"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="isUsingBankTransfer && !useVirtualBalance" align-h="between" class="order-summary-item">
                <b-col cols="8 order-label">
                  <p>{{ $t("PAYMENT.BANK_TRANSFER_PROCESSING_FEE") }} ({{ bankTransferFeePercent }}%)
                    <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-processing-fee-4" alt="">
                  </p>
                  <b-tooltip variant="secondary" target="tooltip-processing-fee-4" triggers="hover" placement="top">
                    {{ $t("PROCESSING_FEE.TOOLTIP") }}
                  </b-tooltip>
                </b-col>
                <div class="mr-3 order-value">
                  <p v-if="paymentDetails.processingFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                  <p v-if="paymentDetails.processingFee > 0" class="text-right" id="bank-transfer-fee">{{ exchangeValue(paymentDetails.processingFee) }}</p>
                  <CurrencyTooltip v-if="paymentDetails.processingFee > 0" tooltipId="bank-transfer-fee" :value="paymentDetails.processingFee"></CurrencyTooltip>
                </div>
              </b-row>
              <b-row v-if="voucherCode" align-h="between" class="order-summary-item">
                <b-col cols="7 order-label">
                  <p>{{ $t("VOUCHER.PROMO_CODE") }}</p>
                  <p class="sub-text">{{ voucherRewardNote }}</p>
                </b-col>
                <div class="mr-3 order-value">
                  <p class="text-right fit-content" id="order-voucher-code">-{{ exchangeValue(paymentDetails.voucherRewardAmount) }}</p>
                  <CurrencyTooltip tooltipId="order-voucher-code" :value="paymentDetails.voucherRewardAmount"></CurrencyTooltip>
                </div>
              </b-row>
            </div>
            <hr class="solid">
            <b-row class="mt-0 pt-1" align-h="between" align-v="center">
              <b-col cols="6">
                <h3 class="font-18 font-weight-bold order-sub-heading-level-2 mb-0">{{ $t("PAYMENT.TOTAL_PAYMENT") }}</h3>
              </b-col>
              <b-col cols="6">
                <h3 class="text-right font-24 font-weight-bold mt-0 cls-total-payment-value mb-0" id="total-payment">
                  {{ exchangeValue(paymentDetails.totalPayment) }}
                </h3>
                <CurrencyTooltip tooltipId="total-payment" :value="paymentDetails.totalPayment"></CurrencyTooltip>
              </b-col>
              <b-col cols="12" v-if="referralBonusTokenMessage">
                <p class="font-15 cls-referral-bonus-message mt-2" v-html="referralBonusTokenMessage"></p>
              </b-col>
            </b-row>
            <hr class="solid">
            <Form v-slot="{ errors, handleSubmit }" @invalid-submit="onInvalidPaymentSubmit">
              <b-form @submit.prevent="handleSubmit(continueToPayment)">
                <Field v-if="!useVirtualBalance"
                  :name="$t('PAYMENT.I_HAVE_READ_AND_AGREE')"
                  :rules="{ required: {allowFalse: false} }"
                  v-slot="{ valid, errors, field, meta, handleChange }"
                  :model-value="agreeToContract" @update:modelValue="agreeToContract = $event">
                  <b-form-group :class="{ shake: this.agreeToContractShaking }">
                    <b-form-checkbox v-model="agreeToContract" name="accept_status" @change="handleChange"
                                    style="font-weight: 400; font-size: 14px; z-index: 0; margin-top: 12px">
                    <span class="font-14">
                      {{ $t("PAYMENT.I_HAVE_READ_AND_AGREE") }}
                      <u class="click-able token-transaction" @click.prevent="openContractAgreement">
                        {{ $t("PAYMENT.TOKEN_TRANSACTION_AGREEMENT") }}
                      </u>
                      {{ $t("PAYMENT.I_AGREE_TO_BE_BOUND") }}
                    </span>
                    </b-form-checkbox>
                    <p v-if="errors && errors.length > 0" class="color-error font-medium font-13 ml-4 mt-1">
                      {{ $t("PAYMENT.PLEASE_AGREE_TO_AGREEMENT") }}
                    </p>
                  </b-form-group>
                </Field>
                <p v-if="!isPresale" class="next-expected-payout-date mt-3 mb-1 pt-2 pb-2">
                  {{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE") }} {{ nextExpectedPayoutDate }}
                </p>
                <b-button id="id_continue_process_payment_btn" class="btn-main col-12 mt-2 mb-2 btn-process-payment pt-2" :disabled="!enabledContinuePayment" type="submit" variant="none">
                  {{ $t("PAYMENT.CONTINUE_TO_PAYMENT") }}
                </b-button>
              </b-form>
            </Form>
          </div>
        </b-row>
      </b-container>

      <b-container v-if="confirmPayment" class="cls-order-form-container cls-single-page-container">
        <div class="row justify-content-center cls-order-form-content">
          <div class="col-12 col-lg-7 mt-2 p-0">
            <b-row align-content="center" class="cls-page-header-wrapper">
              <b-button style="border: none; background-color: transparent; color: black" @click="back">
                <b-icon icon="arrow-left" class="pr-1 pt-1" style="color: black;" scale="0.8"></b-icon>
                {{ $t("common.BACK") }}
              </b-button>
              <p class="font-28 font-weight-bold text-center cls-heading" style="width:80%;">{{ $t("PAYMENT.PAYMENT_CONFIRMATION") }}</p>
            </b-row>
            <div class="cls-page-content-wrapper d-flex flex-column align-items-center">
              <b-container class="cls-order-form-container">
                <div class="order-summary-block mt-3 order-summary-items">
                  <h3 class="font-20 font-weight-bold order-sub-heading-level-1">
                    {{ $t("PAYMENT.ORDER_SUMMARY") }}
                  </h3>
                  <hr class="solid">
                  <b-row v-if="false" align-h="between" class="order-summary-item">
                    <b-col cols="6" class="order-label">
                      <p>{{ property.name }}</p>
                      <p class="sub-text">{{ formatNumberIntl(quantity) }} {{ quantity > 1 ? $t("PAYMENT.TOKENS") : $t("PAYMENT.TOKEN") }}</p>
                    </b-col>
                    <b-col cols="6" class="pr-3 order-value">
                      <p class="text-right font-16" style="word-wrap: break-word;" id="price-per-token-2">
                        {{ exchangeValue(property.price_per_token) }}/{{ $t("PAYMENT.EACH") }}
                      </p>
                      <CurrencyTooltip tooltipId="price-per-token-2" :value="property.price_per_token"></CurrencyTooltip>
                    </b-col>
                  </b-row>
                  <b-row align-h="between" class="order-summary-item">
                    <b-col cols="8 order-label">
                      <p>{{ $t("PAYMENT.ORDER_TOTAL") }}</p>
                      <p class="sub-text">
                        {{ property.name }} ({{ formatNumberIntl(quantity) }} {{ quantity > 1 ? $t("PAYMENT.TOKENS") : $t("PAYMENT.TOKEN") }})
                      </p>
                    </b-col>
                    <div class="mr-3 order-value">
                      <p class="text-right fit-content" id="order-total-1">{{ exchangeValue(paymentDetails.baseAmount) }}</p>
                      <CurrencyTooltip tooltipId="order-total-1" :value="paymentDetails.baseAmount"></CurrencyTooltip>
                    </div>
                  </b-row>
                  <b-row v-if="useVirtualBalance" align-h="between" class="order-summary-item">
                    <b-col cols="8 order-label">
                      <p>{{ $t("PAYMENT.USE_VIRTUAL_BALANCE") }}</p>
                    </b-col>
                    <div class="mr-3">
                      <div class="fit-content order-value text-right" id="amount-from-balance">-IDR{{ formatNumberIntl(paymentDetails.baseAmount) }}</div>
                      <CurrencyTooltip tooltipId="order-total" :value="paymentDetails.baseAmount" :show-others="true"></CurrencyTooltip>
                    </div>
                  </b-row>
                  <b-row v-if="useBalance && amountFromBalance > 0" align-h="between" class="order-summary-item">
                    <b-col cols="8 order-label">
                      <p>{{ $t("PAYMENT.USE_BALANCE") }}</p>
                    </b-col>
                    <div class="mr-3">
                      <div class="fit-content order-value text-right" id="amount-from-balance">
                        -IDR{{ formatNumberIntl(amountFromBalance) }}
                        <br><span v-if="showExchanged" class="d-inline-block mt-2 text-right">(-{{ exchangeValue(amountFromBalance) }})</span>
                      </div>
                      <CurrencyTooltip tooltipId="amount-from-balance" :value="amountFromBalance" :show-others="true">
                      </CurrencyTooltip>
                    </div>
                  </b-row>
                  <b-row align-h="between" class="order-summary-item" v-if="paymentDetails.displayTransactionFees && !useVirtualBalance">
                    <b-col cols="8 order-label">
                      <p>{{ $t("TRANSACTION_FEE.TITLE") }}
                        <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-transaction-fee-2" alt="">
                      </p>
                      <b-tooltip variant="secondary" target="tooltip-transaction-fee-2" triggers="hover" placement="top">
                        {{ $t("TRANSACTION_FEE.TOOLTIP") }}
                      </b-tooltip>
                      <p v-if="transactionFeeNote"><span class="sub-text"> {{ transactionFeeNote }}</span></p>
                    </b-col>
                    <div class="mr-3 order-value">
                      <p v-if="paymentDetails.transactionFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                      <p v-if="paymentDetails.transactionFee > 0" class="text-right" id="transactionFee">{{ exchangeValue(paymentDetails.transactionFee) }}</p>
                      <CurrencyTooltip v-if="paymentDetails.transactionFee > 0" tooltipId="transaction-fee" :value="paymentDetails.transactionFee"></CurrencyTooltip>
                    </div>
                  </b-row>
                  <b-row v-if="displayXenditProcessingFee && !useVirtualBalance" align-h="between" class="order-summary-item">
                    <b-col cols="8 order-label">
                      <p>{{ $t("PAYMENT.PAYMENT_PROCESSING_FEE") }}
                        <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-processing-fee-5" alt="">
                      </p>
                      <b-tooltip variant="secondary" target="tooltip-processing-fee-5" triggers="hover" placement="top">
                        {{ $t("PROCESSING_FEE.TOOLTIP") }}
                      </b-tooltip>
                      <p v-if="processingFeeNoteForXendit"><span class="sub-text"> {{ processingFeeNoteForXendit }}</span></p>
                    </b-col>
                    <div class="mr-3 order-value">
                      <p v-if="paymentDetails.processingFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                      <p v-if="paymentDetails.processingFee > 0" class="text-right" id="stripe-fee">{{ exchangeValue(paymentDetails.processingFee) }}</p>
                      <CurrencyTooltip v-if="paymentDetails.processingFee > 0" tooltipId="stripe-fee" :value="paymentDetails.processingFee"></CurrencyTooltip>
                    </div>
                  </b-row>
                  <b-row v-if="isUsingCreditCard && !useVirtualBalance" align-h="between" class="order-summary-item">
                    <b-col cols="8 order-label">
                      <p>{{ $t("PAYMENT.PAYMENT_PROCESSING_FEE") }} ({{ stripeFeePercent }}%)
                        <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-processing-fee-6" alt="">
                      </p>
                      <b-tooltip variant="secondary" target="tooltip-processing-fee-6" triggers="hover" placement="top">
                        {{ $t("PROCESSING_FEE.TOOLTIP") }}
                      </b-tooltip>
                    </b-col>
                    <div class="mr-3 order-value">
                      <p v-if="paymentDetails.processingFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                      <p v-if="paymentDetails.processingFee > 0" class="text-right" id="stripe-fee">{{ exchangeValue(paymentDetails.processingFee) }}</p>
                      <CurrencyTooltip v-if="paymentDetails.processingFee > 0" tooltipId="stripe-fee" :value="paymentDetails.processingFee"></CurrencyTooltip>
                    </div>
                  </b-row>
                  <b-row v-if="isUsingPayNow && paymentDetails.processingFee > 0 && !useVirtualBalance" align-h="between" class="order-summary-item">
                    <b-col cols="8 order-label">
                      <p>
                        {{ $t("PAYMENT.PAY_NOW_PROCESSING_FEE") }}
                        <span v-if="payNowFeePercent > 0">({{ payNowFeePercent }}%)</span>
                        <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-processing-fee-7" alt="">
                      </p>
                      <b-tooltip variant="secondary" target="tooltip-processing-fee-7" triggers="hover" placement="top">
                        {{ $t("PROCESSING_FEE.TOOLTIP") }}
                      </b-tooltip>
                    </b-col>
                    <div class="mr-3 order-value">
                      <p class="text-right" id="pay-now-fee">{{ exchangeValue(paymentDetails.processingFee) }}</p>
                      <CurrencyTooltip tooltipId="pay-now-fee" :value="paymentDetails.processingFee"></CurrencyTooltip>
                    </div>
                  </b-row>
                  <b-row v-if="isUsingBankTransfer && !useVirtualBalance" align-h="between" class="order-summary-item">
                    <b-col cols="8 order-label">
                      <p>{{ $t("PAYMENT.BANK_TRANSFER_PROCESSING_FEE") }} ({{ bankTransferFeePercent }}%)
                        <img class="compound-tooltip-icon" src="../../assets/img/info-circle-fill.png" id="tooltip-processing-fee-8" alt="">
                      </p>
                      <b-tooltip variant="secondary" target="tooltip-processing-fee-8" triggers="hover" placement="top">
                        {{ $t("PROCESSING_FEE.TOOLTIP") }}
                      </b-tooltip>
                    </b-col>
                    <div class="mr-3 order-value">
                      <p v-if="paymentDetails.processingFee <= 0" class="text-right color-cyan">{{ $t("common.FREE") }}</p>
                      <p v-if="paymentDetails.processingFee > 0" class="text-right" id="bank-transfer-fee">{{ exchangeValue(paymentDetails.processingFee) }}</p>
                      <CurrencyTooltip v-if="paymentDetails.processingFee > 0" tooltipId="bank-transfer-fee" :value="paymentDetails.processingFee"></CurrencyTooltip>
                    </div>
                  </b-row>
                  <b-row v-if="voucherCode" align-h="between" class="order-summary-item">
                    <b-col cols="8 order-label">
                      <p>{{ $t("VOUCHER.PROMO_CODE") }}</p>
                      <p class="sub-text">{{ voucherRewardNote }}</p>
                    </b-col>
                    <div class="mr-3 order-value">
                      <p class="text-right fit-content" id="order-voucher-code-2">-{{ exchangeValue(paymentDetails.voucherRewardAmount) }}</p>
                      <CurrencyTooltip tooltipId="order-voucher-code-2" :value="paymentDetails.voucherRewardAmount"></CurrencyTooltip>
                    </div>
                  </b-row>
                </div>
                <div v-if="isForeigner && !useVirtualBalance" class="order-summary-block mt-3 order-summary-items">
                  <b-row class="pl-0 pr-0 pb-0 order-summary-item" align-h="between">
                    <b-col cols="8">
                      <h3 class="font-18 font-weight-bold order-sub-heading-level-1 mb-0">
                        {{ $t("PAYMENT.PAYMENT_METHOD") }}
                      </h3>
                    </b-col>
                    <div class="col-4 order-value text-right">
                      <label v-if="isUsingCreditCard && !useVirtualBalance" class="font-16 method-item" for="method-card">
                        {{ $t("PAYMENT.CREDIT_CARD") }} ({{ stripeFeePercent }}%)
                      </label>
                      <label v-if="isUsingPayNow && !useVirtualBalance" class="font-16 method-item" for="method-paynow">
                        PayNow <span v-if="payNowFeePercent > 0">({{ payNowFeePercent }}%)</span>
                      </label>
                      <label v-if="isUsingBankTransfer && !useVirtualBalance" class="font-16 method-item" for="method-transfer">
                        {{ $t("PAYMENT.BANK_TRANSFER") }} ({{ bankTransferFeePercent }}%)
                      </label>
                    </div>
                  </b-row>
                </div>
                <hr class="solid">
                <b-row class="mt-0 pt-1" align-h="between" align-v="center">
                  <b-col cols="6">
                    <h3 class="font-18 font-weight-bold order-sub-heading-level-2 mb-0">{{ $t("PAYMENT.TOTAL_PAYMENT") }}</h3>
                  </b-col>
                  <b-col cols="6">
                    <h3 class="text-right font-24 font-weight-bold mt-0 cls-total-payment-value mb-0" id="total-payment">
                      {{ exchangeValue(paymentDetails.totalPayment) }}
                    </h3>
                    <CurrencyTooltip tooltipId="total-payment" :value="paymentDetails.totalPayment"></CurrencyTooltip>
                  </b-col>
                  <b-col cols="12" v-if="referralBonusTokenMessage">
                    <p class="font-15 cls-referral-bonus-message mt-3" v-html="referralBonusTokenMessage"></p>
                  </b-col>
                </b-row>
                <b-row align-h="center" class="mt-3">
                  <b-col cols="12">
                    <!-- Recaptcha V2 checkbox fallback -->
                    <div v-if="showRecaptchaV2.CREATE_PAYMENT" :ref="recaptchaV2Checkbox.CREATE_PAYMENT" class="d-flex justify-content-center"></div>
                    <b-button class="btn-main col-12 mt-2 pt-2" variant="none" @click="payNow">
                      {{ $t("PAYMENT.PAY_NOW") }}
                    </b-button>
                  </b-col>
                </b-row>
              </b-container>
            </div>
            <b-row align-h="center">
              <b-col cols="12">
                <div v-if="!isPresale" class="next-expected-payout-date medium-notice mt-4 text-center">
                  <h3 class="payout-date-title">{{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE_TITLE") }}</h3>
                  <p>
                    {{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE_DESCRIPTION", payoutDateRules) }} <span class="underline">{{ $t("PAYMENT.NEXT_EXPECTED_PAYOUT_DATE_DESCRIPTION_UNDERLINE", payoutDateRules) }}</span>
                  </p>
                </div>
              </b-col>
            </b-row>
          </div>
        </div>
      </b-container>
      <div ref="bottomDiv"></div>
      <popup ref="popupKyc" @on-positive-clicked="openKyc"></popup>
      <popup ref="popupPendingTask" @on-positive-clicked="openContractAgreement"></popup>
      <modal-contract-agreement ref="contractAgreement" @on-agreed-to-contract="onAgreedToContract"/>
    </div>
  </div>
</template>

<script>
import { useRecaptcha } from "@/composables/useRecaptcha";
import { Field, Form } from "vee-validate"
import VueNumericInput from "../../components/VueNumericInput"
import paymentsService from "../../services/payments.service"
import propertiesService from "../../services/properties.service"
import {
  exchange,
  formatNumberIntl,
  getErrorMessage,
  notify,
  numberWithCommas,
  payoutDateRules,
  urlImage
} from "@/helpers/common"
import authService from "../../services/auth.service"
import userService from "../../services/user.service"
import referralService from "../../services/referral.service"
import contractsService from "../../services/contracts.service"
import moment from "moment"
import CurrencyTooltip from "../../components/CurrencyTooltip.vue"
import Popup from "../../components/Popup.vue"
import messErrors from "../../constants/errors"
import { ERROR_CODE, PAYMENT_METHOD, STATUS, STORAGE_KEYS, VOUCHER } from "@/constants/constants"
import store from "../../store/store"
import { GTM_EVENT_NAMES } from "@/constants/gtm"
import { gtmTrackEvent } from "@/helpers/gtm"
import ModalContractAgreement from "../../modals/ModalContractAgreement"
import { isFullyActive } from "@/constants/userStatus"
import voucherService from "@/services/voucher.service";
import PromoCode from "../../components/PromoCode.vue"
import { calculateFee, getTransactionFeeNote, getProcessingFeeNote } from "@/helpers/fee";
import { calculateVoucherRewardAmount, getVoucherRewardNote } from '@/helpers/voucher'

export default {
  components: {
    Form,
    Field,
    VueNumericInput,
    CurrencyTooltip,
    ModalContractAgreement,
    Popup,
    PromoCode,
  },
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ CREATE_PAYMENT: 'createPayment' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      title: "Pay",
      isInitializationComplete: false,
      propertyUuid: this.$route.query.uuid,
      property: {},
      quantity: 1,
      useBalance: false,
      amountFromBalance: 0,
      voucherCodeInput: "",
      voucherCode: null,
      voucherCodeError: null,
      confirmPayment: false,
      paymentData: {},
      useVirtualBalance: false,
      virtualBalance: 0,
      selectedPayMethod: 'bankTransfer',
      bonusInfoForReferee: null,
      agreeToContractPendingTask: null,
      requestingPurchase : false,
      showXenditPaymentLink: false,
      usePayNow: false,
      agreeToContract: false,
      agreeToContractShaking: false,
      tokenSelection: {
        shouldSum: false,
        lastSelectedValue: null,
        lastValue: 0
      },
    }
  },
  created () {
    this.selectedPayMethod = this.enabledStripe ? 'creditCard' : 'bankTransfer'
  },
  async mounted() {
    await Promise.all([
      this.getUserProfile(),
      this.getProperty(),
      this.getVirtualBalance(),
      this.getBonusInfoForReferee(),
      this.getContractStatus(),
    ])
    this.quantity = this.defaultTokenPurchaseAmount
    await this.$nextTick(() => {
      this.isInitializationComplete = true;
    });
  },
  watch: {
    quantity() {
      if (this.isInitializationComplete) {
        this.isQuantityModified = true;
      }
    },
    useVirtualBalance(value) {
      if (value) {
        this.useBalance = false
      }
    },
    useBalance(value) {
      if (value) {
        this.useVirtualBalance = false
      }
      setTimeout(() => {
        this.updatePlusMinus()
      }, 100)
    },
    amountFromBalance(value) {
      this.updatePlusMinus()
    },
  },
  methods: {
    formatNumberIntl,
    getValidationState({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    async getUserProfile () {
      const res = await authService.getUserProfile(false)
      if (res && res.data) {
        await store.dispatch('setUserProfile', res.data);
      }
    },
    async getProperty() {
      const res = await propertiesService.getByUuid(this.propertyUuid)
      this.property = res.data
    },
    async getVirtualBalance() {
      const res = await userService.getVirtualBalance()
      if (res && res.data) {
        this.virtualBalance = res.data
      }
    },
    async getBonusInfoForReferee () {
      this.bonusInfoForReferee = null
      this.bonusInfoForReferee = await referralService.getBonusInfoForReferee()
    },
    async getContractStatus () {
      if (localStorage.getItem(STORAGE_KEYS.AUTHORIZATION.key) && this.propertyUuid) {
        this.agreeToContractPendingTask = null
        const response = await contractsService.getContractStatus(this.propertyUuid)
        if (response) {
          this.agreeToContractPendingTask = response.agree_to_contract_pending_task
        }
      }
    },
    getAvatar(images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    onTokenQuantityChanged(newValue) {
      if (this.useVirtualBalance) {
        if (this.property.price_per_token * newValue > this.virtualBalance) {
          this.quantity = Math.floor(this.virtualBalance / this.property.price_per_token)
        } else {
          this.quantity = newValue;
        }
      } else {
        this.quantity = newValue;
      }

      // Reset token selection
      this.resetTokenSelection()
    },
    onBlurQuantityInput(e) {
      if (this.quantity < this.allowedMinQuantity) {
        this.quantity = this.allowedMinQuantity
      }
    },
    async openKyc() {
      await this.$router.push({ path: "/account/my-account/kyc", query: { redirect: this.$route.fullPath } })
    },
    openKycPopup () {
      this.$refs.popupKyc.openPopup({
        title: this.$t("AUTH.COMPLETE_YOUR_PROFILE"),
        message: this.$t("PAYMENT.PLEASE_COMPLETE_PROFILE", { value: numberWithCommas(this.kycTokenLimit) }),
        positiveButton: this.$t("PAYMENT.CONTINUE_TO_KYC")
      })
    },
    async openContractAgreement () {
      const contractPreviewUrl = await contractsService.getContractPreview({ property_uuid: this.propertyUuid })
      if (contractPreviewUrl) {
        this.$refs.contractAgreement.showModal(contractPreviewUrl)
      }
    },
    async onAgreedToContract (data) {
      if (data && data.propertyUuid === this.propertyUuid) {
        await this.getContractStatus()
      }
    },
    async isValidPayment () {
      if (this.paymentDetails.totalPayment < 0) {
        notify({ text: "Total payment is negative. Please check before paying", type: "error" })
        return false
      } else if (this.voucherCodeError) {
        return false
      } else if (this.voucherCode && this.paymentDetails.voucherRewardAmount <= 0) {
        notify({ text: this.$t("VOUCHER.WARNING_NO_DISCOUNT"), type: "error" })
        return false
      } else if (!this.useVirtualBalance && !this.agreeToContract) {
        return false
      }
      return await this.checkKycTokenLimit();
    },
    async onInvalidPaymentSubmit ({ values, errors, results }) {
      if (!this.agreeToContract) {
        this.agreeToContractShaking = true
        this.$refs.bottomDiv.scrollIntoView({behavior: 'smooth'})
        setTimeout(() => {this.agreeToContractShaking = false}, 1500)
      }
    },
    async continueToPayment () {
      if (this.agreeToContractPendingTask) {
        this.$refs.popupPendingTask.openPopup({
          title: this.$t("PENDING_TASKS.COMPLETE_PENDING_TASK"),
          message: this.$t("PENDING_TASKS.PLEASE_READ_AND_AGREE_TO_AGREEMENT"),
          positiveButton: this.$t("common.OK")
        })
      } else if (await this.isValidPayment()) {
        this.confirmPayment = true
        await this.getBonusInfoForReferee()
      }
    },
    async checkKycTokenLimit () {
      if (!isFullyActive(true) && this.userOwningTokens !== null && this.userOwningTokens !== undefined && this.kycTokenLimit) {
        if ((this.userOwningTokens + this.quantity) > this.kycTokenLimit) {
          this.openKycPopup()
          return false
        } else {
          return true
        }
      } else {
        try {
          let response = await paymentsService.checkKycToken({ num_of_tokens: this.quantity })
          this.userOwningTokens = response && response.user_owning_tokens || 0
          return true
        } catch (err) {
          if (err.extraData && err.extraData.error_code === ERROR_CODE.KYC_REQUIRED) {
            this.userOwningTokens = err.extraData.user_owning_tokens
            this.openKycPopup()
          } else {
            const errMess = getErrorMessage(err)
            notify({
              text: errMess || messErrors.INTERNAL,
              type: "error"
            })
          }
          return false
        }
      }
    },
    async back () {
      this.confirmPayment = false
      await this.getBonusInfoForReferee();
    },
    async payNow() {
      if (await this.isValidPayment() && !this.requestingPurchase) {
        this.requestingPurchase = true
        if (this.useVirtualBalance) {
          gtmTrackEvent({
            event: GTM_EVENT_NAMES.USER_PAY,
            property_id: this.property.id,
            num_of_tokens: this.quantity,
            balance_type: 'virtual'
          })
          const res = await paymentsService.buyTokensUsingVirtualBalance({
            property_id: this.property.id,
            num_of_tokens: this.quantity,
          })
          if (res && res.data) {
            if (res.data) {
              gtmTrackEvent({
                event: GTM_EVENT_NAMES.USER_PAY_SUCCESS,
                property_id: this.property.id,
                num_of_tokens: this.quantity,
                balance_type: 'virtual',
                payment_method: 'virtual',
                customerID: this.userProfile.id,
                customerEmail: this.userProfile.email,
                customerPhoneNumber: this.userProfile.phone,
                value: this.paymentDetails.baseAmount,
                totalPayment: this.paymentDetails.totalPayment,
                currency: 'IDR',
                transactionId: res.data.secondary_id
              })
              await this.$router.push({ name: "paySuccess", query: { trx_id: res.data.secondary_id } })
            }
          }
        } else {
          try {
            if (!this.validateRecaptchaV2.CREATE_PAYMENT()) return
            gtmTrackEvent({
              event: GTM_EVENT_NAMES.USER_PAY,
              property_id: this.property.id,
              num_of_tokens: this.quantity,
              balance_type: 'real',
              payment_method: this.selectedPayMethod
            })
            const recaptchaTokenV3 = await this.recaptchaV3Exec.CREATE_PAYMENT()
            const recaptchaTokenV2 = this.recaptchaTokenV2.CREATE_PAYMENT
            let createPaymentResponse = await paymentsService.createPayment({
              property_id: this.property.id,
              num_of_tokens: this.quantity,
              amount_from_balance: this.amountFromBalance,
              pay_now: this.isUsingPayNow,
              bank_transfer: this.isUsingBankTransfer,
              agree_to_contract: this.agreeToContract,
              recaptcha_token: recaptchaTokenV3,
              recaptcha_token_v2: recaptchaTokenV2,
              voucher_code:this.voucherCodeInput,
            }, false, true)
            if (createPaymentResponse) {
              if (createPaymentResponse.payment) {
                this.paymentData = createPaymentResponse.payment
                if (this.paymentData.status === STATUS.PAID) {
                  await this.$router.push({ name: "paySuccess", query: { payment_id: String(this.paymentData.id) } })
                  gtmTrackEvent({
                    event: GTM_EVENT_NAMES.USER_PAY_SUCCESS,
                    property_id: this.property.id,
                    num_of_tokens: this.quantity,
                    balance_type: 'real',
                    payment_method: this.selectedPayMethod,
                    customerID: this.userProfile.id,
                    customerEmail: this.userProfile.email,
                    customerPhoneNumber: this.userProfile.phone,
                    value: this.paymentDetails.baseAmount,
                    totalPayment: this.paymentDetails.totalPayment,
                    currency: 'IDR',
                    transactionId: this.paymentData.external_id
                  })
                } else {
                  window.location.href = this.paymentData.partner_invoice_url
                }
              } else if (createPaymentResponse.order) {
                await this.$router.push({
                  name: "orderDetail",
                  query: { order_uuid: String(createPaymentResponse.order.uuid) }
                })
              }
            }
          } catch (ex) {
            if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
              this.showRecaptchaV2.CREATE_PAYMENT = true
            } else {
              notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
            }
          } finally {
            this.resetRecaptchaV2.CREATE_PAYMENT()
          }
        }
        this.requestingPurchase = false
      }
    },
    exchangeValue(value) {
      return exchange(value)
    },
    useMaxBalance () {
      this.amountFromBalance = this.paymentDetails.maxBalanceAllowed
    },
    selectTokenRecommendation(quantity) {
      if (!quantity) {
        quantity = this.defaultTokenPurchaseAmount
      }

      if (this.tokenSelection.shouldSum) {
          this.tokenSelection.lastValue += quantity
      } else {
        this.tokenSelection.shouldSum = true
        this.tokenSelection.lastSelectedValue = quantity
        this.tokenSelection.lastValue = quantity
      }

      quantity = this.tokenSelection.lastValue
      if (quantity > this.availableTokens) {
        quantity = this.availableTokens
      }

      if (quantity > this.maxTokensCheckoutForIndoUser && !this.isForeigner) {
        quantity = this.maxTokensCheckoutForIndoUser
        }

      this.quantity = quantity
    },
    resetTokenSelection() {
      this.tokenSelection.shouldSum = false
      this.tokenSelection.lastSelectedValue = null
      this.tokenSelection.lastValue = 0
    },
    updatePlusMinus() {
      if (this.hasError) {
        const btnIncrementElements = document.getElementsByClassName('btn-increment');
        if (btnIncrementElements.length > 1) {
          btnIncrementElements[1].classList.add('btn-error')
        }
        const btnDecrementElements = document.getElementsByClassName('btn-decrement');
        if (btnDecrementElements.length > 1) {
          btnDecrementElements[1].classList.add('btn-error')
        }
      } else {
        const btnIncrementElements = document.getElementsByClassName('btn-increment');
        if (btnIncrementElements.length > 1) {
          btnIncrementElements[1].classList.remove('btn-error')
        }
        const btnDecrementElements = document.getElementsByClassName('btn-decrement');
        if (btnDecrementElements.length > 1) {
          btnDecrementElements[1].classList.remove('btn-error')
        }
      }
    },
    openGoogleMap (property) {
      if (property.map_link) {
        window.open(property.map_link, "_blank")
      }
    },
    isNumber (evt) {
      evt = (evt) ? evt : window.event;
      const charCode = (evt.which) ? evt.which : evt.keyCode;
      if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        evt.preventDefault();
      } else {
        return true;
      }
    },
    clearVoucherCodeInfo () {
      this.voucherCodeInput = "";
      this.voucherCode = null;
      this.voucherCodeError = null;
    },
    async validateVoucherCode () {
      try {
        const response = await voucherService.validateVoucherCode({ voucher_code: this.voucherCodeInput, action: VOUCHER.REDEEM_ACTION_PAYMENT }, true);
        if (response.voucher_code) {
          this.voucherCode = response.voucher_code
          this.voucherCodeError = null
        }
      } catch (error) {
        this.voucherCode = null
        this.voucherCodeError = getErrorMessage(error)
      }
    },
  },
  computed: {
    userProfile () {
      return this.$store.getters.userProfile
    },
    isForeigner() {
      return this.userProfile.payment_method === PAYMENT_METHOD.STRIPE;
    },
    enableVoucherCodeInput () {
      return store.state.configs.enable_voucher_code_input
    },
    isEnableVirtualBalance () {
      if (store.state.configs && store.state.configs.enabled_virtual_balance) {
        return store.state.configs.enabled_virtual_balance
      }
      return false
    },
    recommendedTokenSelection () {
      if (this.isForeigner) {
        if (store.state.configs && store.state.configs.recommended_token_selection_foreigners) {
          return store.state.configs.recommended_token_selection_foreigners
        }
      } else {
        if (store.state.configs && store.state.configs.recommended_token_selection) {
          return store.state.configs.recommended_token_selection
        }
      }

      return []
    },
    maxTokensCheckoutForIndoUser() {
      if (store.state.configs && store.state.configs.max_tokens_checkout_for_indo_user) {
        return store.state.configs.max_tokens_checkout_for_indo_user
      }
      return Number.MAX_SAFE_INTEGER
    },
    maxCheckoutTokens() {
      if (this.isForeigner) {
        return this.availableTokens
      }
      return Math.min(this.availableTokens, this.maxTokensCheckoutForIndoUser)
    },
    defaultTokenPurchaseAmount () {
      let quantity = 10
      if (store.state.configs && store.state.configs.default_token_purchase_amount) {
        quantity = store.state.configs.default_token_purchase_amount
      }

      if (this.useBalance) {
        let newQuantity = Math.floor(this.userProfile.balance / this.property.price_per_token)
        if (quantity > newQuantity) {
          quantity = newQuantity
        }
      }
      if (this.useVirtualBalance) {
        let newQuantity = Math.floor(this.virtualBalance / this.property.price_per_token)
        if (quantity > newQuantity) {
          quantity = newQuantity
        }
      }

      if (quantity > this.availableTokens) {
        quantity = this.availableTokens
      }

      return quantity
    },
    referralTokenAmountForReferee () {
      if (this.$store.state.configs && this.$store.state.configs.referral_token_amount_for_referee) {
        return this.$store.state.configs.referral_token_amount_for_referee
      }
      return 0
    },
    referralTokenMinimumPurchaseForReferee() {
      return this.$store.state.configs.referral_token_minimum_purchase;
    },
    referralBonusPercentForReferee () {
      if (this.$store.state.configs && this.$store.state.configs.referral_bonus_non_indo_referee_percent) {
        return this.$store.state.configs.referral_bonus_non_indo_referee_percent
      }
      return 0
    },
    referralBonusTokenMessage() {
      if (this.bonusInfoForReferee) {
        if (this.bonusInfoForReferee.is_bonus_token_available) {
          const pricePerToken = 10000;
          const tokenBonusRequired = this.referralTokenMinimumPurchaseForReferee > 0
            ? this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_FOR_REFEREE_REQUIRED_INFO", {
              token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
              token_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
            }) : ""
          if (this.isQuantityModified && this.quantity < this.referralTokenMinimumPurchaseForReferee) {
            return this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_FOR_REFEREE_REMIND_INFO", {
              token_bonus: this.referralTokenAmountForReferee,
              token_label: this.referralTokenAmountForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase(),
              token_value: exchange(this.referralTokenAmountForReferee * pricePerToken, 100, false, "IDR"),
              token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
              token_minimum_required_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
            })
          } else {
            return this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_FOR_REFEREE_INFO", {
              token_bonus: this.referralTokenAmountForReferee,
              token_label: this.referralTokenAmountForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase(),
              token_value: exchange(this.referralTokenAmountForReferee * pricePerToken, 100, false, "IDR"),
              token_bonus_required: tokenBonusRequired
            })
          }
        } else if (this.bonusInfoForReferee.is_bonus_amount_available && this.bonusInfoForReferee.bonus_percent > 0) {
          return this.$t("REFERRAL.REFERRAL_BONUS_AMOUNT_FOR_REFEREE_INFO", { percent: this.referralBonusPercentForReferee })
        }
      }
      return null
    },
    availableTokens() {
      return this.property.total_tokens - this.property.display_sold_tokens
    },
    paymentDetails () {
      const baseAmount = this.property.price_per_token * this.quantity
      const displayTransactionFees = this.$store.getters.configs.display_transaction_fees
      const transactionFeeConfig = this.isForeigner
        ? this.$store.getters.configs.foreigner_transaction_fees
        : this.$store.getters.configs.indonesian_transaction_fees
      const transactionFee = displayTransactionFees ? calculateFee(transactionFeeConfig, baseAmount) : 0

      let voucherRewardAmount = 0
      let voucherRewardType = this.voucherCode?.voucher?.reward_type
      let voucherRequiredTransactionAmount = this.voucherCode?.voucher?.required_transaction_amount || 0;
      if (baseAmount >= voucherRequiredTransactionAmount) {
        switch (voucherRewardType) {
          case VOUCHER.REWARD_TYPE_DISCOUNT:
            voucherRewardAmount = Math.min(calculateVoucherRewardAmount(this.voucherCode, baseAmount), baseAmount + transactionFee)
            break;
          case VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES:
          case VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES_BUY:
          case VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_BUY:
            voucherRewardAmount = Math.min(calculateVoucherRewardAmount(this.voucherCode, transactionFee), transactionFee)
            break;
        }
      }

      const maxBalanceAllowed = Math.min(baseAmount + transactionFee - voucherRewardAmount, this.userProfile.balance)
      if (this.amountFromBalance > maxBalanceAllowed) {
        this.amountFromBalance = maxBalanceAllowed
      }
      const remainingCost = baseAmount + transactionFee - voucherRewardAmount - this.amountFromBalance
      let processingFee = 0
      let processingFeePercent = 0
      let processingFeeConfig = null
      if (remainingCost > 0) {
        if (this.isForeigner) {
          if (this.isUsingBankTransfer) {
            processingFeePercent = this.bankTransferFeePercent
          } else if (this.isUsingPayNow) {
            processingFeePercent = this.payNowFeePercent
          } else {
            processingFeePercent = this.stripeFeePercent
          }
          processingFee = processingFeePercent > 0 ? Math.round(remainingCost * (processingFeePercent / 100)) : 0
        } else {
          if (this.displayXenditProcessingFee) {
            if (this.xenditQrisMaxPaymentLimit > 0 && remainingCost < this.xenditQrisMaxPaymentLimit) {
              processingFeeConfig = this.$store.getters.configs.xendit_processing_fees_below_limit
            } else {
              processingFeeConfig = this.$store.getters.configs.xendit_processing_fees_above_limit
            }
            processingFee = calculateFee(processingFeeConfig, remainingCost)
          }
        }
        if (baseAmount >= voucherRequiredTransactionAmount) {
          if (voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_FEE_BUY) {
            voucherRewardAmount = Math.min(calculateVoucherRewardAmount(this.voucherCode, processingFee), processingFee)
          } else if (voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES || voucherRewardType === VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES_BUY) {
            const usedRewardAmount = voucherRewardAmount // previously used for transaction fee
            voucherRewardAmount += Math.min(calculateVoucherRewardAmount(this.voucherCode, processingFee, usedRewardAmount), processingFee)
          }
        }
      }
      const totalPayment = baseAmount + transactionFee + processingFee - voucherRewardAmount - this.amountFromBalance

      return {
        baseAmount,
        maxBalanceAllowed,
        totalPayment,
        displayTransactionFees,
        transactionFeeConfig,
        transactionFee,
        processingFee,
        processingFeePercent,
        processingFeeConfig,
        voucherRewardAmount,
      }
    },
    transactionFeeNote () {
      return getTransactionFeeNote(this.paymentDetails.displayTransactionFees, this.paymentDetails.transactionFeeConfig, this.$t, this.exchangeValue)
    },
    processingFeeNoteForXendit () {
      return getProcessingFeeNote(this.paymentDetails.processingFeeConfig, this.$t, this.exchangeValue)
    },
    voucherRewardNote () {
      return getVoucherRewardNote(this.voucherCode, VOUCHER.REDEEM_ACTION_PAYMENT, this.$t, this.exchangeValue)
    },
    displayXenditProcessingFee() {
      return !this.isForeigner && this.$store.getters.configs?.display_xendit_processing_fees || false
    },
    xenditQrisMaxPaymentLimit () {
      return this.$store.getters.configs?.xendit_qris_max_payment_limit || 0
    },
    enabledStripe () {
      return this.$store.state.configs.stripe_payment_enabled
    },
    isUsingCreditCard () {
      return this.isForeigner && this.enabledStripe && this.selectedPayMethod === "creditCard"
    },
    isUsingPayNow () {
      return this.isForeigner && this.enabledStripe && this.selectedPayMethod === "payNow"
    },
    isUsingBankTransfer () {
      return this.isForeigner && this.selectedPayMethod === "bankTransfer"
    },
    stripeFeePercent() {
      if (!this.$store.getters.configs) {
        return 0;
      }
      return this.$store.getters.configs.stripe_payment_processing_fees || 0
    },
    payNowFeePercent() {
      if (!this.$store.getters.configs) {
        return 0;
      }
      return this.$store.getters.configs.paynow_processing_fees || 0
    },
    bankTransferFeePercent() {
      if (!this.$store.getters.configs) {
        return 0;
      }
      return this.$store.getters.configs.bank_transfer_payment_processing_fees || 0
    },
    isPresale() {
      return this.property && this.property.status === 'presale'
    },
    kycTokenLimit () {
      if (!this.$store.getters.configs) {
        return 0
      }
      return this.$store.getters.configs.kyc_token_limit || 0
    },
    nextExpectedPayoutDate() {
      let expectedPayoutDate = process.env.VUE_APP_EXPECTED_PAYOUT_DATE
      return moment().set("date", expectedPayoutDate).add(1, "M").format("MMM DD, YYYY")
    },
    payoutDateRules() {
      const payoutDate = payoutDateRules()
      // The label depend on the totalDaysReceiveRent
      const totalDaysReceiveRent = payoutDate?.totalDaysReceiveRent || 0
      const totalDaysReceiveRentLabel = (totalDaysReceiveRent > 1) ? this.$t('common.DAYS') : this.$t('common.DAY')
      return {
        ...payoutDate,
        totalDaysReceiveRentLabel
      }
    },
    enabledContinuePayment() {
      return this.confirmPayment || !this.useBalance || this.amountFromBalance > 0
    },
    hasError() {
      return this.useBalance && !this.amountFromBalance
    },
    allowedMinQuantity() {
      return this.property?.min_purchase_token >= 1 ? this.property?.min_purchase_token : 1
    },
    showExchanged() {
      return this.userProfile.preferred_currency !== 'IDR'
    },
  },
  metaInfo() {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
}
</script>

<style lang="scss">
.pay-form {
  &.new-pay-form{
    .content-left {
      -ms-flex: 0 0 50%;
      flex: 0 0 50%;
      max-width: 50%;
      padding: 0 40px 20px 0;
    }

    .content-right {
      -ms-flex: 0 0 50%;
      flex: 0 0 50%;
      max-width: 50%;
      height: fit-content;
      padding: 15px 20px;
      box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
      border-radius: 8px;
    }

    @media(max-width: 1366px) {
      .content-left {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
      }

      .content-right {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
      }
    }

    @media(max-width: 1024px) {
      .content-left {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 0 20px 0 !important;
      }

      .content-right {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
      }
    }
    @media(max-width: 991.98px) {
      .content-left {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
      }

      .content-right {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
      }
    }

    p {
      margin: 0;
      padding: 0;
    }

    .cls-currency-exchange{
      font-weight: 500;
      line-height: 14.4px;
      letter-spacing: -0.03em;
      text-align: left;
      color: var(--layout2-primary-color-text);
    }

    .next-expected-payout-date {
      padding: 5px 15px;
      color: white;
      background: darkorange;
      border-radius: 16px;
      font-size: 13px;
      font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
      text-align: center;
      &.medium-notice{
        padding: 20px;
        h3{
          margin-top: 0;
        }
        p{
          .underline{
            text-decoration: underline;
            text-underline-offset: 3px;
          }
        }
      }
    }
    .purchase-amount-action{
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .btn-numberic-custom{
        max-width: 220px;
        width: 175px !important;
        @media(max-width: 400px) {
          // width: 135px !important;
        }
      }

      .btn-numberic-custom{
        .vue-numeric-input{
          &.custom{
            @media(max-width: 400px) {
              width: 130px !important;
            }
          }
        }
        button.btn-max{
          padding: 3px 6px;
          border-radius: 6px;
          margin-left: 2px;
        }
        .amount-with-currency,
        input.numeric-input{
          font-family: "AcuminVariableConcept", Helvetica, sans-serif;
          font-size: 16px;
          font-weight: 500;
          line-height: 17px;
          color: var(--primary-darker-color);
          padding-top: 7px !important;
          @media(max-width: 400px) {
            font-size: 13px;
          }
        }
        .amount-with-currency{
          line-height: 20px;
        }
      }

      .btn-text-custom {
        max-width: 100%;
        flex: 1; // Allows it to grow based on parent
      }

      .recommended-token-selections{
        margin-top: 6px;
        display: flex;
        button{
          padding: 4px 14px;
          flex: 1;
          margin-right: 5px;
          max-width: 70px;
          &.last-item{
            margin-right: 0;
          }
          @media(max-width: 400px) {
            padding: 3px 8px;
          }
        }
      }
      &.full-width{
        .btn-numberic-custom{
          flex: 1;
          max-width: 100% !important;
          width: 100% !important;
          @media(max-width: 400px) {
            // width: 135px !important;
          }

          .vue-numeric-input{
            &.custom{
              @media(max-width: 400px) {
                width: 130px !important;
              }
            }
          }
          button.btn-max{
            padding: 6px 20px 3px 20px !important;
            border-radius: 6px;
          }
        }
      }
      @media(max-width: 400px) {
        flex-direction: column !important;
        justify-content: center;
        align-items: center;
        .btn-numberic-custom{
          max-width: 100%;
        }
        .recommended-token-selections{
          justify-content: center;
        }
      }
    }
  }

  .position-relative {
    position: relative;
  }

  /* Input field */
  .with-button {
    padding-right: 60px; /* Space for the button inside the textbox */
    height: 38px;
  }

}

[type="radio"]:checked,
[type="radio"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="radio"]:checked+label,
[type="radio"]:not(:checked)+label {
  position: relative;
  padding-left: 28px;
  cursor: pointer;
  line-height: 20px;
  display: inline-block;
  color: #666;
}

[type="radio"]:checked+label:before,
[type="radio"]:not(:checked)+label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid var(--primary-color);
  border-radius: 100%;
  background: #fff;
}

[type="radio"]:checked+label:after,
[type="radio"]:not(:checked)+label:after {
  content: '';
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  position: absolute;
  top: 3px;
  left: 3px;
  border-radius: 100%;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="radio"]:not(:checked)+label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="radio"]:checked+label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.shake {
  animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  transform: translate3d(0, 0, 0);
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

.balance-input-wrong {
  border-color: red !important;
  color: red;
}

.error-text {
  font-size: 12px;
  text-align: end;
  color: red;
  white-space: pre;
}
.voucher-gray {
  color: #a2a2a2!important;
}
.text-success {
  font-size: 14px;
  text-align: right;
  color: rgb(0, 145, 142)!important;
}

.divider {
    width: 100%;
    border-bottom: solid 1px rgb(227, 227, 227);
}

.compound-tooltip-icon {
  margin-bottom: 2px;
  width: 15px;
  height: 15px;
  z-index: 1;
}

</style>
