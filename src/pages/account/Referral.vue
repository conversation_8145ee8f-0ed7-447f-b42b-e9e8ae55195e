<template>
    <div class="rf-container">
        <p class="font-28 font-weight-bold">{{ $t('REFERRAL.REFERRALS') }}</p>
        <div class="content col-12 d-flex flex-column align-items-center">
            <div class="text-center col-12 d-flex justify-content-end">
                <button class="btn btn-main white-normal cls-btn-top-leaderboard d-flex justify-content-between align-items-center" @click="onClickShowRefferralLeaderboard">
                    <img :src="require(`@/assets/img/account/referrals/top-referrer.png`)" alt="Top Referrer" class="icon mr-3"/>
                    <span class="font-16">{{ $t("REFERRAL.TOP_REFERRER") }}</span>
                </button>
            </div>
            <div class="text-center col-md-12 col-lg-6 mt-5">
                <img width="112" height="95" :src="require(`@/assets/img/account/referrals/refer_and_earn.png`)" alt="Refer and earn"/>
                <p class="font-28 font-weight-bold mt-3 mb-3">{{ $t('account.shareAndEarn') }}</p>
                <p class="font-18 cls-referral-message" v-if="referralMessage" v-html="referralMessage"></p>
                <!-- <p class="font-weight-bold" v-if="isUserFullyActive">{{ $t('account.shareYourLink') }}</p> -->
            </div>
            <div class="share-link col-md-12 col-lg-7 mt-4 d-flex flex-row" v-if="isUserFullyActive">
                <div class="cls-input-wrapper d-flex flex-row w-100">
                    <input readonly type="text" :value="shareLink" class="font-16 cls-input">
                    <button id="btn_copyLink" class="font-16 cls-btn" type="button" :disabled="!canCopy" :class="{disabled: !canCopy}" @click="copyLink">{{
                        canCopy ? $t('account.copyLink'): '' }}</button>
                </div>
            </div>
            <p class="mt-3 font-16 cls-invites-left" v-if="isUserFullyActive && getInvitesLeft">
                {{ getInvitesLeft }}. {{ $t('REFERRAL.BUY_TOKENS_TO_EARN_MORE_INVITES') }}
            </p>
            <p class="mt-3 font-16 cls-invites-expire-soon" v-if="isUserFullyActive && getExpireSoonInviteMessage">
                {{ getExpireSoonInviteMessage }}<br/><span>{{ $t('REFERRAL.USE_THEM_SOON') }}</span>
            </p>
            <div class="col-md-12 col-lg-7 d-flex flex-row justify-content-center flex-wrap mt-4 mb-3 cls-social-sharing-lists" v-if="isUserFullyActive">
                <button @click="shareWhatsapp" class="btn btn-main btn-social social-whatsapp">
                    <img :src="require(`@/assets/img/socials/v2/whatsapp.svg`)" alt="Social share" class="icon"/>
                    <span class="font-15">Share</span>
                </button>
                <button @click="shareFacebook" class="btn btn-main btn-social social-facebook">
                    <img :src="require(`@/assets/img/socials/v2/facebook.svg`)" alt="Social share" class="icon"/>
                    <span class="font-15">Share</span>
                </button>
                <button @click="shareTwitter" class="btn btn-main btn-social social-x">
                    <img :src="require(`@/assets/img/socials/v2/x.svg`)" alt="Social share" class="icon"/>
                    <span class="font-15">Share</span>
                </button>
                <button v-if="canShare" @click="shareSMS" class="btn btn-main btn-social social-sms">
                    <img :src="require(`@/assets/img/socials/v2/sms.svg`)" alt="Social share" class="icon"/>
                    <span class="font-15">Share</span>
                </button>
            </div>
            <p class="no-access" v-if="!isUserFullyActive">{{ $t('account.DO_NOT_HAVE_TRANSACTION_ACCESS') }}</p>
            <div class="container text-center cls-how-to-work mt-5 mb-3">
                <p class="font-28 font-weight-bold mb-4">{{ $t('REFERRAL.HOW_IT_WORK') }}</p>
                <b-row align-h="between">
                    <b-col cols="12" lg="4" class="ls-how-to-work-item mt-3 mb-3 pl-5 pr-5">
                        <img height="78" :src="require(`@/assets/img/account/referrals/Invite your Friend.png`)" alt="Invite your Friend" class="cls-icon"/>
                        <h4 class="font-20 font-weight-bold cls-heading">
                            {{ $t('REFERRAL.HOW_IT_WORK_ITEMS.INVITE_YOUR_FRIEND.HEADING') }}
                        </h4>
                        <p class="font-15 cls-description">
                            {{ $t('REFERRAL.HOW_IT_WORK_ITEMS.INVITE_YOUR_FRIEND.DESCRIPTION') }}
                        </p>
                    </b-col>
                    <b-col cols="12" lg="4" class="ls-how-to-work-item mt-3 mb-3 pl-5 pr-5">
                        <img height="78" :src="require(`@/assets/img/account/referrals/Make First Purchase.png`)" alt="Make First Purchase" class="cls-icon"/>
                        <h4 class="font-20 font-weight-bold cls-heading">
                            {{ $t('REFERRAL.HOW_IT_WORK_ITEMS.MAKE_FIRST_PURCHASE.HEADING') }}
                        </h4>
                        <p class="font-15 cls-description">{{ makeFirstPurchaseDescription }}</p>
                    </b-col>
                    <b-col cols="12" lg="4" class="ls-how-to-work-item mt-3 mb-3 pl-5 pr-5">
                        <img height="78" :src="require(`@/assets/img/account/referrals/Get Rewards.png`)" alt="Get Rewards" class="cls-icon"/>
                        <h4 class="font-20 font-weight-bold cls-heading">
                            {{ $t('REFERRAL.HOW_IT_WORK_ITEMS.GET_REWARDS.HEADING') }}
                        </h4>
                        <p class="font-15 cls-description" v-html="getRewardMessages">
                        </p>
                    </b-col>
                </b-row>
            </div>
            <div v-if="referrals.length" class="container text-center invite-list d-flex flex-column align-items-start mt-4 mb-5">
                <b-row align-h="center" align-v="center" class="w-100 pl-0 pr-0 ml-0 mr-0">
                    <b-col cols="12" md="7" class="pl-0 pr-0">
                        <p class="font-28 font-weight-bold mb-4">{{ $t('REFERRAL.INVITED_USERS') }}</p>
                        <ul class="cls-invited-user-lists col-12 d-flex flex-column justify-content-center align-items-center rf-item">
                            <li v-for="(referral, index) in referrals" :key="index" class="item">
                                <div class="cls-invited-left">
                                    <img v-if="referral.user && referral.user.avatar_url" width="41" height="41" :src="avatarUrl(referral.user.avatar_url)" alt="Invited user" class="cls-icon cls-invited-thumb"/>
                                    <img v-else width="41" height="41" :src="require(`@/assets/img/account/referrals/invited_thumb.svg`)" alt="Invited user" class="cls-icon"/>
                                    <div class="cls-invited-info d-flex flex-column justify-content-center align-items-start">
                                        <p class="font-16 item-content invited-user mb-0 mt-1">
                                            {{ getName(referral) }}
                                        </p>
                                        <p v-if="isOnMobile" class="font-16 item-content invited-email">({{ referral.user.email }})</p>
                                        <p v-if="isOnMobile" class="font-14 item-content joined-on d-flex justify-content-start align-items-center">
                                            <img width="12" height="12" :src="require(`@/assets/img/account/referrals/joined_date.svg`)" alt="Invited user" class="icon-joined mr-2"/>
                                            <span class="time font-14">{{ getJoinAt(referral) }}</span>
                                        </p>
                                    </div>
                                </div>
                                <p v-if="!isOnMobile" class="font-14 item-content joined-on d-flex justify-content-start align-items-center">
                                    <img width="12" height="12" :src="require(`@/assets/img/account/referrals/joined_date.svg`)" alt="Invited user" class="icon-joined mr-2"/>
                                    <span class="time font-14">{{ getJoinAt(referral) }}</span>
                                </p>
                            </li>
                        </ul>
                    </b-col>
                </b-row>
            </div>
        </div>
        <modal-referral-leaderboard :show="showReferralLeaderboard" @on-close="showReferralLeaderboard = false"></modal-referral-leaderboard>
    </div>
</template>

<script>

import moment from "moment"
import { exchange, formatNumberIntl, notify } from "@/helpers/common"
import store from "../../store/store"
import SocialButton from "../../components/SocialButton"
import accountService from "../../services/account.service"
import { isFullyActive } from "@/constants/userStatus"
import { INDO, PAYMENT_METHOD } from "../../constants/constants"
import { urlImage } from '../../helpers/common'
import ModalReferralLeaderboard from "../../modals/ModalReferralLeaderboard.vue"

export default {
    components: {
        SocialButton,
        ModalReferralLeaderboard
    },

    data() {
        return {
            shareLink: '',
            invitesLeft: 0,
            canShare: false,
            isOnMobile: true,
            referrals: [],
            showReferralLeaderboard: false,
            referralCodeExpiries: [],
        };
    },

    async mounted() {
        this.onResize();
        this.canShare = navigator.canShare !== undefined;
        window.addEventListener('resize', this.onResize);
        if (this.isUserFullyActive) {
            await Promise.all([
                this.getReferrals(),
                this.getReferralCode(),
            ]);
        }
    },

    methods: {
      formatNumberIntl,
        async getReferralCode() {
            if (this.$store.getters.referralCode) {
                this.shareLink = `${window.location.origin}/invite/${this.$store.getters.referralCode.code}`;
                this.invitesLeft = this.$store.getters.referralCode.invites_left;
                this.referralCodeExpiries = this.$store.getters.referralCode.referral_code_expiries;
            } else {
                const res = await accountService.getReferralCode(true);
                if (res && res.data) {
                    this.shareLink = `${window.location.origin}/invite/${res.data.code}`;
                    this.invitesLeft = res.data.invites_left;
                    this.referralCodeExpiries = res.data.referral_code_expiries;
                    await store.dispatch('setReferralCode', res.data);
                }
            }
        },
        async getReferrals() {
            const res = await accountService.getReferrals();
            if (res && res.data) {
                this.referrals = res.data;
            }
        },
        shareFacebook() {
            if (this.shareLink) {
                window.open(`https://www.facebook.com/sharer/sharer.php?u=${this.shareLink}`, '_blank');
            }
        },
        shareTwitter() {
            if (this.shareLink) {
                window.open(`https://twitter.com/share?url=${this.shareLink}`, '_blank');
            }
        },
        shareSMS() {
            if (this.shareLink) {
                navigator.share({
                    url: this.shareLink,
                });
            }
        },
        shareWhatsapp() {
            if (this.shareLink) {
                let message = 'Get rewarded exclusive cashback on your first investment. Claim it now before it’s gone! Click the link now\n';
                
                if (
                    this.$store.getters.userProfile.payment_method === PAYMENT_METHOD.XENDIT ||
                    this.$store.getters.userProfile.phone.startsWith('+62')
                ) {
                    message = 'Nih, bonus cashback spesial buat kamu yang pertama kali investasi di GORO, buruan klaim karena kuotanya terbatas! Klik link ini yaa\n';
                }

                const fullMessage = `${message}${this.shareLink}`;
                window.open(`https://wa.me/?text=${encodeURIComponent(fullMessage)}`, '_blank');
            }
        },

        copyLink() {
            if (this.shareLink) {
                navigator.clipboard.writeText(this.shareLink);
                notify({ text: this.$t('common.COPIED') });
            }
        },

        onResize() {
            if (window.innerWidth <= 767) {
                this.isOnMobile = true;
            } else {
                this.isOnMobile = false;
            }
        },

        getName (referral) {
          if (referral && referral.user) {
            const name = referral.user.name
            const email = referral.user.email || referral.user.email_apple || referral.user.email_fb || referral.user.email_google

            if (this.isOnMobile) {
                return name
            }

            return `${name}\n(${email})`
          }
          return ""
        },

        getJoinAt (referral) {
          if (referral) {
            return moment(referral.created_at).format("DD/MM/yyyy")
          }
          return ""
        },
        avatarUrl(avatar_url = null) {
            if (avatar_url) {
                return urlImage({ image: avatar_url })
            }
            return null
        },
        onClickShowRefferralLeaderboard() {
            this.showReferralLeaderboard = true
        },

        getPeriod(expiresAt) {
            let daysLeft = expiresAt.diff(moment(), 'days');
            let display = '';

            if (daysLeft > 0) {
                const remainingHours = expiresAt.diff(moment().add(daysLeft, 'days'), 'hours');
                if (remainingHours > 12) {
                    daysLeft += 1;
                }
            } else {
                daysLeft = 1;
            }
            display = `${daysLeft} ${daysLeft != 1 ? this.$t('common.DAYS') : this.$t('common.DAY')}`;
            return display
        },
    },

  computed: {
    referralBonusPercentForReferrer () {
      if (this.$store.getters.referralCode && this.$store.getters.referralCode.referrer_bonus_percent) {
        return this.$store.getters.referralCode.referrer_bonus_percent
      } else if (store.state.configs && store.state.configs.referral_bonus_non_indo_referrer_percent) {
        return store.state.configs.referral_bonus_non_indo_referrer_percent
      }
      return 0
    },
    referralBonusPercentForReferee () {
      if (store.state.configs && store.state.configs.referral_bonus_non_indo_referee_percent) {
        return store.state.configs.referral_bonus_non_indo_referee_percent
      }
      return 0
    },
    referralTokenAmountForReferrer () {
      if (store.state.configs && store.state.configs.referral_token_amount_for_referrer) {
        return store.state.configs.referral_token_amount_for_referrer
      }
      return 0
    },
    referralTokenAmountForReferee () {
      if (store.state.configs && store.state.configs.referral_token_amount_for_referee) {
        return store.state.configs.referral_token_amount_for_referee
      }
      return 0
    },
    referralTokenMinimumPurchaseForReferee() {
      if (store.state.configs && store.state.configs.referral_token_minimum_purchase) {
        return store.state.configs.referral_token_minimum_purchase;
      }
      return 0
    },
    isEnableReferralTokenForIndo () {
      if (store.state.configs && store.state.configs.enable_referral_token_for_indo) {
        return store.state.configs.enable_referral_token_for_indo
      }
      return false
    },
    isIndonesian () {
      if (this.$store.getters.userProfile) {
        return this.$store.getters.userProfile.iso_country_code === INDO.ISO_COUNTRY_CODE
      } else if (this.$store.getters.geoLocation) {
        return this.$store.getters.geoLocation.country_code === INDO.COUNTRY_CODE
          || this.$store.getters.geoLocation.iso_country_code === INDO.ISO_COUNTRY_CODE
      }
      return false
    },
    referralMessage() {
      if (this.isIndonesian && this.isEnableReferralTokenForIndo && this.referralTokenAmountForReferrer > 0) {
        const pricePerToken = 10000;
        const tokenBonusRequired = this.referralTokenMinimumPurchaseForReferee > 0
          ? this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_REQUIRED_INFO", {
            token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
            token_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
          }) : ""
        return this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_INFO", {
          token_bonus: this.referralTokenAmountForReferrer,
          token_label: this.referralTokenAmountForReferrer > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase(),
          token_value: exchange(this.referralTokenAmountForReferrer * pricePerToken, 100, false, "IDR"),
          token_bonus_required: tokenBonusRequired
        })
      } else if (this.referralBonusPercentForReferrer > 0) {
        return this.$t("REFERRAL.REFERRAL_BONUS_INFO", { percent: this.referralBonusPercentForReferrer })
      }
      return null
    },
    isUserFullyActive () {
      return isFullyActive()
    },
    longestExpirationDate () {
        if (this.referralCodeExpiries.length) {
            const expiresAt = moment(this.referralCodeExpiries[this.referralCodeExpiries.length - 1].expires_at);
            return this.getPeriod(expiresAt)
        }
        return '';
    },
    
    getInvitesLeft () {
        if (!this.invitesLeft) {
            return null;
        }
        return this.$t("account.INVITES_LEFT", { value: this.invitesLeft, period: this.longestExpirationDate })
    },
    getExpireSoonInviteMessage() {
         if (this.referralCodeExpiries.length) {
            const invite = this.referralCodeExpiries[0];
            const expiresAt = moment(invite.expires_at);

            let daysLeft = expiresAt.diff(moment(), 'days');
            if (daysLeft <= 7) {
                const expiringInvites = this.referralCodeExpiries.filter(e => expiresAt.format('YYYY-MM-DD') === moment(e.expires_at).format('YYYY-MM-DD'))
                    .map(e => e.max_invites - e.used_count).reduce((a, b) => a + b, 0);
                return this.$t("REFERRAL.INVITES_WILL_EXPIRE_SOON", 
                { 
                    invites: expiringInvites, 
                    period: this.getPeriod(expiresAt),
                    plugal: expiringInvites === 1 ? '' : 's'

                })
            }
         }
        return null;
    },
    canCopy () {
      return window.isSecureContext && navigator.clipboard
    },
    makeFirstPurchaseDescription() {
      const tokenBonusRequired = this.isIndonesian && this.isEnableReferralTokenForIndo && this.referralTokenAmountForReferrer > 0 && this.referralTokenMinimumPurchaseForReferee > 0
        ? this.$t("REFERRAL.HOW_IT_WORK_ITEMS.MAKE_FIRST_PURCHASE.DESCRIPTION_REQUIRED", {
          token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
          token_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
        }) : ""
      return this.$t('REFERRAL.HOW_IT_WORK_ITEMS.MAKE_FIRST_PURCHASE.DESCRIPTION', {
        token_bonus_required: tokenBonusRequired,
      })
    },
    getRewardMessages() {
      if (this.isIndonesian && this.isEnableReferralTokenForIndo) {
        const referrerBonus = `${this.referralTokenAmountForReferrer} ${this.referralTokenAmountForReferrer > 1 ? this.$t('common.FREE_TOKENS').toLowerCase() : this.$t('common.FREE_TOKEN').toLowerCase()}`
        const refereeBonus = `${this.referralTokenAmountForReferee} ${this.referralTokenAmountForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()}`
        return this.$t('REFERRAL.HOW_IT_WORK_ITEMS.GET_REWARDS.DESCRIPTION', {
          referrer_bonus: referrerBonus,
          referee_bonus: refereeBonus
        })
      } else if (this.referralBonusPercentForReferrer > 0) {
        const referrerBonus = `${this.referralBonusPercentForReferrer}%`
        const refereeBonus = `${this.referralBonusPercentForReferee}%`
        return this.$t('REFERRAL.HOW_IT_WORK_ITEMS.GET_REWARDS.DESCRIPTION_CASHBACK', {
          referrer_bonus: referrerBonus,
          referee_bonus: refereeBonus
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.rf-container {
    *{
        font-family: "AcuminVariableConcept", Helvetica, sans-serif !important;
    }
    width: 100%;
    margin-top: 20px;

    .content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding-top: 20px;
        padding-bottom: 20px;
        padding-left: 5px;
        padding-right: 5px;
        margin-top: 15px;

        .share-unique-text {
            color: #858BA9 !important;
        }

        .invite-list {
            .cls-invited-user-lists{
                margin: 0;
                padding: 0;
                list-style: none;
                display: flex;
                justify-content:flex-start;
                align-content: center;
                li.item{
                    flex: 1;
                    width: 100%;
                    max-width: 100%;
                    box-shadow: 0px 0px 6px 0px #00000040;
                    background-color: var(--primary-light-color);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 7px 0;
                    border-radius: 10px;
                    overflow: hidden;
                    padding: 10px 20px;
                    .cls-invited-left{
                        display: flex;
                        justify-content: flex-start;
                        align-content: center;
                        flex: 1;
                        .cls-icon{
                            width: 41px;
                            height: 41px;
                            margin-right: 20px;
                            border-radius: 100%;
                        }
                        .cls-invited-info{
                            flex: 1;
                            // max-width: calc(100% - 56px - 20px);
                            // width: calc(100% - 56px - 20px);
                            text-align: left;
                            .invited-user,
                            .invited-email{
                                font-weight: 500;
                                line-height: 19.2px;
                                text-align: left;
                                color: var(--green-dark-color);
                                word-break: break-all;
                                overflow-wrap: break-word;
                                white-space: normal;
                            }
                        }
                    }
                    .joined-on{
                        font-weight: 382;
                        line-height: 16.8px;
                        color: #858585;
                        .time{
                            padding: 3px 0 0 0;
                        }
                    }
                }
            }
        }

        .cls-referral-message{
            font-weight: 600;
            line-height: 21.6px;
            text-align: center;
            color: #005455;
        }

        .cls-invites-left{
            color: #8E8E8E;
            font-weight: 600;
            line-height: 19.2px;
            text-align: center;
        }

        .cls-invites-expire-soon {
            color: #FB234A;
            font-weight: 600;
            line-height: 19.2px;
            text-align: center;
            padding: 16px 20px;
            background-color: #FB234A1A;
            border-radius: 8px;

            span {
                font-weight: 500;
            }
        }

        .share-link {
            min-height: 50px;
            .cls-input-wrapper{
                background-color: #EAEAEA;
                border-radius: 10px;
                overflow: hidden;
            }
            input {
                color: var(--green-dark-color);
                width: 100%;
                background-color: #EAEAEA;
                border: none;
                border-radius: 10px 0 0 10px;
                padding-left: 1rem;
                padding-right: 1rem;
                color: var(--primary-color);
                font-weight: 600;
                padding-top: 7px;
            }

            button {
                min-width: 141px;
                border: none;
                border-radius: 10px;
                color: white;
                background-color: var(--primary-color);
                font-weight: 600;
                &.disabled {
                    background-color: #cacee2;
                }
                @media(max-width: 991px) {
                    min-width: 100px;
                }
            }
        }

        .no-access {
            font-size: 17px;
            color: red;
            text-align: center;
        }
    }

    .cls-social-sharing-lists{
        .btn-social{
            width: 111px !important;
            margin: 5px;
        }
    }
    .cls-btn-top-leaderboard{
        padding: 0.5rem 1.5rem;
        border: 3px solid var(--layout2-primary-color);
        margin-top: 20px;
        margin-right: 20px;
        @media screen and (max-width: 768px) {
            padding: 5px 15px;
            margin-top: 10px;
            margin-right: 10px;
        }
        @media screen and (max-width: 400px) {
            margin-top: 0px;
            margin-right: 0px;
        }
        img.icon{
            widows: 19px;
            height: 19px;
        }
        span{
            font-weight: 600;
            margin-top: 5px;
        }
        &:hover,
        &:focus,
        &:active{
            border: 3px solid var(--layout2-primary-color) !important;
            background-color: var(--layout2-primary-color) !important;
        }
    }
}
</style>
