<template>
  <div class="wr-container">
    <p class="font-28 font-weight-bold">{{ $t("SELL_TOKEN.SELL_TOKEN_REQUEST_HISTORY") }}</p>
    <div class="content d-flex flex-column align-items-center">
      <div v-if="!rows && showNoSellTokenRequests" class="text-center">
        <p class="no-requests mb-5">{{ $t("SELL_TOKEN.THERE_ARE_NO_SELL_TOKEN_REQUEST_HISTORY") }}</p>
        <b-button id="btn_sellTokenHistory_viewMyAssets" @click="viewMyAssets" class="bg-main-color color-white mr-1" type="submit"
                  style="padding: 7px 10px;">
          {{ $t("SELL_TOKEN.VIEW_MY_ASSETS") }}
        </b-button>
      </div>
      <b-pagination v-if="rows" class="align-self-end" v-model="currentPage" :total-rows="rows" :per-page="perPage"
                    aria-controls="my-table">
      </b-pagination>
      <b-table v-if="rows" responsive hover head-variant="light" id="my-table" :fields="fields" :items="items"
               :per-page="perPage" :tbody-tr-class="rowClass" @row-clicked="onRowClicked" :current-page="currentPage"
               small>
        <template v-slot:cell(numOfTokens)="data">
          <p :id="`amount-item-${data.item.item.id}`">{{ formatNumberIntl(data.value) }}</p>
        </template>
        <template v-slot:cell(receivedAmount)="data">
          <p :id="`amount-item-${data.item.item.id}`">{{ exchangeValue(data.value) }}</p>
          <CurrencyTooltip :tooltipId="`amount-item-${data.item.item.id}`" :value="data.value"></CurrencyTooltip>
        </template>
        <template v-slot:cell(fee)="data">
          <p :id="`fee-item-${data.item.item.id}`">{{ exchangeValue(data.value) }}</p>
          <CurrencyTooltip :tooltipId="`fee-item-${data.item.item.id}`" :value="data.value"></CurrencyTooltip>
        </template>
      </b-table>
      <b-pagination v-if="rows" class="align-self-end" v-model="currentPage" :total-rows="rows" :per-page="perPage"
                    aria-controls="my-table">
      </b-pagination>
    </div>
    <b-modal v-model="showModal">
      <template #modal-header>
        <div class="w-100 d-flex flex-row align-items-center justify-content-between">
          <div class="font-24 font-weight-bold">{{ $t("SELL_TOKEN.REQUEST_DETAILS") }}</div>
          <div class="font-14">{{ sellTokenRequestDate }}</div>
        </div>
      </template>
      <div class="text-center">
        <p :class="statusClass">{{ sellTokenRequestStatus }}</p>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t("SELL_TOKEN.PROPERTY_NAME") }}</p>
          <p class="value">{{ sellTokenRequestPropertyName }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t("SELL_TOKEN.NUM_OF_TOKENS") }}</p>
          <p class="value">{{ formatNumberIntl(sellTokenRequestNumOfTokens) }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t("SELL_TOKEN.AMOUNT") }}</p>
          <p id="request-amount" class="value">{{ exchangeValue(sellTokenRequestAmount) }}</p>
          <CurrencyTooltip tooltipId="request-amount" :value="sellTokenRequestAmount"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t("SELL_TOKEN.RECEIVED_AMOUNT") }}</p>
          <p id="request-received-amount" class="value">{{ exchangeValue(sellTokenRequestReceivedAmount) }}</p>
          <CurrencyTooltip tooltipId="request-received-amount"
                           :value="sellTokenRequestReceivedAmount"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t("SELL_TOKEN.FEE") }}</p>
          <p id="request-fee" class="value">{{ exchangeValue(sellTokenRequestFee) }}</p>
          <CurrencyTooltip tooltipId="request-fee" :value="sellTokenRequestFee"></CurrencyTooltip>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t("SELL_TOKEN.FEE_PERCENT") }}</p>
          <p class="value">{{ sellTokenRequestFeePercent }}</p>
        </div>
        <div class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t("SELL_TOKEN.DESCRIPTION") }}</p>
          <p class="value">{{ sellTokenRequestDescription }}</p>
        </div>
        <div v-if="isRejected" class="d-flex flex-row justify-content-between item">
          <p class="title">{{ $t("SELL_TOKEN.REASON") }}</p>
          <p class="value">{{ sellTokenRequestReason }}</p>
        </div>
      </div>
      <template #modal-footer="{ ok, cancel, hide }">
        <b-button id="btn_sellTokenRequestHistory_cancelRequest" v-if="isPending" size="sm" variant="danger" @click="onCancelRequest">
          {{ $t("SELL_TOKEN.CANCEL_REQUEST") }}
        </b-button>
        <b-button id="btn_sellTokenRequestHistory_close" size="sm" variant="primary" @click="ok()">
          {{ $t("common.CLOSE") }}
        </b-button>
      </template>
    </b-modal>
  </div>
</template>

<script>

import { exchange, formatNumberIntl } from "@/helpers/common"
import sellTokenRequestService from "../../services/sellTokens.service"
import moment from "moment"
import CurrencyTooltip from "../../components/CurrencyTooltip.vue"

export default {
  components: {
    CurrencyTooltip,
  },
  data () {
    return {
      perPage: 10,
      currentPage: 1,
      showNoSellTokenRequests: false,
      items: [],
      showModal: false,
      selectedItem: null,
      fields: this.getTableHeaderFields(),
    }
  },

  async mounted () {
    await this.getSellTokenRequestHistory()
  },

  watch: {
    "$i18n.locale" (newVal, oldVal) {
      this.items = this.items.map(e => {
        e.displayStatus = this.$t(`SELL_TOKEN_STATUSES.${e.status}`)
        return e
      })
      this.fields = this.getTableHeaderFields()
    }
  },

  methods: {
    formatNumberIntl,
    async onRowClicked (item, index, evt) {
      this.selectedItem = item.item
      this.showModal = true
    },

    async getSellTokenRequestHistory () {
      const res = await sellTokenRequestService.getSellTokenRequestHistory()
      this.showNoSellTokenRequests = true
      if (res && res.data) {
        this.items = res.data.map(e => {
          return {
            status: e.status,
            displayStatus: this.$t(`SELL_TOKEN_STATUSES.${e.status}`),
            propertyName: e.property.name,
            numOfTokens: e.num_of_tokens,
            receivedAmount: e.received_amount,
            fee: e.fee,
            note: e.description,
            date: moment(e.created_at).format("DD/MM/YYYY"),
            item: e,
          }
        })
      }
    },

    rowClass (item, type) {
      if (item && type === "row") {
        if (item.status === "CANCELLED" || item.status === "REJECTED") {
          return "text-cancelled"
        } else if (item.status === "APPROVED") {
          return "text-approved"
        } else {
          return "text-pending"
        }
      } else {
        return null
      }
    },

    async onCancelRequest () {
      if (this.selectedItem) {
        const res = await sellTokenRequestService.cancelSellTokenRequest({
          request_uuid: this.selectedItem.uuid,
        })
        if (res.sell_token_request) {
          this.showModal = false
          this.getSellTokenRequestHistory()
        }
      }
    },
    async viewMyAssets () {
      await this.$router.push({ name: "assetsOverview" })
    },

    getTableHeaderFields () {
      return [
        { key: "displayStatus", label: this.$t("SELL_TOKEN_TABLE_HEADER.STATUS") },
        { key: "propertyName", label: this.$t("SELL_TOKEN_TABLE_HEADER.PROPERTY") },
        { key: "numOfTokens", label: this.$t("SELL_TOKEN_TABLE_HEADER.NO_OF_TOKENS") },
        { key: "receivedAmount", label: this.$t("SELL_TOKEN_TABLE_HEADER.RECEIVED_AMOUNT") },
        { key: "fee", label: this.$t("SELL_TOKEN_TABLE_HEADER.FEE") },
        { key: "note", label: this.$t("SELL_TOKEN_TABLE_HEADER.NOTE") },
        { key: "date", label: this.$t("SELL_TOKEN_TABLE_HEADER.DATE") }
      ]
    },

    exchangeValue (value) {
      return exchange(value)
    }
  },

  computed: {
    rows () {
      return this.items.length
    },

    sellTokenRequestPropertyName () {
      if (!this.selectedItem) {
        return 0
      }
      return this.selectedItem.property.name
    },

    sellTokenRequestNumOfTokens () {
      if (!this.selectedItem) {
        return 0
      }
      return this.selectedItem.num_of_tokens
    },

    sellTokenRequestAmount () {
      if (!this.selectedItem) {
        return 0
      }
      return this.selectedItem.amount
    },

    sellTokenRequestReceivedAmount () {
      if (!this.selectedItem) {
        return 0
      }
      return this.selectedItem.received_amount
    },

    sellTokenRequestFee () {
      if (!this.selectedItem) {
        return 0
      }
      return this.selectedItem.fee
    },

    sellTokenRequestFeePercent () {
      if (!this.selectedItem) {
        return 0
      }
      return this.selectedItem.fee_percent
    },

    sellTokenRequestDescription () {
      if (!this.selectedItem) {
        return ""
      }
      return this.selectedItem.description
    },

    sellTokenRequestReason () {
      if (!this.selectedItem) {
        return ""
      }
      return this.selectedItem.reason
    },

    sellTokenRequestDate () {
      if (!this.selectedItem) {
        return ""
      }
      return moment(this.selectedItem.created_at).format("DD/MM/YYYY")
    },

    statusClass () {
      if (!this.selectedItem) {
        return ""
      }
      if (this.selectedItem.status === "CANCELLED" || this.selectedItem.status === "REJECTED") {
        return "status-cancelled"
      } else if (this.selectedItem.status === "APPROVED") {
        return "status-approved"
      }
      return "status-pending"
    },

    sellTokenRequestStatus () {
      if (!this.selectedItem) {
        return ""
      }
      return this.$t(`SELL_TOKEN_STATUSES.${this.selectedItem.status}`)
    },

    isPending () {
      return this.selectedItem && this.selectedItem.status === "PENDING"
    },

    isRejected () {
      return this.selectedItem && this.selectedItem.status === "REJECTED"
    },
  },
}
</script>

<style lang="scss">
.wr-container {
  width: 100%;
  margin-top: 20px;

  .content {
    width: 100%;
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding: 20px 5px;
    margin-top: 15px;

    .no-requests {
      font-size: 18px;
      font-weight: 600;
    }

    .b-table > tbody > tr {
      cursor: pointer;
    }

    .text-pending {
      color: gray;
    }

    .text-cancelled {
      color: red;
    }

    .text-approved {
      color: var(--primary-color);
    }
  }
}

.item {
  background-color: rgb(241, 241, 241);
  border-radius: 5px;
  padding: 15px 10px 0px 10px;
  margin-top: 5px;
}

.title {
  font-size: 18px;
  text-align: start;
  color: gray;
}

.value {
  font-size: 18px;
  font-weight: 500;
  text-align: end;
}

.status-pending {
  background-color: gray;
  border-radius: 5px;
  padding: 10px;
  color: white;
  font-weight: 700;
  font-size: 20px;
}

.status-cancelled {
  background-color: red;
  border-radius: 5px;
  padding: 10px;
  color: white;
  font-weight: 700;
  font-size: 20px;
}

.status-approved {
  background-color: var(--primary-color);
  border-radius: 5px;
  padding: 10px;
  color: white;
  font-weight: 700;
  font-size: 20px;
}
</style>
