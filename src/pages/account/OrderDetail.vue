<template>
  <div class="order-detail p-3 p-md-3 p-lg-4 mt-4 mb-4" v-if="order && order.property">
    <b-container class="mt-2">
      <b-row v-if="order.status === 'PENDING'" class="text-center">
        <b-col align-h="center">
          <h2 class="font-bold mt-0 mb-0">{{ $t("ORDER.PLACED") }}</h2>
          <b-row align-h="center mt-3 mb-2">
            <div class="important-info">
              <p class="title">{{ $t("ORDER.IMPORTANT_UPDATE") }} <span class="break"><br></span>{{ $t("ORDER.BANK_ACCOUNT_DETAILS_CHANGED") }}</p>
              <p class="value mt-2" v-html="$t('ORDER.IMPORTANT_UPDATE_CONTENT')"></p>
            </div>
          </b-row>
          <b-row align-h="center">
            <div class="order-info">
              <b-row class="title">
                <b-col cols="8">
                  <h5 class="info-value text-left font-bolder pt-0 mb-0 mt-1 ml-0">{{ $t("ORDER.INFO") }}</h5>
                  <p class="info-value-small text-left ml-0">{{ order.uuid }}</p>
                </b-col>
                <b-col>
                  <img class="logo" src="@/assets/img/logo_with_text.png" alt="">
                </b-col>
              </b-row>
              <div class="divider"></div>
              <!-- <p class="info-key text-left ml-3">{{ $t("ORDER.TRANSACTION_ID") }}</p> -->
              <p class="info-key text-left ml-3">{{ $t("ORDER.REFERENCE_ID") }}</p>
              <p class="info-value text-left ml-3 mb-1">{{ order.reference_id }}</p>
              <b-row align-h="center">
                <b-col cols="12" xl="5" lg="5">
                  <p class="info-key text-left ml-3">{{ $t("ORDER.PROPERTY_NAME") }}</p>
                  <p class="info-value text-left ml-3">{{ order.property.name }}</p>
                </b-col>
                <b-col cols="12" xl="7" lg="7">
                  <p class="info-key text-left ml-3">{{ $t("ORDER.NUM_OF_TOKENS") }}</p>
                  <p class="info-value text-left ml-3">{{ formatNumberIntl(order.num_of_tokens) }}</p>

                  <p class="info-key text-left ml-3">{{ $t("ORDER.PRICE") }}</p>
                  <p class="info-value text-left ml-3">
                    {{ exchangeValue(order.amount + order.amount_from_balance, userPreferredCurrency) }}
                    <span v-if="userPreferredCurrency !== 'IDR'">
                      (~{{ exchangeValue(order.amount + order.amount_from_balance, "IDR") }})
                    </span>
                  </p>
                  <p class="info-key text-left ml-3">{{ $t("TRANSACTION_FEE.TITLE") }}</p>
                  <p class="info-value text-left ml-3">
                    {{ exchangeValue(order.transaction_fee, userPreferredCurrency) }}
                    <span v-if="userPreferredCurrency !== 'IDR'">(~{{ exchangeValue(order.transaction_fee, "IDR") }})</span>
                  </p>
                  <p class="info-key text-left ml-3">{{ $t("PAYMENT.PAYMENT_PROCESSING_FEE") }}</p>
                  <p class="info-value text-left ml-3">
                    {{ exchangeValue(order.fee, userPreferredCurrency) }}
                    <span v-if="userPreferredCurrency !== 'IDR'">(~{{ exchangeValue(order.fee, "IDR") }})</span>
                  </p>

                  <p class="info-key text-left ml-3">{{ $t("ORDER.BALANCE_USED") }}</p>
                  <b-row>
                    <label class="ml-3">-</label>
                    <p class="info-value text-left ml-2">
                      {{ exchangeValue(order.amount_from_balance, userPreferredCurrency) }}
                      <span v-if="userPreferredCurrency !== 'IDR'">
                        (~{{ exchangeValue(order.amount_from_balance, "IDR") }})
                      </span>
                    </p>
                  </b-row>
                  <div class="remaining-amount-info text-left">
                    <p class="info-key text-left ml-3 mt-1">{{ $t("ORDER.REMAINING_AMOUNT_TO_TRANSFER") }}</p>
                    <p class="info-value font-18 text-left ml-3">
                      {{ exchangeValue(order.amount + order.fee + order.transaction_fee, userPreferredCurrency, true) }}
                      <span v-if="userPreferredCurrency !== 'IDR'">
                        (~{{ exchangeValue(order.amount + order.fee, "IDR", true) }})
                      </span>
                    </p>
                  </div>
                </b-col>
                <div class="note mt-4 mb-1">
                  <ul>
                    <li>{{ $t("ORDER.NOTE") }}</li>
                    <li>{{ $t("ORDER.TOKEN_NOTE") }}</li>
                    <li>{{ $t("ORDER.TRANSACTION_NOTE") }}</li>
                  </ul>
                </div>
              </b-row>
            </div>
          </b-row>

          <b-row align-h="center">
            <p class="font-18 text-left col-12 col-xl-10 col-lg-11" v-html="$t('ORDER.BANK_TRANSFER_INTRO')"/>
          </b-row>
          <div class="bank-info">
            <h5 class="text-left mb-1" @click="toggleCollapse('sgd')">
              <b-icon :icon='collapses.sgd ? "caret-down-fill" : "caret-right-fill"' class="mr-1"></b-icon>
              {{ $t("ORDER.SGD_GORO_BANK_DETAILS") }}
            </h5>
            <b-collapse v-model="collapses.sgd" id="collapse-sgd">
              <b-tabs class="tabs-panel mt-2" align="center" content-class="mt-3">
                <b-tab class="tab-content" :title="$t('PAYMENT.BANK_TRANSFER')" active>
                  <p class="info-key">{{ $t("ORDER.ACCOUNT_HOLDER_NAME") }}</p>
                  <p class="info-key-sub">{{ $t("ORDER.SGD_IF_FAST_ACT_GIRO") }}</p>
                  <p class="info-value" style="margin-left: 2.5rem"
                     ref="sgdAccountHolderName1" @click="copyText('sgdAccountHolderName1')">
                    {{ $t("ORDER.SGD_IF_FAST_ACT_GIRO_VALUE") }}
                    <i class="fas fa-copy icon-copy"></i>
                  </p>
                  <p class="info-key-sub">{{ $t("ORDER.SGD_IF_FAST_MEPS_LOCAL_TT") }}</p>
                  <p class="info-value" style="margin-left: 2.5rem"
                     ref="sgdAccountHolderName2" @click="copyText('sgdAccountHolderName2')">
                    {{ $t("ORDER.SGD_IF_FAST_MEPS_LOCAL_TT_VALUE") }}
                    <i class="fas fa-copy icon-copy"></i>
                  </p>
                  <p class="info-key">{{ $t("ORDER.SGD_ACCOUNT_NUMBER") }}</p>
                  <p class="info-value" ref="sgdAccountNumber" @click="copyText('sgdAccountNumber')">
                    {{ $t("ORDER.SGD_ACCOUNT_NUMBER_VALUE") }}
                    <i class="fas fa-copy icon-copy"></i>
                  </p>
                  <p class="info-key">{{ $t("ORDER.BANK_NAME") }}</p>
                  <p class="info-value" ref="sgdBankName" @click="copyText('sgdBankName')">
                    {{ $t("ORDER.SGD_BANK_NAME_VALUE") }}
                    <i class="fas fa-copy icon-copy"></i>
                  </p>
                  <p class="info-key">{{ $t("ORDER.BANK_CODE") }}</p>
                  <p class="info-value" ref="sgdBankCode" @click="copyText('sgdBankCode')">
                    {{ $t("ORDER.SGD_BANK_CODE_VALUE") }}
                    <i class="fas fa-copy icon-copy"></i>
                  </p>
                  <p class="info-key">{{ $t("ORDER.BRANCH_CODE") }}</p>
                  <p class="info-value" ref="sgdBranchCode" @click="copyText('sgdBranchCode')">
                    {{ $t("ORDER.SGD_BRANCH_CODE_VALUE") }}
                    <i class="fas fa-copy icon-copy"></i>
                  </p>
                  <p class="info-key">{{ $t("ORDER.TRANSFER_CONTENT") }}</p>
                  <p class="info-value" ref="sgdTransferContents" @click="copyText('sgdTransferContents')">
                    {{ $t("ORDER.TRANSFER_CONTENT_VALUE", { value: order.reference_id }) }}
                    <i class="fas fa-copy icon-copy"></i>
                  </p>
                  <p class="info-key">{{ $t("ORDER.TRANSFER_AMOUNT") }}</p>
                  <p class="info-value" ref="sgdTransferAmount" @click="copyText('sgdTransferAmount')">
                    {{ exchangeValue(order.amount + order.fee, "SGD", true) }}
                    <i class="fas fa-copy icon-copy"></i>
                  </p>
                </b-tab>
                <b-tab :title="$t('ORDER.PAY_NOW_ON_SGQR')">
                  <b-container>
                    <b-row align-h="center" class="mt-4 pt-2">
                      <b-img :src="require('@/assets/img/order_paynow_sgqr.png')" width="160"/>
                    </b-row>
                    <b-row align-h="center mt-3">
                      <label class="font-bold font-22 mb-0 color-black">
                        {{$t("ORDER.SGD_IF_FAST_ACT_GIRO_VALUE") }}
                      </label>
                    </b-row>
                    <b-row align-h="center mt-2">
                      <b-img :src="require('@/assets/img/order_sgqr_code.jpeg')" alt="code" width="280"/>
                    </b-row>
                    <b-row align-h="center">
                      <label class="scan-to-pay">{{ $t("ORDER.SCAN_TO_PAY") }}</label>
                    </b-row>
                  </b-container>
                </b-tab>
              </b-tabs>
            </b-collapse>
          </div>

          <div class="bank-info">
            <h5 class="text-left mb-1" @click="toggleCollapse('usd')">
              <b-icon :icon='collapses.usd ? "caret-down-fill" : "caret-right-fill"' class="mr-1"></b-icon>
              {{ $t("ORDER.USD_GORO_BANK_DETAILS") }}
            </h5>
            <b-collapse v-model="collapses.usd" id="collapse-usd">
              <p class="info-key">{{ $t("ORDER.ACCOUNT_HOLDER_NAME") }}</p>
              <p class="info-value" ref="usdAccountHolderName" @click="copyText('usdAccountHolderName')">
                {{ $t("ORDER.USD_ACCOUNT_HOLDER_NAME_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.USD_ACCOUNT_NUMBER") }}</p>
              <p class="info-value" ref="usdAccountNumber" @click="copyText('usdAccountNumber')">
                {{ $t("ORDER.USD_ACCOUNT_NUMBER_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_NAME") }}</p>
              <p class="info-value" ref="usdBankName" @click="copyText('usdBankName')">
                {{ $t("ORDER.USD_BANK_NAME_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.USD_BANK_SWIFT_BIC") }}</p>
              <p class="info-value" ref="usdBankSwift" @click="copyText('usdBankSwift')">
                {{ $t("ORDER.USD_BANK_SWIFT_BIC_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_ADDRESS") }}</p>
              <p class="info-value" ref="usdBankAddress" @click="copyText('usdBankAddress')">
                {{ $t("ORDER.USD_BANK_ADDRESS_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_COUNTRY") }}</p>
              <p class="info-value" ref="usdBankCountry" @click="copyText('usdBankCountry')">
                {{ $t("ORDER.USD_BANK_COUNTRY_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.TRANSFER_CONTENT") }}</p>
              <p class="info-value" ref="usdTransferContents" @click="copyText('usdTransferContents')">
                {{ $t("ORDER.TRANSFER_CONTENT_VALUE", { value: order.reference_id }) }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.TRANSFER_AMOUNT") }}</p>
              <p class="info-value" ref="usdTransferAmount" @click="copyText('usdTransferAmount')">
                {{ exchangeValue(order.amount + order.fee, "USD", true) }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
            </b-collapse>
          </div>

          <div class="bank-info">
            <h5 class="text-left mb-1" @click="toggleCollapse('euro')">
              <b-icon :icon='collapses.euro ? "caret-down-fill" : "caret-right-fill"' class="mr-1"></b-icon>
              {{ $t("ORDER.EURO_GORO_BANK_DETAILS") }}
            </h5>
            <b-collapse v-model="collapses.euro" id="collapse-euro">
              <p class="info-key">{{ $t("ORDER.ACCOUNT_HOLDER_NAME") }}</p>
              <p class="info-value" ref="euroAccountHolderName" @click="copyText('euroAccountHolderName')">
                {{ $t("ORDER.EURO_ACCOUNT_HOLDER_NAME_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.EURO_ACCOUNT_NUMBER") }}</p>
              <p class="info-value" ref="euroAccountNumber" @click="copyText('euroAccountNumber')">
                {{ $t("ORDER.EURO_ACCOUNT_NUMBER_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_NAME") }}</p>
              <p class="info-value" ref="euroBankName" @click="copyText('euroBankName')">
                {{ $t("ORDER.EURO_BANK_NAME_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.EURO_BANK_SWIFT_BIC") }}</p>
              <p class="info-value" ref="euroBankSwift" @click="copyText('euroBankSwift')">
                {{ $t("ORDER.EURO_BANK_SWIFT_BIC_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_ADDRESS") }}</p>
              <p class="info-value" ref="euroBankAddress" @click="copyText('euroBankAddress')">
                {{ $t("ORDER.EURO_BANK_ADDRESS_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_COUNTRY") }}</p>
              <p class="info-value" ref="euroBankCountry" @click="copyText('euroBankCountry')">
                {{ $t("ORDER.EURO_BANK_COUNTRY_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.TRANSFER_CONTENT") }}</p>
              <p class="info-value" ref="euroTransferContents" @click="copyText('euroTransferContents')">
                {{ $t("ORDER.TRANSFER_CONTENT_VALUE", { value: order.reference_id }) }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.TRANSFER_AMOUNT") }}</p>
              <p class="info-value" ref="euroTransferAmount" @click="copyText('euroTransferAmount')">
                {{ exchangeValue(order.amount + order.fee, "EUR", true) }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
            </b-collapse>
          </div>

          <div class="bank-info">
            <h5 class="text-left mb-1" @click="toggleCollapse('aud')">
              <b-icon :icon='collapses.aud ? "caret-down-fill" : "caret-right-fill"' class="mr-1"></b-icon>
              {{ $t("ORDER.AUD_GORO_BANK_DETAILS") }}
            </h5>
            <b-collapse v-model="collapses.aud" id="collapse-aud">
              <p class="info-key">{{ $t("ORDER.ACCOUNT_HOLDER_NAME") }}</p>
              <p class="info-value" ref="audAccountHolderName" @click="copyText('audAccountHolderName')">
                {{ $t("ORDER.AUD_ACCOUNT_HOLDER_NAME_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.AUD_ACCOUNT_NUMBER") }}</p>
              <p class="info-value" ref="audAccountNumber" @click="copyText('audAccountNumber')">
                {{ $t("ORDER.AUD_ACCOUNT_NUMBER_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.AUD_BSB_NUMBER") }}</p>
              <p class="info-value" ref="audBSBNumber" @click="copyText('audBSBNumber')">
                {{ $t("ORDER.AUD_BSB_NUMBER_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.AUD_BANK_ADDRESS") }}</p>
              <p class="info-value" ref="audBankAddress" @click="copyText('audBankAddress')">
                {{ $t("ORDER.AUD_BANK_ADDRESS_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.TRANSFER_CONTENT") }}</p>
              <p class="info-value" ref="audTransferContents" @click="copyText('audTransferContents')">
                {{ $t("ORDER.TRANSFER_CONTENT_VALUE", { value: order.reference_id }) }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.TRANSFER_AMOUNT") }}</p>
              <p class="info-value" ref="audTransferAmount" @click="copyText('audTransferAmount')">
                {{ exchangeValue(order.amount + order.fee, "AUD", true) }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
            </b-collapse>
          </div>

          <div class="bank-info">
            <h5 class="text-left mb-1" @click="toggleCollapse('uk')">
              <b-icon :icon='collapses.uk ? "caret-down-fill" : "caret-right-fill"' class="mr-1"></b-icon>
              {{ $t("ORDER.UK_GORO_BANK_DETAILS") }}
            </h5>
            <b-collapse v-model="collapses.uk" id="collapse-uk">
              <p class="info-key">{{ $t("ORDER.ACCOUNT_HOLDER_NAME") }}</p>
              <p class="info-value" ref="ukAccountHolderName" @click="copyText('ukAccountHolderName')">
                {{ $t("ORDER.UK_ACCOUNT_HOLDER_NAME_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.UK_SORT_CODE") }}</p>
              <p class="info-value" ref="ukSortCode" @click="copyText('ukSortCode')">
                {{ $t("ORDER.UK_SORT_CODE_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.UK_ACCOUNT_NUMBER") }}</p>
              <p class="info-value" ref="ukAccountNumber" @click="copyText('ukAccountNumber')">
                {{ $t("ORDER.UK_ACCOUNT_NUMBER_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.UK_IBAN") }}</p>
              <p class="info-value" ref="ukIBAN" @click="copyText('ukIBAN')">
                {{ $t("ORDER.UK_IBAN_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_NAME") }}</p>
              <p class="info-value" ref="ukBankName" @click="copyText('ukBankName')">
                {{ $t("ORDER.UK_BANK_NAME_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_ADDRESS") }}</p>
              <p class="info-value" ref="ukBankAddress" @click="copyText('ukBankAddress')">
                {{ $t("ORDER.UK_BANK_ADDRESS_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.BANK_COUNTRY") }}</p>
              <p class="info-value" ref="ukBankCountry" @click="copyText('ukBankCountry')">
                {{ $t("ORDER.UK_BANK_COUNTRY_VALUE") }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.TRANSFER_CONTENT") }}</p>
              <p class="info-value" ref="ukTransferContents" @click="copyText('ukTransferContents')">
                {{ $t("ORDER.TRANSFER_CONTENT_VALUE", { value: order.reference_id }) }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
              <p class="info-key">{{ $t("ORDER.TRANSFER_AMOUNT") }}</p>
              <p class="info-value" ref="ukTransferAmount" @click="copyText('ukTransferAmount')">
                {{ exchangeValue(order.amount + order.fee, "EUR", true) }}
                <i class="fas fa-copy icon-copy"></i>
              </p>
            </b-collapse>
          </div>
        </b-col>
      </b-row>

      <b-row v-if="order.status === 'REJECTED'" class="text-center" align-h="center">
        <b-col>
          <p class="font-28 font-weight-bold color-orangered">{{ $t("ORDER.REJECTED") }}</p>
          <p class="font-18 color-orangered">({{ $t("ORDER.REJECTED_REASON", { reason: order.reason }) }})</p>
          <p class="font-18 mt-3">
            {{
              $t("ORDER.REJECTED_DETAIL", {
                numOfTokens: formatNumberIntl(order.num_of_tokens),
                propertyInfo: order.property.name + " (" + order.property.metadata.address + ")"
              })
            }}
          </p>
          <b-img class="mt-3 mt-lg-4 mb-3 mb-lg-4" :src="getAvatar(order.property.images)" fluid
                 blank-src="property image"
                 width="450" height="300"/>
          <br>
          <p class="font-14 mt-1 font-italic">({{ $t("ORDER.CONTACT_INFO") }})</p>
        </b-col>
      </b-row>

      <b-row v-if="order.status === 'APPROVED'" class="text-center" align-h="center">
        <b-col>
          <p class="font-28 font-weight-bold">{{ $t("ORDER.SUCCESSFUL") }}</p>
          <p class="font-18 mt-3">{{ $t("ORDER.SUCCESSFUL_DETAIL") }}</p>
          <p class="font-18 mt-1 font-weight-bolder">
            {{ order.property.name }} ({{ order.property.metadata.address }})
          </p>
          <p v-if="referralMessage" class="font-16 mt-3" v-html="referralMessage"></p>
          <p v-if="referralMessage" class="ml-1">
            <u v-if="canCopy" @click="copyReferralLink"
               style="color: var(--primary-lighter-color);">{{ $t("REFERRAL.COPY_REFERRAL_LINK") }}</u>
            <span v-else>{{ referralLink }}</span>
          </p>
          <b-img class="mt-3 mt-lg-4 mb-3 mb-lg-4" :src="getAvatar(order.property.images)" fluid
                 blank-src="property image"
                 width="450" height="300"/>
          <br>
          <b-button id="btn_orderDetail_goToMyAssets" class="btn-main" variant="none" @click="goToMyAssets">
            {{ $t("ASSETS.VIEW_MY_ASSETS") }}
          </b-button>
        </b-col>
      </b-row>
    </b-container>
  </div>
</template>

<script>
import { exchange, formatNumberIntl, notify, urlImage } from "@/helpers/common"
import ordersService from "@/services/orders.service"
import store from "@/store/store"
import { GTM_EVENT_NAMES } from "../../constants/gtm"
import { gtmTrackEvent } from "../../helpers/gtm"

export default {
  data () {
    return {
      title: "Order Detail",
      orderUuid: this.$route.query.order_uuid,
      order: {},
      collapses: {
        sgd: false,
        usd: false
      }
    }
  },
  async mounted () {
    await this.getOrderInfo()
  },
  methods: {
    formatNumberIntl,
    async getOrderInfo () {
      let response = await ordersService.getOrderInfo({
        order_uuid: this.orderUuid,
      })
      if (response && response.order) {
        this.order = response.order
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_PAY_SUCCESS,
          property_id: this.order.property.id,
          num_of_tokens: this.order.num_of_tokens,
          balance_type: 'real',
          payment_method: 'bank_transfer',
          customerID: this.$store.getters.userProfile && this.$store.getters.userProfile.id || this.$route.query.user_id,
          customerEmail: this.$store.getters.userProfile && this.$store.getters.userProfile.email || this.$route.query.email,
          customerPhoneNumber: this.$store.getters.userProfile && this.$store.getters.userProfile.phone || this.$route.query.phone,
          value: this.order.amount,
          totalPayment: this.order.amount,
          currency: this.order.currency,
          transactionId: this.order.uuid,
        })
      }
    },
    getAvatar (images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    exchangeValue (value, currency, ceil = false) {
      return exchange(value, 100, false, currency, ceil, this.order?.exchange_rates?.rates)
    },
    toggleCollapse (collapseId) {
      // Close the other collapse
      for (const id in this.collapses) {
        if (id !== collapseId) {
          this.collapses[id] = false
        }
      }
      // Toggle the clicked collapse
      this.collapses[collapseId] = !this.collapses[collapseId]
    },
    copyText (refName) {
      const element = this.$refs[refName]
      navigator.clipboard.writeText(element.innerText)
      notify({ text: this.$t("common.COPIED") })
    },
    copyReferralLink () {
      if (this.referralLink) {
        navigator.clipboard.writeText(this.referralLink)
        notify({ text: this.$t("common.COPIED") })
      }
    },
    async goToMyAssets () {
      await this.$router.push({ name: "assetsOverview" })
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
  computed: {
    userPreferredCurrency () {
      if (this.$route.query.currency) {
        return this.$route.query.currency
      }
      if (this.$store.getters.userProfile) {
        return this.$store.getters.userProfile.preferred_currency
      }
      return "IDR"
    },
    referralBonusPercentForReferrer () {
      if (this.$store.getters.referralCode && this.$store.getters.referralCode.referrer_bonus_percent) {
        return this.$store.getters.referralCode.referrer_bonus_percent
      } else if (store.state.configs && store.state.configs.referral_bonus_non_indo_referrer_percent) {
        return store.state.configs.referral_bonus_non_indo_referrer_percent
      }
      return 0
    },
    referralTokenAmountForReferrer () {
      if (store.state.configs && store.state.configs.referral_token_amount_for_referrer) {
        return store.state.configs.referral_token_amount_for_referrer
      }
      return 0
    },
    referralTokenMinimumPurchaseForReferee() {
      if (store.state.configs && store.state.configs.referral_token_minimum_purchase) {
        return store.state.configs.referral_token_minimum_purchase;
      }
      return 0
    },
    isEnableReferralTokenForIndo () {
      if (store.state.configs && store.state.configs.enable_referral_token_for_indo) {
        return store.state.configs.enable_referral_token_for_indo
      }
      return false
    },
    isIndonesian () {
      if (this.$store.getters.userProfile && this.$store.getters.userProfile.iso_country_code) {
        return this.$store.getters.userProfile.iso_country_code === "ID"
      }
      if (this.$route.query.iso_country_code) {
        return this.$route.query.iso_country_code === "ID"
      }
      return false
    },
    referralMessage() {
      if (this.isIndonesian && this.isEnableReferralTokenForIndo && this.referralTokenAmountForReferrer > 0) {
        const pricePerToken = 10000;
        const tokenBonusRequired = this.referralTokenMinimumPurchaseForReferee > 0
          ? this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_REQUIRED_INFO", {
            token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
            token_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
          }) : ""
        return this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_INFO", {
          token_bonus: this.referralTokenAmountForReferrer,
          token_label: this.referralTokenAmountForReferrer > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase(),
          token_value: this.exchangeValue(this.referralTokenAmountForReferrer * pricePerToken, "IDR"),
          token_bonus_required: tokenBonusRequired
        })
      } else if (this.referralBonusPercentForReferrer > 0) {
        return this.$t("REFERRAL.REFERRAL_BONUS_INFO", { percent: this.referralBonusPercentForReferrer })
      }
      return null
    },
    referralLink () {
      const referralCode = this.$store.getters.referralCode && this.$store.getters.referralCode.code || this.$route.query.referral_code
      return referralCode &&
        `${window.location.origin}/invite/${referralCode}`
    },
    canCopy () {
      return window.isSecureContext && navigator.clipboard
    }
  },
}
</script>

<style lang="scss">
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css');

body{
  position: relative !important;
}
.order-detail {
  background-color: white;
  box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
  border-radius: 16px;
  color: var(--primary-color);

  p {
    margin: 0;
    padding: 0;
  }

  u {
    cursor: pointer;
  }

  h5 {
    cursor: pointer;
    color: var(--primary-darker-color);
    font-family: "Figtree-Bold", Helvetica, sans-serif, serif;

    &:hover {
      color: var(--primary-hover-color);
    }
  }

  .order-info {
    width: 570px;
    margin-top: 20px;
    margin-bottom: 30px;
    background-color: #E8F6F6;
    border-color: #BCD8D7;
    border-width: 1px;
    border-style: solid;
    border-radius: 20px;
    box-shadow: 0 1px 15px rgba(7, 55, 99, 0.16);
    padding: 20px 25px 25px 25px;
    background-size: auto 45%;
    background-repeat: no-repeat;
    background-position: left bottom;
    background-position-x: -35px;
    background-position-y: 110%;
    background-image: url('~@/assets/img/logo_background.png');

    .title {
      margin-top: -2px;
      margin-left: 0;
      margin-right: 0;
    }

    .logo {
      width: auto;
      height: 60px;
      margin-top: -3px;
      margin-left: auto;
      margin-right: 20px;
    }

    .note {
      color: var(--primary-color);
      font-weight: 600;
      font-size: 14px;
      border-width: 1.8px;
      border-style: solid;
      border-radius: 10px;
      padding: 8px 18px 8px 18px;

      ul li {
        text-align: left;
      }
    }
  }

  .divider {
    width: auto;
    height: 0.8px;
    background-color: #BCD8D7;
    margin: 5px 19px 35px 16px;
  }

  .remaining-amount-info {
    margin: 15px 18px 10px -5px;
    background-color: white;
    border-radius: 15px;
    padding: 13px 18px 18px 5px;
  }

  .bank-info {
    background-color: #F5F5F5;
    border-radius: 20px;
    margin-top: 22px;
    padding: 25px 28px 25px 28px;

    .tabs-panel {
      margin-left: 1.8rem;

      .tab-content {
        margin-top: 20px;
        margin-left: -0.85rem;
      }

      .nav-link {
        color: #959595;
        background-color: transparent;
        border: none;

        &:focus {
          font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
          color: var(--primary-hover-color) !important;
          border-bottom: 2.5px solid var(--primary-color);
        }
      }

      .active {
        font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
        color: var(--primary-hover-color) !important;
        border-bottom: 2.5px solid var(--primary-color);
      }

      .tab-pane {
        border-bottom: none !important;
      }
    }

    .scan-to-pay {
      width: 350px;
      color: white;
      background-color: #EC3657;
      border-radius: 10px;
      margin-top: 15px;
      padding: 8px;
    }
  }

  .info-key {
    margin-top: 1rem;
    margin-left: 1.8rem;
    text-align: left;
    color: #959595;
    font-weight: 600;
    font-size: 14px;
  }

  .info-key-sub {
    margin-top: 0.1rem;
    margin-left: 1.8rem;
    text-align: left;
  }

  .info-value {
    margin-top: 0.1rem;
    margin-left: 1.8rem;
    text-align: left;
    font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
    color: var(--primary-color);

    &:hover {
      cursor: pointer;
      color: var(--primary-darker-color);
    }

    .icon-copy {
      font-size: 14px;
      margin-left: 5px;
      color: darkgrey;

      &:hover {
        color: grey;
      }
    }
  }

  .info-value-small {
    @extend .info-value; // Inherit all styles
    margin-top: 0;
    padding-top: 0;
    font-size: 0.75em; // Make font smaller againsts the original
  }

  .important-info {
      width: 570px;
      padding: 10px 16px;
      color: white;
      background: darkorange;
      border-radius: 12px;
      font-size: 13px;
      text-align: center;
      font-family: "Figtree-Bold", Helvetica, sans-serif, serif;

      .title {
        font-size: 18px;
        font-weight: 700;

        .break {
          display: none;
        }

        @media (max-width: 600px) {
          .break {
            display: inline;
          }
        }
      }

      .value {
        font-size: 14px;
        font-weight: 400;
      }
    }
}
</style>
