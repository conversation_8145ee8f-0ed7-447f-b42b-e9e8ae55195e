<template>
    <div class="buy-container">
        <p class="font-28 font-weight-bold pl-3 pr-3">{{ $t('account.marketplace') }}</p>
        <b-container>
            <b-row v-if="properties && properties.data && properties.data.length" class="marketplace-content equal">
                <b-col xl="4" lg="4" md="6" sm="6" cols="12" class="property" v-for="property in properties.data"
                    :key="property.id">
                    <property-light-card :property="property" :is-from-account="true"/>
                </b-col>
                <b-col cols="12">
                    <b-pagination v-if="properties.total" align="right" v-model="properties.current_page"
                        :total-rows="properties.total" :per-page="properties.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
                </b-col>
            </b-row>
        </b-container>
    </div>
</template>

<script>

import PropertyLightCard from "../../components/Cards/PropertyLightCard.vue"
import propertiesService from "../../services/properties.service"
import { urlImage } from "../../helpers/common"

export default {

    components: {
        PropertyLightCard,
    },

    data() {
        return {
            properties: [],
        }
    },
    async mounted() {
        await this.getProperties(1)
    },

    methods: {
        async getProperties(page) {
            const result = await propertiesService.getList({ page });
            if (result) {
                const { data } = result;
                data.map((property) => {
                    property.imageURL = property.images && property.images.length ? urlImage(property.images[0]) : '';
                });
                this.properties = result
            }
        },

        async onChangePage(page) {
            await this.getProperties(page)
        },
    },
}
</script>

<style lang="scss" scoped>
.buy-container {
    width: 100%;
    margin-top: 20px;

    .marketplace-content {
        padding: 24px 0;
    }

    .property {
        margin-bottom: 30px;
    }
}
</style>