<template>
    <div class="settings" ref="kyc">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-12 mt-2">
                <p class="font-28 font-weight-bold">
                    {{ $t('account.KYC_TITLE') }}
                </p>
                <div class="content col-12 d-flex flex-column">
                    <div>
                        <KycSteps v-if="currentStep < 4" :current-step="currentStep" :success-cft-pep="successCftPep" :failed-cft-pep="failedCftPep" :typeSelected="typeSelected" class="mb-4">
                        </KycSteps>
                        <UploadCard v-if="currentStep === 1" @next-step="nextStep" @on-change-type-selected="onChangeTypeSelected"></UploadCard>
                        <InputCardInfo v-if="currentStep === 2" @next-step="nextStep"></InputCardInfo>
                        <TakeSelfie v-if="currentStep === 3" @next-step="nextStep"></TakeSelfie>
                        <KycStatus v-if="currentStep === 4"></KycStatus>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import authService from "@/services/auth.service";
import KycSteps from "@/components/kyc/KycSteps";
import UploadCard from '@/components/kyc/UploadCard';
import TakeSelfie from '@/components/kyc/TakeSelfie';
import InputCardInfo from '@/components/kyc/InputCardInfo';
import KycStatus from '@/components/kyc/KycStatus';
import store from '@/store/store';
import userStatus from "@/constants/userStatus";
import externalSites from '@/constants/externalSites';

export default {

    components: {
        KycSteps,
        UploadCard,
        TakeSelfie,
        InputCardInfo,
        KycStatus,
    },

    data() {
        return {
            // user: store.state.userProfile,
            contact: externalSites.CUSTOMER_EMAIL,
            contactMailTo: externalSites.MAIL_TO.CUSTOMER_SUPPORT,
            currentStep: 1,
            successCftPep: false,
            failedCftPep: false,
            typeSelected: 'YES',
        };
    },

    mounted() {
        this.currentStep = this.getCurrentStep
    },

    computed: {
        user() {
            return this.$store.getters.userProfile;
        },
        completedEmail() {
            return this.user.email
                || this.user.email_apple
                || this.user.email_fb
                || this.user.email_google;
        },

        completedPhone() {
            return this.user.phone && this.user.country_code;
        },

        completedSSN() {
            if (this.user.country) {
                if (this.user.country.code === 'US') {
                    return this.user.ssn;
                }
            }
            return true;
        },

        completedPersonalInfo() {
            return this.user.name
                && this.completedEmail
                && this.completedPhone
                && this.user.country
                && this.user.street_address
                && this.user.city
                && this.user.state
                && this.user.zip_code
                && this.user.dob
                && this.completedSSN;
        },

        isIdCard() {
            return this.user.id_card && this.user.id_card.card_type === 'indonesia_ktp';
        },

        compledUpload() {
            return this.user.kyc_status && this.user.kyc_status.compledUpload;
        },

        completedIDVerfification() {
            return this.user.kyc_status && this.user.kyc_status.completedIDVerfification;
        },

        completedSelfie() {
            return this.user.kyc_status && this.user.kyc_status.completedSelfie;
        },

        completedVerification() {
            return this.user.kyc_status && this.user.kyc_status.completedVerification;
        },

        beingReviewed() {
            return this.user.kyc_status && this.user.kyc_status.beingReviewed;
        },

        failedVerification() {
            return this.user.kyc_status && this.user.kyc_status.failedVerification;
        },

        isUserFullyActive() {
            return store.state.userProfile.status === userStatus.FullyActive;
        },

        getCurrentStep() {
            if (this.completedSelfie) {
                return 4
            }
            if (this.completedIDVerfification) {
                return 3
            }
            if (this.compledUpload) {
                return 2
            }
            return 1
        },
    },

    methods: {
        async onUpdateSuccess() {
            const res = await authService.getUserProfile(false);
            if (res && res.data) {
                await store.dispatch('setUserProfile', res.data);
                this.user = res.data;
            }
        },

        onSuccessVerification() {
            this.successCftPep = true
        },

        onFailedVerification() {
            this.failedCftPep = true
        },

        async nextStep() {
            await this.onUpdateSuccess()
            this.currentStep++
        },

        onChangeTypeSelected(typeSelected) {
            this.typeSelected = typeSelected
        },
    },
}
</script>

<style lang="scss">
.settings {
    width: 100%;
    margin-top: 20px;
    padding-bottom: 60px;

    .content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding: 20px;
        margin-top: 15px;

        .verified {
            font-size: 28px;
            font-weight: 700;
            color: val(--primary-color);
        }
    }

    .success-img {
        width: 150px;
        height: 150px;
        object-fit: contain;
    }
}
</style>
