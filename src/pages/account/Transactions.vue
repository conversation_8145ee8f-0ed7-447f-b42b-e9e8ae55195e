<template>
  <div class="ts-container">
    <p class="font-28 font-weight-bold">{{ $t('account.TRANSACTIONS') }}</p>
    <div class="transaction-content d-flex flex-column align-items-start">
      <v-select class="goro-select select mb-2" v-model="selectedType" :options="transactionTypes" :clearable="false" label="name">
        <template v-slot:option="option">
          {{ option.name }}
        </template>
        <template #selected-option="option">
          {{ option.name }}
        </template>
      </v-select>

      <div v-if="showNoTransactions" class="text-center w-100">
        <p class="no-transactions mb-5">
          {{ $t('account.THERE_ARE_NO_TRANSACTIONS') }}
        </p>
        <router-link :to="{ name: 'marketplace' }">
          <b-button id="btn_noTransaction_GotoMarketplace" class="btn-main" type="submit" variant="none">
            {{ $t('account.marketplace') }}
          </b-button>
        </router-link>
      </div>

      <div v-else class="w-100">
        <div v-for="item in items" :key="item.externalId" class="transaction-item" @click="onRowClicked(item)">
          <div class="item-header d-flex justify-content-between align-items-center" :style="{ backgroundColor: item.headerBgColor }">
            <div>
              <strong class="text-bold">{{ item.displayType }}</strong>
              <span class="hidden-on-mobile ml-3">{{ item.externalId }}</span>
            </div>
            <div>{{ item.date }}</div>
          </div>

          <div class="item-content d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-3">
              <template v-if="item.transactionIcon">
                <div class="item-image d-flex justify-content-center align-items-center">
                  <img :src="item.transactionIcon" alt="" style="width: 45px; height: 45px"/>
                </div>
              </template>
              <template v-else>
                <img :src="getAvatar(item.property.images)" class="item-image" alt=""/>
              </template>
              <div class="ml-3">
                <div v-if="item.num_of_tokens">
                  {{ formatNumberIntl(item.num_of_tokens) }} {{ item.num_of_tokens > 1 ? $t("common.TOKENS") : $t("common.TOKEN") }}
                </div>
                <div class="text-bold font-20">
                  {{ exchangeValue(item.value) }}
                </div>
              </div>
            </div>
            <div class="d-flex flex-row align-items-center">
              <b-button v-if="canContinueToPay(item.item)" class="btn-main item-continue-to-pay mr-2 hidden-on-mobile" variant="none" @click="continueToPay(item.item)">
                {{ $t("account.CONTINUE_TO_PAY") }}
              </b-button>
              <div class="item-status" :style="{ backgroundColor: item.statusBgColor, color: item.statusColor }">
                <img :src="require(`@/assets/img/icons/status_${item.statusIcon}.svg`)" alt=""/>
                <span> {{ item.displayStatus }}</span>
              </div>
            </div>
          </div>

          <div class="item-footer">
            <div>{{ item.externalId }}</div>
            <b-button v-if="canContinueToPay(item.item)" class="btn-main item-continue-to-pay" variant="none" @click="continueToPay(item.item)">
              {{ $t("account.CONTINUE_TO_PAY") }}
            </b-button>
          </div>
        </div>

        <div class="d-flex flex-column align-items-center">
          <b-pagination v-if="totalItems" class="trx-pagination mt-4 mb-0"
                        v-model="currentPage" :total-rows="totalItems" :per-page="perPage"
                        :prev-text="$t('common.PREVIOUS')" :next-text="$t('common.NEXT')" pills/>
        </div>
      </div>
    </div>
    <ModalTransactionDetail ref="modalTransactionDetail"/>
  </div>
</template>

<script>
import moment from "moment"
import { exchange, formatNumberIntl, getTransactionHeaderBgColor, getTransactionStatus, getTransactionStatusBgColor, getTransactionStatusColor, getTransactionStatusIcon } from "@/helpers/common"
import { STATUS, TRANSACTION_TYPE } from "@/constants/constants"
import { PAGINATION_DEFAULT } from "@/constants/pagination"
import CurrencyTooltip from "../../components/CurrencyTooltip.vue"
import ModalTransactionDetail from '../../modals/ModalTransactionDetail.vue';
import accountService from "../../services/account.service"

export default {
  components: {
    CurrencyTooltip,
    ModalTransactionDetail
  },
  data() {
    return {
      perPage: PAGINATION_DEFAULT.perPage,
      currentPage: 1,
      items: [],
      totalItems: 0,
      showNoTransactions: false,
      selectedType: {
        name: this.$t('TRANSACTIONS_TYPES.ALL'),
        value: 'ALL',
      },
    }
  },
  async mounted() {
    await this.fetchTransactions();
  },

  watch: {
    '$i18n.locale'(newVal, oldVal) {
      this.items = this.items.map(e => {
        e.displayType = this.$t(`TRANSACTIONS_TYPES.${e.type}`)
        e.displayStatus = this.$t(`TRANSACTIONS_STATUS.${e.rawStatus}`)
        return e
      });
      this.selectedType = this.transactionTypes.find(e => e.value === this.selectedType.value)
    },
    async 'selectedType.value'(newValue) {
      await this.fetchTransactions()
    },
    currentPage: {
      handler: async function (value) {
        await this.fetchTransactions()
      }
    }
  },

  computed: {
    transactionTypes() {
      return Object.values(TRANSACTION_TYPE)
        .filter(type =>
          type !== TRANSACTION_TYPE.BUY_TOKEN_VIRTUAL_BALANCE &&
          type !== TRANSACTION_TYPE.VIRTUAL_RENTAL_DISTRIBUTION
        )
        .map(type => ({
          name: this.$t(`TRANSACTIONS_TYPES.${type}`),
          value: type
        }))
    },
  },

  methods: {
    formatNumberIntl,

    async fetchTransactions() {
      const filters = {
        page: this.currentPage,
        per_page: this.perPage,
        type: this.selectedType.value !== 'ALL' ? this.selectedType.value : null
      }

      const res = await accountService.getTransactions(filters);
      const data = res && res.data ? res.data : []
      this.showNoTransactions = (data.length === 0);
      this.totalItems = res && res.total ? parseInt(res.total) : 0
      this.items = data && data.map(transaction => {
        const status = getTransactionStatus(transaction)
        let value = transaction.amount;
        switch (transaction.type) {
          case TRANSACTION_TYPE.WITHDRAWAL:
            value = transaction.withdrawal && transaction.withdrawal.required_amount || 0
            break;
          case TRANSACTION_TYPE.BUY_TOKEN:
          case TRANSACTION_TYPE.ORDER_TOKEN:
          case TRANSACTION_TYPE.SELL_TOKEN:
            const pricePerToken = transaction.property.price_per_token || 10000
            value = (transaction.num_of_tokens * pricePerToken) || 0
            break;
        }
        return {
          item: transaction,
          externalId: transaction.external_id || (transaction.payment && transaction.payment.external_id) || (transaction.withdrawal && transaction.withdrawal.external_id) || '--',
          date: moment(transaction.created_at).format('DD/MM/YYYY HH:mm:ss'),
          type: transaction.type,
          displayType: this.$t(`TRANSACTIONS_TYPES.${transaction.type}`),
          rawStatus: status,
          displayStatus: this.$t(`TRANSACTIONS_STATUS.${status}`),
          property: transaction.property,
          num_of_tokens: transaction.num_of_tokens,
          value: value,
          statusIcon: getTransactionStatusIcon(transaction),
          statusColor: getTransactionStatusColor(transaction),
          statusBgColor: getTransactionStatusBgColor(transaction),
          headerBgColor: getTransactionHeaderBgColor(transaction),
          transactionIcon: this.getTransactionIcon(transaction),
        }
      }) || [];
    },

    getAvatar(images) {
      const baseUrl = process.env.VUE_APP_IMG_HOST
      if (images && images.length) {
        return baseUrl + "/" + images[0].image;
      }
      return ""
    },

    async onRowClicked(item) {
      this.$refs.modalTransactionDetail.openPopup(item.item, item.property)
    },

    exchangeValue(value, exchangeRates = null) {
      return exchange(value, 100, false, null, false, exchangeRates)
    },

    getTransactionIcon(transaction) {
      switch (transaction.type) {
        case TRANSACTION_TYPE.WITHDRAWAL:
          return require('@/assets/img/transaction/withdrawal.svg')
        case TRANSACTION_TYPE.REFERRAL_BONUS:
        case TRANSACTION_TYPE.REFERRAL_BONUS_TOKEN:
          return require('@/assets/img/transaction/referral_bonus.svg')
        case TRANSACTION_TYPE.ADMIN_ADD_BALANCE:
        case TRANSACTION_TYPE.ADMIN_DEDUCT_BALANCE:
        case TRANSACTION_TYPE.SYSTEM_ADD_BALANCE:
          return require('@/assets/img/transaction/balance_deposited.svg')
        default:
          return null
      }
    },

    canContinueToPay(transaction) {
      return (transaction.type === TRANSACTION_TYPE.BUY_TOKEN && transaction.payment && transaction.payment.status === STATUS.PENDING)
        || (transaction.type === TRANSACTION_TYPE.ORDER_TOKEN && transaction.order && transaction.order.status === STATUS.PENDING)
    },

    continueToPay(transaction) {
      if (transaction.payment) {
        window.location.href = transaction.payment.partner_invoice_url
      } else if (transaction.order) {
        this.$router.push({ name: "orderDetail", query: { order_uuid: transaction.order.uuid } })
      }
    },
  },
}
</script>

<style lang="scss">
.ts-container {
  width: 100%;
  margin-top: 20px;

  .transaction-content {
    padding: 30px;
    margin-top: 15px;
    border-radius: 20px;
    background-color: white;
    box-shadow: 4px 6px 14.6px 0 rgba(0, 0, 0, 0.15);
    font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;

    .select {
      min-width: 250px;
      box-shadow: 0 3px 18.5px 0 rgba(0, 0, 0, 0.1);
    }

    .transaction-item {
      margin-top: 15px;
      background-color: white;
      border-radius: 10px;
      box-shadow: 2px 2px 15px 0 rgba(0, 0, 0, 0.13);
      font-size: 16px;
      font-weight: 300;
      color: #7B7B7B;
      cursor: pointer;

      .external-id-desktop {
        display: inline;
      }

      .external-id-mobile {
        display: none;
      }

      .item-header {
        padding: 8px 20px;
        border-radius: 10px 10px 0 0;
      }

      .item-content {
        padding: 16px 20px;

        .item-image {
          width: 114px;
          height: 66px;
          border-radius: 8px;

          img {
            width: 45px;
            height: 45px
          }
        }

        .item-status {
          padding: 4px 12px;
          border-radius: 8px;

          img {
            width: 15px;
            height: 15px;
            margin-right: 6px;
            margin-bottom: 3px
          }
        }
      }

      .item-footer {
        padding: 0 20px 18px;
        display: none !important;
      }

      .item-continue-to-pay {
        padding: 4px 18px;
        border-radius: 8px;
        justify-self: end;
      }

      .text-bold {
        color: black;
        font-weight: 600;
      }
    }

    .no-transactions {
      font-size: 18px;
      font-weight: 600;
    }

  }

  @media (max-width: 800px) {
    .transaction-content {
      padding: 18px;

      .transaction-item {
        font-size: 15px;

        .item-content {
          padding: 16px 20px 0;
        }

        .item-footer {
          display: grid !important;
          grid-template-columns: 1fr auto;
          align-items: end;

          div {
            margin-top: 10px;
            margin-right: 10px;
          }
        }

        .item-continue-to-pay {
          font-size: 15px;
        }

        .hidden-on-mobile {
          display: none !important;
        }
      }
    }
  }

  @media (max-width: 550px) {
    .transaction-content {
      padding: 10px;

      .transaction-item {
        font-size: 14px;

        .item-header {
          padding: 8px 12px;
        }

        .item-content {
          padding: 16px 12px 0;

          .item-image {
            width: 96px;
            height: 55px;
            border-radius: 8px;

            img {
              width: 38px;
              height: 38px
            }
          }
        }

        .item-footer {
          padding: 0 12px 18px;
          display: flex !important;
          flex-direction: column;
          align-items: center;
          gap: 10px;

          div {
            margin-top: 8px;
            margin-right: 0;
          }
        }

        .item-continue-to-pay {
          margin-top: -3px;
          font-size: 14px;
        }

        .hidden-on-mobile {
          display: none !important;
        }
      }
    }
  }
}

.trx-table .table .thead-light th {
  background-color: white;
  color: var(--primary-color);
  font-weight: bolder;
}

.trx-pagination.pagination .page-item .page-link {
  border: none !important;
  color: var(--primary-color);
}

.trx-pagination.pagination .active .page-link {
  background-color: #D7E6E6;
  border-radius: 2px !important;
  color: var(--primary-color);
  font-weight: bold;
}
</style>
