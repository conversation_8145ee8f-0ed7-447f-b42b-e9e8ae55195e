<template>
    <div class="bh-container">
        <p class="font-28 font-weight-bold">{{ $t('BALANCE_HISTORY.TITLE') }}</p>
        <div class="bh-content">
            <div class="mt-3 d-flex flex-column flex-md-row">
                <v-select class="goro-select select" v-model="selectedDate" :options="dateOptions" :clearable="false"
                    label="name">
                    <template v-slot:option="option">
                        {{ option.name }}
                    </template>
                    <template #selected-option="option">
                        {{ pickedDate ? pickedDate : option.name }}
                    </template>
                </v-select>
                <v-select class="goro-select select ml-0 ml-md-3 mt-1 mt-md-0" v-model="selectedType"
                    :options="typeOptions" :clearable="false" label="name">
                    <template v-slot:option="option">
                        {{ option.name }}
                    </template>
                    <template #selected-option="option">
                        {{ option.name }}
                    </template>
                </v-select>
            </div>
            <div v-if="!histories.length" class="mt-3 mb-3 d-flex flex-column align-items-center">
                <p class="font-18">{{ $t('BALANCE_HISTORY.THERE_ARE_NO_HISTORIES') }}</p>
            </div>
            <div class="d-flex flex-column mt-3">
                <div v-for="(history, index) in histories">
                    <p v-if="sessionDate(history, index)" class="font-24 font-semibold">{{ sessionDate(history, index)
                        }}</p>
                    <div class="history-item d-flex flex-row align-items-center click-able"
                        @click="getHistoryDetails(history)">
                        <div class="icon-bg d-flex justify-content-center align-items-center">
                            <img :src="require(`@/assets/img/balance-history/${history.type.toLowerCase()}.png`)"
                                alt="" />
                        </div>
                        <div
                            class="ml-4 flex-grow-1 d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
                            <div class="d-flex flex-column align-items-start flex-grow-0 flex-sm-grow-1 mb-1 mb-sm-0">
                                <p class="font-20 font-semibold">{{ $t(`BALANCE_HISTORY.TYPE_MAP.${history.type}`) }}
                                </p>
                                <div class="d-block d-sm-none">
                                    <p v-if="isIncrease(history)" class="mr-4 font-18 font-semibold color-deposit">{{
                                        exchangeValue(history.inc_balance) }}</p>
                                    <p v-else class="mr-4 font-18 font-semibold color-deduct">-{{
                                        exchangeValue(history.dec_balance)
                                        }}
                                    </p>
                                </div>
                                <div class="d-flex flex-row align-items-center">
                                    <img class="img-clock" src="@/assets/img/clock.png" alt="" />
                                    <p class="color-gray font-13 ml-2">{{ getTime(history.created_at) }}</p>
                                </div>
                            </div>
                            <div class="d-none d-sm-block">
                                <p v-if="isIncrease(history)" class="mr-4 font-18 font-semibold color-deposit">{{
                                    exchangeValue(history.inc_balance) }}</p>
                                <p v-else class="mr-4 font-18 font-semibold color-deduct">-{{
                                    exchangeValue(history.dec_balance)
                                    }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex flex-column w-100 mt-3">
                <b-pagination class="trx-pagination align-self-center" v-model="currentPage" :total-rows="totalItems"
                    :per-page="perPage" :prev-text="$t('common.PREVIOUS')" :next-text="$t('common.NEXT')"
                    aria-controls="my-table" pills>
                </b-pagination>
            </div>
        </div>
        <ModalBalanceHistoryDetail ref="modalBalanceHistoryDetail"></ModalBalanceHistoryDetail>
        <ModalPickDateRange ref="modalPickDateRange" @on-select="onSelectRange" @on-close="onClose">
        </ModalPickDateRange>
    </div>
</template>

<script>
import moment from 'moment';
import balanceHistoryService from '../../services/balance_history.service';
import { exchange } from "../../helpers/common"
import ModalBalanceHistoryDetail from '../../modals/ModalBalanceHistoryDetail.vue';
import ModalPickDateRange from '../../modals/ModalPickDateRange.vue';

export default {
    components: {
        ModalBalanceHistoryDetail,
        ModalPickDateRange,
    },

    data() {
        return {
            perPage: 5,
            currentPage: 1,
            totalItems: 0,
            ignoreChange: false,
            selectedDate: {
                name: this.$t('BALANCE_HISTORY.ALL_DATES'),
                value: 'ALL',
            },
            previousSelectedDate: null,
            fromDate: null,
            toDate: null,
            dateOptions: [
                {
                    name: this.$t('BALANCE_HISTORY.ALL_DATES'),
                    value: 'ALL',
                },
                {
                    name: this.$t('BALANCE_HISTORY.PICK_DATE'),
                    value: 'PICK',
                },
                {
                    name: this.$t('BALANCE_HISTORY.TODAY'),
                    value: moment().format('yyyy-MM-DD'),
                },
                {
                    name: this.$t('BALANCE_HISTORY.LAST_X_DAYS', { value: 7 }),
                    value: moment().subtract(7, 'days').format('yyyy-MM-DD'),
                },
                {
                    name: this.$t('BALANCE_HISTORY.LAST_X_DAYS', { value: 60 }),
                    value: moment().subtract(60, 'days').format('yyyy-MM-DD'),
                },
                {
                    name: this.$t('BALANCE_HISTORY.LAST_X_DAYS', { value: 90 }),
                    value: moment().subtract(90, 'days').format('yyyy-MM-DD'),
                }
            ],
            selectedType: {
                name: this.$t('BALANCE_HISTORY.ALL_TYPES'),
                value: 'ALL',
            },
            typeOptions: [
                {
                    name: this.$t('BALANCE_HISTORY.ALL_TYPES'),
                    value: 'ALL',
                },
                {
                    name: this.$t('BALANCE_HISTORY.TYPE_MAP.BUY'),
                    value: 'BUY',
                },
                {
                    name: this.$t('BALANCE_HISTORY.TYPE_MAP.SELL'),
                    value: 'SELL',
                },
                {
                  name: this.$t('BALANCE_HISTORY.TYPE_MAP.SWAP'),
                  value: 'SWAP',
                },
                {
                    name: this.$t('BALANCE_HISTORY.TYPE_MAP.WITHDRAW'),
                    value: 'WITHDRAW',
                },
                {
                    name: this.$t('BALANCE_HISTORY.TYPE_MAP.ADD'),
                    value: 'ADD',
                },
                {
                    name: this.$t('BALANCE_HISTORY.TYPE_MAP.DEDUCT'),
                    value: 'DEDUCT',
                },
            ],
            histories: [],
        }
    },

    async mounted() {
        // await this.getFilterDates()
        await this.getBalanceHistories()
    },

    watch: {
        async selectedDate(newValue, oldValue) {
            if (newValue.value === 'PICK') {
                if (!this.ignoreChange) {
                    this.$refs.modalPickDateRange.openPopup()
                }
                this.previousSelectedDate = oldValue
            } else {
                this.fromDate = null
                this.toDate = null
                await this.getBalanceHistories()
            }
        },
        async selectedType(newValue) {
            await this.getBalanceHistories()
        },
        async currentPage(newValue) {
            await this.getBalanceHistories()
        },
    },

    methods: {
        async getFilterDates() {
            const res = await balanceHistoryService.getFilterDates();
            if (res && res.data.length) {
                this.dateOptions = [
                    {
                        name: this.$t('BALANCE_HISTORY.ALL_DATES'),
                        value: 'ALL',
                    },
                    ...res.data.map(e => {
                        return {
                            name: moment(e).format('MMM DD, yyyy'),
                            value: e,
                        }
                    })
                ]
            }
        },

        async getBalanceHistories() {
            const res = await balanceHistoryService.getHistories({
                page: this.currentPage,
                per_page: this.perPage,
                from: this.selectedDate.value === 'PICK' ? moment(this.fromDate).format('yyyy-MM-DD') : this.selectedDate.value !== 'ALL' ? this.selectedDate.value : null,
                to: this.selectedDate.value === 'PICK' && this.toDate != null ? moment(this.toDate).format('yyyy-MM-DD') : null,
                type: this.selectedType.value !== 'ALL' ? this.selectedType.value : null,
            })
            if (res) {
                this.histories = res.data
                this.totalItems = res.total
            }
        },

        async getHistoryDetails(history) {
            const res = await balanceHistoryService.getHistoryDetail(history.transaction_uuid)
            this.$refs.modalBalanceHistoryDetail.openPopup(res.data, history)
        },

        getTime(date) {
            return moment(date).format('DD/MM/YYYY HH:mm:ss')
        },

        isIncrease(history) {
            const inc = history.inc_balance || 0
            const dec = history.dec_balance || 0
            return inc > dec
        },

        exchangeValue(value) {
            return exchange(value)
        },

        sessionDate(history, index) {
            const currentDate = moment(history.created_at).format('MMM DD, YYYY')
            if (index == 0) {
                return currentDate
            }
            const previousDate = moment(this.histories[index - 1].created_at).format('MMM DD, YYYY')
            if (currentDate !== previousDate) {
                return currentDate
            }
            return null
        },

        async onSelectRange(dates) {
            this.setIgnore()
            this.fromDate = dates[0]
            this.toDate = dates[1]
            this.selectedDate = {
                name: `${moment(dates[0]).format('D MMM')} - ${moment(dates[1]).format('DD MMM YYYY')}`,
                value: 'PICK',
            }
            await this.getBalanceHistories()
        },

        onClose() {
            if (this.fromDate && this.toDate) {
                this.setIgnore()
                this.selectedDate = {
                    name: `${moment(this.fromDate).format('D MMM')} - ${moment(this.toDate).format('DD MMM YYYY')}`,
                    value: 'PICK',
                }
            } else {
                this.selectedDate = this.previousSelectedDate
            }
        },
        setIgnore() {
            this.ignoreChange = true
            setTimeout(() => {
                this.ignoreChange = false
            }, 1000)
        }
    },

    computed: {
        pickedDate() {
            if (this.fromDate && this.toDate) {
                return `${moment(this.fromDate).format('D MMM')} - ${moment(this.toDate).format('DD MMM YYYY')}`
            }
            return null
        }
    },
}
</script>

<style lang="scss">
.bh-container {
    width: 100%;
    margin-top: 20px;

    .bh-content {
        background-color: white;
        box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
        border-radius: 16px;
        padding: 20px;
        margin-top: 15px;

        .select {
            min-width: 250px;
        }

        .history-item {
            background-color: white;
            box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
            border-radius: 8px;
            padding: 16px 20px 16px 20px;
            margin-top: 8px;
            margin-bottom: 8px;

            .icon-bg {
                background-color: var(--primary-color);
                width: 64px;
                height: 64px;
                min-width: 64px;
                min-height: 64px;
                border-radius: 50%;

                img {
                    width: 32px;
                    height: 32px;
                }
            }

            .color-deposit {
                color: #74A600;
            }

            .color-deduct {
                color: #CD1500;
            }

            .img-clock {
                width: 14px;
                height: 14px;
            }
        }
    }
}

.trx-pagination.pagination .page-item .page-link {
    border: none !important;
    color: var(--primary-color);
}

.trx-pagination.pagination .active .page-link {
    background-color: #D7E6E6;
    border-radius: 2px !important;
    color: var(--primary-color);
    font-weight: bold;
}

.picker-container {
    background-color: red;
    padding: 16px;
    position: absolute;
}
</style>