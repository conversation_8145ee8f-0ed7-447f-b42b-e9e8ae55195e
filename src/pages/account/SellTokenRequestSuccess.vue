<template>
  <b-container class="cls-single-page-container sell-request-status p-3 p-md-3 p-lg-4 mt-4 mb-4"
      v-if="sellTokenRequest && sellTokenRequest.id && property && property.id">
    <b-row class="text-center" align-h="center">
      <b-col cols="12" lg="7">
        <div class="cls-page-header-wrapper">
          <img class="cls-icon" width="112" height="112" :src="require(`@/assets/img/account/${iconStatus}`)"/>
          <h1 class="font-28 font-weight-bold mb-0 mt-3 page-heading">
            {{ status === $t("SELL_TOKEN_STATUSES.PENDING") ? $t("common.NEARLY_THERE") : status }}
          </h1>
          <p class="font-18 mt-2 mb-2 cls-caption" v-html="messageStatus"></p>
        </div>
        <div class="cls-page-content-wrapper no-boder">
          <div class="cls-property-purchased-container">
            <div class="cls-property-purchased-content">
              <sell-token-card :property="property" :sellTokenRequest="sellTokenRequest"></sell-token-card>
            </div>
          </div>

          <b-button id="btn_sellTokenRequestSuccess_ViewMyTransaction" class="btn-main mt-4 cls-btn-view-transaction" variant="none" @click="goToTransactions">
            {{ $t("SELL_TOKEN.VIEW_MY_TRANSACTIONS") }}
          </b-button>
        </div>
      </b-col>
    </b-row>
  </b-container>
</template>

<script>
import sellTokensService from "@/services/sellTokens.service"
import { formatNumberIntl, urlImage } from "@/helpers/common"
import SellTokenCard from '../../components/Cards/SellTokenCard.vue'

export default {
  components: {
    SellTokenCard,
  },
  data () {
    return {
      title: "Sell Request Success",
      sellTokenRequestUuid: this.$route.query.request_uuid,
      sellTokenRequest: {},
      property: {},
    }
  },
  async mounted () {
    await this.getSellTokenRequest()
  },
  methods: {
    async getSellTokenRequest () {
      let response = await sellTokensService.getSellTokenRequest({
        request_uuid: this.sellTokenRequestUuid,
      })
      if (response && response.data) {
        this.sellTokenRequest = response.data
        this.property = response.data.property
      }
    },

    getAvatar (images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    async goToTransactions () {
      await this.$router.push({ name: "transactions" })
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
  computed: {
    status () {
      if (this.sellTokenRequest) {
        if (this.sellTokenRequest.status === "APPROVED") {
          return this.$t("SELL_TOKEN_STATUSES.APPROVED")
        } else if (this.sellTokenRequest.status === "REJECTED") {
          return this.$t("SELL_TOKEN_STATUSES.REJECTED")
        } else if (this.sellTokenRequest.status === "CANCELLED") {
          return this.$t("SELL_TOKEN_STATUSES.CANCELLED")
        }
      }
      return this.$t("SELL_TOKEN_STATUSES.PENDING")
    },
    iconStatus () {
      if (this.sellTokenRequest) {
        if (this.sellTokenRequest.status === "APPROVED") {
          return 'sell-token-approved.png'
        } else if (this.sellTokenRequest.status === "REJECTED") {
          return 'sell-token-failed.png'
        } else if (this.sellTokenRequest.status === "CANCELLED") {
          return 'sell-token-failed.png'
        }
      }
      return 'sell-token-pending.png'
    },
    statusClass () {
      if (this.sellTokenRequest) {
        if (this.sellTokenRequest.status === "REJECTED") {
          return "status-rejected"
        } else if (this.sellTokenRequest.status === "PENDING") {
          return "status-pending"
        }
      }
      return ""
    },
    numOfTokens () {
      return this.sellTokenRequest && this.sellTokenRequest.num_of_tokens || 0
    },
    messageStatus() {
      return this.$t("SELL_TOKEN.SELL_REQUEST_SUBMITTED", {
        tokenValue: `${formatNumberIntl(this.numOfTokens)} ${this.numOfTokens > 1
          ? this.$t("common.TOKENS").toLocaleLowerCase() : this.$t("common.TOKEN").toLocaleLowerCase()}`,
        propertyValue: `${this.property.name} (${this.property.metadata.address})`,
        status: this.status === this.$t("SELL_TOKEN_STATUSES.PENDING") ? this.$t("SELL_TOKEN.PENDING_APPROVAL") : `${this.status.toLowerCase()}`
      })
    }
  },
}
</script>

<style lang="scss">
.sell-request-status {
  *{
    font-family: "AcuminVariableConcept", Helvetica, sans-serif;
  }

  p {
    margin: 0;
    padding: 0;
  }

  u {
    cursor: pointer;
  }

  .status-pending {
    background-color: transparent;
    color: darkorange;
  }

  .status-rejected {
    background-color: transparent;
    color: red;
  }

  .cls-page-header-wrapper{
    h1.page-heading{
      font-weight: 600;
      line-height: 28.8px;
      text-align: center;
      color: #616161;
      text-transform: capitalize;
    }
    .cls-caption{
      font-weight: 500;
      line-height: 19.2px;
      text-align: center;
      color: #A5A4A4;
    }
  }
  .cls-page-content-wrapper{
    .cls-property-purchased-container{
      background-color: #00AAA9;
      border-radius: 13px;
      box-shadow: 0px 8px 22.3px 0px #00000033;
    }
    .cls-warning-guide-container{
      background-color: #FFEED9;
      border: 1px solid #FFA705;
      border-radius: 10px;
      padding: 15px;
      .cls-icon{
        width: 25px;
        margin-right: 15px;
      }
      .cls-message{
        font-weight: 500;
        line-height: 16px;
        letter-spacing: -0.03em;
        text-align: left;
        color: #5F5F5F;
        .underline{
          font-weight: 600;
        }
      }
    }
    button{
      &.cls-btn-view-transaction{
        width: 230px;
        padding-top: 12px;
        padding-bottom: 8px;
      }
    }
  }
}
</style>
