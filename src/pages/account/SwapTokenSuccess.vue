<template>
  <div class="swap-success-form p-3 p-md-3 p-lg-4 mt-4 mb-4"
       v-if="swapTokenTransaction && swapTokenTransaction.id && currentProperty && currentProperty.id && newProperty && newProperty.id">
    <b-container>
      <b-row class="text-center" align-h="center">
        <b-col>
          <p class="font-28 font-weight-bold">{{ $t("common.CONGRATS") }}!</p>
          <p class="font-18 mt-2 mb-2"
             v-html="$t('SWAP_TOKEN.SUCCESSFULLY_SWAPPED', { numOfToken: formatNumberIntl(swapTokenTransaction.num_of_tokens), plural: swapTokenTransaction.num_of_tokens > 1 ? 's': '', currentPropertyName: currentProperty.name, newPropertyName: newProperty.name })"></p>
          <p class="font-18 mt-1"
             v-html="$t('PAYMENT.YOU_ARE_NOW_OWNER_OF_SWAP', { numOfTokens: formatNumberIntl(newPropertyTotalOwningTokens), tokenLabel: newPropertyTotalOwningTokens > 1 ? $t('common.TOKENS').toLowerCase() : $t('common.TOKEN').toLowerCase(), propertyName: `${newProperty.name} (${newProperty.metadata.address})` })"></p>
          <p v-if="referralMessage" class="font-16 mt-2" v-html="referralMessage"></p>
          <p v-if="referralMessage" class="ml-1">
            <u v-if="canCopy" @click="copyReferralLink"
               style="color: var(--primary-lighter-color);">{{ $t("REFERRAL.COPY_REFERRAL_LINK") }}</u>
            <span v-else>{{ referralLink }}</span>
          </p>
          <b-img class="mt-3 mt-lg-4 mb-3 mb-lg-4" :src="getAvatar(newProperty.images)" fluid blank-src="property image"
                 width="450" height="300"/>
          <br>
          <p class="font-16 mt-1 mb-4">
            {{ $t("TRANSACTION.EXTERNAL_ID") }}: {{ swapTokenTransaction.uuid }}
          </p>
          <b-button id="btn_swapTokenSuccess_ViewMyAssets" class="btn-main" variant="none" @click="goToMyAssets">
            {{ $t("ASSETS.VIEW_MY_ASSETS") }}
          </b-button>
        </b-col>
      </b-row>
    </b-container>
  </div>
</template>

<script>
import accountService from "../../services/account.service"
import swapTokensService from "../../services/swapTokens.service"
import { exchange, formatNumberIntl, notify, urlImage } from "@/helpers/common"
import store from "@/store/store";

export default {
  data () {
    return {
      title: "Swap Token Success",
      swapTokenTransactionUuid: this.$route.query.request_uuid,
      swapTokenTransaction: {},
      currentProperty: {},
      newProperty: {},
      newPropertyTotalOwningTokens: 0,
    }
  },
  async mounted () {
    await this.getSwapTokenTransaction()
  },
  methods: {
    formatNumberIntl,
    async getSwapTokenTransaction () {
      let response = await swapTokensService.getSwapTokenTransaction({
        request_uuid: this.swapTokenTransactionUuid,
      })
      if (response && response.data) {
        this.swapTokenTransaction = response.data
        this.currentProperty = response.data.current_property
        this.newProperty = response.data.new_property
        await this.getNewPropertyOwningTokens()
        this.startConfetti()
      }
    },
    async getNewPropertyOwningTokens () {
      if (this.newProperty) {
        const response = await accountService.getOwningTokensOfProperty({
          property_id: this.newProperty.id,
        })
        if (response.data) {
          this.newPropertyTotalOwningTokens = response.data
        }
      }
    },
    startConfetti () {
      this.$confetti.start({
        particles: [
          { type: "heart", },
          { type: "circle", },
          { type: "rect", },
        ],
        particlesPerFrame: 3
      })
      setTimeout(() => this.$confetti.stop(), 3000)
    },
    getAvatar (images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    async goToMyAssets () {
      await this.$router.push({ name: "assetsOverview" })
    },
    copyReferralLink () {
      if (this.referralLink) {
        navigator.clipboard.writeText(this.referralLink)
        notify({ text: this.$t("common.COPIED") })
      }
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
  computed: {
    referralBonusPercentForReferrer () {
      if (this.$store.getters.referralCode && this.$store.getters.referralCode.referrer_bonus_percent) {
        return this.$store.getters.referralCode.referrer_bonus_percent
      } else if (store.state.configs && store.state.configs.referral_bonus_non_indo_referrer_percent) {
        return store.state.configs.referral_bonus_non_indo_referrer_percent
      }
      return 0
    },
    referralTokenAmountForReferrer () {
      if (store.state.configs && store.state.configs.referral_token_amount_for_referrer) {
        return store.state.configs.referral_token_amount_for_referrer
      }
      return 0
    },
    referralTokenMinimumPurchaseForReferee() {
      if (store.state.configs && store.state.configs.referral_token_minimum_purchase) {
        return store.state.configs.referral_token_minimum_purchase;
      }
      return 0
    },
    isEnableReferralTokenForIndo () {
      if (store.state.configs && store.state.configs.enable_referral_token_for_indo) {
        return store.state.configs.enable_referral_token_for_indo
      }
      return false
    },
    isIndonesian () {
      if (this.$store.getters.userProfile && this.$store.getters.userProfile.iso_country_code) {
        return this.$store.getters.userProfile.iso_country_code === "ID"
      }
      return false
    },
    referralMessage() {
      if (this.isIndonesian && this.isEnableReferralTokenForIndo && this.referralTokenAmountForReferrer > 0) {
        const pricePerToken = 10000;
        const tokenBonusRequired = this.referralTokenMinimumPurchaseForReferee > 0
          ? this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_REQUIRED_INFO", {
            token_minimum_required: this.referralTokenMinimumPurchaseForReferee,
            token_label: this.referralTokenMinimumPurchaseForReferee > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
          }) : ""
        return this.$t("REFERRAL.REFERRAL_BONUS_TOKEN_INFO", {
          token_bonus: this.referralTokenAmountForReferrer,
          token_label: this.referralTokenAmountForReferrer > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase(),
          token_value: exchange(this.referralTokenAmountForReferrer * pricePerToken, 100, false, "IDR"),
          token_bonus_required: tokenBonusRequired
        })
      } else if (this.referralBonusPercentForReferrer > 0) {
        return this.$t("REFERRAL.REFERRAL_BONUS_INFO", { percent: this.referralBonusPercentForReferrer })
      }
      return null
    },
    referralLink () {
      return this.$store.getters.referralCode &&
        `${window.location.origin}/invite/${this.$store.getters.referralCode.code}`
    },
    canCopy () {
      return window.isSecureContext && navigator.clipboard
    }
  },
}
</script>

<style lang="scss">
.swap-success-form {
  background-color: white;
  box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
  border-radius: 16px;

  p {
    margin: 0;
    padding: 0;
  }

  u {
    cursor: pointer;
  }
}
</style>
