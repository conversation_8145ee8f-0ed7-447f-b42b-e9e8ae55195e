<template>
    <div class="text-center">
        <p class="confirm-title">{{ $t('WITHDRAWALS.CONFIRM_BANK_ACCOUNT') }}</p>
        <p class="success-message" v-if="success === true">{{ $t('WITHDRAWALS.CONFIRM_BANK_ACCOUNT_SUCCESS') }}</p>
        <p class="failed-message" v-if="error">{{ error }}</p>
        <p class="mt-3" v-if="time">{{ redirectMessage }}</p>
    </div>
</template>

<script>

import withdrawalsService from '../../services/withdrawals.service'

export default {

    data() {
        return {
            success: null,
            error: null,
            timer: null,
            time: null,
        };
    },

    beforeUnmount() {
        if (this.timer) {
            clearInterval(this.timer);
        }
    },

    async mounted() {
        await this.confirmBankAccount();
    },

    methods: {
        async confirmBankAccount() {
            try {
                const res = await withdrawalsService.confirmBankAccount({
                    code: this.$route.query.code,
                });
                if (res && res.data) {
                    this.success = true;
                    await this.$store.dispatch('refreshUserProfile');
                    this.startRedirectTimer();
                }
            } catch (e) {
                this.error = e.data;
                this.startRedirectTimer();
            }
        },

        startRedirectTimer() {
            this.time = 5;
            this.timer = setInterval(() => {
                this.time--;
                if (this.time === 0) {
                    clearInterval(this.timer);
                    this.$router.push({ name: 'bankAccountHistory' });
                }
            }, 1000);
        },
    },

    computed: {
        redirectMessage() {
            return this.$t('WITHDRAWALS.REDIRECT_IN', { value: this.time });
        },
    },
}
</script>

<style lang="scss" scoped>
.confirm-title {
    font-size: 35px;
    font-weight: 700;
    margin-top: 20px;
}

.success-message {
    color: green;
    font-size: 18px;
    margin-top: 15px;
}

.failed-message {
    color: red;
    font-size: 18px;
    margin-top: 15px;
}
</style>
