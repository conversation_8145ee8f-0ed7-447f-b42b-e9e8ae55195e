<template>
<b-container class="cls-single-page-container cls-payment-failed-container p-1 p-md-3 p-lg-4 mt-4 mb-4"
    v-if="payment.id && payment.status !== 'PAID' && property && property.id">
    <b-row class="text-center" align-h="center">
      <b-col cols="12" lg="7">
        <div class="cls-page-header-wrapper">
          <img class="cls-icon" width="112" height="112" :src="require(`@/assets/img/account/payment-failed.png`)"/>
          <h1 class="font-28 font-weight-bold mb-0 mt-3 page-heading">
            {{ $t("PAYMENT.PAYMENT_FAILED") }}
          </h1>
          <p class="font-18 mt-2 mb-2 cls-caption">
            {{ $t("PAYMENT.PAYMENT_FAILED_DETAIL") }} {{ property.name }} ({{ property.metadata.address }}) {{ $t("PAYMENT.PAYMENT_FAILED_DETAIL_2") }}
          </p>
        </div>
        <div class="cls-page-content-wrapper no-boder">
          <div class="cls-property-purchased-container">
            <div class="cls-property-purchased-content">
              <order-card :property="property" :payment="payment" :transaction="transaction"></order-card>
            </div>
          </div>

          <div class="cls-warning-guide-container d-flex justify-content-start align-items-center mt-4">
            <img class="cls-icon" width="24" height="24" :src="require(`@/assets/img/account/warning-message.svg`)"/>
            <p class="font-14 cls-message" v-html="$t('PAYMENT.PAYMENT_FAILED_GUIDE')">
            </p>
          </div>

          <b-button id="btn_paymentFail_BackToProperty" class="btn-main mt-4 cls-btn-to-property" variant="none" @click="backToProperty">
            {{ $t("PAYMENT.BACK_TO_PROPERTY") }}
          </b-button>
        </div>
      </b-col>
    </b-row>
  </b-container>
</template>
<script>
import paymentsService from "@/services/payments.service"
import OrderCard from '../../components/Cards/OrderCard.vue'

export default {
  components: {
    OrderCard,
  },
  data () {
    return {
      title: "Pay Failed",
      paymentExternalId: this.$route.query.external_id,
      payment: {},
      property: {},
    }
  },
  async mounted () {
    await Promise.all([this.getPaymentDetail()])
  },
  methods: {
    async getPaymentDetail () {
      let response = await paymentsService.getPaymentDetail({ external_id: this.paymentExternalId })
      if (response && response.payment && response.property) {
        this.payment = response.payment
        this.property = response.property
      }
    },
    async backToProperty () {
      await this.$router.push({ name: "propertyDetail", params: { uuid: this.property.uuid } })
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
}
</script>

<style lang="scss">
.cls-payment-failed-container{
  *{
    font-family: "AcuminVariableConcept", Helvetica, sans-serif;
  }
  .cls-page-header-wrapper{
    h1.page-heading{
      font-weight: 600;
      line-height: 28.8px;
      text-align: center;
      color: #616161;
      text-transform: capitalize;
    }
    .cls-caption{
      font-weight: 500;
      line-height: 19.2px;
      text-align: center;
      color: #A5A4A4;
    }
  }
  .cls-page-content-wrapper{
    .cls-property-purchased-container{
      background-color: #00AAA9;
      border-radius: 13px;
      box-shadow: 0px 8px 22.3px 0px #00000033;
    }
    .cls-warning-guide-container{
      background-color: #FFEED9;
      border: 1px solid #FFA705;
      border-radius: 10px;
      padding: 15px;
      .cls-icon{
        width: 25px;
        margin-right: 15px;
      }
      .cls-message{
        font-weight: 500;
        line-height: 16px;
        letter-spacing: -0.03em;
        text-align: left;
        color: #5F5F5F;
        .underline{
          font-weight: 600;
        }
      }
    }
    button{
      &.cls-btn-to-property{
        width: 200px;
        padding-top: 12px;
        padding-bottom: 8px;
      }
    }
  }
}
</style>
