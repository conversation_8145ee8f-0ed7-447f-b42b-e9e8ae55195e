<template>
  <div class="cpw-container">
    <div class="row justify-content-center">
      <div class="col-12 col-lg-7 mt-2">
        <p class="font-28 font-weight-bold">{{ $t("CHANGE_PASSWORD.HEADER") }}</p>
        <div class="content d-flex flex-column align-items-center">
          <b-container>
            <b-row align-h="center">
              <img class="lock-img" width="120" src="@/assets/img/pin-lock-1.png" alt="" />
            </b-row>
            <b-row align-h="center">
              <Form class="form col-12 col-md-12 col-lg-11" v-slot="{ handleSubmit }">
                <b-form @submit.prevent="handleSubmit(onSubmit)">
                  <h5 v-if="message">{{ message }}</h5>
                  <Field
                    vid="password"
                    :name="$t('CHANGE_PASSWORD.NEW_PASSWORD')"
                    :rules="{ required: true, min: 8, strong_password_user_info: [ userInfo ], strong_password_criteria: [ userInfo ]}"
                    v-slot="validationContext"
                    :model-value="form.password"  @update:modelValue="form.password = $event"
                  >
                    <b-form-group :label="$t('CHANGE_PASSWORD.NEW_PASSWORD')">
                      <b-input-group>
                        <b-form-input
                          v-bind="validationContext.field"
                          placeholder="••••••••••••"
                          :type="showPassword ? 'text' : 'password'"
                          :state="getValidationState(validationContext.meta)"
                          aria-describedby="input-password-feedback"
                        ></b-form-input>
                        <b-input-group-append>
                          <div class="show-hide d-flex justify-content-center align-items-center" @click="showPassword=!showPassword">
                            <b-icon width="18" height="18" color="white" :icon="showPassword ? 'eye' : 'eye-slash'"></b-icon>
                          </div>
                        </b-input-group-append>
                        <b-form-invalid-feedback id="input-password-feedback">
                          {{ validationContext.errors[0] }}
                        </b-form-invalid-feedback>
                      </b-input-group>
                      <ul class="goro-password-strength-checklist">
                        <li :style="getPasswordIconStyle(passwordLeast8Length)">
                          <b-icon :icon="getPasswordIcon(passwordLeast8Length)"
                            :style="getPasswordIconStyle(passwordLeast8Length)" scale="1.4"></b-icon>
                          <span class="font-14">{{ $t("CHANGE_PASSWORD.CHECKLISTS.LEAST1CHAR") }}</span>
                        </li>
                        <li :style="getPasswordIconStyle(passwordLeast1Number)">
                          <b-icon :icon="getPasswordIcon(passwordLeast1Number)"
                            :style="getPasswordIconStyle(passwordLeast1Number)" scale="1.4"></b-icon>
                          <span class="font-14">{{ $t("CHANGE_PASSWORD.CHECKLISTS.LEAST1NUMBER") }}</span>
                        </li>
                        <li :style="getPasswordIconStyle(passwordLeast1Upper1LowerCase)">
                          <b-icon :icon="getPasswordIcon(passwordLeast1Upper1LowerCase)"
                            :style="getPasswordIconStyle(passwordLeast1Upper1LowerCase)" scale="1.4"></b-icon>
                          <span class="font-14">{{ $t("CHANGE_PASSWORD.CHECKLISTS.LEAST1CHARCASE") }}</span>
                        </li>
                        <li :style="getPasswordIconStyle(passwordLeast1Special)">
                          <b-icon :icon="getPasswordIcon(passwordLeast1Special)"
                            :style="getPasswordIconStyle(passwordLeast1Special)" scale="1.4"></b-icon>
                          <span class="font-14">{{ $t("CHANGE_PASSWORD.CHECKLISTS.LEAST1SPECIAL") }}</span>
                        </li>
                      </ul>
                    </b-form-group>
                  </Field>

                  <Field
                    vid="password"
                    :name="$t('CHANGE_PASSWORD.CONFIRM_PASSWORD')"
                    :rules="{ required: true, min: 8, strong_password_user_info: [ userInfo ], strong_password_criteria: [ userInfo ], confirmed: form.password}"
                    v-slot="validationContext"
                    :model-value="form.password_confirmation"  @update:modelValue="form.password_confirmation = $event"
                  >
                    <b-form-group :label="$t('CHANGE_PASSWORD.CONFIRM_PASSWORD')">
                      <b-input-group>
                        <b-form-input
                          v-bind="validationContext.field"
                          placeholder="••••••••••••"
                          :type="showPassword ? 'text' : 'password'"
                          :state="getValidationState(validationContext.meta)"
                          aria-describedby="input-password-feedback"
                        ></b-form-input>
                        <b-input-group-append>
                          <div class="show-hide d-flex justify-content-center align-items-center" @click="showPassword=!showPassword">
                            <b-icon width="18" height="18" color="white" :icon="showPassword ? 'eye' : 'eye-slash'"></b-icon>
                          </div>
                        </b-input-group-append>
                        <b-form-invalid-feedback id="input-password-feedback">
                          {{ validationContext.errors[0] }}
                        </b-form-invalid-feedback>
                      </b-input-group>
                    </b-form-group>
                  </Field>
                  <Field
                    vid="otpcode"
                    :name="$t('AUTH.VERIFICATION_CODE')"
                  >
                    <b-form-group :label="$t('AUTH.VERIFICATION_CODE')">
                      <div class="goro-verification-code-block">
                        <v-otp-input
                          class="verification-input"
                          ref="otpInput"
                          input-classes="otp-input"
                          separator="-"
                          :num-inputs="6"
                          :should-auto-focus="false"
                          :is-input-num="true"
                          @on-change="handleOnChange"
                          @on-complete="handleOnComplete"
                        />

                        <p v-show="showNotifyCheckMail" class="goro-text-notify show-mobile" :style="{ 'display': !showNotifyCheckMail ? 'none !important' : 'block' }">
                          {{$t('AUTH.NOTIFY_CHECK_MAIL_TO_GET_VERIFICATION_CODE', { email: userInfo.email })}}
                        </p>

                        <a v-if="!isVerifiedCode" class="btn btn-main white-normal btn-send-code" @click="handleSendVerificationCode()">
                          {{$t('AUTH.SEND_VERIFICATION_CODE')}}
                        </a>
                        <p v-else class="text-verified-code">{{$t('AUTH.VERIFIED_CODE')}}</p>
                      </div>
                      <p v-show="showNotifyCheckMail" class="goro-text-notify show-desktop" :style="{ 'display': !showNotifyCheckMail ? 'none !important' : 'block' }">
                        {{$t('AUTH.NOTIFY_CHECK_MAIL_TO_GET_VERIFICATION_CODE', { email: userInfo.email })}}
                      </p>
                    </b-form-group>
                  </Field>

                  <b-row class="text-center pt-4 mt-5" align-h="center">
                    <b-button id="btn_changePassword_Submit" class="btn-main btn-medium pl-4 pr-4" type="submit" variant="none" :disabled="submitting">
                      {{ $t("CHANGE_PASSWORD.CHANGE_PASSWORD") }}
                    </b-button>
                  </b-row>
                </b-form>
              </Form>
            </b-row>
          </b-container>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineRule, Field, Form, configure } from "vee-validate"
import { email, min, required, regex, confirmed } from "@vee-validate/rules"
import { setLocale, localize } from '@vee-validate/i18n'
import accountService from "../../services/account.service"
import { notify } from "@/helpers/common"
import VOtpInput from "vue3-otp-input"
import authService from '../../services/auth.service';

defineRule("email", email)
defineRule("min", min)
defineRule("required", required)
defineRule('regex', regex)
defineRule('confirmed', confirmed)

configure({
  generateMessage: localize({
    en: {
      messages: {
        required: 'The {field} is required a',
        strong_password_user_info: 'Password cannot contain user-related information.',
        strong_password_criteria: 'Password should contain a mix of uppercase letters, lowercase letters, numbers, and special characters.',
        confirmed: 'Confirm password does not match',
      },
    },
    id: {
      messages: {
        required: 'Kolom {field} wajib diisi',
        strong_password_user_info: 'Kata sandi tidak boleh mengandung informasi yang berhubungan dengan pengguna.',
        strong_password_criteria: 'Kata sandi harus mengandung kombinasi huruf kapital, huruf kecil, angka, dan karakter khusus.',
        confirmed: 'Konfirmasi kata sandi tidak cocok',
      },
    },
  }),
})

/**
 * Not based on something that is easy to guess or uses information related to the User, for example: name, telephone number, date of birth
 */
defineRule('strong_password_user_info', (value, [otherValues]) => {
  let { name, email, phone, dob } = otherValues

  if (!name) {
    name = ''
  }
  name = name.split(' ').join('').trim()

  if (name && value.toLowerCase().includes(name.toLowerCase())) {
    return false
  }

  if (phone && value.includes(phone)) {
    return false
  }

  if (email && value.includes(email)) {
    return false
  }

  if (dob && value.includes(dob)) {
    return false
  }

  return true
})

/**
 * Uses a combination of uppercase letters, lowercase letters and numbers, wherever possible using special characters (such as: !$%#*)
 */
defineRule('strong_password_criteria', (value, [otherValues]) => {
  // Check if the password contains a combination of uppercase, lowercase, numbers, and special characters
  const regexPattern = /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9])[A-Za-z0-9!@#$%^&*()_+{}\[\]:;<>,.?/~\-\\]+$/
  if (!regexPattern.test(value)) {
    return false
  }

  return true
})

export default {
  components: {
    Field,
    Form,
    defineRule,
    VOtpInput
  },
  data () {
    return {
      title: "Change Password",
      form: {
        code: null,
        password: '',
        password_confirmation: ''
      },
      message: "",
      showPassword: false,
      submitting: false,
      sendingCode: false,
      isVerifiedCode: false,
      userInfo: {},
      showNotifyCheckMail: false,
    }
  },
  mounted() {
    setLocale(this.$i18n.locale)

    const user = store.state.userProfile || {};
    this.userInfo = {
      name: user.name || '',
      email: user.email || user.email_fb || user.email_google || user.email_apple || '',
      phone: user.phone || '',
      dob: user.dob || ''
    }
  },
  computed: {
    passwordLeast8Length() {
      let isValid = /^(?=.*[^\s]).{8,}$/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Number() {
      let isValid = /\d/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Upper1LowerCase() {
      let isValid = /^(?=.*[a-z])(?=.*[A-Z]).+$/.test(this.form.password)
      return this.form.password && isValid
    },
    passwordLeast1Special() {
      const regex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;
      // Test if the password contains at least one special character
      const isValid = regex.test(this.form.password)
      return isValid
    },
    isValidForm() {
      return true
    }
  },
  methods: {
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    async handleOnComplete(value) {
      try {
        const result = await authService.validVerificationCode({
          "email": this.userInfo.email,
          "code": value,
          "reuse": true
        }, true, false)

        if (result && result.status) {
          this.isVerifiedCode = true
          this.form.code = value
        } else {
          this.isVerifiedCode = false
        }
      } catch (e) {
        this.isVerifiedCode = false
        notify({ text: e.message, type: "error" })
      }
    },
    handleOnChange(value) {
    },
    async handleSendVerificationCode() {
      try {
        await authService.requestVerificationCode({
          "email": this.userInfo.email,
          "type": "CHANGE_PASSWORD"
        })
        this.showNotifyCheckMail = true
      } catch (e) {
        if (e.extraData && e.extraData.type === 'sent') {
          this.showNotifyCheckMail = true
        } else {
          this.showNotifyCheckMail = false
        }
        notify({ text: e.message, type: "error" })
      }
    },
    async onSubmit () {
      const self = this
      if (!this.isVerifiedCode) {
        notify({ text: this.$t("AUTH.REQUIRE_VERIFICATION_CODE_BEFORE_CHANGE_PASSWORD"), type: "error" })
      } else {
        if (!this.submitting) {
          this.submitting = true
          try {
            this.message = ""

            const data = await accountService.changePassword(this.form)
            if (data && data.user) {
              this.message = data.message

              notify({ text: this.$t("CHANGE_PASSWORD.SUCCESSFULLY") })
              setTimeout(async () => {
                try {
                  // Auto logout
                  await store.dispatch('doLogout');
                  // Redirect to login page
                  await self.$router.push({ name: 'login' });
                } catch (e) {
                  // Nothing
                }
              }, 2000)
            } else {
              if (data && data.invalidCode) {
                this.isVerifiedCode = false
              }
            }
          } finally {
            this.submitting = false
            this.isVerifiedCode = false
          }
        }
      }
    },
    getPasswordIconStyle(valid) {
      if (this.form.password) {
        if (valid) {
          return { color: '#28a745' };
        }
        return { color: '#dc3545' };
      }
      return { color: '#707a8a' };
    },
    getPasswordIcon(valid) {
      if (this.form.password) {
        if (!valid) {
          return 'x';
        }
      }
      return 'check';
    },
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
  watch: {
    '$i18n.locale'(newVal, oldVal) {
      setLocale(newVal)
    },
  }
}
</script>
<style lang="scss">
.cpw-container {
  width: 100%;
  margin-top: 20px;

  .content {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 5px;
    padding-right: 5px;
    margin-top: 15px;

    .form {
      margin: 10px;
      padding: 24px !important;
    }

    h5 {
      color: var(--primary-color);
      border: 2px var(--primary-background-darker-color);
      border-radius: 8px;
      background: var(--primary-background-color);
      padding: 10px 15px;
    }

    //b-form-input
    input {
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      color: var(--primary-darker-color);

      &::placeholder {
        color: #ABB5BE !important;
        opacity: 1;
      }
    }

    .show-hide {
      width: 40px;
      background-color: rgba(0, 102, 102, 0.9);;
      cursor: pointer;
      border-radius: 0px 4px 4px 0px;
      transition: .5s;

      &:hover {
        background-color: rgba(0, 102, 102, 1);;
      }
    }
  }

  .goro-text-notify{
    font-size: 14px;
    color: rgb(112, 122, 138);
    padding: 10px 4px;
    word-break: break-word;
    @media screen and (max-width: 768px) {
      font-size: 13px !important;
    }
  }
}
</style>
