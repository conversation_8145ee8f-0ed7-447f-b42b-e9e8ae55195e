<template>
    <b-modal v-model="showModal" size="xl" modal-class="cls-goro-custom-modal" header-class="modal-header no-border" footer-class="modal-footer" @hidden="$emit('on-close')" hide-footer>
        <template #modal-header>
            <div class="d-flex justify-content-between align-items-center w-100">
                <div style="width: 30px;"></div>
                <h5 class="font-20 font-weight-bold header-title mb-0">
                    {{ $t('PARTNER.PROPERTY.TOKEN_HOLDERS') }}
                </h5>
                <button class="btn-close" @click="onClose()">
                    <b-icon icon="x-lg" style="color: gray;" scale="1.6"></b-icon>
                </button>
            </div>
        </template>
        <template #default="{ hide }">
            <div class="container cls-property-holders-container">
                <b-row class="mb-3 ml-0 mr-0 cls-property-info" align-v="center" v-if="property">
                    <b-col cols="12" md="3" class="pl-0 pr-0">
                        <img :src="getImageUrl(property.images[0])" alt="" />
                    </b-col>
                    <b-col cols="12" md="8" class="ml-0 mxl-3 pt-3 pb-3 ml-lg-2">
                        <p class="font-28 font-weight-bold">{{ property.name }}<b-icon class="link-icon"
                                @click="openPropertyDetails" icon="arrow-up-right-square">
                            </b-icon></p>
                        <p class="mb-1">{{ property.metadata.address }}</p>
                        <p>Total tokens: {{ property.total_tokens }}</p>
                        <p>Sold tokens: {{ property.sold_tokens }} ({{ getPercent(property.sold_tokens) }}%)</p>
                        <p>Tokens left: {{ property.total_tokens - property.sold_tokens }}
                            ({{ getPercent(property.total_tokens - property.sold_tokens) }}%)</p>
                        <p>Price per token: IDR{{ getFormattedCurrency(property.price_per_token) }}</p>
                    </b-col>
                </b-row>
                <div slot="raw-content" class="table-responsive">
                    <paper-table :data="items" :columns="tableColumns" class="cls-goro-custom-table" :borderless="true">
                        <template #theadSearch>
                            <thead class="search">
                                <th width="30px"></th>
                                <th width="20%">
                                    <b-input v-model="filters.id"></b-input>
                                </th>
                                <th width="30%">
                                    <b-input v-model="filters.name"></b-input>
                                </th>
                                <th width="20%">
                                    <b-input v-model="filters.email"></b-input>
                                </th>
                                <th width="25%"></th>
                            </thead>
                        </template>
                    </paper-table>
                    <b-pagination v-if="holders.total" align="left" class="mt-3" v-model="holders.current_page"
                        :total-rows="holders.total" :per-page="holders.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
                </div>
            </div>
        </template>
    </b-modal>
</template>

<script>

import { PaperTable } from "../../../components";
import propertiesService from '../../../services/partner/properties.service';
import { urlImage, formatNumberIntl } from "@/helpers/common";
import { PAGINATION_DEFAULT } from '../../../constants/pagination';

export default {
    components: {
        PaperTable,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        property: {
            type: Object,
            default: null,
        },
    },

    data() {
        return {
            perPage: PAGINATION_DEFAULT.perPage,
            showModal: false,
            holders: [],
            items: [],
            tableColumns: ["No", "Id", "Name", "Email", "Owned"],
            filters: {
                id: '',
                name: '',
                email: '',
            },
        };
    },

    emits: ['on-close'],

    watch: {
        async property(value) {
            if (value) {
                await this.getHolders(value.id, 1);
            }
        },

        show(value) {
            this.showModal = value;
        },
        'filters.id'() {
            this.searchTimeOut();
        },
        'filters.name'() {
            this.searchTimeOut();
        },
        'filters.email'() {
            this.searchTimeOut();
        },
    },

    methods: {
        async getHolders(id, page = 1) {
            const filters = {
                ...this.filters,
                per_page: this.perPage,
                page,
            };
            const res = await propertiesService.getHolders(id, filters);
            if (res) {
                this.holders = res;
                this.items = res.data.map((e, idx) => {
                    return {
                        'no': (idx + 1) + ((page - 1) * this.perPage),
                        'id': e.id,
                        'name': e.name,
                        'email': e.email,
                        'owned': `${e.owned_tokens} tokens`,
                        // 'avg_price_per_token': `IDR${numberWithCommas(e.avg_price_per_token)}`,
                    };
                });
            }
        },

        onClose: function () {
            this.$emit("on-close")
        },

        getImageUrl(image) {
            return urlImage(image);
        },

        getPercent(tokens) {
            return Math.round((tokens * 100 / this.property.total_tokens) * 10) / 10;
        },

        async openPropertyDetails() {
            const route = this.$router.resolve({ name: 'propertyDetail', params: { uuid: this.property.uuid } });
            window.open(route.href, '_blank');
        },

        searchTimeOut(page = 1) {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setTimeout(async () => {
                await this.getHolders(this.property.id, page);
            }, 400);
        },

        async onChangePage(page) {
            await this.getHolders(this.property.id, page);
        },

        getFormattedCurrency(value) {
            return formatNumberIntl(value)
        },
    },

    computed: {
        isLoading() {
            return this.$store.getters.isLoading;
        },
    },
}
</script>

<style lang="scss" scoped>
.cls-property-holders-container{
    .cls-property-info{
        box-shadow: 1px 1px 10px 0px #0000001A;
        border-radius: 10px !important;
        overflow: hidden;
        img{
            min-width: 100%;
            width: auto;
            height: 100%;
            object-fit: cover;
            max-width: 102%;
            border-radius: 10px !important;
        }
    }
}
:deep() #full-screen-modal {
    padding: 0 !important;
    background: #fff;
}

:deep() #full-screen-modal .modal-dialog {
    width: 100%;
    max-width: 100%;
    height: 100vh;
    max-height: 100vh;
    min-height: 100vh;
    margin: 0;
    background: #fff;
}

:deep() #full-screen-modal .modal-content {
    height: 100%;
    border: none;
    border-radius: 0;
    background: #fff;
}

:deep() #full-screen-modal .modal-header {
    border-radius: 0;
}

.item {
    background-color: white;
    padding: 20px;
}

.link-icon {
    width: 20px;
    height: 20px;
    margin-left: 15px;
    margin-bottom: 5px;
    cursor: pointer;
}
</style>
