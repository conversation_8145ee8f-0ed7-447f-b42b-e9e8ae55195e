<template>
  <section class="gradient-custom">
    <div class="container py-5 h-100">
      <div class="row d-flex justify-content-center align-items-center h-100">
        <div class="col-12 col-md-8 col-lg-6 col-xl-5">
          <div class="card bg-dark text-white" style="border-radius: 1rem;">
            <div class="card-body p-5 text-center">

              <div class="mb-md-5 mt-md-4 pb-5">

                <h2 class="fw-bold mb-2 text-uppercase">{{ $t("PARTNER.LOGIN_HEADER")}}</h2>
                <p class="text-white-50 mb-5">{{ $t("PARTNER.LOGIN_DESCRIPTION")}}</p>

                <form @submit.prevent="doLogin">
                  <div class="form-outline form-white mb-4">
                    <input type="email" placeholder="Email" v-model="email" class="form-control form-control-lg" />
                  </div>

                  <div class="form-outline form-white mb-4">
                    <input type="password" placeholder="Password" v-model="password"
                      class="form-control form-control-lg" />
                  </div>
                  <!-- Recaptcha V2 checkbox fallback -->
                  <div v-if="showRecaptchaV2.LOGIN" :ref="recaptchaV2Checkbox.LOGIN" class="d-flex justify-content-center mt-1"></div>
                  <button id="btn_loginPartner" type="submit" class="btn btn-outline-light btn-lg px-5 mt-3">Login</button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { useRecaptcha } from '@/composables/useRecaptcha'
import { getErrorMessage, notify } from "@/helpers/common";
import roles from '../../constants/roles';
import authService from "../../services/auth.service";
import { ERROR_CODE } from "@/constants/constants";
import messErrors from "@/constants/errors";

export default {
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ LOGIN: 'login' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data() {
    return {
      email: '',
      password: '',
    };
  },
  methods: {
    async doLogin() {
      if (this.email === '') {
        notify({
          text: 'Email is required',
          type: 'error',
        });
        return;
      }
      if (this.password === '') {
        notify({
          text: 'Password is required',
          type: 'error',
        });
        return;
      }
      if (!this.validateRecaptchaV2.LOGIN()) return
      try {
        const recaptchaTokenV3 = await this.recaptchaV3Exec.LOGIN()
        const recaptchaTokenV2 = this.recaptchaTokenV2.LOGIN
        const data = await authService.login({
          email: this.email,
          password: this.password,
          role: roles.Partner,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }, false, true);
        if (data) {
          const { access_token, user } = data;
          await store.dispatch('setUserProfile', user);
          localStorage.setItem('Authorization', access_token);
          await store.dispatch('getPartnerPermissions');
          await this.$router.push({ name: 'partner' })
        }
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.LOGIN = true
        } else {
          notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.LOGIN()
      }
    },
  }
};
</script>
<style>

</style>
