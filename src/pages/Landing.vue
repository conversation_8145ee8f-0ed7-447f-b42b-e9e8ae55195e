<!--suppress HtmlUnknownTag -->
<template>
  <div class="landing">
    <div class="main__content">
      <b-container>
        <b-row>
          <b-col cols="11" xl="6" lg="7" md="8" sm="8">
            <div class="pb-5">
              <h1 class="font-46">{{ $t("LANDING.TITLE") }}</h1>
              <p class="font-18">{{ $t("LANDING.DESCRIPTION") }}</p>
            </div>
            <b-row style="margin-left: 1px;" align-v="end">
              <b-button id="btn_Landing_JoinNow" class="btn-main mt-2 font-18" type="submit" style="border-radius: 18px; padding: 12px 40px;"
                        variant="none" @click="scrollToRegistrationForm()">
                {{ $t("common.JOIN_NOW") }}
              </b-button>
            </b-row>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="pt-4 pb-4" style="background-color: var(--primary-background-darker-color)">
      <b-container>
        <b-row align-h="center">
          <b-col class="text-center" cols="10" lg="8" xl="6">
            <h1 class="font-38">{{ $t("LANDING.FEATURED_PROPERTIES") }}</h1>
            <label class="mb-3 font-18">{{ $t("LANDING.FEATURED_PROPERTIES_DESCRIPTION") }}</label>
          </b-col>
        </b-row>
        <b-row class="justify-content-md-center pt-4 pb-1">
          <b-col cols="12" xl="4" lg="4" md="4" class="pb-4" v-for="property in properties" :key="property.id">
            <property-light-card-for-landing :property="property"
                                       :bottom-button-text='$t("INVEST_NOW")' @on-clicked="scrollToRegistrationForm()"/>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="pb-4 pt-4 pb-lg-5 pt-lg-5" style="background-color: var(--primary-background-color)">
      <b-container>
        <b-row align-h="center">
          <b-col class="text-center" cols="10" lg="8" xl="8">
            <h1 class="font-38 mt-0">{{ $t("LANDING.WHY") }}</h1>
            <label class="col-10 col-lg-9 col-xl-8">{{ $t("LANDING.WHY_DESCRIPTION") }}</label>
          </b-col>
        </b-row>
        <b-row class="pt-3">
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="../assets/img/ic_appreciation.svg" alt=""/>
              <h5>{{ $t("LANDING.VALUE_APPRECIATION") }}</h5>
              <p class="font-16">{{ $t("LANDING.VALUE_APPRECIATION_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="../assets/img/ic_inflation.svg" alt=""/>
              <h5>{{ $t("LANDING.HEDGE_FOR_INFLATION") }}</h5>
              <p class="font-16">{{ $t("LANDING.HEDGE_FOR_INFLATION_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="../assets/img/ic_income.svg" alt=""/>
              <h5>{{ $t("LANDING.PASSIVE_INCOME") }}</h5>
              <p class="font-16">{{ $t("LANDING.PASSIVE_INCOME_DESCRIPTION") }}</p>
            </div>
          </b-col>
          <b-col cols="12" xl="3" lg="3">
            <div class="w-card-3">
              <img src="../assets/img/ic_wealth.svg" alt=""/>
              <h5>{{ $t("LANDING.STOREHOLD_OF_WEALTH") }}</h5>
              <p class="font-16">{{ $t("LANDING.STOREHOLD_OF_WEALTH_DESCRIPTION") }}</p>
            </div>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="text-center pb-3 pt-3" style="background-color: var(--primary-background-darker-color)">
      <b-container class="benefit-container">
        <b-row class="align-items-center" align-h="around">
          <b-col cols="12" xl="6" lg="6">
            <h1 class="font-38 text-center mt-3">{{ $t("LANDING.OUR_INVESTOR_BENEFIT") }}</h1>
            <p class="font-18 text-center">{{ $t("LANDING.HIGH_RETURNS_AND_LOW_VOLATILITY") }}</p>
            <img v-if="this.$i18n.locale === 'en'" class="mt-4 mb-3" style="width: 110%; margin-left: -20px"
                 src="../assets/img/landing_benefit_1.png" alt="Landing Benefit 1">
            <img v-if="this.$i18n.locale !== 'en'" class="mt-4 mb-3" style="width: 110%; margin-left: -20px"
                 src="../assets/img/landing_benefit_1_id.png" alt="Landing Benefit 1">
          </b-col>
          <b-col cols="12" xl="6" lg="6">
            <img v-if="this.$i18n.locale === 'en'" class="mt-2 mb-3" style="width: 105%;"
                 src="../assets/img/landing_benefit_2.png" alt="Landing Benefit 2">
            <img v-if="this.$i18n.locale !== 'en'" class="mt-4 mb-3" style="width: 110%; margin-left: -20px"
                 src="../assets/img/landing_benefit_2_id.png" alt="Landing Benefit 2">
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="text-center pb-1 pt-3" style="background-color: var(--primary-background-color)">
      <b-container>
        <b-row class="align-items-center" align-h="around">
          <b-col cols="12">
            <h1 class="font-38 text-center mb-0">{{ $t("LANDING.TESTIMONIALS") }}</h1>
            <b-row v-if="innerWidth >= 992">
              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-4-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%"
                           :src="testimonial_1_photo" alt="Testimonial 1">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_1_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">{{ $t("LANDING.TESTIMONIAL_1_TITLE") }}</p>
                    </b-col>
                  </b-row>
                  <b-row>
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_1_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-4-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%"
                           :src="testimonial_2_photo" alt="Testimonial 2">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_2_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">
                        {{ $t("LANDING.TESTIMONIAL_2_TITLE") }}
                      </p>
                    </b-col>
                  </b-row>
                  <b-row>
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_2_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>

              <b-col cols="12" xl="4" lg="4" class="mt-5">
                <div class="w-card-4-a text-left">
                  <b-row>
                    <b-col>
                      <img style="width: 105%; margin-left: -18px; margin-top: -30%"
                           :src="testimonial_3_photo" alt="Testimonial 3">
                    </b-col>
                    <b-col>
                      <h5 class="font-medium" style="margin-left: -35px">{{ $t("LANDING.TESTIMONIAL_3_NAME") }}</h5>
                      <p style="margin-left: -35px; margin-top: -5px; font-size: 14px">
                        {{ $t("LANDING.TESTIMONIAL_3_TITLE") }}
                      </p>
                    </b-col>
                  </b-row>
                  <b-row>
                    <p class="ml-3 mr-3 mt-1">{{ $t("LANDING.TESTIMONIAL_3_COMMENT") }}</p>
                  </b-row>
                </div>
              </b-col>
            </b-row>
            <b-row v-if="innerWidth < 992">
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-4-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;"
                         :src="testimonial_1_photo" alt="Testimonial 1">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_1_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_1_TITLE") }}</p>
                      <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_1_COMMENT") }}</p>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-4-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;"
                         :src="testimonial_2_photo" alt="Testimonial 2">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_2_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_2_TITLE") }}</p>
                      <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_2_COMMENT") }}</p>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
              <b-col cols="12" xl="4" lg="4" class="mt-4">
                <div class="w-card-4-b text-left">
                  <b-row class="align-items-center">
                    <img style="width: 25%; height: 25%; margin-left: -8%;"
                         :src="testimonial_3_photo" alt="Testimonial 3">
                    <b-col>
                      <h5 class="font-medium">{{ $t("LANDING.TESTIMONIAL_3_NAME") }}</h5>
                      <p style="margin-top: -5px">{{ $t("LANDING.TESTIMONIAL_3_TITLE") }}</p>
                      <p class="mt-3 mb-2">{{ $t("LANDING.TESTIMONIAL_3_COMMENT") }}</p>
                    </b-col>
                  </b-row>
                </div>
              </b-col>
            </b-row>
          </b-col>
        </b-row>
      </b-container>

      <b-container class="text-center mt-4" ref="registrationForm">
        <b-row align-h="center">
          <Form v-if="!isInputOtp && !isReverseOtp" class="form text-left col-lg-6" ref="observer"
                v-slot="{ handleSubmit }">
            <b-form @submit.prevent="handleSubmit(requestOtp)">
              <h3 class="text-center mt-0 mb-0">{{ $t("LANDING.JOIN_THE_FUTURE_TODAY") }}</h3>
              <p class="font-18 text-center mb-3">{{ $t("LANDING.ONLY_TAKE_5_MINUTES") }}</p>
              <Field
                :name="$t('AUTH.FULL_NAME')"
                :rules="{ required: true, min: 2, legal_name: [{ ...form }]}"
                v-slot="validationContext" :model-value="form.name" @update:modelValue="form.name = $event"
              >
                <b-form-group class="font-14" :label="$t('AUTH.FULL_NAME')">
                  <b-form-input
                    v-bind="validationContext.field"
                    placeholder="John Doe"
                    :state="getValidationState(validationContext.meta)"
                    aria-describedby="input-name-feedback"
                  ></b-form-input>
                  <b-form-invalid-feedback id="input-name-feedback">
                    {{ validationContext.errors[0] }}
                  </b-form-invalid-feedback>
                </b-form-group>
              </Field>

              <Field
                :name="$t('AUTH.EMAIL')"
                :rules="{ required: true, email: true }"
                v-slot="validationContext"
                :model-value="form.email"  @update:modelValue="form.email = $event"
              >
                <b-form-group class="font-14" :label="$t('AUTH.EMAIL')">
                  <b-form-input v-bind="validationContext.field"
                                placeholder="<EMAIL>"
                                type="email"
                                :state="getValidationState(validationContext.meta)"
                                aria-describedby="input-email-feedback"
                  ></b-form-input>
                  <b-form-invalid-feedback id="input-email-feedback">
                    {{ validationContext.errors[0] }}
                  </b-form-invalid-feedback>
                </b-form-group>
              </Field>

              <Field
                :name="$t('AUTH.PHONE')"
                :rules="{}"
                v-slot="{ valid, errors, field }"
              >
                <legend>{{ $t("AUTH.PHONE") }}</legend>
                <vue-tel-input v-bind="field" v-model="vuePhone.phone" @on-input="updatePhoneNumber"
                  :validCharactersOnly="false" :dropdownOptions="{ showDialCodeInSelection: true, showFlags: true }"
                  :inputOptions="{ autocomplete: false, name: 'phone', placeholder: 'Enter your phone number'}"
                  defaultCountry="ID" :preferredCountries="['ID']"></vue-tel-input>
                <label class="invalid" v-if="!vuePhone.isValid">
                  {{ errors[0] ? errors[0] : "Invalid phone number" }}</label>
              </Field>

              <b-form-group v-show="false" class="font-14" :label="$t('REFERRAL.REFERRAL_CODE')">
                <b-form-input
                  name="referral_code"
                  placeholder="123456ABC"
                  :disabled="disabledReferralInput"
                  v-model="form.referral_code"
                ></b-form-input>
              </b-form-group>

              <Field
                :name="$t('AUTH.I_ACCEPT_TERMS_AND_PRIVACY')"
                :rules="{required : { allowFalse: false }}"
                v-slot="{ valid, errors, field, handleChange }"
                :model-value="form.accept_status"  @update:modelValue="form.accept_status = $event"
              >
                <b-form-checkbox @change="handleChange" class="pt-3" name="accept_status"
                                 style="font-weight: 400; font-size: 16px; z-index: 0" :state="errors[0] ? false : (valid ? true : null)">
                  {{ $t("AUTH.I_ACCEPT_TERMS_AND_PRIVACY") }}
                </b-form-checkbox>
              </Field>

              <b-col class="text-center pt-4" align-h="center">
                <p class="font-bold mb-2" style="color: dodgerblue; font-size: 12px" v-if="isOtpTypeWaba">
                  {{ $t("AUTH.OTP_NOTICE") }}
                </p>
                <b-button id="btn_Landing_Register" class="btn-main pl-4 pr-4" type="submit" variant="none">
                  {{ $t("AUTH.REGISTER") }}
                </b-button>
              </b-col>
            </b-form>
          </Form>

          <b-form v-if="isInputOtp" class="form text-center" style="width: 510px" @submit.prevent="register">
            <p class="font-bold font-32 pb-1" style="color: var(--primary-color)">
              {{ $t("AUTH.VERIFICATION") }}
            </p>
            <p class="font-bold pl-5 pr-5" style="color: #989898; font-size: 14px">
              {{ $t("AUTH.ENTER_THE_OTP") }}
            </p>
            <p class="font-bold font-26 color-gray-6d pt-3" style="margin-bottom: -6px">
              {{ form.phone }}
            </p>
            <label class="hyperlink font-bold font-14 pb-1" @click="changePhoneNumber">
              <u>{{ $t("AUTH.CHANGE_PHONE_NUMBER") }}</u>
            </label>
            <label class="invalid font-bold pb-2"
                   :style="{ visibility: !isInputOtpValid && observeInputOtp ? 'visible' : 'hidden' }">
              {{ $t("AUTH.OTP_REQUIRED") }}
            </label>
            <div style="display: flex; justify-content: center; align-items: center;">
              <v-otp-input
                ref="otpInput" v-model:value="form.otp_code" input-classes="otp-input" separator=" " :num-inputs="6"
                :should-auto-focus="true" input-type="numeric" @on-change="onInputOtpChange"/>
            </div>
            <b-col class="text-center pt-3" align-h="center">
              <b-row class="align-items-center" align-h="center" v-if="isOtpTypeWaba && inputOtpStatus">
                <p class="font-bold font-14 color-gray-6d">{{ $t("AUTH.OTP_DELIVERY_STATUS") }}</p>
                <p class="font-bold font-14 text-white ml-2" :class="otpStatusClass">{{ otpStatusText }}</p>
              </b-row>
              <b-row class="otp-notice-background">
                <p class="font-bold font-12" v-if="isOtpTypeWaba">
                  {{ $t("AUTH.OTP_NOTICE") }}
                </p>
              </b-row>
              <b-button id="btn_Landing_AuthVerify" class="btn-main mt-1 pt-2 pb-2" type="submit" variant="none" style="width: 350px">
                {{ $t("AUTH.VERIFY") }}
              </b-button>
            </b-col>
            <b-row class="flex text-center pt-2 pb-2" align-h="center">
              <p class="font-bold color-gray-6d font-14">{{ $t("AUTH.OTP_DID_NOT_RECEIVE") }}</p>
              <p class="font-bold font-14 ml-1" :class="otpResendClass" @click="requestOtp">
                {{ resendOtpText }}
              </p>
            </b-row>
          </b-form>

          <b-col v-if="isReverseOtpStep1" class="text-center mb-4" cols="auto">
            <img width="300" class="mt-2 mb-4"
                 src="@/assets/img/registration/otp_reverse_send.svg" alt="" @click="onRequestInputOtpClicked"/>
            <b-form class="form reverse-otp-form text-center" @submit.prevent="openWhatsappToSendReverseOtp">
              <p class="font-bold" style="font-size:26px; color: var(--text-color-darker)">
                {{ $t("AUTH.VERIFY_ACCOUNT_VIA_WHATSAPP") }}
              </p>
              <p class="font-normal mt-2 mb-3" style="color: var(--text-color-darker)">
                {{ $t("AUTH.VERIFY_ACCOUNT_VIA_WHATSAPP_DESCRIPTION") }}
              </p>
              <div class="reverse-otp-warning d-flex align-items-center px-2 py-2 mt-3 mb-3">
                <img src="@/assets/img/registration/warning.svg" alt="Warning" class="ml-1 mr-2"/>
                <p class="font-bold text-center flex-grow-1" style="color: var(--text-color-warning); font-size: 14px">
                  {{ $t("AUTH.VERIFY_ACCOUNT_VIA_WHATSAPP_WARNING") }}
                </p>
              </div>
              <b-button class="btn-main mt-1 pt-2 pb-2" type="submit" variant="none" style="font-size: 18px; width: 100%">
                {{ $t("AUTH.SEND_VERIFICATION_VIA_WHATSAPP") }}
              </b-button>
            </b-form>
          </b-col>

          <b-col v-if="isReverseOtpStep2" class="text-center mb-4" cols="auto">
            <img width="300" class="mt-2 mb-4"
                 src="@/assets/img/registration/otp_reverse_verifying.svg" alt="" @click="onRequestInputOtpClicked"/>
            <b-form class="form reverse-otp-form text-center" @submit.prevent="checkReverseOtpStatus(true)">
              <p class="font-bold" style="font-size:26px; color: var(--text-color-darker)">
                {{ $t("AUTH.VERIFYING_YOUR_CODE") }}
              </p>
              <p class="font-normal mt-2 mb-3" style="color: var(--text-color-darker); white-space: pre-wrap;">
                {{ $t("AUTH.VERIFYING_YOUR_CODE_DESCRIPTION") }}
              </p>
              <div class="reverse-otp-warning d-flex align-items-center px-2 py-2 mt-3 mb-3">
                <img src="@/assets/img/registration/warning.svg" alt="Warning" class="ml-1 mr-2"/>
                <p class="font-bold text-center flex-grow-1" style="color: var(--text-color-warning); font-size: 14px">
                  {{ $t("AUTH.VERIFY_ACCOUNT_VIA_WHATSAPP_WARNING") }}
                </p>
              </div>
              <!-- Recaptcha V2 checkbox fallback -->
              <div v-if="showRecaptchaV2.PHONE_VERIFICATION_STATUS" :ref="recaptchaV2Checkbox.PHONE_VERIFICATION_STATUS" class="d-flex justify-content-center mt-1"></div>
              <b-button class="btn-main mt-1 pt-2 pb-2" type="submit" variant="none" style="font-size: 18px; width: 100%">
                {{ $t("AUTH.CHECK_VERIFICATION_STATUS") }}
              </b-button>
              <p class="font-medium mt-3" style="color: var(--text-color-darker);">
                {{ $t("AUTH.VERIFYING_YOUR_CODE_WARNING") }}
              </p>
              <b-row class="flex text-center" align-h="center">
                <p class="font-normal" style="font-size: 15px; color: var(--text-color-darker);">
                  {{ $t("AUTH.OTP_DID_NOT_RECEIVE") }}</p>
                <p class="font-normal ml-1" :class="otpResendClass" @click="requestOtp">
                  {{ resendOtpText }}
                </p>
              </b-row>
            </b-form>
            <b-row v-if="isReverseOtpStep2" align-h="center">
              <p class="reverse-otp-warning text-center font-normal px-3 py-2 mt-4"
                 style="color: var(--text-color-darker); font-size: 16px" v-html="needHelpText"/>
            </b-row>
          </b-col>

          <!-- Recaptcha V2 checkbox fallback -->
          <div v-if="showRecaptchaV2.REQUEST_OTP" :ref="recaptchaV2Checkbox.REQUEST_OTP" class="d-flex justify-content-center mt-2"></div>
          <div v-if="showRecaptchaV2.REQUEST_INPUT_OTP_METHOD" :ref="recaptchaV2Checkbox.REQUEST_INPUT_OTP_METHOD" class="d-flex justify-content-center mt-2"></div>
          <div v-if="showRecaptchaV2.REGISTER" :ref="recaptchaV2Checkbox.REGISTER" class="d-flex justify-content-center mt-2"></div>
        </b-row>
      </b-container>
    </div>
  </div>
</template>

<script>
import { useRecaptcha } from "@/composables/useRecaptcha";
import { getDeviceUUID, urlImage } from "@/helpers/common"
import propertiesService from "../services/properties.service"
import PropertyLightCardForLanding from "../components/Cards/PropertyLightCardForLanding"
import RippleButton from "../components/RippleButton.vue"
import authService from "@/services/auth.service"
import { configure, defineRule, Field, Form } from "vee-validate"
import { email, min, required } from "@vee-validate/rules"
import commonService from "@/services/common.service"
import { localize, setLocale } from "@vee-validate/i18n"
import VOtpInput from "vue3-otp-input"
import store from "@/store/store"
import { OTP_METHOD, OTP_STATUS, OTP_TYPE, STORAGE_KEYS } from "@/constants/constants"
import { FOREIGNER, REGEX } from "@/constants/constants"
import { getCookie, getErrorMessage, notify } from "@/helpers/common"
import messErrors from "../constants/errors"
import { gtmTrackEvent } from "@/helpers/gtm"
import { GTM_EVENT_NAMES } from "@/constants/gtm"
import {FIREBASE_EVENTS, firebaseLogEvent} from "@/helpers/firebase";
import { ERROR_CODE } from "@/constants/constants";
import externalSites from "@/constants/externalSites";

let DEFAULT_RESEND_OTP_TIME_IN_SECONDS = 120
defineRule("email", email)
defineRule("min", min)
defineRule("required", required)

defineRule("legal_name", (value, [otherValues]) => {
  // Check if the password contains a combination of uppercase, lowercase, numbers, and special characters
  if (!REGEX.LEGAL_NAME.test(value)) {
    return false
  }

  return true
})

configure({
  generateMessage: localize({
    en: {
      messages: {
        required: "The {field} is required",
        legal_name: "Legal name only accepts alphabets, single quotes, commas, full stops, and single spaces.",
      },
    },
    id: {
      messages: {
        required: "Kolom {field} wajib diisi",
        legal_name: "Nama resmi hanya menerima alfabet, tanda kutip tunggal, koma, titik, dan satu spasi.",
      },
    },
  }),
})

export default {
  components: {
    Field,
    defineRule,
    PropertyLightCardForLanding,
    RippleButton,
    Form,
    VOtpInput
  },
  setup() {
    const {
      recaptchaV3Exec,
      recaptchaV2Checkbox,
      recaptchaTokenV2,
      showRecaptchaV2,
      validateRecaptchaV2,
      resetRecaptchaV2
    } = useRecaptcha({ REQUEST_OTP: 'requestOtp', REQUEST_INPUT_OTP_METHOD: 'requestInputOtpMethod', PHONE_VERIFICATION_STATUS: 'phoneVerificationStatus', REGISTER: 'register' })
    return { recaptchaV3Exec, recaptchaV2Checkbox, recaptchaTokenV2, showRecaptchaV2, validateRecaptchaV2, resetRecaptchaV2 }
  },
  data () {
    return {
      title: "GORO | Property for All",
      innerWidth: 0,
      properties: [],
      form: {
        "email": this.$route.params.email,
        "name": this.$route.params.name,
        "platform": "web",
        "uuid": getDeviceUUID(),
        "cookie_fbp": getCookie("_fbp"),
      },
      referUser: "",
      vuePhone: {
        phone: "",
        isValid: true,
        props: {
          error: true,
          clearable: true,
          //fetchCountry: true, //Do not use it with defaultCountryCode
          defaultCountryCode: "ID",
          preferredCountries: ["ID"],
          translations: {
            countrySelectorLabel: this.$t("AUTH.COUNTRY_CODE"),
            phoneNumberLabel: this.$t("AUTH.PHONE_HINT"),
            example: this.$t("common.example"),
          },
          color: "#80BDFF",
          validColor: "#006666",
          errorColor: "#DC3445",
        }
      },
      oldPhone: null,
      resendOtpTimer: 0,
      isReverseOtpStep1: false,
      isReverseOtpStep2: false,
      isReversedOtpVerified: false,
      reverseOtpMessageTo: "",
      reverseOtpMessageContent: "",
      requestInputOtpClickCount: 0,
      isInputOtp: false,
      isInputOtpValid: false,
      observeInputOtp: false,
      inputOtpStatus: OTP_STATUS.QUEUED,
      getInputOtpStatusInterval: null,
    }
  },
  async created () {
    await this.getReferralInviter()
  },
  async mounted () {
    window.fbq("track", "Landing:PageView")
    this.$i18n.locale = "id"
    setLocale('id');
    this.handleWindowResize();
    window.addEventListener('resize', this.handleWindowResize);
    await this.getProperties()
  },
  async beforeUpdate () {
    setLocale('id');
    this.$i18n.locale = "id"
  },
  async beforeDestroy () {
    window.removeEventListener('resize', this.handleWindowResize);
    this.stopGetInputOtpStatus()
    this.stopCheckReverseOtpStatus()
    if (this.userProfile) {
      const preferredLocale = localStorage.getItem(STORAGE_KEYS.PREFERRED_LOCALE.key)
      if (preferredLocale) {
        this.$i18n.locale = preferredLocale
      }
    }
  },
  methods: {
    handleWindowResize () {
      this.innerWidth = window.innerWidth
    },
    getAvatar (images) {
      if (images && images.length) {
        return urlImage(images[0])
      }
      return ""
    },
    async getProperties () {
      const result = await propertiesService.getFeatures()
      if (result && result.data) {
        this.properties = result.data
      }
    },
    scrollToRegistrationForm () {
      window.fbq("trackCustom", "Landing:JoinNowClicked")
      if (this.$refs.registrationForm) {
        this.$refs.registrationForm.scrollIntoView({ behavior: "smooth", block: "start" })
      }
    },
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    updatePhoneNumber(number, data) {
      if(number) {
        this.vuePhone.isValid = data.valid
      }
      this.form.phone = data.number
      this.form.country_code = data.countryCallingCode
      this.form.iso_country_code = data.countryCode
    },
    async changePhoneNumber () {
      this.isInputOtp = false
      this.isReverseOtpStep1 = false
      this.isReverseOtpStep2 = false

      this.resendOtpTimer = 0
      this.observeInputOtp = false
      this.inputOtpStatus = OTP_STATUS.QUEUED
      this.stopGetInputOtpStatus()
      this.stopCheckReverseOtpStatus()
    },
    async requestOtp() {
      let isResend = this.oldPhone != null;
      if (this.oldPhone && this.resendOtpTimer > 0) {
        return
      }
      if (this.vuePhone.isValid) {
        if (!this.validateRecaptchaV2.REQUEST_OTP()) return
        try {
          window.fbq("trackCustom", "Landing:RequestOtp", { is_resend: isResend })
          gtmTrackEvent({
            event: GTM_EVENT_NAMES.USER_REQUEST_OTP,
            is_resend: isResend,
          })
          const recaptchaTokenV3 = await this.recaptchaV3Exec.REQUEST_OTP()
          const recaptchaTokenV2 = this.recaptchaTokenV2.REQUEST_OTP
          let payload = {
            recaptcha_token: recaptchaTokenV3,
            recaptcha_token_v2: recaptchaTokenV2,
            preferred_locale: this.$i18n.locale === FOREIGNER.LOCALE ? "en" : this.$i18n.locale,
            source: "landing",
            ...this.form
          }
          if (isResend) {
            payload.is_resend = true;
          }
          let data = await authService.requestOtp(payload, false, true)
          this.handleOtpResponse(data)
        } catch (ex) {
          if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.REQUEST_OTP = true
          } else {
            const errorMessage = getErrorMessage(ex) || messErrors.INTERNAL;
            notify({ text: errorMessage, type: "error" })
            firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
              email: this.form.email,
              phone: this.form.phone,
              method: "RequestOtp",
              error: errorMessage
            });
          }
        } finally {
          this.resetRecaptchaV2.REQUEST_OTP()
        }
      }
    },
    displayInputOtp () {
      this.isInputOtp = true
      this.isReverseOtpStep1 = false
      this.isReverseOtpStep2 = false
      this.startGetInputOtpStatus()
      if (this.$refs.registrationForm) {
        this.$refs.registrationForm.scrollIntoView({ behavior: "smooth", block: "end" })
      }
    },
    onInputOtpChange (value) {
      this.isInputOtpValid = value.toString().trim().length === 6
    },
    async getInputOtpStatus () {
      if (this.inputOtpStatus !== OTP_STATUS.DELIVERED && this.inputOtpStatus !== OTP_STATUS.ERROR) {
        let data = await authService.getOtpStatus({
          email: this.form.email,
          phone: this.form.phone,
        })
        if (data && data.status) {
          this.inputOtpStatus = data.status
        }
      } else {
        this.stopGetInputOtpStatus()
      }
    },
    startGetInputOtpStatus () {
      this.stopGetInputOtpStatus()
      this.getInputOtpStatusInterval = setInterval(this.getInputOtpStatus, 6000)
    },
    stopGetInputOtpStatus () {
      if (this.getInputOtpStatusInterval) {
        clearInterval(this.getInputOtpStatusInterval)
      }
    },
    onRequestInputOtpClicked () {
      this.requestInputOtpClickCount++;
      if (this.requestInputOtpClickCount === 12) {
        this.requestInputOtpMethod();
        this.requestInputOtpClickCount = 0;
      }
    },
    async requestInputOtpMethod() {
      if (!this.validateRecaptchaV2.REQUEST_INPUT_OTP_METHOD()) return
      try {
        const recaptchaTokenV3 = await this.recaptchaV3Exec.REQUEST_INPUT_OTP_METHOD()
        const recaptchaTokenV2 = this.recaptchaTokenV2.REQUEST_INPUT_OTP_METHOD
        let data = await authService.requestInputOtpMethod({
          phone: this.form.phone,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }, false, true)
        this.handleOtpResponse(data)
      } catch (ex) {
        if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
          this.showRecaptchaV2.REQUEST_INPUT_OTP_METHOD = true
        } else {
          notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
        }
      } finally {
        this.resetRecaptchaV2.REQUEST_INPUT_OTP_METHOD()
      }
    },
    handleOtpResponse (data) {
      if (data && data.otp_method) {
        if (data.otp_method === OTP_METHOD.REVERSE_OTP) {
          this.reverseOtpMessageTo = data.message_to
          this.reverseOtpMessageContent = data.message_content
          this.displayReverseOtpStep1()
        } else if (data.otp_method === OTP_METHOD.INPUT_OTP) {
          this.displayInputOtp()
        }
        this.oldPhone = this.form.phone;
        this.resendOtpTimer = store.state.configs?.otp_retry_time != null
          ? store.state.configs.otp_retry_time * 60 : DEFAULT_RESEND_OTP_TIME_IN_SECONDS
      }
    },
    displayReverseOtpStep1 () {
      this.isInputOtp = false
      this.isReverseOtpStep1 = true
      this.isReverseOtpStep2 = false
      if (this.$refs.registrationForm) {
        this.$refs.registrationForm.scrollIntoView({ behavior: "smooth", block: "end" })
      }
    },
    openWhatsappToSendReverseOtp () {
      if (this.reverseOtpMessageTo && this.reverseOtpMessageContent) {
        const phone = this.reverseOtpMessageTo;
        const message = encodeURIComponent(this.reverseOtpMessageContent);
        const url = `https://wa.me/${phone}?text=${message}`;
        window.open(url, '_blank');
        this.displayReverseOtpStep2()
      } else {
        notify({
          text: "Missing reverse OTP info. Please try again <NAME_EMAIL> for assistance.",
          type: "error"
        })
      }
    },
    displayReverseOtpStep2 () {
      this.isInputOtp = false
      this.isReverseOtpStep1 = false
      this.isReverseOtpStep2 = true
      this.startCheckReverseOtpStatus()
      if (this.$refs.registrationForm) {
        this.$refs.registrationForm.scrollIntoView({ behavior: "smooth", block: "end" })
      }
    },
    async checkReverseOtpStatus(isUserRequest = false) {
      if (!this.validateRecaptchaV2.PHONE_VERIFICATION_STATUS()) return
      try {
        const recaptchaTokenV3 = await this.recaptchaV3Exec.PHONE_VERIFICATION_STATUS()
        const recaptchaTokenV2 = this.recaptchaTokenV2.PHONE_VERIFICATION_STATUS
        let data = await authService.getReverseOtpStatus({
          phone: this.form.phone,
          recaptcha_token: recaptchaTokenV3,
          recaptcha_token_v2: recaptchaTokenV2,
        }, false, true)
        if (data) {
          if (data.is_verified) {
            this.isReversedOtpVerified = true
            this.stopCheckReverseOtpStatus()
            await this.register()
          } else {
            if (isUserRequest) {
              //Notify to user
            }
          }
        }
      } catch (ex) {
        if (isUserRequest) {
          if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.PHONE_VERIFICATION_STATUS = true
          } else {
            notify({ text: getErrorMessage(ex) || messErrors.INTERNAL, type: "error" })
          }
        }
      } finally {
        this.resetRecaptchaV2.PHONE_VERIFICATION_STATUS()
      }
    },
    startCheckReverseOtpStatus () {
      this.stopCheckReverseOtpStatus()
      this.checkReverseOtpStatusInterval = setInterval(this.checkReverseOtpStatus, 6000)
    },
    stopCheckReverseOtpStatus () {
      if (this.checkReverseOtpStatusInterval) {
        clearInterval(this.checkReverseOtpStatusInterval)
      }
    },
    async register() {
      this.observeInputOtp = true
      if ((this.isReverseOtp && this.isReversedOtpVerified)
        || (this.isInputOtp && this.vuePhone.isValid && this.isInputOtpValid)) {
        if (!this.validateRecaptchaV2.REGISTER()) return
        try {
          let method = "Manual"
          window.fbq("trackCustom", "Landing:Register", { method: method })
          const recaptchaTokenV3 = await this.recaptchaV3Exec.REGISTER()
          const recaptchaTokenV2 = this.recaptchaTokenV2.REGISTER
          const formData = {
            ...this.form,
            preferred_locale: this.$i18n.locale === FOREIGNER.LOCALE ? "en" : this.$i18n.locale,
            uuid: getDeviceUUID(),
            cookie_fbp: getCookie("_fbp"),
            source: "landing",
            recaptcha_token: recaptchaTokenV3,
            recaptcha_token_v2: recaptchaTokenV2,
          }
          const data = await authService.register(formData, false, true)
          if (data && data.user && data.access_token) {
            await this.onRegisterSuccess(data.user, data.access_token)
          }
        } catch (ex) {
          if (ex.extraData && ex.extraData.error_code === ERROR_CODE.RECAPTCHA_ERROR_SCORE_TOO_LOW) {
            this.showRecaptchaV2.REGISTER = true
          } else {
            const errorMessage = getErrorMessage(ex) || messErrors.INTERNAL;
            notify({ text: errorMessage, type: "error" })
            firebaseLogEvent(FIREBASE_EVENTS.REGISTRATION_FAILED, {
              email: this.form.email,
              phone: this.form.phone,
              method: method,
              error: errorMessage
            });
          }
        } finally {
          this.resetRecaptchaV2.REGISTER()
        }
      }
    },
    async onRegisterSuccess (user, accessToken) {
      if (user && accessToken) {
        this.stopGetInputOtpStatus()
        this.stopCheckReverseOtpStatus()
        window.fbq("trackCustom", "Landing:CompleteRegistration")
        localStorage.setItem(STORAGE_KEYS.AUTHORIZATION.key, accessToken)
        await store.dispatch("setUserProfile", user)
        await this.$router.push(this.$route.query.redirect || { name: "assetsOverview" })
        gtmTrackEvent({
          event: GTM_EVENT_NAMES.USER_REGISTER_SUCCESS,
        })

        this.vuePhone.phone = ""
        this.form = { "platform": "web" }
        if (this.referUser != null && this.referUser !== "" && this.referralCode) {
          this.form.referral_code = this.referralCode
        }
      }
    },
    async getReferralInviter () {
      if (this.referralCode != null) {
        const response = await commonService.getReferralInviter(this.referralCode)
        if (response.name) {
          this.referUser = response.name
          this.form.referral_code = this.referralCode
          window.fbq("trackCustom", "Landing:GetReferralInviter", { success: true })
          gtmTrackEvent({
            event: GTM_EVENT_NAMES.USER_GET_REFERRAL_INVITER,
            success: true,
          })
        } else {
          window.fbq("trackCustom", "Landing:GetReferralInviter", { success: false })
          gtmTrackEvent({
            event: GTM_EVENT_NAMES.USER_GET_REFERRAL_INVITER,
            success: false,
          })
        }
      }
    },
  },
  computed: {
    userProfile () {
      return this.$store.getters.userProfile
    },
    testimonial_1_photo () {
      if (this.$i18n.locale !== "en") {
        return require("@/assets/img/landing_testimonial_danisworo.png")
      } else {
        return require("@/assets/img/landing_testimonial_florian_k.png")
      }
    },
    testimonial_2_photo () {
      if (this.$i18n.locale !== "en") {
        return require("@/assets/img/landing_testimonial_sofyan.png")
      } else {
        return require("@/assets/img/landing_testimonial_karan_s.png")
      }
    },
    testimonial_3_photo () {
      if (this.$i18n.locale !== "en") {
        return require("@/assets/img/landing_testimonial_aldi.png")
      } else {
        return require("@/assets/img/landing_testimonial_alvin_d.png")
      }
    },
    isReverseOtp () {
      return this.isReverseOtpStep1 || this.isReverseOtpStep2
    },
    isOtpTypeWaba () {
      if (store.state.configs && store.state.configs.otp_type) {
        return store.state.configs.otp_type === OTP_TYPE.RADIST || store.state.configs.otp_type === OTP_TYPE.TWILIO
      }
      return false
    },
    referralCode () {
      const codeParam = this.$route.query.code
      const savedReferralCode = localStorage.getItem(STORAGE_KEYS.REFERRAL_CODE.key)
      if (codeParam && codeParam !== savedReferralCode) {
        localStorage.setItem(STORAGE_KEYS.REFERRAL_CODE.key, codeParam)
      }
      return codeParam || savedReferralCode
    },
    disabledReferralInput () {
      return this.referUser != null && this.referUser !== ""
    },
    resendOtpText () {
      if (this.resendOtpTimer > 0) {
        const minutes = Math.floor(this.resendOtpTimer / 60).toString().padStart(2, '0');
        const seconds = (this.resendOtpTimer % 60).toString().padStart(2, '0');
        return this.$t("AUTH.OTP_RESEND_AFTER") + " " + `${minutes}:${seconds}`;
      }
      return this.$t("AUTH.OTP_RESEND")
    },
    needHelpText () {
      let whatsapp = externalSites.WHATSAPP_SUPPORTS['id']
      if (this.userPaymentMethodStripe) {
        whatsapp = externalSites.WHATSAPP_SUPPORTS['en-UK']
      }
      return `${this.$t('AUTH.NEED_HELP_HERE', { whatsapp })}`
    },
    otpStatusText () {
      if (this.inputOtpStatus === OTP_STATUS.DELIVERED) {
        return "Sent"
      }
      return this.inputOtpStatus
    },
    otpStatusClass() {
      return {
        'otp-status-queued': this.inputOtpStatus === OTP_STATUS.QUEUED,
        'otp-status-sent': this.inputOtpStatus === OTP_STATUS.DELIVERED,
        "otp-status-error": this.inputOtpStatus === OTP_STATUS.ERROR,
      };
    },
    otpResendClass () {
      return {
        "hyperlink": this.resendOtpTimer === 0,
        "hyperlink-disabled": this.resendOtpTimer > 0,
      }
    },
  },
  watch: {
    resendOtpTimer (value) {
      if (value > 0) {
        setTimeout(() => {this.resendOtpTimer--}, 1000)
      }
    }
  },
  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
}
</script>
<style lang="scss">
.landing {
  padding-top: 0 !important;

  h1 {
    font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
    font-weight: 500;
    color: var(--primary-color);
  }

  p {
    color: var(--primary-color);
  }

  .main__content {
    padding-top: 10px;
    padding-bottom: 40px;
    background-size: cover;
    background-image: url('../assets/img/landing_banner_min_w_1100.png');

    @media only screen and (max-width: 1100px) {
      background-image: url('../assets/img/landing_banner_max_w_1100.png');
    }

    @media only screen and (max-width: 800px) {
      background-image: url('../assets/img/landing_banner_max_w_800.png');
    }

    @media only screen and (max-width: 650px) {
      background-image: url('../assets/img/landing_banner_max_w_650.png');
    }
  }

  .w-card {
    padding: 15px 25px 25px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-color);
    border-radius: 16px;

    img {
      width: 24px;
      height: 24px;
      margin-bottom: 5px;
    }

    label {
      padding-left: 0.5rem;
      font-size: 20px;
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-2 {
    padding: 15px 25px 30px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-color);
    border-radius: 16px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 18px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-3 {
    padding: 15px 25px 30px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-color);
    border-radius: 16px;

    img {
      width: 30px;
      height: 30px;
    }

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 18px;
      color: var(--primary-background-color);
    }

    p {
      font-size: 16px;
      color: var(--primary-background-color);
    }
  }

  .w-card-4-a {
    padding: 15px 25px 50px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-darker-color);
    border-radius: 32px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 22px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .w-card-4-b {
    padding: 10px 25px 15px;
    margin-left: 15px;
    margin-right: 15px;
    background-color: var(--primary-background-darker-color);
    border-radius: 32px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 22px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .join-goro {
    padding: 0.6rem 5% 1rem;
    margin-top: 5rem;
    background-color: var(--primary-color);
    //box-shadow: 0 4px 10px var(--primary-darker-color);
    border-radius: 16px;
  }

  .form {
    background-color: white;
    box-shadow: 0 8px 35px rgba(7, 55, 99, 0.16);
    border-radius: 16px;
    margin: 10px;
    padding: 24px !important;
  }

  .invalid {
    width: 100%;
    margin-top: .25rem;
    margin-bottom: 0;
    font-size: 80%;
    color: #dc3545
  }

  //b-form-group label
  legend {
    padding-top: 5px !important;
    font-style: normal;
    font-weight: bold;
    font-size: 15px;
    color: var(--primary-darker-color);
  }

  //b-form-input
  input {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    color: var(--primary-darker-color);

    &::placeholder {
      color: #ABB5BE !important;
      opacity: 1;
    }
  }

  //b-form-invalid-feedback
  .invalid-feedback {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
  }

  .show-hide {
    width: 40px;
    background-color: rgba(0, 102, 102, 0.9);;
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    transition: .5s;

    &:hover {
      background-color: rgba(0, 102, 102, 1);;
    }
  }

  .reverse-otp-form {
    width: 100%;
    max-width: 644px;
  }

  .reverse-otp-warning {
    background-color: #FFEFB9;
    //border: 1px solid #ffd680;
    border-radius: 8px;
  }

  .reverse-otp-warning a {
    color: #1E90FF;
    text-decoration: underline;
  }
}
</style>
