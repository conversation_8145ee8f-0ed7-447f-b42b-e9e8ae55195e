<template>
  <div v-if="property.category" class="property-detail marketplace">
    <b-container>
      <div class="image_primary mt-1">
        <carousel class="carousel" v-if="property && property.images" :wrap-around="true" :items-to-show="2" :autoplay="4000"
          :breakpoints="breakpoints" breakpoint-mode="carousel">
          <slide v-for="(image, index) in property.images" :key="image.id">
            <img class="image-viewer" width="100%" :src="getUrl(image)" alt="" @click="showImageViewer(index)"/>
          </slide>
          <template #addons>
            <navigation>
              <template #prev>
                <div class="icon">
                  <b-icon scale="0.6" icon="arrow-left" style="color: white"></b-icon>
                </div>
              </template>
              <template #next>
                <div class="icon">
                  <b-icon scale="0.6" icon="arrow-right" style="color: white"></b-icon>
                </div>
              </template>
            </navigation>
          </template>
        </carousel>
      </div>
      <vue-easy-lightbox
        :visible="imageViewerVisible"
        :imgs="propertyImages"
        :index="imageViewerIndex"
        :loop="true"
        @hide="imageViewerVisible = false">
      </vue-easy-lightbox>
      <b-row class="content">
        <div class="content-left">
          <div class="d-flex flex-row justify-content-between align-items-center">
            <div class="card-status">
              <p class="sold-out coming-soon" v-if="isComingSoon">{{ $t('PROPERTY_STATUS.COMING_SOON') }}</p>
              <p class="sold-out" v-else-if="isSoldOut">{{ $t('PROPERTY_STATUS.SOLD_OUT') }}</p>
              <p class="active" v-if="property.status === 'available' && !isSoldOut">{{ $t('PROPERTY_STATUS.AVAILABLE') }}
              </p>
              <p class="promo" v-if="property.status === 'promo' && !isSoldOut">{{ $t('PROPERTY_STATUS.PROMO') }}</p>
              <p class="timer timer-promo" v-if="property.status === 'promo' && showPromo && !isSoldOut">
                {{ promoTime }}</p>
              <p class="presale" v-if="property.status === 'presale' && !isSoldOut">{{ $t('PROPERTY_STATUS.PRESALE') }}
              </p>
              <p class="timer timer-presale" v-if="property.status === 'presale' && showPresale && !isSoldOut">
                {{ presaleTime }}</p>
            </div>
            <div v-if="showTopTokenHolders" class="top-onwers ml-2 text-capitalize" @click="onClickTopOwners">
              <b-icon class="mr-2" icon="award-fill" variant="warning" scale="1.5"></b-icon>
              {{ $t('propertyDetail.TOP_TOKEN_HOLDERS') }}
            </div>
          </div>
          <div class="info main-color">
            <h2 class="font-52">{{ property.name }}</h2>
            <span class="font-24">{{ property.metadata.address }}</span>
            <p v-if="property.status === 'presale'" class="note">{{ $t('propertyDetail.PRESALE_NOTE', { value: firstLiveOn}) }}</p>
          </div>
          <section class="high-light">
            <div v-if="property.num_bed" class="color-gray">
              <p>
                <!--                <b-icon scale="1.5" icon="inboxes-fill"></b-icon>-->
                <img src="../../assets/img/bedroom-icon.png"  alt=""/>
              </p>
              <span>{{ property.num_bed }} {{ $t("propertyDetail.Bedrooms") }}</span>
            </div>
            <div v-if="property.num_bath" class="color-gray">
              <p>
                <img src="../../assets/img/bathroom-icon.png" alt=""/>
              </p>
              <span>{{ property.num_bath }} {{ $t("propertyDetail.Bathrooms") }}</span>
            </div>
            <div v-if="property.sqm" class="color-gray">
              <p>
                <img src="../../assets/img/sqm-icon.png" alt=""/>
              </p>
              <span>{{ property.sqm }} sqm</span>
            </div>
            <div class="color-gray">
              <p>
                <img src="../../assets/img/house-fill.png" alt=""/>
              </p>
              <span>{{ property.category.name }}</span>
            </div>
          </section>
          <invest-card class="invest-card" :property="property" :tokens-status="tokensStatus"
                       @on-invest="onInvestClicked" @on-sell="onSellClicked" @on-swap="onSwapClicked" :num="1"/>
          <AnnualizedReturnsChart :annualizedReturns="annualizedReturns"></AnnualizedReturnsChart>
          <div class="tab-content mt-5">
            <b-tabs content-class="mt-3" class="tab__primary">
              <b-tab :title="$t('Details')" active>
                <div class="tab__details">
                  <div class="about__property">
                    <h3 class="title">{{ $t("MARKETPLACE.ABOUT_THE_PROPERTY") }}</h3>
                    <div class="color-black" v-html="propertyDetail"></div>
                  </div>
                  <div class="map__property">
                    <h3 class="title">{{ $t("MARKETPLACE.LOCATION") }}</h3>
                    <l-map style="width: 100%; height: 364px;" :options="mapView.options" :zoom="mapView.zoom"
                      :center="mapView.center">
                      <l-tile-layer :url="mapView.url" :attribution="mapView.attribution"></l-tile-layer>
                      <l-circle :lat-lng="mapView.center" :radius="mapView.circle.radius" :color="mapView.circle.color" />
                    </l-map>
                  </div>
                </div>
              </b-tab>
              <b-tab :title="$t('Financials')" @click="trackGtmEvent(gtmEvent.financials)">
                <financial-card :title="$t('propertyDetail.ASSET_VALUE')" :items="financials.asset_value" :show-est="true"></financial-card>
                <financial-card :title="$t('propertyDetail.ANNUAL_RETURN')" :items="financials.annual_return"></financial-card>
              </b-tab>
              <b-tab :title="$t('Documents')" @click="trackGtmEvent(gtmEvent.documents)">
                <div class="tab__details">
                  <h3 class="title">{{ $t("Documents") }}</h3>
                  <div v-for="document in property.documents" class="d-flex flex-row align-items-center">
                    <button id="btn_detail_downloadDoc" type="button" class="link-btn mb-1 font-22 download-btn" @click="downloadDoc(document)">
                      <img v-if="document.type === 'LINK'" style="width: 30px; margin-bottom: 2px;"
                        :src="document.icon" alt="">
                      <b-icon v-else icon="cloud-download-fill"></b-icon>
                    </button>
                    <p class="font-weight-bold ml-3" style="cursor: pointer;" @click="downloadDoc(document)">{{
                      document.file_name }}
                    </p>
                  </div>
                </div>
              </b-tab>
              <b-tab :title="$t('Market')" @click="trackGtmEvent(gtmEvent.market)">
                <div class="color-black" v-html="propertyMarket"></div>
              </b-tab>
              <b-tab :title="$t('TIMELINE')" @click="trackGtmEvent(gtmEvent.timeline)">
                <TimelineView v-if="milestones.length" :items="milestones" :total-count="totalCount"
                  :display-count="displayMilestoneCount">
                </TimelineView>
              </b-tab>
              <b-tab :title="$t('BLOCKCHAIN.TITLE')" @click="trackGtmEvent(gtmEvent.blockchain)">
                <blockchain-card :nftTokenId="property.id"></blockchain-card>
              </b-tab>
            </b-tabs>
          </div>
        </div>
        <div class="content-right invest__primary">
          <invest-card class="invest-card" :property="property" :tokens-status="tokensStatus"
                       @on-invest="onInvestClicked" @on-sell="onSellClicked" @on-swap="onSwapClicked" :num="2"/>
        </div>
      </b-row>
    </b-container>
    <popup ref="popupPendingTask" @on-positive-clicked="openContractAgreement"></popup>
    <modal-contract-agreement ref="contractAgreement" @on-agreed-to-contract="onAgreedToContract"/>
    <virtual-balance-invest :property="property" :show="showVirtualInvest" @on-close="showVirtualInvest = false" />
    <modal-top-owners :property-id="property.id" :show="showTopOwners" :property-name="property.name"
      @on-close="showTopOwners = false"></modal-top-owners>
  </div>
</template>
<script>
import "viewerjs/dist/viewer.css"
import { directive } from "v-viewer"
import VueEasyLightbox from 'vue-easy-lightbox'
import accountService from "../../services/account.service"
import propertiesService from "../../services/properties.service"
import { formattedDuration, urlImage } from "../../helpers/common"
import "vue3-carousel/dist/carousel.css"
import { Carousel, Navigation, Slide } from "vue3-carousel"
import moment from "moment"
import "leaflet/dist/leaflet.css"
import { LCircle, LMap, LTileLayer } from "@vue-leaflet/vue-leaflet"
import InvestAnnual from "../../components/InvestAnnual.vue"
import InvestCard from "../../components/InvestCard.vue"
import VirtualBalanceInvest from "../../components/VirtualBalanceInvest.vue"
import AnnualizedReturnsChart from "../../components/AnnualizedReturnsChart.vue"
import ModalTopOwners from "../../modals/ModalTopOwners.vue"
import TimelineView from "../../components/TimelineView.vue"
import Popup from "../../components/Popup"
import FinancialCard from "../../components/FinancialCard.vue"
import BlockchainCard from "../../components/BlockchainCard.vue"
import ModalContractAgreement from "../../modals/ModalContractAgreement"
import contractsService from "../../services/contracts.service"
import store from "../../store/store"
import { gtmTrackEvent } from "../../helpers/gtm"
import { GTM_EVENT_NAMES } from "../../constants/gtm"
import { isFullyActive } from "../../constants/userStatus"

export default {
  directives: {
    viewer: directive(),
  },
  components: {
    Carousel,
    Slide,
    Navigation,
    LMap,
    LTileLayer,
    LCircle,
    InvestAnnual,
    InvestCard,
    VirtualBalanceInvest,
    AnnualizedReturnsChart,
    ModalTopOwners,
    TimelineView,
    FinancialCard,
    BlockchainCard,
    ModalContractAgreement,
    Popup,
    VueEasyLightbox,
  },
  data() {
    return {
      property: {},
      propertyUuid: this.$route.params.uuid,
      viewOptions: {
        movable: false,
        toolbar: {
          prev: 4,
          play: false,
          next: 4,
        },
        zoom: false,
        keyboard: false,
        navbar: 4,
        zoomOnWheel: false,
        title: false,
      },
      promoTime: '',
      presaleTime: '',
      mapView: {
        options: {
          scrollWheelZoom: true,
          touchZoom: true,
          doubleClickZoom: true,
          dragging: true,
          zoomControl: true,
          attributionControl: false
        },
        url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '',
        zoom: 18,
        center: [51.505, -0.159],
        circle: {
          radius: 30,
          color: '#005151'
        }
      },
      showVirtualInvest: false,
      annualizedReturns: [],
      showTopOwners: false,
      milestones: [],
      totalCount: 0,
      displayMilestoneCount: 0,
      tokensStatus: {},
      financials: {},
      agreeToContractPendingTask: null,
      gtmEvent: {
        topOwners: GTM_EVENT_NAMES.TOP_TOKEN_HOLDERS,
        financials: GTM_EVENT_NAMES.FINANCIALS,
        documents: GTM_EVENT_NAMES.DOCUMENTS,
        market: GTM_EVENT_NAMES.MARKET,
        timeline: GTM_EVENT_NAMES.TIMELINE,
        blockchain: GTM_EVENT_NAMES.BLOCKCHAIN,
      },
      hasTokenHolders: false,
      breakpoints: {
        300: {
          itemsToShow: 1,
          snapAlign: "center",
        },
        500: {
          itemsToShow: 1.75,
          snapAlign: "center",
        },
        768: {
          itemsToShow: 2,
          snapAlign: "center",
        },
        900: {
          itemsToShow: 2.5,
          snapAlign: "center",
        },
        1000: {
          itemsToShow: 3,
          snapAlign: "start",
        }
      },
      imageViewerVisible: false,
      imageViewerIndex: 0,
    }
  },

  async mounted() {
    this.checkReferralCode()
    await Promise.all([
      this.getProperty(),
      this.getTokensStatus(),
    ]);
    if ((this.property.status === 'presale' || this.property.status === 'promo') && (this.showPresale || this.showPromo)) {
      const self = this;
      setInterval(() => {
        self.updateTimer();
      }, 1000);
    }
    gtmTrackEvent({
      event: GTM_EVENT_NAMES.USER_VIEW_PROPERTY,
      productId: this.property.id,
      productUuid: this.property.uuid,
      productName: this.property.name,
      customerID: this.$store.getters.userProfile ? this.$store.getters.userProfile.id : null,
      customerEmail: this.$store.getters.userProfile ? this.$store.getters.userProfile.email : null,
      customerPhoneNumber: this.$store.getters.userProfile ? this.$store.getters.userProfile.phone : null,
    })
  },
  methods: {
    async getProperty() {
      const res = await propertiesService.getByUuid(this.propertyUuid);
      this.property = res.data
      this.mapView.center = [this.property.lat, this.property.lng];
      this.getAnnualizedReturns(this.property.id)
      this.getMilestones()
      this.getFinancials()
      this.getContractStatus()
      this.checkTokenHolders()
      this.displayMilestoneCount = this.property.display_milestones_count
    },
    async getAnnualizedReturns(id) {
      const res = await propertiesService.getAnnualizedReturns(id)
      if (res && res.data) {
        this.annualizedReturns = res.data
      }
    },
    async getMilestones(all) {
      const res = await propertiesService.getMilestones({
        property_id: this.property.id,
      })
      if (res && res.data) {
        this.milestones = res.data
        this.totalCount = res.total_count
      }
    },
    async getTokensStatus () {
      if (this.$store.getters.userProfile && localStorage.getItem("Authorization") && isFullyActive()) {
        const res = await accountService.getTokensStatus(this.propertyUuid)
        if (res && res.data) {
          this.tokensStatus = res.data
        }
      }
    },
    async getContractStatus () {
      if (this.$store.getters.userProfile && localStorage.getItem("Authorization") && this.property) {
        this.agreeToContractPendingTask = null
        const response = await contractsService.getContractStatus(this.property.uuid)
        if (response) {
          this.agreeToContractPendingTask = response.agree_to_contract_pending_task
        }
      }
    },
    async getFinancials() {
      const res = await propertiesService.getFinancials({
        property_id: this.property.id,
      })
      if (res && res.data) {
        this.financials = res.data
      }
    },
    async checkTokenHolders() {
      const res = await propertiesService.checkTokenHolders(this.property.id)
      if (res) {
        this.hasTokenHolders = res.data
      }
    },
    getUrl(image) {
      return urlImage(image)
    },
    async onInvestClicked() {
      gtmTrackEvent({
        event: GTM_EVENT_NAMES.USER_CLICKED_INVEST,
        property_uuid: this.property.uuid,
        property_name: this.property.name,
      })
      if (this.$store.getters.userProfile && localStorage.getItem("Authorization")) {
        // Redirect to referral code input page / activation page
        if (!isFullyActive()) {
          await this.$router.push({ name: "referrals" })
        } else {
          if (this.agreeToContractPendingTask) {
            this.$refs.popupPendingTask.openPopup(this.$t("PENDING_TASKS.COMPLETE_PENDING_TASK"),
              this.$t("PENDING_TASKS.PLEASE_READ_AND_AGREE_TO_AGREEMENT"), this.$t("common.OK"))
          } else {
            await this.$router.push({ name: "pay", query: { uuid: this.property.uuid } })
          }
        }
      } else {
        if (this.isEnableVirtualBalance && this.initialBalance > 0) {
          this.showVirtualInvest = true
        } else {
          await this.$router.push({ name: "register" })
        }
      }
    },
    async onSellClicked () {
      if (this.$store.getters.userProfile && localStorage.getItem("Authorization")) {
        if (this.agreeToContractPendingTask) {
          this.$refs.popupPendingTask.openPopup(this.$t("PENDING_TASKS.COMPLETE_PENDING_TASK"),
            this.$t("PENDING_TASKS.PLEASE_READ_AND_AGREE_TO_AGREEMENT"), this.$t("common.OK"))
        } else {
          await this.$router.push({ name: "sellTokenRequest", query: { uuid: this.property.uuid } })
        }
      } else {
        await this.$router.push({ name: "register" })
      }
    },
    async onSwapClicked () {
      if (this.$store.getters.userProfile && localStorage.getItem("Authorization")) {
        if (this.agreeToContractPendingTask) {
          this.$refs.popupPendingTask.openPopup(this.$t("PENDING_TASKS.COMPLETE_PENDING_TASK"),
            this.$t("PENDING_TASKS.PLEASE_READ_AND_AGREE_TO_AGREEMENT"), this.$t("common.OK"))
        } else {
          await this.$router.push({ name: "swapToken", query: { uuid: this.property.uuid } })
        }
      } else {
        await this.$router.push({ name: "register" })
      }
    },
    async openContractAgreement () {
      const contractPreviewUrl = await contractsService.getContractPreviewForExisting({ property_uuid: this.property.uuid })
      this.$refs.contractAgreement.showModal(contractPreviewUrl, this.property.uuid)
    },
    async onAgreedToContract (data) {
      if (data && data.propertyUuid === this.propertyUuid) {
        await this.getContractStatus()
      }
    },
    updateTimer() {
      if (this.property.status === 'presale') {
        const presaleEndsAt = this.property.presale_ends_at;
        const currentDate = new Date();
        const presaleDate = new Date(presaleEndsAt);
        if (currentDate >= presaleDate) {
          this.presaleTime = this.$t('propertyDetail.ENDED');
        } else {
          const diffTime = presaleDate.getTime() - currentDate.getTime();
          this.presaleTime = this.$t('propertyDetail.END_IN', { value: formattedDuration(diffTime) });
        }
      } else if (this.property.status === 'promo') {
        const promoEndsAt = this.property.promo_ends_at;
        const currentDate = new Date();
        const promoDate = new Date(promoEndsAt);
        if (currentDate >= promoDate) {
          this.promoTime = this.$t('propertyDetail.ENDED');
        } else {
          const diffTime = promoDate.getTime() - currentDate.getTime();
          this.promoTime = this.$t('propertyDetail.END_IN', { value: formattedDuration(diffTime) });
        }
      }
    },
    downloadDoc(document) {
      const url = document.type === 'FILE' ? `${process.env.VUE_APP_IMG_HOST}/${encodeURIComponent(document.file)}` : document.link
      window.open(url, '_blank');
    },

    checkReferralCode() {
      if (this.referralCode) {
        localStorage.setItem('referral_code', this.referralCode)
      }
    },
    trackGtmEvent(event) {
      gtmTrackEvent({
        event: event,
      })
    },
    onClickTopOwners() {
      this.showTopOwners = true
      this.trackGtmEvent(this.gtmEvent.topOwners)
    },

    showImageViewer(index) {
      this.imageViewerVisible = true,
      this.imageViewerIndex = index
    }
  },
  metaInfo() {
    let title = 'GORO';
    if (this.property.name) {
      title = this.property ? `GORO - ${this.property.name}` : 'GORO';
    }
    return {
      title,
      meta: [
        { property: 'og:title', content: title },
        { property: 'og:site_name', content: title },
      ],
    };
  },
  computed: {
    isSoldOut() {
      return this.property.status === "sold" || this.property.display_sold_tokens === this.property.total_tokens
    },

    isComingSoon() {
      return this.property.status === 'coming_soon'
    },

    showPresale() {
      const presaleEndsAt = this.property.presale_ends_at;
      const currentDate = moment(new Date()).add(1, 'days');
      const presaleDate = new Date(presaleEndsAt);
      return currentDate > presaleDate;
    },

    showPromo() {
      const promoEndsAt = this.property.promo_ends_at;
      const currentDate = moment(new Date()).add(1, 'days');
      const promoDate = new Date(promoEndsAt);
      return currentDate > promoDate;
    },

    propertyDetail() {
      if (!this.property.metadata) {
        return ''
      }
      return this.isIndonesian ? this.property.metadata.detail_id_locale || this.property.metadata.detail : this.property.metadata.detail;
    },

    propertyFinalcials() {
      if (!this.property.metadata) {
        return ''
      }
      return this.isIndonesian ? this.property.metadata.financial_id_locale || this.property.metadata.financial : this.property.metadata.financial;
    },

    propertyMarket() {
      if (!this.property.metadata) {
        return ''
      }
      return this.isIndonesian ? this.property.metadata.market_id_locale || this.property.metadata.market : this.property.metadata.market;
    },

    isIndonesian() {
      return this.$i18n.locale.toLowerCase() === 'id';
    },

    referralCode() {
      return this.$route.query.code
    },

    isEnableVirtualBalance () {
      if (store.state.configs && store.state.configs.enabled_virtual_balance) {
        return store.state.configs.enabled_virtual_balance
      }
      return false
    },

    initialBalance() {
      return store.getters.initialBalance || 0
    },
    firstLiveOn() {
      return moment(this.property.first_live_on).format('DD/MM/YYYY')
    },
    showTopTokenHolders() {
      return this.hasTokenHolders && this.property && this.property.status !== 'coming_soon'
    },
    propertyImages() {
      if (this.property) {
        const images = this.property.images || []
        return images.map(e => this.getUrl(e))
      }
      return []
    },
  }
}
</script>
<style lang="scss" scoped>
#map {
  pointer-events: none;
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}

.viewer-canvas {
  img {
    border-radius: 16px;
  }

}

.viewer-footer {
  overflow: unset;
}

.viewer-backdrop {
  background-color: rgba(0, 0, 0, .75);
}

.viewer-button {
  display: none;
}

.viewer-navbar {
  background-color: transparent;
  padding-bottom: 30px;
}

.viewer-list {
  height: 80px;

  >li {
    width: 80px;
    height: 80px;

    img {
      width: 80px !important;
      height: 80px !important;
      transform: unset !important;
      padding: 2px;
      border-radius: 16px;
      object-fit: cover;
    }
  }
}

.viewer-toolbar {
  ul {
    margin: 0;
    padding: 0;

    li {
      position: absolute;
      top: -50vh;
      transform: translate(-50%, 5vh);
      background-color: transparent;
      background-repeat: no-repeat;
      width: 21px;
      height: 39px;

      &:hover {
        background-color: transparent;
        opacity: .75;
      }

      &:before {
        background-image: unset;
      }
    }

    li:nth-child(1) {
      left: 80px;
      background-image: url("../../assets/img/arrow-left.png");

    }

    li:nth-child(2) {
      right: 60px;
      background-image: url("../../assets/img/arrow-right.png");
    }
  }
}

.viewer-toolbarz {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.property-detail {
  .content-left {
    -ms-flex: 0 0 62%;
    flex: 0 0 62%;
    max-width: 62%;
    padding-left: 15px;
    padding-right: 15px;

    .invest-card {
      @media screen and (min-width: 992px) {
        display: none;
      }
    }

    .top-onwers {
      cursor: pointer;
      border: 1px solid var(--primary-color);
      padding: 4px 8px 4px 8px;
      border-radius: 4px;
      transition: 0.2s;
      font-size: 12px;

      &:hover {
        background-color: #ffffcb;
      }
    }
  }

  .content-right {
    -ms-flex: 0 0 38%;
    flex: 0 0 38%;
    max-width: 38%;
    padding-left: 15px;
    padding-right: 15px;

    .invest-card {
      @media screen and (max-width: 991.98px) {
        display: none;
      }
    }
  }

  @media(max-width: 991.98px) {
    .content-left {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }

    .content-right {
      -ms-flex: 0 0 100%;
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  .carousel {
    img {
      width: 100%;
      height: 360px;
      object-fit: cover;
      cursor: pointer;
      -webkit-filter: brightness(100%);
      filter: brightness(100%);
    }

    img:hover {
      -webkit-filter: brightness(60%);
      filter: brightness(60%);
      -webkit-transition: all 500ms ease;
      -moz-transition: all 500ms ease;
      -o-transition: all 500ms ease;
      -ms-transition: all 500ms ease;
      transition: all 500ms ease;
    }
  }


  .invest__primary {
    position: relative;
  }

  .tab-content {
    .tab__primary {
      ul.nav-tabs {
        border: none;
        border-bottom: 1px solid #ccc;

        li {
          a {
            color: #878787;
            border: none;
            font-size: 18px;
          }

          a.active {
            color: #143863;
            border-bottom: 2px solid #143863;
            font-weight: bold;
          }
        }
      }
    }

    .tab__details {
      h3.title {
        font-size: 20px;
        font-weight: 800;
        line-height: 24px;
        color: var(--primary-darker-color);
      }

      .note {
        margin-bottom: 5px;
      }

      .download-btn {
        background-color: rgba(170, 170, 170, 0.2);
        width: 70px;
        min-width: 70px;
        height: 40px;
        border-radius: 10px;
        transition: 0.3s;

        &:hover {
          background-color: rgba(170, 170, 170, 0.4);
        }

        &.link-btn {
          border: none;
        }
      }
    }
  }

  .content {
    padding: 20px 0;

    .status {
      background-color: #9FD159;
      color: #fff;
      padding: 4px 16px;
      border-radius: 2px;
      display: inline-block;
      font-size: 16px;
      text-transform: capitalize;
      width: auto;
      font-weight: 600;
    }

    .info {
      padding-bottom: 20px;

      h2 {
        margin: 10px 0 0;
      }

      .note {
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        max-width: 70%;
        padding: 8px;
        background-color: #F7B733;
        border-radius: 5px;
        margin-top: 10px;
      }
    }

    .high-light {
      display: inline-block;
      padding-bottom: 30px;

      div {
        margin: 0 15px 15px 0;
        text-align: center;
        width: 120px;
        height: 120px;
        padding: 26px 10px;
        background-color: #ffffff;
        float: left;
        box-shadow: 0 4px 10px rgba(7, 55, 99, 0.12);
        border-radius: 8px;

        p {
          margin-bottom: 4px;
        }

        span {
          font-size: 16px;
          font-weight: 600;
          color: var(--primary-darker-color);
        }
      }
    }
  }
}

#full-screen-modal {
  padding: 0 !important;
  background: #f4f3ef;
}

#full-screen-modal .modal-dialog {
  width: 100%;
  max-width: 100%;
  height: 100vh;
  max-height: 100vh;
  min-height: 100vh;
  margin: 0;
  background: #f4f3ef;
}

#full-screen-modal .modal-content {
  height: 100%;
  border: none;
  border-radius: 0;
  background: #f4f3ef;
}

.card-status {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
  flex-direction: row;

  p {
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-weight: 600;
  }

  .active {
    background-color: #9FD159;
    color: #ffffff;
    border-radius: 2px;
  }

  .sold-out {
    background-color: #54595E;
    color: #ffffff;
    border-radius: 2px;
  }

  .coming-soon {
    background-color: #008e8e;
  }

  .promo {
    background-color: #DC3545;
    color: #ffffff;
    border-radius: 2px 0 0 2px;
  }

  .presale {
    background-color: #F7B733;
    color: #ffffff;
    border-radius: 2px 0 0 2px;
  }

  .timer {
    background-color: #ffffff;
    color: #54595E;
    border-radius: 0 2px 2px 0;
    min-width: 150px;
    text-align: center;
  }

  .timer-promo {
    border: 1px solid #DC3545;
  }

  .timer-presale {
    border: 1px solid #F7B733;
  }
}

.view-more {
  margin-left: 100px;
  margin-top: 5px;
  cursor: pointer;
  font-size: 13px;
  text-decoration: underline;
  width: fit-content;
}

.carousel {
  border-radius: 10px !important;
  overflow: hidden;
}

.icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: var(--primary-darker-color);
  }

}
</style>
