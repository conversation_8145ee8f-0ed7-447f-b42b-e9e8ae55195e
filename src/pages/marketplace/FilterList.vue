<template>
  <div class="marketplace">
    <b-container>
      <earn-referral-message class="mb-0 mt-4 pt-1"/>
      <div v-if="images.length" class="image_primary">
        <carousel class="carousel-list" :perPage="1" :spacePadding="0" :scrollPerPage="true" :paginationEnabled="true"
          :autoplay="true" :autoplayTimeout="7000" :loop="true">
          <slide v-for="image in images" :key="image.id" @slide-click="openUrl(image.link)">
            <img :src="getImageUrl(image)" style="width: 100%;" :style="image.link ? 'cursor: pointer;' : ''" alt=""/>
          </slide>
        </carousel>
      </div>
      <div class="filters" hidden>
        <!-- <div class="filters__item">
          <label>{{$t("MARKETPLACE.LOCATION")}}</label>
          <v-select v-if="locations.length" class="goro-select" v-model="filters.location_id" :options="locations"
            :reduce="v => v.id" :clearable="false" label="name">
            <template v-slot:option="option">
              {{option.id === null ? $t(option.name) : option.name}}
            </template>
            <template #selected-option="option">
              {{option.id === null ? $t(option.name) : option.name}}
            </template>
          </v-select>
        </div> -->
        <div class="filters__item">
          <label>{{ $t("MARKETPLACE.PROPERTY_TYPE") }}</label>
          <v-select v-if="categories.length" class="goro-select main-color" v-model="filters.category_id"
            :options="categories" :reduce="v => v.id" :clearable="false" label="name">
            <template v-slot:option="option">
              {{ option.name }}
            </template>
            <template #selected-option="option">
              {{ option.name }}
            </template>
          </v-select>
        </div>
        <div class="filters__item ery-irr">
          <b-button style="background:transparent;border:none;padding:0" id="tooltip-target-irr">
            <span class="main-color">{{ $t("propertyDetail.IRR") }}</span>
            <img src="@/assets/img/info-circle-fill.png">
          </b-button>
          <b-tooltip class="mb-1" variant="secondary" target="tooltip-target-irr" triggers="hover" placement="top">
            {{ $t("propertyDetail.IRR_TOOLTIP") }}
          </b-tooltip>
          <div class="range">
            <span ref="irr">{{ filters.irr }}%</span>
            <input ref="irr_range" type="range" step="0.1" class="form-control-range" :min="irr.min" :max="irr.max"
              v-model="filters.irr">
            <i class="min font-12 color-gray">{{ irr.min }}%</i>
            <i class="max font-12 color-gray">{{ irr.max }}%</i>
          </div>
        </div>
        <div class="filters__item ery-irr">
          <b-button style="background:transparent;border:none;padding:0" id="tooltip-target-ery">
            <span class="main-color">{{ $t("propertyDetail.ERY") }}</span>
            <img src="@/assets/img/info-circle-fill.png">
          </b-button>
          <b-tooltip class="mb-1" variant="secondary" target="tooltip-target-ery" triggers="hover" placement="top">
            {{ $t("propertyDetail.ERY_TOOLTIP") }}
          </b-tooltip>
          <div class="range">
            <span ref="ery">{{ filters.ery }}%</span>
            <input ref="ery_range" type="range" step="0.1" class="form-control-range" :min="ery.min" :max="ery.max"
              v-model="filters.ery">
            <i class="min font-12 color-gray">{{ ery.min }}%</i>
            <i class="max font-12 color-gray">{{ ery.max }}%</i>
          </div>
        </div>
        <div class="filters__item">
          <label>{{ $t("MARKETPLACE.STATUS") }}</label>
          <v-select v-if="statusList.length" class="goro-select" v-model="filters.status" :options="statusList"
            :reduce="v => v.status" :clearable="false" :searchable="false">
            <template v-slot:option="option">
              {{ $t(`status.${option.label }`) + ' (' + option.total + ')' }}
            </template>
            <template #selected-option="{ label, total }">
              {{ $t(`status.${ label }`) + ' (' + total + ')' }}
            </template>
          </v-select>
        </div>
      </div>
      <b-row v-if="properties && properties.data && properties.data.length" class="marketplace-content equal">
        <b-col xl="4" lg="4" md="6" sm="6" cols="12" class="property" v-for="property in properties.data"
          :key="property.id">
          <property-light-card :property="property" />
        </b-col>
        <b-col cols="12">
          <b-pagination v-if="properties.total" align="right" v-model="properties.current_page"
            :total-rows="properties.total" :per-page="properties.per_page" @change="onChangePage"
            aria-controls="my-table"></b-pagination>
        </b-col>
      </b-row>
    </b-container>
  </div>
</template>
<script>
import locationsService from "../../services/locations.service";
import categoryService from "../../services/categories.service";
import propertiesService from "../../services/properties.service";
import commonService from "../../services/common.service";
import { urlImage } from "@/helpers/common";
import PropertyLightCard from "../../components/Cards/PropertyLightCard";
import { Carousel, Slide } from '@jambonn/vue-concise-carousel';
import '@jambonn/vue-concise-carousel/lib/vue-concise-carousel.css'
import {FOREIGNER, PAYMENT_METHOD} from "../../constants/constants"
import EarnReferralMessage from "../../layout/account/EarnReferralMessage.vue"

export default {
  components: {
    PropertyLightCard,
    Carousel,
    Slide,
    EarnReferralMessage,
  },
  data() {
    return {
      rangenumber: {
        value: 3
      },
      title: "Marketplace",
      properties: [],
      locations: [],
      statusList: [],
      categories: [],
      ery: {
        left: 0,
        min: 5,
        max: 20,
      },
      irr: {
        left: 0,
        min: 5,
        max: 20,
      },
      filters: {
        location_id: null,
        category_id: null,
        status: null,
        ery: 5,
        irr: 5,
      },
      images: [],
    }
  },
  async mounted() {
    await Promise.all([
      this.getLocations(),
      this.getCategories(),
      this.getStatusCount(),
      this.getProperties(1),
      this.getBanners(),
    ]);
    const leftC = ((this.filters.ery - this.ery.min) / (this.ery.max - this.ery.min)) * 100;
    this.$refs['ery'].style.left = `calc(${leftC}% - ${(leftC / 10) * 1.8}% + 19px)`;
    this.$refs['ery_range'].style.backgroundSize = (this.filters.ery - this.ery.min) * 100 / (this.ery.max - this.ery.min) + '% 100%';
    const leftI = ((this.filters.irr - this.irr.min) / (this.irr.max - this.irr.min)) * 100;
    this.$refs['irr'].style.left = `calc(${leftI}% - ${(leftI / 10) * 1.8}% + 19px)`;
    this.$refs['irr_range'].style.backgroundSize = (this.filters.irr - this.irr.min) * 100 / (this.irr.max - this.irr.min) + '% 100%';
  },
  watch: {
    async "filters.location_id"() {
      this.getProperties(1)
    },
    async "filters.status"() {
      this.getProperties(1)
    },
    async "filters.category_id"() {
      this.getProperties(1)
    },
    async "filters.ery"(v) {
      const leftC = ((v - this.ery.min) / (this.ery.max - this.ery.min)) * 100;
      this.$refs['ery'].style.left = `calc(${leftC}% - ${(leftC / 10) * 1.8}% + 19px)`;
      this.$refs['ery_range'].style.backgroundSize = (v - this.ery.min) * 100 / (this.ery.max - this.ery.min) + '% 100%';

      this.searchTimeOut(1)
    },
    async "filters.irr"(v) {
      const leftC = ((v - this.irr.min) / (this.irr.max - this.irr.min)) * 100;
      this.$refs['irr'].style.left = `calc(${leftC}% - ${(leftC / 10) * 1.8}% + 19px)`;
      this.$refs['irr_range'].style.backgroundSize = (v - this.irr.min) * 100 / (this.irr.max - this.irr.min) + '% 100%';
      this.searchTimeOut(1)
    },
    "$store.state.userProfile" (newVal, oldVal) {
        this.getBanners()
    }
  },
  methods: {
    async onChangePage(page) {
      await this.getProperties(page)
    },
    async getLocations() {
      const locations = await locationsService.getLocations();
      this.locations = [{ name: this.$t("FILTER.ALL_LOCATION"), id: null }, ...locations]
    },
    async getStatusCount() {
      this.statusList = await propertiesService.getStatusCount();
    },
    async getCategories() {
      const categories = await categoryService.getCategories();
      this.categories = [{ name: this.$t("FILTER.ALL_PROPERTIES"), id: null }, ...categories];
    },
    async getProperties(page) {
      const filters = {
        ...this.filters,
        ery: [5, this.filters.ery],
        irr: [5, this.filters.irr],
        page,
      };
      const result = await propertiesService.getList(filters);
      if (result) {
        const { data } = result;
        data.map((property) => {
          property.imageURL = property.images && property.images.length ? urlImage(property.images[0]) : '';
        });
        this.properties = result
      }
    },
    async getBanners() {
      const res = await commonService.getBanners({ platform: 'web' });
      if (res && res.data) {
        this.images = res.data;
      }
    },
    searchTimeOut(page) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(async () => {
        await this.getProperties(page);
      }, 500);
    },
    getImageUrl(image) {
      const baseUrl = process.env.VUE_APP_IMG_HOST
      let path = image.image
      const user = this.$store.getters.userProfile
      if (image.image_id_locale && (this.$i18n.locale.toLowerCase() === 'id' || (user && (user.iso_country_code == 'ID' || user.country_code == 62 || user.payment_method === PAYMENT_METHOD.XENDIT)))) {
        path = image.image_id_locale
      }
      return `${baseUrl}/${path}`;
    },

    openUrl(url) {
      if(url) {
        window.open(url, '_blank')
      }
    }
  },

  metaInfo() {
    return {
      title: this.title,
      meta: [
        { property: 'og:title', content: this.title },
        { property: 'og:site_name', content: this.title },
      ],
    };
  },
}
</script>
<style lang="scss">
.marketplace {
  .marketplace-content {
    padding: 24px 0;
  }

  .image_primary {
    margin-top: 32px;
    box-shadow: 0 4px 10px rgba(7, 55, 99, 0.12);
    overflow: hidden;
    border-radius: 16px;

    img {
      aspect-ratio: 4 / 1;
      object-fit: cover;
    }

    .carousel-list {
      border-radius: 16px;
      border: 3px solid var(--primary-color);
      overflow: hidden;
    }
  }

  .VueCarousel-pagination {
    position: absolute;
    bottom: 8px;
    pointer-events: none;

    .VueCarousel-dot {
      border-radius: 50%;
      background-color: transparent !important;
      border: 1px solid var(--primary-lighter-color);
      padding: 0 !important;
      margin-left: 7px;
      margin-right: 7px;
      width: 9px !important;
      height: 9px !important;
      pointer-events: auto;
    }

    .VueCarousel-dot--active {
      background-color: var(--primary-lighter-color) !important;
    }
  }

  .filters {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -5px;
    margin-left: -5px;
    padding-bottom: 16px;

    .filters__item {
      -ms-flex: 0 0 20%;
      flex: 0 0 20%;
      max-width: 20%;
      width: 100%;
      padding-right: 5px;
      padding-left: 5px;

      img {
        width: 16px;
        margin-left: 6px;
        margin-bottom: 4px;
      }

      &.ery-irr {
        margin-top: 6px;
      }
    }

    @media (max-width: 991.98px) {
      .filters__item {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        padding-right: 0px;
        padding-left: 0px;
      }
    }
  }

  .range {
    position: relative;
    border: 1px solid #CED4DA;
    border-radius: 4px;
    padding: 20px 0.75rem;

    i {
      font-style: normal;
      position: absolute;
      bottom: 2px;
    }

    i.min {
      left: 0.75rem;
    }

    i.max {
      right: 0.75rem;
    }

    span {
      position: absolute;
      top: -4px;
      width: 38px;
      text-align: center;
      font-weight: bold;
      transform: translate(-50%, 0);
    }

    .form-control-range {
      -webkit-appearance: none;
      /* Override default CSS styles */
      appearance: none;
      width: 100%;
      height: 2px;
      background: #ABB5BE;
      outline: none;
      /* Remove outline */
      opacity: 0.7;
      /* Set transparency (for mouse-over effects on hover) */
      -webkit-transition: .2s;
      /* 0.2 seconds transition on hover */
      transition: opacity .2s;

      background-image: linear-gradient(#006666, #006666);
      background-repeat: no-repeat;
    }

    .form-control-range::-webkit-slider-runnable-track {
      -webkit-appearance: none;
      box-shadow: none;
      border: none;
      background: transparent;
    }

    .form-control-range::-moz-range-track {
      -webkit-appearance: none;
      box-shadow: none;
      border: none;
      background: transparent;
    }

    .form-control-range::-ms-track {
      -webkit-appearance: none;
      box-shadow: none;
      border: none;
      background: transparent;
    }

    .form-control-range::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 16px;
      height: 16px;
      background: var(--primary-color);
      cursor: pointer;
      border: 3px solid #FFFFFF;
      border-radius: 50%;
    }

    .form-control-range::-moz-range-thumb {
      width: 16px;
      height: 16px;
      background: var(--primary-color);
      cursor: pointer;
      border: 3px solid #FFFFFF;
      border-radius: 50%;
    }
  }

  .marketplace-h1 {
    font-size: 80px;
    font-weight: bold;
  }

  .property {
    margin-bottom: 30px;
  }

}
</style>
