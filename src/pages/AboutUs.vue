<template>
  <div class="about-us">
    <div class="intro-content">
      <b-container>
        <b-row align-h="center">
          <b-col cols="12" xl="9" lg="10">
            <h1 class="font-46">{{ $t("ABOUT_US.WE_ARE_ON_MISSION") }}</h1>
            <b-col class="pl-0 pr-0" cols="12" xl="9" lg="10">
              <p class="font-18 mt-4 mb-4">{{ $t("ABOUT_US.THROUGH_FRACTIONAL_INVESTING") }}</p>
            </b-col>
            <router-link :to="{ name: 'marketplace' }">
              <b-button id="btn_aboutUs_ViewProperties" class="btn-main pl-4 pr-4" variant="none">{{ $t("ABOUT_US.VIEW_PROPERTIES") }}
              </b-button>
            </router-link>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="text-center pb-3 pt-1" style="background-color: var(--primary-background-darker-color)">
      <b-container>
        <b-row class="align-items-center pt-4 pb-3 pb-lg-5 pb-xl-5" align-h="around">
          <b-col cols="12" xl="5" lg="4 mb-2 mt-2">
            <h1 class="font-38 text-left">{{ $t("LANDING.OUR_MISSION") }}</h1>
            <p class="font-18 text-left mt-3">{{ $t("LANDING.OUR_MISSION_DESCRIPTION") }}</p>
          </b-col>
          <b-col cols="12" xl="6" lg="6">
            <b-row>
              <b-col cols="12" xl="6" lg="6">
                <div class="our-mission-card text-left mt-lg-5 mt-xl-5">
                  <h5 class="font-bolder">{{ $t("LANDING.OUR_MISSION_1") }}</h5>
                  <p>{{ $t("LANDING.OUR_MISSION_1_DESCRIPTION") }}</p>
                </div>
              </b-col>
              <b-col cols="12" xl="6" lg="6">
                <div class="our-mission-card text-left">
                  <h5 class="font-bolder">{{ $t("LANDING.OUR_MISSION_2") }}</h5>
                  <p>{{ $t("LANDING.OUR_MISSION_2_DESCRIPTION") }}</p>
                </div>
              </b-col>
            </b-row>
            <b-row>
              <b-col cols="12" xl="6" lg="6">
                <div class="our-mission-card text-left mt-lg-5 mt-xl-5">
                  <h5 class="font-bolder">{{ $t("LANDING.OUR_MISSION_3") }}</h5>
                  <p>{{ $t("LANDING.OUR_MISSION_3_DESCRIPTION") }}</p>
                </div>
              </b-col>
              <b-col cols="12" xl="6" lg="6">
                <div class="our-mission-card text-left">
                  <h5 class="font-bolder">{{ $t("LANDING.OUR_MISSION_4") }}</h5>
                  <p>{{ $t("LANDING.OUR_MISSION_4_DESCRIPTION") }}</p>
                </div>
              </b-col>
            </b-row>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="partners-content text-center">
      <b-container class="mb-0">
        <b-row align-h="center">
          <b-col cols="12" xl="9" lg="10">
            <h1 class="title text-center mt-4 mb-4">{{ $t("ABOUT_US.OUR_INVESTORS") }}</h1>
            <b-container>
              <b-row align-h="center">
                <b-col v-for="n in 2" cols="6" md="6" xl="3" lg="3" class="mb-3 pr-1 pl-1">
                  <div class="item-container">
                    <img :src="getInvestorImage(n)" />
                  </div>
                </b-col>
              </b-row>
            </b-container>

            <h1 class="title text-center mt-4 mb-4">{{ $t("ABOUT_US.OUR_ANGLES") }}</h1>
            <b-container>
              <b-row align-h="center">
                <b-col v-for="n in 24" cols="6" md="6" xl="3" lg="3" class="mb-3 pr-1 pl-1">
                  <div class="item-container">
                    <img :src="getAngleImage(n)" height="85"/>
                  </div>
                </b-col>
              </b-row>
            </b-container>
          </b-col>
        </b-row>
      </b-container>
    </div>
    <div class="join-content ">
      <b-container class="mt-0">
        <b-row align-h="center">
          <b-col cols="12" xl="9" lg="10">
            <b-row>
              <b-col>
                <div class="bg-container text-center">
                  <h1 class="font-32 font-weight-bold mt-0 mb-3"> {{ $t("ABOUT_US.JOIN_GORO") }}</h1>
                  <p class="mb-4">{{ $t("ABOUT_US.ARE_YOU_PASSIONATE") }}</p>
                  <a :href="careersUrl" target="_blank" class="careers">
                    {{ $t("ABOUT_US.VIEW_OPEN_POSITIONS") }}
                  </a>
                </div>
              </b-col>
            </b-row>
          </b-col>
        </b-row>
      </b-container>
    </div>
  </div>
</template>

<script>

import externalSites from "@/constants/externalSites"

export default {
  data() {
    return {
      careersUrl: externalSites.SOCIAL_MEDIA.CAREERS,
      title: "About Us",
    }
  },

  methods: {
    getInvestorImage(i) {
      const images = require.context("@/assets/img/investors/", false, /\.png$/)
      return images("./" + i + ".png")
    },

    getAngleImage(i) {
      const images = require.context("@/assets/img/angels/", false, /\.png$/)
      return images("./" + i + "-21.png")
    },

    openUrl(url) {
      window.open(url, "_blank")
    }
  },

  metaInfo() {
    return {
      title: this.title,
      meta: [
        { property: "og:title", content: this.title },
        { property: "og:site_name", content: this.title },
      ],
    }
  },
}
</script>

<style lang="scss" scoped>
.about-us {
  background-color: #EEF6F7;

  h1 {
    font-family: "Figtree-Bold", Helvetica, sans-serif, serif;
    font-weight: 500;
    color: var(--primary-color);
  }

  p {
    color: var(--primary-color);
  }

  .intro-content {
    margin-top: -20px;
    background-size: auto 100%;
    background-image: url('~@/assets/img/about-pattern-right.png');
    background-repeat: no-repeat;
    background-position: right;
    min-height: 200px;
    padding: 20px 150px 30px 150px;

    @media screen and (max-width: 992px) {
      padding: 10px 10px 30px 10px;
    }
  }

  .comments-content {
    background-color: #b6dade;
    padding: 10px 150px 40px 150px;

    @media screen and (max-width: 992px) {
      padding: 10px;
    }

    .avatar-frame {
      width: 100%;
      height: auto;
      padding: 3px;
      border-radius: 10px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 10px;
      }
    }
  }

  .our-mission-card {
    padding: 15px 25px 30px;
    margin-top: 10px;
    height: 90%;
    background-color: var(--primary-background-color);
    border-radius: 16px;

    h5 {
      margin-top: 5px;
      margin-bottom: 5px;
      font-size: 18px;
      color: var(--primary-color);
    }

    p {
      font-size: 16px;
    }
  }

  .partners-content {
    padding: 20px 150px 10px 150px;

    @media screen and (max-width: 992px) {
      padding: 10px;
    }

    .title {
      font-weight: bold;
      font-size: 30px;
    }

    .item-container {
      border: 1px solid #348685;
      border-radius: 20px;
    }

    img {
      max-width: 100%;
      // height: 100%;
    }
  }

  .join-content {
    background-size: auto 100%;
    background-image: url('~@/assets/img/about-pattern-left.png');
    background-repeat: no-repeat;
    background-position: left;
    min-height: 250px;
    padding: 20px 150px 10px 150px;

    @media screen and (max-width: 992px) {
      padding: 10px;
    }

    .bg-container {
      background-color: var(--primary-background-darker-color);
      border-radius: 20px;
      padding: 30px 10px 40px 10px;

      p {
        padding: 0 20px 0 20px;
      }

      @media (min-width: 992px) {
        p {
          padding: 0 80px 0 60px;
        }
      }

      .careers {
        padding: 8px 20px 8px 20px;
        background-color: var(--primary-color);
        border-radius: 8px;
        color: white;
        font-weight: 500;

        &:hover {
          background-color: var(--primary-hover-color);
        }
      }
    }
  }
}
</style>
