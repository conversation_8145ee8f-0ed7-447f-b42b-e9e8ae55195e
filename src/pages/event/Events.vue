<template>
  <div class="events">
    <b-container>
      <p class="font-28 font-weight-bold mt-3">{{ $t('EVENT.EVENTS') }}</p>
      <b-row v-if="events && events.data && events.data.length" class="events-content">
        <b-col cols="12" v-for="event in events.data" :key="event.id">
          <event-card :event-data="event"/>
        </b-col>
        <b-col cols="12">
          <b-pagination v-if="events.total" align="right" v-model="events.current_page"
                        :total-rows="events.total" :per-page="events.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
        </b-col>
      </b-row>
    </b-container>
  </div>
</template>

<script>
import eventsService from "../../services/events.service";
import EventCard from "../../components/Events/EventCard.vue";

export default {
  components: {
    EventCard,
  },
  data () {
    return {
      title: "Events",
      events: [],
      filters: {
        status: null,
      },
    }
  },
  async mounted () {
    await Promise.all([
      this.getEvents(1),
    ]);
  },
  // watch: {
  //   async "filters.status" () {
  //     await this.getEvents(1)
  //   },
  // },
  methods: {
    async onChangePage (page) {
      await this.getEvents(page)
    },
    async getEvents (page) {
      const filters = {
        ...this.filters,
        page,
      };
      const result = await eventsService.getEvents(filters);
      if (result) {
        this.events = result;
      }
    },
    searchTimeOut (page) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(async () => {
        await this.getEvents(page);
      }, 500);
    },
  },

  metaInfo () {
    return {
      title: this.title,
      meta: [
        { property: 'og:title', content: this.title },
        { property: 'og:site_name', content: this.title },
      ],
    };
  },
}
</script>
<style lang="scss">
.events {
  .events-content {
    padding: 24px 0;
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 30px;
    width: 100%;

    @media screen and (min-width: 950px) {
      width: 84%;
    }

    @media screen and (min-width: 1100px) {
      width: 73%;
    }
  }
}
</style>
