<template>
  <div class="event-container d-flex flex-column align-items-center" :style="eventBackgroundStyle">
    <div class="content">
      <!-- ── BACK + RULES BUTTONS ─────────────────────────────────────────────── -->
      <div class="top-bar d-flex justify-content-between mb-2">
        <b-button @click="goBack" class="btn-outline-main btn-back">
          {{ $t("common.BACK") }}
        </b-button>
        <b-button @click="showModalEventRules = true" class="btn-main btn-rules" :class="isReferralEvent ? 'btn-rules--referral' : ''">
          {{ $t("EVENT.RULES") }}
        </b-button>
      </div>

      <!-- ── EVENT “DETAIL” BANNER IMAGE ───────────────────────────────────────── -->
      <div class="detail-banner-wrapper position-relative mb-2" ref="detailBannerRef">
        <img v-if="event.image_detail" :src="getImageUrl(event.image_detail)" alt="" class="detail-banner-image w-100"/>
      </div>

      <!-- ── EVENT “SULTAN JURAGAN” ────────────────────────────────────────────── -->
      <div v-if="isOngoing && isSultanJuraganEvent && lastUpdatedTimeFormatted" class="last-updated-time">
        {{ $t("EVENT.LEADERBOARD_REFRESH_NOTE", { last_updated: lastUpdatedTimeFormatted }) }}
      </div>
      <EventSultanJuraganTabs v-if="isSultanJuraganEvent" style="margin-top: 40px; margin-bottom: 35px;"
                              :model-value="selectedSultanJuraganTab" @update:modelValue="selectedSultanJuraganTab = $event"/>

      <!-- ── TOP SCORER BANNER or TOP 3 PODIUM ──────────────────────────────────────────────────────── -->
      <div>
        <div v-if="isTopScorerTabSelected" class="top-scorer-banner-wrapper position-relative pb-4" :style="{ height: topRankingHeight + 'px' }">
          <img v-if="event.image_top_score" :src="getImageUrl(event.image_top_score)" alt="" class="top-scorer-banner-image w-100"/>
        </div>

        <div class="event-top-ranking-wrapper" :class="isReferralEvent ? 'event-top-ranking-wrapper--referral': ''">
          <EventTopRanking ref="topRankingRef" :class="{ 'off-screen': isTopScorerTabSelected }"
                           :style="{ 'visibility': isTopScorerTabSelected ? 'hidden' : 'visible' }"
                           :eventType="event.type"
                           :rank1User="firstPlace" :rank2User="secondPlace" :rank3User="thirdPlace"
                           :isWideContainer="isWideDetail" :parentWidth="detailWidth" :fillParent="true"/>
        </div>
      </div>

      <!-- ── “ADD X TOKENS TO REACH NEXT RANK” BANNER ───────────────────────────── -->
      <template v-if="isReferralEvent">
        <div class="token-gap-banner-referral align-content-center" v-if="isEnded">
          {{ $t("EVENT.EVENT_ENDED") }}
        </div>
        <div class="token-gap-banner-referral align-content-center" v-else-if="isUpcoming">
          {{ $t("EVENT.STARTING_SOON") }}
        </div>
        <div v-else-if="yourPosition?.rank && yourPosition?.rank === 1" class="token-gap-banner-referral align-content-center">
          {{ $t("EVENT.REFERRAL_LEADERBOARD_RANK1") }}
        </div>
        <div v-else-if="yourPosition?.rank && yourPosition?.rank <= event.display_top_scorer_users" class="token-gap-banner-referral align-content-center">
          {{ $t("EVENT.REFERRAL_LEADERBOARD_ON_VISIBLE") }}
        </div>
        <div v-else-if="yourPosition?.rank && yourPosition?.rank > event.display_top_scorer_users" class="token-gap-banner-referral align-content-center">
          {{ $t("EVENT.REFERRAL_LEADERBOARD_ON_NOT_VISIBLE") }}
        </div>
        <div v-else class="token-gap-banner-referral align-content-center">
          {{ $t("EVENT.REFERRAL_LEADERBOARD_NOT_ON") }}
        </div>
      </template>
      <template v-else>
        <div class="token-gap-banner align-content-center" v-if="isEnded">
          {{ $t("EVENT.EVENT_ENDED") }}
        </div>
        <div class="token-gap-banner align-content-center" v-else-if="isUpcoming">
          {{ $t("EVENT.STARTING_SOON") }}
        </div>
        <div v-else-if="yourPosition?.rank && yourPosition?.rank === 1" class="token-gap-banner align-content-center" style="white-space: pre-wrap;">
          {{ $t("EVENT.MAINTAIN_YOUR_LEAD") }}
        </div>
        <div v-else-if="isTopScorerTabSelected && yourPosition?.rank && yourPosition?.rank <= event.display_top_scorer_users"
             class="token-gap-banner align-content-center" style="white-space: pre-wrap;">
          {{ $t("EVENT.MAINTAIN_YOUR_TOP", { top_scorer: event.display_top_scorer_users }) }}
        </div>
        <div v-else-if="yourPosition?.token_joining > 0" class="token-gap-banner align-content-center">
          {{
            $t("EVENT.ADD_TOKEN_TO_JOIN_LEADERBOARD", {
              token: formatNumberIntl(yourPosition.token_joining),
              token_label: yourPosition.token_joining > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
            })
          }}
        </div>
        <div v-else-if="yourPosition?.token_gap > 0" class="token-gap-banner align-content-center">
          {{
            $t("EVENT.ADD_TOKEN_TO_NEXT_RANK", {
              token: formatNumberIntl(yourPosition.token_gap),
              token_label: yourPosition.token_gap > 1 ? this.$t('common.TOKENS').toLowerCase() : this.$t('common.TOKEN').toLowerCase()
            })
          }}
        </div>
        <div v-else class="token-gap-banner align-content-center">
          {{ $t("EVENT.JOIN_LEADERBOARD") }}
        </div>
      </template>

      <!-- ── LEADERBOARD TABLE ──────────────────────────────────────────────── -->
      <div class="leaderboard-container" :class="isReferralEvent ? 'leaderboard-container--referral': ''">
        <table class="table mb-0">
          <thead>
          <tr v-if="isReferralEvent">
            <th class="text-center font-bold" style="width: 18%;">{{ $t('EVENT.RANKING') }}</th>
            <th class="text-left font-bold" style="width: 40%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
              {{ $t('EVENT.PARTICIPANT') }}
            </th>
            <th class="text-center font-bold" style="width: 15%;">
              {{ $t('EVENT.TOTAL_INVITE') }}
            </th>
            <th class="text-center font-bold" style="width: 27%;">
              {{ $t('EVENT.TOKEN_VOLUME') }}
              <img src="@/assets/img/icons/arrow_up_green.svg" style="width: 11px; height: 10px; margin-left: 1px; margin-bottom: 2px" alt=""/>
            </th>
          </tr>
          <tr v-else-if="!isTopScorerTabSelected">
            <th class="text-center font-bold" style="width: 18%;">{{ $t('EVENT.RANKING') }}</th>
            <th class="text-left font-bold" style="width: 55%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
              {{ $t('EVENT.PARTICIPANT') }}
            </th>
            <th class="text-center font-bold" style="width: 27%;">
              {{ $t('common.TOKENS') }}
              <img src="@/assets/img/icons/arrow_up_green.svg" style="width: 11px; height: 10px; margin-left: 1px; margin-bottom: 2px" alt=""/>
            </th>
          </tr>
          <tr v-else-if="isTopScorerTabSelected">
            <th class="text-center font-bold" style="width: 40%;">{{ $t('EVENT.JURAGAN') }}</th>
            <th class="text-center font-bold" style="width: 20%;">{{ $t('EVENT.RANKING') }}</th>
            <th class="text-center font-bold" style="width: 40%;">{{ $t('EVENT.SULTAN') }}</th>
          </tr>
          </thead>
          <div v-if="!isReferralEvent" style="height: 6px; background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0));"/>
          <tbody>
          <tr v-if="!isTopScorerTabSelected" v-for="(user, index) in leaderboardRows" :key="index">
            <!-- If “user” is not null, show actual data; otherwise show placeholders -->
            <template v-if="user">
              <!-- RANK COLUMN -->
              <td class="align-middle rank-cell">
                <span v-if="user.rank === 1" class="rank-cell-top-3">🥇</span>
                <span v-else-if="user.rank === 2" class="rank-cell-top-3">🥈</span>
                <span v-else-if="user.rank === 3" class="rank-cell-top-3">🥉</span>
                <span v-else>{{ user.rank }}</span>
              </td>
              <!-- USERNAME + AVATAR COLUMN -->
              <td class="d-flex align-items-center user-cell">
                <div class="avatar-container position-relative">
                  <img v-if="user.avatar_url" :src="getImageUrl(user.avatar_url)" class="small-avatar" alt=""/>
                  <img v-else src="@/assets/img/logo_white.png" class="small-avatar p-2" alt=""/>
                  <img v-if="user.rank <= event.no_of_winners" src="@/assets/img/events/crown.svg" class="crown-icon" alt="👑"/>
                </div>
                <div class="d-flex flex-column align-items-start text-start">
                  <span class="table-username text-truncate">{{ user.username }}</span>
                  <span class="table-user-id text-truncate">ID: {{ user.uuid }}</span>
                </div>
                <img v-if="user.iso_country_code" class="flag" alt=""
                     :src="`https://flagcdn.com/w20/${user.iso_country_code.toLowerCase()}.png`"
                     :srcset="`https://flagcdn.com/w40/${user.iso_country_code.toLowerCase()}.png 2x`"/>
              </td>
              <template v-if="isReferralEvent">
                <td class="align-middle tokens-cell" style="width: 15%">{{ user.referees }}</td>
                <td class="align-middle tokens-cell">{{ user.total_token_gap }}</td>
              </template>
              <template v-else>
                <!-- TOKENS COLUMN -->
                <td class="align-middle tokens-cell">{{ user.token_gap }}</td>
              </template>
            </template>
            <template v-else>
              <!-- RANK COLUMN: medal for 0/1/2, otherwise index+1 -->
              <td class="align-middle rank-cell">
                <span v-if="index === 0" class="rank-cell-top-3">🥇</span>
                <span v-else-if="index === 1" class="rank-cell-top-3">🥈</span>
                <span v-else-if="index === 2" class="rank-cell-top-3">🥉</span>
                <span v-else>{{ index + 1 }}</span>
              </td>
              <!-- AVATAR + “—” TEXT -->
              <td class="d-flex align-items-center user-cell">
                <div class="avatar-container position-relative">
                  <img src="@/assets/img/logo_white.png" class="small-avatar p-2" alt=""/>
                  <img v-if="(index + 1) <= event.no_of_winners" src="@/assets/img/events/crown.svg" class="crown-icon" alt="👑"/>
                </div>
                <div class="d-flex flex-column align-items-start text-start">
                  <span class="table-username placeholder-text">Username</span>
                  <span class="table-user-id placeholder-text">ID: xxxxxxxxxx</span>
                </div>
                <!-- <img class="flag" alt="" src="@/assets/img/logo_white.png" style="padding: 3px"/>-->
              </td>
              <template v-if="isReferralEvent">
                <td class="align-middle tokens-cell" style="width: 15%">-</td>
                <td class="align-middle tokens-cell">-</td>
              </template>
              <template v-else>
                <!-- TOKENS COLUMN: dash -->
                <td class="align-middle tokens-cell">-</td>
              </template>
            </template>
          </tr>
          <tr v-if="isTopScorerTabSelected" v-for="(row, index) in topScorerLeaderboardRows" :key="index">
            <!-- Juragan -->
            <td>
              <div class="d-flex justify-content-end align-items-center">
                <template v-if="row.juraganUser">
                  <div class="d-flex flex-column align-items-end">
                    <span class="table-username text-truncate">{{ row.juraganUser.username }}</span>
                    <span class="table-user-id text-truncate">ID: {{ row.juraganUser.uuid }}</span>
                  </div>
                  <div class="avatar-container position-relative">
                    <img v-if="row.juraganUser.avatar_url" :src="getImageUrl(row.juraganUser.avatar_url)" class="small-avatar small-avatar-margin-left ml-2" alt=""/>
                    <img v-else src="@/assets/img/logo_white.png" class="small-avatar small-avatar-margin-left  ml-2 p-2" alt=""/>
                    <img v-if="row.juraganUser.rank <= event.no_of_winners" src="@/assets/img/events/crown.svg" class="crown-icon" alt="👑"/>
                  </div>
                </template>
                <template v-else>
                  <div class="d-flex flex-column align-items-end">
                    <span class="table-username placeholder-text">Username</span>
                    <span class="table-user-id placeholder-text">XXXXXXXXXX</span>
                  </div>
                  <div class="avatar-container position-relative">
                    <img src="@/assets/img/logo_white.png" class="small-avatar small-avatar-margin-left p-2" alt=""/>
                    <img v-if="(index + 1) <= event.no_of_winners" src="@/assets/img/events/crown.svg" class="crown-icon" alt="👑"/>
                  </div>
                </template>
              </div>
            </td>
            <!-- Rank -->
            <td class="align-middle rank-cell text-center">
              <span v-if="index === 0" class="rank-cell-top-3">🥇</span>
              <span v-else-if="index === 1" class="rank-cell-top-3">🥈</span>
              <span v-else-if="index === 2" class="rank-cell-top-3">🥉</span>
              <span v-else>{{ index + 1 }}</span>
            </td>
            <!-- Sultan -->
            <td>
              <div class="d-flex justify-content-start align-items-center">
                <template v-if="row.sultanUser">
                  <div class="avatar-container position-relative">
                    <img v-if="row.sultanUser.avatar_url" :src="getImageUrl(row.sultanUser.avatar_url)" class="small-avatar" alt=""/>
                    <img v-else src="@/assets/img/logo_white.png" class="small-avatar p-2" alt=""/>
                    <img v-if="row.sultanUser.rank <= event.no_of_winners" src="@/assets/img/events/crown.svg" class="crown-icon" alt="👑"/>
                  </div>
                  <div class="d-flex flex-column align-items-start">
                    <span class="table-username text-truncate">{{ row.sultanUser.username }}</span>
                    <span class="table-user-id text-truncate">ID: {{ row.sultanUser.uuid }}</span>
                  </div>
                </template>
                <template v-else>
                  <div class="avatar-container position-relative">
                    <img src="@/assets/img/logo_white.png" class="small-avatar p-2" alt=""/>
                    <img v-if="(index + 1) <= event.no_of_winners" src="@/assets/img/events/crown.svg" class="crown-icon" alt="👑"/>
                  </div>
                  <div class="d-flex flex-column align-items-start">
                    <span class="table-username placeholder-text">Username</span>
                    <span class="table-user-id placeholder-text">XXXXXXXXXX</span>
                  </div>
                </template>
              </div>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- ── “MY POSITION” BAR (avatar + username/ID grouped, rank on left) ───────────────────── -->
      <div v-if="isReferralEvent" class="leaderboard-container leaderboard-container--referral my-position-bar">
        <table class="table mb-0">
          <tbody>
          <tr>
            <!-- RANK COLUMN -->
            <td class="align-middle rank-cell">
              <span v-if="yourPosition?.rank === 1" class="rank-cell-top-3">🥇</span>
              <span v-else-if="yourPosition?.rank === 2" class="rank-cell-top-3">🥈</span>
              <span v-else-if="yourPosition?.rank === 3" class="rank-cell-top-3">🥉</span>
              <span v-else>{{ yourPosition?.rank ?? "N/A" }}</span>
            </td>
            <!-- USERNAME + AVATAR COLUMN -->
            <td class="d-flex align-items-center user-cell" style="margin-left: -4px">
              <div class="avatar-container position-relative">
                <img v-if="yourPosition?.avatar_url || avatarUrl" :src="getImageUrl(yourPosition?.avatar_url || avatarUrl)" class="small-avatar" alt=""/>
                <img v-else src="@/assets/img/logo_white.png" class="small-avatar p-2" alt=""/>
                <img v-if="yourPosition?.rank && yourPosition?.rank <= event.no_of_winners" src="@/assets/img/events/crown.svg" class="crown-icon" alt="👑"/>
              </div>
              <div class="d-flex flex-column align-items-start text-start">
                <span class="table-username text-truncate">{{ yourPosition?.username ?? userProfile?.name ?? "Username" }}</span>
                <span class="table-user-id text-truncate">ID: {{ yourPosition?.uuid ?? userProfile?.uuid ?? "xxxxxxxxxx" }}</span>
              </div>
              <img v-if="yourPosition?.iso_country_code || userProfile?.iso_country_code" class="flag" alt=""
                   :src="`https://flagcdn.com/w20/${yourPosition.iso_country_code.toLowerCase() ?? userProfile.iso_country_code.toLowerCase()}.png`"
                   :srcset="`https://flagcdn.com/w40/${yourPosition.iso_country_code.toLowerCase() ?? userProfile.iso_country_code.toLowerCase()}.png 2x`"/>
            </td>
            <td class="align-middle tokens-cell" style="width: 15%">{{ yourPosition?.referees ?? "-" }}</td>
            <td class="align-middle tokens-cell">{{ yourPosition?.total_token_gap ?? "-" }}</td>
          </tr>
          </tbody>
        </table>
      </div>
      <template v-else>
        <div v-if="yourPosition && isSultanJuraganEvent  && ((isInSultanTeam && !isSultanTabSelected) || (!isInSultanTeam && !isJuraganTabSelected))"
             class="my-position-bar d-flex align-items-center justify-content-center click-able" @click="viewMyLeaderboard">
          <p class="banner pt-2 pb-2 " style="text-transform: uppercase;">
            {{ $t("EVENT.YOU_ARE_ON_TEAM", { team: isInSultanTeam ? $t('EVENT.SULTAN') : $t('EVENT.JURAGAN') }) }}
          </p>
        </div>
        <div v-else-if="yourPosition && yourPosition.rank" class="my-position-bar d-flex align-items-center justify-content-between">
          <div class="rank-left text-white">
            <span class="rank-number">{{ yourPosition?.rank ?? 'N/A' }}</span>
          </div>
          <div class="user-info d-flex align-items-center">
            <div class="avatar-wrapper">
              <img :src="yourPosition?.avatar_url ? getImageUrl(yourPosition?.avatar_url) : avatarUrl" class="avatar" alt=""/>
            </div>
            <div class="info-text text-left">
              <div class="username">{{ yourPosition?.username ?? userProfile?.name ?? "" }}</div>
              <div class="user-id">ID: {{ yourPosition?.uuid ?? userProfile?.uuid ?? "" }}</div>
            </div>
          </div>
          <div class="tokens-right d-flex align-items-center">
            <span class="token-count">{{ yourPosition?.token_gap ?? '0' }}</span>
            <img src="@/assets/img/icons/star.svg" alt="★" class="star-icon ms-2"/>
          </div>
        </div>
        <div v-else-if="isEnded" class="my-position-bar d-flex align-items-center justify-content-center click-able" @click="goToEvents">
          <p class="banner pt-2 pb-2">👉 {{ $t("EVENT.READ_FOR_NEXT_CHALLENGE") }}</p>
        </div>
        <div v-else-if="isUpcoming" class="my-position-bar d-flex align-items-center justify-content-center click-able">
          <p class="banner pt-2 pb-2">👉 {{ $t("EVENT.STAY_TUNE") }}</p>
        </div>
        <div v-else class="my-position-bar d-flex align-items-center justify-content-center click-able" @click="goToRegister">
          <p class="banner pt-2 pb-2">👉 {{ $t("LANDING.BUY_PROPERTY_NOW") }}!</p>
        </div>
      </template>

      <ModalEventRules :event="this.event" :show="showModalEventRules" @on-close="showModalEventRules = false"/>
    </div>
  </div>
</template>

<script>

import { formatNumberIntl, urlImage } from '@/helpers/common';
import { gtmTrackEvent } from "@/helpers/gtm";
import { GTM_EVENT_NAMES } from "@/constants/gtm";
import ModalEventRules from "@/components/Events/ModalEventRules.vue";
import EventTopRanking from '@/components/Events/EventTopRanking.vue';
import EventSultanJuraganTabs, { EVENT_SULTAN_JURAGAN_KEYS } from '@/components/Events/EventSultanJuraganTabs.vue';
import { EVENT_STATUS, EVENT_TYPES } from "@/constants/constants";
import eventsService from '@/services/events.service';

export default {
  name: 'EventDetail',
  components: {
    ModalEventRules,
    EventTopRanking,
    EventSultanJuraganTabs
  },
  data() {
    return {
      eventUuid: this.$route.params.uuid,
      event: { image_detail: null, image_top_score: null },
      ranks: [],
      sultanRanks: [],
      juraganRanks: [],
      isInSultanTeam: false,
      lastUpdatedTime: null, //seconds since the Unix Epoch
      currentUserUuid: this.$store.getters.userProfile?.uuid || this.$route.query.user_uuid || null,
      // For measuring detail banner
      detailWidth: 0,
      isWideDetail: false,
      topRankingHeight: 238,
      detailBreakpoint: 800,
      showModalEventRules: false,
      selectedSultanJuraganTab: EVENT_SULTAN_JURAGAN_KEYS.TOP_SCORER,
    };
  },
  computed: {
    eventBackgroundStyle() {
      if (this.event && this.event.bg_image_detail_web) {
        return {
          backgroundImage: `url('${this.getImageUrl(this.event.bg_image_detail_web)}')`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center center',
          backgroundSize: 'cover',
        }
      } else if (this.$store.getters.configs && this.$store.getters.configs.event_detail_background_url_web) {
        return {
          backgroundImage: `url('${this.$store.getters.configs.event_detail_background_url_web}')`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center center',
          backgroundSize: 'cover',
        }
      }
      return {}
    },
    userProfile() {
      return this.$store.getters.userProfile;
    },
    avatarUrl() {
      const url = this.userProfile && this.userProfile.avatar_url || ""
      return urlImage({ image: url })
    },
    // Pull out the top 3 by rank === 1,2,3
    firstPlace() {
      if (this.isSultanJuraganEvent) {
        if (this.isSultanTabSelected) {
          return this.sultanRanks.find(r => r.rank === 1) || null;
        } else if (this.isJuraganTabSelected) {
          return this.juraganRanks.find(r => r.rank === 1) || null;
        }
      } else {
        return this.ranks.find(r => r.rank === 1) || null;
      }
      return null
    },
    secondPlace() {
      if (this.isSultanJuraganEvent) {
        if (this.isSultanTabSelected) {
          return this.sultanRanks.find(r => r.rank === 2) || null;
        } else if (this.isJuraganTabSelected) {
          return this.juraganRanks.find(r => r.rank === 2) || null;
        }
      } else {
        return this.ranks.find(r => r.rank === 2) || null;
      }
      return null
    },
    thirdPlace() {
      if (this.isSultanJuraganEvent) {
        if (this.isSultanTabSelected) {
          return this.sultanRanks.find(r => r.rank === 3) || null;
        } else if (this.isJuraganTabSelected) {
          return this.juraganRanks.find(r => r.rank === 3) || null;
        }
      } else {
        return this.ranks.find(r => r.rank === 3) || null;
      }
      return null
    },
    leaderboardRows() {
      let source = [];
      if (this.isSultanJuraganEvent) {
        if (this.isSultanTabSelected) {
          source = this.sultanRanks
        } else if (this.isJuraganTabSelected) {
          source = this.juraganRanks
        }
      } else {
        source = this.ranks
      }
      const valid = source.filter(item => item && item.rank != null)
      const result = [...valid];
      while (result.length < 10) {
        result.push(null);
      }
      return result;
    },
    topScorerLeaderboardRows() {
      const result = [];
      if (this.isSultanJuraganEvent) {
        const sultan = this.sultanRanks.filter(item => item && item.rank != null)
        const juragan = this.juraganRanks.filter(item => item && item.rank != null)
        const maxLength = Math.max(this.event.display_top_scorer_users, 10)
        for (let i = 0; i < maxLength; i++) {
          result.push({
            sultanUser: sultan[i] || null,
            juraganUser: juragan[i] || null
          });
        }
      }
      return result;
    },
    // “Your” position so we know token_gap & rank
    yourPosition() {
      if (this.isSultanJuraganEvent) {
        if (this.isInSultanTeam) {
          return this.sultanRanks.find(r => r.uuid === this.currentUserUuid) || null
        } else {
          return this.juraganRanks.find(r => r.uuid === this.currentUserUuid) || null
        }
      } else {
        return this.ranks.find(r => r.uuid === this.currentUserUuid) || null
      }
    },
    isUpcoming() {
      return this.event && this.event.status === EVENT_STATUS.UPCOMING
    },
    isOngoing() {
      return this.event && this.event.status === EVENT_STATUS.ONGOING
    },
    isEnded() {
      return this.event && this.event.status === EVENT_STATUS.ENDED
    },
    isReferralEvent() {
      return this.event && this.event.type === EVENT_TYPES.TYPE_REFERRAL
    },
    isSultanJuraganEvent() {
      return this.event && this.event.type === EVENT_TYPES.TYPE_SULTAN_JURAGAN
    },
    isSultanTabSelected() {
      return this.isSultanJuraganEvent && this.selectedSultanJuraganTab === EVENT_SULTAN_JURAGAN_KEYS.SULTAN
    },
    isJuraganTabSelected() {
      return this.isSultanJuraganEvent && this.selectedSultanJuraganTab === EVENT_SULTAN_JURAGAN_KEYS.JURAGAN
    },
    isTopScorerTabSelected() {
      return this.isSultanJuraganEvent && this.selectedSultanJuraganTab === EVENT_SULTAN_JURAGAN_KEYS.TOP_SCORER
    },
    lastUpdatedTimeFormatted() {
      if (!this.lastUpdatedTime) return null

      const date = new Date(this.lastUpdatedTime * 1000)
      const locale = this.$i18n.locale || 'en-US'

      if (locale.startsWith('id')) {
        // Indonesian: 29 Juli 2025, pukul 14:05 WIB
        const parts = Object.fromEntries(
          new Intl.DateTimeFormat('id-ID', {
            year: 'numeric', month: 'long', day: 'numeric',
            hour: '2-digit', minute: '2-digit', hour12: false
          }).formatToParts(date).map(p => [p.type, p.value])
        )
        return `${parts.day} ${parts.month} ${parts.year}, pukul ${parts.hour}:${parts.minute} WIB`
      } else {
        // English: July 29, 2025, 2:05 PM
        const parts = Object.fromEntries(
          new Intl.DateTimeFormat('en-US', {
            year: 'numeric', month: 'long', day: 'numeric',
            hour: 'numeric', minute: '2-digit', hour12: true
          }).formatToParts(date).map(p => [p.type, p.value])
        )
        return `${parts.month} ${parts.day}, ${parts.year}, ${parts.hour}:${parts.minute} ${parts.dayPeriod}`
      }
    }
  },
  async mounted() {
    // 1) Fetch initial data
    await this.getEventDetail(true);

    // 2) Poll every 10 seconds without loading spinner
    this._pollInterval = setInterval(() => {
      this.getEventDetail(false);
    }, 10_000);

    // 3) Wait for detail banner <div> to render, then measure
    this.$nextTick(() => {
      const el = this.$refs.detailBannerRef;
      if (el) {
        this.detailWidth = el.offsetWidth;
        this.isWideDetail = this.detailWidth >= this.detailBreakpoint;
        // Watch for future size changes
        this._resizeObserver = new ResizeObserver(entries => {
          for (let entry of entries) {
            const newWidth = entry.contentRect.width;
            window.requestAnimationFrame(() => {
              if (newWidth !== this.detailWidth) {
                this.detailWidth = entry.contentRect.width;
                this.isWideDetail = this.detailWidth >= this.detailBreakpoint;
              }
            });
          }
        });
        this._resizeObserver.observe(el);
      }

      const topRankingEl = this.$refs.topRankingRef?.$refs?.rankContainerRef;
      if (topRankingEl instanceof Element) {
        this.topRankingHeight = el.offsetHeight;
        this._rankingResizeObserver = new ResizeObserver(entries => {
          for (let entry of entries) {
            const newHeight = entry.contentRect.height;
            window.requestAnimationFrame(() => {
              if (newHeight !== this.topRankingHeight) {
                this.topRankingHeight = newHeight;
              }
            });
          }
        });
        this._rankingResizeObserver.observe(topRankingEl);
      }
    });
  },
  beforeDestroy() {
    if (this._pollInterval) {
      clearInterval(this._pollInterval);
    }
    if (this._resizeObserver) {
      this._resizeObserver.disconnect();
    }
    if (this._rankingResizeObserver) {
      this._rankingResizeObserver.disconnect();
    }
  },
  methods: {
    formatNumberIntl,
    async getEventDetail(showLoading = true) {
      try {
        const res = await eventsService.getEventDetail(this.eventUuid, showLoading);
        if (res && res.event) {
          this.event = res.event;
          this.ranks = res.data || [];
          this.sultanRanks = res.sultan_ranks || [];
          this.juraganRanks = res.juragan_ranks || [];
          this.isInSultanTeam = res.is_in_sultan || false;
          this.lastUpdatedTime = res.last_updated || null;
        }
      } catch (err) {
        console.error('Error fetching event detail:', err);
      }
    },
    getImageUrl(path) {
      return urlImage({ image: path });
    },
    goBack() {
      this.$router.back();
    },
    async goToEvents() {
      await this.$router.push({ name: "events" })
    },
    async goToRegister() {
      gtmTrackEvent({ event: GTM_EVENT_NAMES.GET_STARTED })
      await this.$router.push({ name: "register" })
    },
    async viewMyLeaderboard() {
      if (this.isSultanJuraganEvent) {
        if (this.isInSultanTeam) {
          this.selectedSultanJuraganTab = EVENT_SULTAN_JURAGAN_KEYS.SULTAN
        } else {
          this.selectedSultanJuraganTab = EVENT_SULTAN_JURAGAN_KEYS.JURAGAN
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>

.event-container {
  position: relative;
  min-height: 100vh;
  margin-top: -65px;
  background-color: var(--primary-hover-color);
  font-family: "NewAcuminVariableConcept", Helvetica, sans-serif, serif;

  .content {
    margin: 0 auto;
    overflow: hidden;
    text-align: center;

    @media screen and (min-width: 400px) {
      width: 100%;
    }

    @media screen and (min-width: 550px) {
      width: 86%;
    }

    @media screen and (min-width: 700px) {
      width: 68%;
    }

    @media screen and (min-width: 950px) {
      width: 52%;
    }

    @media screen and (min-width: 1100px) {
      width: 46%;
    }

    @media screen and (min-width: 1250px) {
      width: 40%;
    }

    @media screen and (min-width: 1400px) {
      width: 36%;
    }

    @media screen and (min-width: 1550px) {
      width: 33%;
    }

    @media screen and (min-width: 1700px) {
      width: 30%;
    }
  }
}

.event-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.event-container > .content {
  position: relative;
  z-index: 1;
}

/* ─────────────────────────────────────────────────
   ADDITIONAL STYLES FOR THE INNER CONTENT
   (only inside the .content area—your original CSS is untouched)
   ───────────────────────────────────────────────── */

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .btn-back,
  .btn-rules {
    font-weight: bold !important;
    padding: 0.5rem 1.2rem;
    border-radius: 0.5rem;
    cursor: pointer;

    &--referral {
      background: #E944D3 !important;

      &:hover, &:active, &:focus, &.is-selected {
        background: #d83dca !important;
      }
    }
  }
}

.detail-banner-wrapper {
  position: relative;
  width: 96%;
  overflow: hidden;
  border-radius: 1rem;
  margin-left: auto;
  margin-right: auto;
}

.detail-banner-image {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 1rem;
}

.last-updated-time {
  margin-top: 40px;
  margin-bottom: -15px;
  display: inline-block;
  padding: 0.5rem 1rem;
  font-size: 16px;
  color: white;
  white-space: pre-wrap;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);

  @media only screen and (max-width: 500px) {
    font-size: 14px;
    padding: 0.4rem 0.8rem;
  }
}

.event-top-ranking-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.event-top-ranking-wrapper--referral {
  position: relative;
  overflow: hidden;

  > * {
    position: relative;
    z-index: 1;
  }

  &::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(46, 7, 79, 0) 0%, #2E074F 100%);
    pointer-events: none;
    z-index: 0;

    aspect-ratio: 4.5;
  }

  @supports not (aspect-ratio: 1) {
    &::after {
      height: 0;
      padding-top: 50%;
    }
  }
}


.top-scorer-banner-wrapper {
  position: relative;
  width: 96%;
  overflow: hidden;
  border-radius: 1rem;
  margin-left: auto;
  margin-right: auto;
}

.top-scorer-banner-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  border-radius: 1rem;
}

.token-gap-banner {
  margin: 0 auto;
  min-height: 65px;
  background: linear-gradient(236.16deg, #31E3E3 -52.92%, #148989 122.21%);
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
  border-radius: 0.8rem;
  padding: 0.5rem 1.8rem;
  color: white;
  font-size: 1rem;
  font-weight: 600;

  @media only screen and (max-width: 500px) {
    min-height: 55px;
    font-size: 0.85rem;
  }
}

.token-gap-banner-referral {
  min-height: 100px;
  font-family: "PressStart2P", Helvetica, sans-serif, serif !important;
  font-weight: 400;
  font-size: 10px;
  background: #2E074F;
  padding: 30px 25px;
  color: white;
  text-shadow: 0 0 28px rgba(255, 255, 255, 0.91);

  @media only screen and (max-width: 500px) {
    min-height: 85px;
    font-size: 8.5px;
  }
}

/* ────────────────────────────────────────────────────────────────
   LEADERBOARD CONTAINER & TABLE (always 10 visible rows)
   ──────────────────────────────────────────────────────────────── */
.leaderboard-container {
  margin-left: 1rem;
  margin-right: 1rem;
  background-color: white;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

  /* Force the <table> header to stay put, and make <tbody> scroll */
  .table {
    width: 100%;
    border-collapse: collapse;
  }

  /* Freeze thead, allow tbody to scroll */
  .table thead {
    display: table;
    width: 100%;
    table-layout: fixed; /* ensures columns line up */
    background-color: white;
    z-index: 2;
  }

  .table tbody {
    display: block;
    max-height: 10 * 65px; /* 10 rows x ~56px per row = 560px */
    overflow-y: auto;
    width: 100%;
  }

  /* Make each row behave like a table-row (so columns line up) */
  .table tbody tr {
    display: table;
    table-layout: fixed;
    width: 100%;
  }

  /* Remove default Bootstrap borders */
  .table th,
  .table td {
    border: none;
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
  }

  /* ── THEAD STYLING ── */
  .table thead th {
    color: #0F6A64;
    font-weight: 700;
    font-size: 15px;
    text-align: left;
    padding-bottom: 0.5rem;
    padding-top: 0.5rem;

    @media only screen and (max-width: 500px) {
      font-size: 12px;
    }
  }

  /* ── TBODY ROWS ── */
  .table tbody tr {
    background-color: white;
    transition: background-color 0.2s ease;
  }

  .table tbody tr:hover {
    background-color: #f5f5f5;
  }

  &.leaderboard-container--referral {
    margin-left: 0;
    margin-right: 0;
    padding: 0;
    background-color: transparent;

    .table {
      border-collapse: separate;
      border-spacing: 0;
    }

    .table thead {
      background: transparent;
      z-index: 2;
    }

    .table thead th {
      background: #2E074F;
      color: #fff;
      border: 0;
      background-clip: padding-box;
    }

    .table thead th:first-child {
      border-bottom-left-radius: 12px;
    }

    .table thead th:last-child {
      border-bottom-right-radius: 12px;
    }

    &.my-position-bar {
      border-left: 4px solid rgba(195, 72, 151, 1);
      border-radius: 0 0 8px 8px;
      margin-bottom: 25px;

      .table tbody tr {
        background: linear-gradient(90deg, #FFCEED 0.64%, rgba(253, 227, 244, 0.971301) 3.49%, rgba(253, 227, 244, 0) 100%);
        box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.15);
      }
    }
  }

  /* ── RANK CELL (LEFT COLUMN) ── */
  .rank-cell {
    width: 18%;
    text-align: center;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 14px;

    @media only screen and (max-width: 500px) {
      font-size: 12px;
    }

    .rank-cell-top-3 {
      font-size: 30px;

      @media only screen and (max-width: 500px) {
        font-size: 26px;
      }
    }
  }

  /* Keep emojis vertically aligned */
  .rank-cell span {
    line-height: 1;
  }

  /* ── USER CELL (MIDDLE) ── */
  .user-cell {
    width: 100%;

    img.flag {
      width: 22px;
      height: 22px;
      margin-left: auto;
      background-color: var(--primary-color);
      border: 1px solid var(--primary-color);
      border-radius: 50%;

      @media only screen and (max-width: 500px) {
        width: 18px;
        height: 18px;
      }
    }
  }

  .avatar-container {
    position: relative;
    display: inline-block;

    .small-avatar {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 50%;
      background-color: var(--primary-color);
      margin-right: 0.5rem;

      @media only screen and (max-width: 500px) {
        width: 2.2rem;
        height: 2.2rem;
        margin-right: 0.45rem;
      }

      @media only screen and (max-width: 400px) {
        width: 1.8rem;
        height: 1.8rem;
        margin-right: 0.35rem;
      }
    }

    .small-avatar-margin-left {
      margin-left: 0.5rem;

      @media only screen and (max-width: 500px) {
        margin-left: 0.45rem;
      }

      @media only screen and (max-width: 400px) {
        margin-left: 0.35rem;
      }
    }

    .crown-icon {
      position: absolute;
      width: 16px;
      height: 16px;
      bottom: -2px;
      right: 6px;
      z-index: 10;

      @media only screen and (max-width: 500px) {
        width: 13.5px;
        height: 13.5px;
        bottom: -1px;
        right: 4.5px;
      }

      @media only screen and (max-width: 400px) {
        width: 11.5px;
        height: 11.5px;
      }
    }
  }

  .table-username {
    color: var(--primary-color);
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;

    @media only screen and (max-width: 500px) {
      font-size: 11px;
    }

    @media only screen and (max-width: 400px) {
      font-size: 10px;
    }
  }

  .table-user-id {
    color: var(--primary-color);
    font-size: 11px;
    font-weight: normal;
    opacity: 0.85;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;

    @media only screen and (max-width: 500px) {
      font-size: 10px
    }

    @media only screen and (max-width: 400px) {
      font-size: 9px
    }
  }

  .user-cell > .d-flex {
    display: flex; /* already set by “d-flex” */
    align-items: center; /* vertically centered */
    column-gap: 0.25rem; /* 4px between avatar & text */
  }

  /* ── TOKENS CELL (RIGHT COLUMN) ── */
  .tokens-cell {
    width: 27%;
    text-align: center;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--primary-color);

    @media only screen and (max-width: 500px) {
      font-size: 0.65rem;
    }
  }

  /* ── PLACEHOLDER TEXT & AVATAR ── */
  .placeholder-text {
    color: rgba(0, 0, 0, 0.2);
    font-style: italic;
    line-height: 1.2;
  }
}

.my-position-bar {
  background: #038D95;
  border-radius: 0.8rem;
  padding: 0.8rem 1.8rem;
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.my-position-bar .banner {
  font-size: 1.25rem;

  @media only screen and (max-width: 500px) {
    font-size: 1.05rem;
  }
}

/* Leftmost: rank number */
.my-position-bar .rank-left {
  flex: 0 0 auto; /* do not shrink */
}

.my-position-bar .rank-number {
  font-size: 1.1rem;
  font-weight: 700;
  line-height: 1;
  white-space: nowrap;

  @media only screen and (max-width: 500px) {
    font-size: 0.9rem;
  }
}

/* Middle-left: avatar + username/ID */
.my-position-bar .user-info {
  display: flex;
  align-items: center;
  flex: 1 1 auto; /* allow to grow slightly if needed */
}

/* Avatar wrapper fixed size */
.my-position-bar .avatar-wrapper {
  width: 3.6rem;
  height: 3.6rem;
  position: relative;
  flex: 0 0 auto;
  margin-left: 20px;
  margin-right: 8px;
}

/* Actual avatar image */
.my-position-bar .avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  background-color: #E7F6F6;
  border: 2px solid #ececec;
}

/* Username / ID text (stacked vertically) */
.my-position-bar .info-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.my-position-bar .info-text .username {
  font-size: 1rem;
  font-weight: bold;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @media only screen and (max-width: 500px) {
    font-size: 0.85rem;
  }
}

.my-position-bar .info-text .user-id {
  font-size: 0.8rem;
  font-weight: 400;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @media only screen and (max-width: 500px) {
    font-size: 0.65rem;
  }
}

/* Right: token count + star */
.my-position-bar .tokens-right {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

.my-position-bar .tokens-right .token-count {
  font-size: 1.1rem;
  font-weight: 700;
  line-height: 1;

  @media only screen and (max-width: 500px) {
    font-size: 0.9rem;
  }
}

.my-position-bar .star-icon {
  width: 1.2rem;
  height: 1.2rem;
  object-fit: contain;
  margin-left: 0.25rem; /* same as .ms-1 */
}


/* Utility to vertically space things if needed */
.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}
</style>
