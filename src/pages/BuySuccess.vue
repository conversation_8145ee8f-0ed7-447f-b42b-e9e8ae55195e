<template>
    <div class="buy-success-form" v-if="property">
        <b-container>
            <b-row class="text-center" align-h="center">
                <b-col>
                    <p class="font-28 font-weight-bold">{{ $t("PAYMENT.SUCCESS") }}</p>
                    <p class="font-18 mt-1">{{ ownedMessage }}</p>
                    <p class="font-18 mt-1">{{ property.name }} ({{ property.metadata.address }})</p>
                    <b-img class="mt-3 mt-lg-4 mb-3 mb-lg-4" :src="getAvatar(property.images)" fluid
                        blank-src="property image" width="450" height="300" />
                    <br>
                    <p class="font-18 mt-1">{{ $t("VIRTUAL_BALANCE.EXPIRES_ON") }} <b>{{ expirationTime }}</b></p>
                </b-col>
            </b-row>
        </b-container>
        <div v-if="showPopup" class="popup d-flex flex-column align-items-center justify-content-center">
            <div class="content-container text-center ml-2 mr-2">
                <p class="font-22 mt-1 mb-3 font-weight-bold">{{ $t('VIRTUAL_BALANCE.REGISTER_TO_BRING') }}</p>
                <b-button id="btn_buySuccess_GetStarted" class="btn-main mt-3" variant="none" @click="goToSignUp">
                    {{ $t("AUTH.GET_STARTED") }}
                </b-button>
            </div>
        </div>
    </div>
</template>

<script>
import guestService from "@/services/guest.service"
import { formatNumberIntl, urlImage } from "@/helpers/common"
import moment from "moment"
import { gtmTrackEvent } from "../helpers/gtm"
import { GTM_EVENT_NAMES } from "../constants/gtm"

export default {
    data() {
        return {
            title: "Buy Success",
            trxId: this.$route.query.trx_id,
            property: null,
            transaction: null,
            showPopup: false,
        }
    },
    async mounted() {
        await this.getTransactionBySecondaryId()
    },
    methods: {
        async getTransactionBySecondaryId() {
            if (!this.trxId) {
                return
            }
            let response = await guestService.getTransactionBySecondaryId(this.trxId)
            if (response.data) {
                this.transaction = response.data
                this.property = response.data.property

                this.$confetti.start({
                    particles: [
                        { type: "heart", },
                        { type: "circle", },
                        { type: "rect", },
                    ],
                    particlesPerFrame: 3
                })
                setTimeout(() => this.$confetti.stop(), 3000)
                setTimeout(() => this.showPopup = true, 4000)
            }
        },
        getAvatar(images) {
            if (images && images.length) {
                return urlImage(images[0])
            }
            return ""
        },
        async goToSignUp() {
            gtmTrackEvent({
                event: GTM_EVENT_NAMES.GET_STARTED
            })
            await this.$router.push({ name: "register" })
        },
    },
    metaInfo() {
        return {
            title: this.title,
            meta: [
                { property: "og:title", content: this.title },
                { property: "og:site_name", content: this.title },
            ],
        }
    },
    computed: {
        expirationTime() {
            return this.transaction && moment(this.transaction.expiration_time).format('DD/MM/YYYY HH:mm') || '';
        },

        ownedMessage() {
            const tokens = this.transaction && this.transaction.num_of_tokens || 0
            return this.$t('VIRTUAL_BALANCE.YOU_ARE_NOW_OWNED_TOKENS', { value: formatNumberIntl(tokens), value2: tokens === 1 ? '' : 's' })
        },
    },
}
</script>

<style lang="scss" scoped>
.buy-success-form {
    p {
        margin: 0;
        padding: 0;
    }

    .popup {
        background-color: rgba(0, 0, 0, 0.6);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        -webkit-animation: fadein 0.5s;
        -moz-animation: fadein 0.5s;
        -ms-animation: fadein 0.5s;
        -o-animation: fadein 0.5s;
        animation: fadein 0.5s;

        .content-container {
            background-color: white;
            padding: 40px;
            border-radius: 20px;
        }
    }
}

@keyframes fadein {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Firefox < 16 */
@-moz-keyframes fadein {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Safari, Chrome and Opera > 12.1 */
@-webkit-keyframes fadein {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Internet Explorer */
@-ms-keyframes fadein {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Opera < 12.1 */
@-o-keyframes fadein {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}
</style>
