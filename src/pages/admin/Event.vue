<template>
    <div>
        <div class="row">
            <div class="col-12">
                <div class="pb-2">
                    <b-button v-if="canAddEvent" size="sm" id="show-btn" @click="showAddModal()">
                        <b-icon icon="plus-lg"></b-icon>
                        Add New
                    </b-button>
                </div>
                <div slot="raw-content" class="table-responsive">
                    <paper-table :data="events.data" :columns="tableColumns" :htmlColumns="htmlColumns"
                        :show-edit-action="canUpdateEvent" :show-delete-action="canDeleteEvent"
                        @onModalEdit="onModalEdit" @onModalDelete="onModalDelete">
                        <template #theadSearch>
                            <thead class="search">
                                <th width="3%"></th>
                                <th width="5%"></th>
                                <th width="20%">
                                    <b-input v-model="filters.uuid"></b-input>
                                </th>
                                <th width="25%">
                                    <b-input v-model="filters.name"></b-input>
                                </th>
                                <th width="5%">

                                </th>
                                <th width="13%">

                                </th>
                                <th width="13%"></th>
                                <th width="6%"></th>
                                <th v-if="showActions" width="10%"></th>
                            </thead>
                        </template>
                    </paper-table>
                    <b-pagination v-if="events.total" align="right" v-model="events.current_page"
                        :total-rows="events.total" :per-page="events.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
                </div>
            </div>
        </div>
        <modal-add-event :show="showModalAdd" @on-close="hideModalAdd" :event="event" />
        <b-modal v-model="modalDelete.show" header-bg-variant="light" header-text-variant="dark">
            <template #modal-header>
                <h5 class="modal-header-title mb-0">Delete confirm</h5>
                <button id="btn_closeModalDeleteEvent" type="button" class="close font-weight-bold" aria-label="Close"
                    @click="modalDelete.show = false">×</button>
            </template>
            <p>Are you sure you want to delete this event?
            </p>
            <template #modal-footer>
                <b-button variant="danger" size="sm" @click="onDeleteEvent">
                    Delete
                </b-button>
                <b-button variant="primary" size="sm" @click="modalDelete.show = false">
                    Close
                </b-button>
            </template>
        </b-modal>
    </div>
</template>
<script>
import moment from 'moment'
import { PaperTable } from "../../components";
import ModalAddEvent from './modals/ModalAddEvent.vue';
import eventService from "../../services/admin/event.service";
import { userHasPermission, getImage } from "../../helpers/common";
import { USER_PERMISSIONS } from "../../constants/permissions";

const tableColumns = ["Id", "Type", "Uuid", "Name", "Weight", "Start", "End", "Display_Platform", "Active"];

export default {
    components: {
        PaperTable,
        ModalAddEvent,
    },
    data() {
        return {
            events: {},
            tableColumns: [...tableColumns],
            htmlColumns: [3, 7],
            event: null,
            showModalAdd: false,
            modalDelete: {
                show: false,
            },
            filters: {
                name: null,
                uuid: null,
            },
        };
    },
    async mounted() {
        await this.getEvents(1);
    },
    watch: {
        'filters.uuid'() {
            this.searchTimeOut(1);
        },
        'filters.name'() {
            this.searchTimeOut(1);
        },
    },
    methods: {

        async getEvents(page) {
            const filters = {
                ...this.filters,
                page,
            };
            const result = await eventService.getList(filters);
            if (result && result.data) {
                this.events = result;
                this.events.data = this.events.data.map(e => {
                    e.name = `
                    <div>
                        <div class="d-flex flex-row align-items-center">
                            <div class="attestation-text-lang mr-2">EN</div>
                            <p>${e.name_en}</p>
                        </div>
                        <div class="d-flex flex-row align-items-center mt-2">
                            <div class="attestation-text-lang mr-2">ID</div>
                            <p>${e.name_id}</p>
                        </div>
                    </div>
                    `
                    e.active = `
                    <div class="pl-3">
                        <img src="${getImage(e.display ? 'checkbox-checked' : 'checkbox-unchecked')}" alt="" width="16" height="16">
                    </div>
                    `
                    e.start = moment(e.start_time).format('DD/MM/YYYY HH:mm:ss');
                    e.end = moment(e.end_time).format('DD/MM/YYYY HH:mm:ss');
                    e.display_platform = e.platform ? e.platform : 'web & app'
                    return e
                })
            }
        },

        async onChangePage(page) {
            await this.getEvents(page);
        },

        searchTimeOut(page) {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setTimeout(async () => {
                await this.getEvents(page);
            }, 400);
        },

        onModalEdit(data) {
            if (data) {
                this.event = this.events.data.find((e) => e.id === data);
                this.showModalAdd = true;
            }
        },

        onModalDelete({ id }) {
            this.modalDelete = {
                show: true,
                id,
            }
        },

        async onDeleteEvent() {
            const res = await eventService.deleteEvent(this.modalDelete.id)
            if (res) {
                this.modalDelete = {
                    show: false,
                }
                this.getEvents(1)
            }
        },

        showAddModal() {
            this.showModalAdd = true;
        },

        hideModalAdd(success) {
            this.showModalAdd = false;
            this.event = null;
            if (success) {
                this.getEvents(1)
            }
        },
    },

    computed: {
        canAddEvent() {
            return userHasPermission(USER_PERMISSIONS.EVENT.CREATE)
        },

        canUpdateEvent() {
            return userHasPermission(USER_PERMISSIONS.EVENT.UPDATE)
        },

        canDeleteEvent() {
            return userHasPermission(USER_PERMISSIONS.EVENT.DELETE)
        },

        showActions() {
            return this.canUpdateEvent && this.canDeleteEvent
        },
    },
};
</script>
<style></style>