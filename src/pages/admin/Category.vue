<template>
    <div>
        <div class="row">
            <div class="col-12">
                <div class="pb-2">
                    <b-button size="sm" id="show-btn" @click="showAddModal()">
                        <b-icon icon="plus-lg"></b-icon>
                        Add new
                    </b-button>
                </div>
                <div class="table-responsive">
                    <paper-table :data="categories.data" :columns="tableColumns" :showEditAction="true"
                        @onModalDelete="onModalDelete" @onModalEdit="onModalEdit">
                        <template #theadSearch>
                            <thead class="search">
                                <th width="30px"></th>
                                <th>
                                    <b-input v-model="filters.name"></b-input>
                                </th>
                                <th></th>
                            </thead>
                        </template>
                    </paper-table>
                    <b-pagination v-if="categories.total" align="right" v-model="categories.current_page"
                        :total-rows="categories.total" :per-page="categories.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
                </div>
            </div>
        </div>
        <modal-create-category :show="showModalCreate" :category="category" @on-close="onModalClose" />
        <b-modal v-model="modalDelete.show" header-bg-variant="light" header-text-variant="dark">
            <template #modal-header>
                <h5 class="modal-header-title mb-0">Delete confirm</h5>
                <button id="btn_closeModalDeleteCategory" type="button" class="close font-weight-bold" aria-label="Close"
                    @click="modalDelete.show=false">×</button>
            </template>
            <p>Are you sure you want to delete this category?</p>
            <template #modal-footer>
                <b-button variant="danger" size="sm" @click="onDeleteConfig">
                    Delete
                </b-button>
                <b-button variant="primary" size="sm" @click="modalDelete.show=false">
                    Close
                </b-button>
            </template>
        </b-modal>
    </div>
</template>
<script>
import { PaperTable } from "../../components";
import categoryService from "../../services/admin/category.service";
import ModalCreateCategory from "./modals/ModalCreateCategory.vue";

const tableColumns = ["Id", "Name"];

export default {
    components: {
        PaperTable,
        ModalCreateCategory,
    },
    data() {
        return {
            categories: {},
            tableColumns: [...tableColumns],
            filters: {
                name: '',
            },
            showModalCreate: false,
            category: null,
            modalDelete: {
                show: false,
                content: '',
                id: null,
            },
        };
    },
    async mounted() {
        await this.getCategories(1);
    },
    watch: {
        'filters.name'() {
            this.searchTimeOut(1);
        },
    },
    methods: {

        async getCategories(page) {
            const filters = {
                ...this.filters,
                page,
            };
            const result = await categoryService.getCategories(filters);
            if (result && result.data) {
                this.categories = result;
            }
        },

        async onChangePage(page) {
            await this.getCategories(page);
        },

        searchTimeOut(page) {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setTimeout(async () => {
                await this.getCategories(page);
            }, 400);
        },

        showAddModal() {
            this.category = null;
            this.showModalCreate = true;
        },

        onModalClose(success) {
            this.showModalCreate = false;
            if (success) {
                this.getCategories(this.categories.current_page);
            }
        },

        onModalDelete(data) {
            this.modalDelete = data;
        },

        onModalEdit(data) {
            if (data) {
                this.category = this.categories.data.find((e) => e.id === data);
                this.showModalCreate = true;
            }
        },

        async onDeleteConfig() {
            if (this.modalDelete.id) {
                await categoryService.delete(this.modalDelete.id);
                this.modalDelete = {
                    show: false,
                    content: '',
                    id: null,
                };
                await this.getCategories(this.categories.current_page);
            }
        }
    }
};
</script>
<style scoped>

</style>
  