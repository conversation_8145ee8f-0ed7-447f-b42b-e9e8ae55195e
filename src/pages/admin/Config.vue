<template>
    <div>
        <div class="row">
            <div class="col-12">
                <div class="pb-2">
                    <b-button size="sm" id="show-btn" @click="showAddModal()">
                        <b-icon icon="plus-lg"></b-icon>
                        Add new
                    </b-button>
                </div>
                <div class="table-responsive">
                    <paper-table :data="configs.data" :columns="tableColumns" :show-edit-action="true" :show-delete-action="true"
                        @onModalDelete="onModalDelete" @onModalEdit="onModalEdit">
                        <template #theadSearch>
                            <thead class="search">
                                <th width="30px"></th>
                                <th>
                                    <b-input v-model="filters.c_key"></b-input>
                                </th>
                                <th width="10%">
                                    <b-input v-model="filters.c_value"></b-input>
                                </th>
                                <th></th>
                            </thead>
                        </template>
                    </paper-table>
                    <b-pagination v-if="configs.total" align="right" v-model="configs.current_page"
                        :total-rows="configs.total" :per-page="configs.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
                </div>
            </div>
        </div>
        <modal-create-config :show="showModalCreate" :config="config" @on-close="onModalClose" />
        <b-modal v-model="modalDelete.show" header-bg-variant="light" header-text-variant="dark">
            <template #modal-header>
                <h5 class="modal-header-title mb-0">Delete confirm</h5>
                <button id="btn_closeModalDeleteConfig" type="button" class="close font-weight-bold" aria-label="Close"
                    @click="modalDelete.show=false">×</button>
            </template>
            <p>Are you sure you want to delete this config?<strong style="color: red">{{modalDelete.content}}</strong>
            </p>
            <template #modal-footer>
                <b-button variant="danger" size="sm" @click="onDeleteConfig">
                    Delete
                </b-button>
                <b-button variant="primary" size="sm" @click="modalDelete.show=false">
                    Close
                </b-button>
            </template>
        </b-modal>
    </div>
</template>
<script>
import { PaperTable } from "../../components";
import configsService from "../../services/admin/configs.service";
import ModalCreateConfig from "./modals/ModalCreateConfig.vue";

const tableColumns = ["Id", "C_Key", "C_Value", "Description"];

export default {
    components: {
        PaperTable,
        ModalCreateConfig,
    },
    data() {
        return {
            configs: {},
            tableColumns: [...tableColumns],
            filters: {
                c_key: '',
                c_value: '',
            },
            showModalCreate: false,
            config: null,
            modalDelete: {
                show: false,
                content: '',
                id: null,
            },
        };
    },
    async mounted() {
        await this.getConfigs(1);
    },
    watch: {
        'filters.c_key'() {
            this.searchTimeOut(1);
        },
        'filters.c_value'() {
            this.searchTimeOut(1);
        }
    },
    methods: {

        async getConfigs(page) {
            const filters = {
                ...this.filters,
                page,
            };
            const result = await configsService.getList(filters);
            if (result && result.data) {
                this.configs = result;
            }
        },

        async onChangePage(page) {
            await this.getConfigs(page);
        },

        searchTimeOut(page) {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setTimeout(async () => {
                await this.getConfigs(page);
            }, 400);
        },

        showAddModal() {
            this.config = null;
            this.showModalCreate = true;
        },

        onModalClose(success) {
            this.showModalCreate = false;
            if (success) {
                this.getConfigs(this.configs.current_page);
            }
        },

        onModalDelete(data) {
            this.modalDelete = data;
        },

        onModalEdit(data) {
            if (data) {
                this.config = this.configs.data.find((e) => e.id === data);
                this.showModalCreate = true;
            }
        },

        async onDeleteConfig() {
            if (this.modalDelete.id) {
                await configsService.delete(this.modalDelete.id);
                this.modalDelete = {
                    show: false,
                    content: '',
                    id: null,
                };
                await this.getConfigs(this.configs.current_page);
            }
        }
    }
};
</script>
<style scoped>

</style>
  