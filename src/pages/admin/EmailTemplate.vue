<template>
    <div>
        <div class="row">
            <div class="col-12">
                <div class="pb-2">
                    <b-button size="sm" id="show-btn" @click="showAddModal()">
                        <b-icon icon="plus-lg"></b-icon>
                        Add new
                    </b-button>
                </div>
                <div slot="raw-content" class="table-responsive">
                    <paper-table :data="templates.data" :columns="tableColumns" :show-edit-action="true" :show-delete-action="true"
                        @onModalDelete="onModalDelete" @onModalEdit="onModalEdit">
                        <template #theadSearch>
                            <thead class="search">
                                <th width="30px"></th>
                                <th width="20%">
                                    <b-input v-model="filters.alias"></b-input>
                                </th>
                                <th width="60%">
                                    <b-input v-model="filters.fields"></b-input>
                                </th>
                                <th></th>
                            </thead>
                        </template>
                    </paper-table>
                    <b-pagination v-if="templates.total" align="right" v-model="templates.current_page"
                        :total-rows="templates.total" :per-page="templates.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
                </div>
            </div>
        </div>
        <modal-create-template :show="showModalCreate" :template="template" @on-close="onModalClose" />
        <b-modal v-model="modalDelete.show" header-bg-variant="light" header-text-variant="dark">
            <template #modal-header>
                <h5 class="modal-header-title mb-0">Delete confirm</h5>
                <button id="btn_closeModalDeleteEmailTemplate" type="button" class="close font-weight-bold" aria-label="Close"
                    @click="modalDelete.show = false">×</button>
            </template>
            <p>Are you sure you want to delete this template?<strong style="color: red">{{ modalDelete.content
            }}</strong>
            </p>
            <template #modal-footer>
                <b-button variant="danger" size="sm" @click="onDeleteTemplate">
                    Delete
                </b-button>
                <b-button variant="primary" size="sm" @click="modalDelete.show = false">
                    Close
                </b-button>
            </template>
        </b-modal>
    </div>
</template>
<script>
import { PaperTable } from "../../components";
import emailTemplateService from "../../services/admin/emailTemplate.service";
import ModalCreateTemplate from "./modals/ModalCreateTemplate.vue";

const tableColumns = ["Id", "Alias", "Fields"];

export default {
    components: {
        PaperTable,
        ModalCreateTemplate,
    },
    data() {
        return {
            templates: {},
            tableColumns: [...tableColumns],
            filters: {
                alias: '',
                fields: '',
            },
            showModalCreate: false,
            template: null,
            modalDelete: {
                show: false,
                content: '',
                id: null,
            },
        };
    },
    async mounted() {
        await this.getTemplates(1);
    },
    watch: {
        'filters.alias'() {
            this.searchTimeOut(1);
        },
        'filters.fields'() {
            this.searchTimeOut(1);
        }
    },
    methods: {

        async getTemplates(page) {
            const filters = {
                ...this.filters,
                page,
            };
            const result = await emailTemplateService.getList(filters);
            if (result && result.data) {
                this.templates = result;
            }
        },

        async onChangePage(page) {
            await this.getTemplates(page);
        },

        searchTimeOut(page) {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setTimeout(async () => {
                await this.getTemplates(page);
            }, 400);
        },

        showAddModal() {
            this.template = null;
            this.showModalCreate = true;
        },

        onModalClose(success) {
            this.showModalCreate = false;
            if (success) {
                this.getTemplates(this.templates.current_page);
            }
        },

        onModalDelete(data) {
            this.modalDelete = data;
        },

        onModalEdit(data) {
            if (data) {
                this.template = this.templates.data.find((e) => e.id === data);
                this.showModalCreate = true;
            }
        },

        async onDeleteTemplate() {
            if (this.modalDelete.id) {
                await emailTemplateService.delete(this.modalDelete.id);
                this.modalDelete = {
                    show: false,
                    content: '',
                    id: null,
                };
                await this.getTemplates(this.templates.current_page);
            }
        }
    }
};
</script>
<style scoped>

</style>
  