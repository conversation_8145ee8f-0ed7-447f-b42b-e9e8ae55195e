<template>
  <div class="row">
    <div class="col-12">
      <div slot="raw-content" class="table-responsive">
        <div class="d-flex flex-row align-items-center justify-content-between">
          <div class="d-flex flex-row">
            <VueDatePicker class="mb-2" v-model="filters.date" model-type="dd/MM/yyyy" format="dd/MM/yyyy"
                :range="true" style="width: 300px;" :enable-time-picker="false"
                placeholder="Search date range" />
          </div>
          <div class="d-flex flex-row align-items-center my-2">
            Export:
            <download-csv ref="downloadCsv" class="ml-2" :data="dataToExport" :fields="downloadFields"
              :labels="downloadLabels" :name="csvFileName">
              <div></div>
            </download-csv>
            <div class="download-btn" @click="exportCsv">csv
              <b-icon icon="download" scale="0.8" />
            </div>
            <export-excel ref="downloadExcel" class="ml-2" :data="dataToExport" :fields="excelDownloadFields"
              :name="excelFileName">
              <div></div>
            </export-excel>
            <div class="download-btn" @click="exportExcel">xls
              <b-icon icon="download" scale="0.8" />
            </div>
          </div>
        </div>
        <paper-table :data="payments.data" :columns="tableColumns" :show-edit-action="true"
          @onModalEdit="onModalDetail">
          <template #theadSearch>
            <thead class="search">
              <th></th>
              <th>
                <b-input v-model="filters.user_uuid"></b-input>
              </th>
              <th width="35px">
                <b-input v-model="filters.property_id"></b-input>
              </th>
              <th>
                <b-input v-model="filters.property_name"></b-input>
              </th>
              <th width="90px">
                <b-input v-model="filters.external_id"></b-input>
              </th>
              <th width="30px"></th>
              <th></th>
              <th>
                <b-form-select v-model="filters.status">
                  <template #first>
                    <b-form-select-option :value="null">Select status</b-form-select-option>
                  </template>
                  <template v-for="status in statuses">
                    <b-form-select-option :value="status.value">
                      {{ status.title }}
                    </b-form-select-option>
                  </template>
                </b-form-select>
              </th>
              <th></th>
              <th></th>
            </thead>
          </template>
        </paper-table>
        <b-pagination v-if="payments.total" align="right" v-model="payments.current_page" :total-rows="payments.total"
          :per-page="payments.per_page" @change="onChangePage" aria-controls="my-table"></b-pagination>
      </div>

      <modal-payment title="Payment Detail" :show="showModalDetail" :payment="payment"
        @onClose="hideModalDetail"></modal-payment>
    </div>
  </div>
</template>
<script>
import moment from "moment"
import { PaperTable } from "../../components"
import ModalPayment from "./modals/ModalPayment"
import paymentsService from "../../services/payments.service"

const tableColumns = ["Id", "User_Uuid", "Property_id", "Property_name", "External_id", "Num_of_tokens", "Amount", "Status", "Created_At"]

export default {
  components: {
    PaperTable,
    ModalPayment,
  },
  data() {
    return {
      payments: {},
      tableColumns: [...tableColumns],
      filters: {
        date: "",
        user_id: "",
        user_uuid: "",
        property_id: "",
        property_name: "",
        external_id: "",
        status: "",
      },
      showModalDetail: false,
      payment: {},
      statuses: [
        {
          title: 'Pending',
          value: 'PENDING',
        },
        {
          title: 'Paid',
          value: 'PAID',
        },
        {
          title: 'Expired',
          value: 'EXPIRED',
        },
        {
          title: 'Failed',
          value: 'FAILED',
        },
        {
          title: 'Refunded',
          value: 'REFUNDED',
        },
      ],
      downloadFields: ['id', 'user_uuid', 'property_id', 'property_name', 'external_id', 'num_of_tokens', 'amount', 'status', 'created_at'],
      downloadLabels: {
        id: 'ID',
        user_uuid: 'User Uuid',
        property_id: 'Property Id',
        property_name: 'Property Name',
        external_id: 'External Id',
        num_of_tokens: 'Num Of Tokens',
        amount: 'Amount',
        status: 'Status',
        created_at: 'Created At',
      },
      excelDownloadFields: {
        'ID': 'id',
        'User Uuid': 'user_uuid',
        'Property Id': 'property_id',
        'Property Name': 'property_name',
        'External Id': 'external_id',
        'Num Of Tokens': 'num_of_tokens',
        'Amount': 'amount',
        'Status': 'status',
        'Created At': 'created_at',
      },
      dataToExport: [],
    }
  },
  async mounted() {
    await Promise.all([
      this.getPayments(1),
    ])
  },
  watch: {
    async "filters.user_uuid"() {
      this.searchTimeOut(1)
    },
    async "filters.property_id"() {
      this.searchTimeOut(1)
    },
    async "filters.property_name"() {
      this.searchTimeOut(1)
    },
    async "filters.external_id"() {
      this.searchTimeOut(1)
    },
    async "filters.status"() {
      this.searchTimeOut(1)
    },
    'filters.date'(value) {
      if (value && value.length === 2) {
          this.filters.start_date = value[0]
          this.filters.end_date = value[1]
          this.searchTimeOut(1);
      } else {
          this.filters.start_date = ''
          this.filters.end_date = ''
          this.searchTimeOut(1);
      }
      this.dataToExport = []
    },
  },
  methods: {
    async getPayments(page) {
      const filters = {
        ...this.filters,
        page,
      }
      const result = await paymentsService.getPayments(filters)
      if (result && result.data) {
        this.payments = result
      }
    },
    async onChangePage(page) {
      await this.getPayments(page)
    },
    searchTimeOut(page) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        await this.getPayments(page)
      }, 800)
    },

    onModalDetail(data) {
      if (data) {
        this.payment = this.payments.data.find((e) => e.id === data)
        this.showModalDetail = true
      }
    },
    async hideModalDetail(data) {
      this.showModalDetail = data.show
      if (data.show === false) {
        this.payment = {}
      }
    },

    async getExportData(page = 1) {
      const filters = {
        ...this.filters,
        page,
        per_page: this.payments?.total || 1000,
      };
      const result = await paymentsService.getPayments(filters);

      if (result && result.data) {
        this.dataToExport = result.data
        await this.$nextTick()
      }
    },

    async exportCsv() {
      this.dataToExport = []
      await this.getExportData(this.payments?.current_page || 1)
      this.$refs.downloadCsv.generate()
    },

    async exportExcel() {
      this.dataToExport = []
      await this.getExportData(this.payments?.current_page || 1)
      this.$refs.downloadExcel.generate()
    },
  },

  computed: {
    fileName() {
      let date = this.filters.start_date ? `_${this.filters.start_date}` : '';
      if (this.filters.end_date) {
          date = `${date}-${this.filters.end_date}`
      }
      return `export${date}_${moment().format('YYYYMMDDHHmmss')}`;
    },

    csvFileName() {
      return `${this.fileName}.csv`;
    },

    excelFileName() {
      return `${this.fileName}.xls`;
    },
  },
}
</script>
<style lang="scss" scoped>
.download-btn {
    cursor: pointer;
    color: white;
    background-color: black;
    padding: 4px 10px 6px 10px;
    border-radius: 6px;
    &:hover{
        opacity: .7;
    }
}
</style>
