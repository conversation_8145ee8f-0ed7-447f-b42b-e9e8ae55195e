<template>
    <div>
        <div class="row">
            <div class="col-12">
                <div slot="raw-content" class="table-responsive">
                    <paper-table :data="items" :columns="tableColumns" :customActionTitles="customActions"
                        :showStatus="'PARENTAL_KYC_REVIEWED'" @onCustomAction="onClickedCustomAction">
                        <template #theadSearch>
                            <thead class="search">
                                <th width="30px"></th>
                                <th>
                                    <b-input v-model="filters.uuid"></b-input>
                                </th>
                                <th>
                                    <b-input v-model="filters.name"></b-input>
                                </th>
                                <th>
                                    <b-input v-model="filters.email"></b-input>
                                </th>
                                <th>
                                    <b-input v-model="filters.phone"></b-input>
                                </th>
                                <th></th>
                                <th>
                                    <b-form-select v-model="filters.status">
                                        <template #first>
                                            <b-form-select-option :value="null">Select status</b-form-select-option>
                                        </template>
                                        <template v-for="status in statuses">
                                            <b-form-select-option :value="status.value">
                                                {{ status.title }}
                                            </b-form-select-option>
                                        </template>
                                    </b-form-select>
                                </th>
                                <th></th>
                                <th></th>
                            </thead>
                        </template>
                    </paper-table>
                    <b-pagination v-if="users.total" align="right" v-model="users.current_page"
                        :total-rows="users.total" :per-page="users.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
                </div>
            </div>
        </div>
        <modal-update-ktp-passport :show="modalUpdateKtpPassport.show" :userId="modalUpdateKtpPassport.userId"
            :isPreRegistered="true" @on-close="onHideUpdateKtpPassportModal" />
        <modal-verify-selfie ref="modalVerifySelfie" @on-success="onVerifiedSelfie"
            :isPreRegistered="true"></modal-verify-selfie>
    </div>
</template>
<script>
import { PaperTable } from "../../components";
import preRegisteredUserService from "../../services/admin/preRegisteredUser.service";
import ModalVerifySelfie from "./modals/ModalVerifySelfie.vue";
import ModalUpdateKtpPassport from "./modals/ModalUpdateKtpPassport.vue";
import { userHasPermission } from "../../helpers/common";
import { USER_CUSTOM_ACTIONS } from "../../constants/constants";
import { USER_PERMISSIONS } from "../../constants/permissions";

const tableColumns = ["Id", "Uuid", "Name", "Email", "Phone", "Dob", "Display_Status", "Created_At"];

export default {
    components: {
        PaperTable,
        ModalUpdateKtpPassport,
        ModalVerifySelfie,
    },
    data() {
        return {
            users: {},
            items: [],
            tableColumns: [...tableColumns],
            filters: {
                uuid: '',
                name: '',
                email: '',
                phone: '',
                status: '',
            },
            statuses: [
                {
                    title: 'Verify OTP',
                    value: 'VERIFY_OTP',
                },
                {
                    title: 'Update Info',
                    value: 'UPDATE_INFO',
                },
                {
                    title: 'Parental KYC',
                    value: 'PARENTAL_KYC',
                },
                {
                    title: 'Reviewing Parental KYC',
                    value: 'PARENTAL_KYC_REVIEWED',
                },
                {
                    title: 'Parental KYC Success',
                    value: 'PARENTAL_KYC_SUCCESS',
                },
                {
                    title: 'Parental KYC Failed',
                    value: 'PARENTAL_KYC_FAILED',
                },
                {
                    title: 'Create Password',
                    value: 'CREATE_PASSWORD',
                },
                {
                    title: 'Review Info',
                    value: 'REVIEW_INFO',
                },
                {
                    title: 'Completed',
                    value: 'COMPLETED',
                },
            ],
            showModal: false,
            user: null,
            modalUpdateKtpPassport: {
                show: false,
                userId: null,
            },
        };
    },
    async mounted() {
        await this.getUsers(1);
    },
    watch: {
        'filters.uuid'() {
            this.searchTimeOut(1);
        },
        'filters.name'() {
            this.searchTimeOut(1);
        },
        'filters.email'() {
            this.searchTimeOut(1);
        },
        'filters.phone'() {
            this.searchTimeOut(1);
        },
        'filters.status'() {
            this.searchTimeOut(1);
        },
    },
    computed: {
        customActions() {
            let actions = []
            if (userHasPermission(USER_PERMISSIONS.PRE_REGISTERED_USER.UPDATE_INFO)) {
                actions.push(USER_CUSTOM_ACTIONS.VIEW_USER_KTP_PASSPORT)
                actions.push(USER_CUSTOM_ACTIONS.VIEW_USER_VERIFY_SELFIE)
            }

            return actions
        },
    },
    methods: {

        async getUsers(page) {
            const filters = {
                ...this.filters,
                page,
            };
            const result = await preRegisteredUserService.getList(filters);
            if (result && result.data) {
                this.users = result;
                this.items = result.data.map(e => {
                    e.display_status = this.statuses.find(s => s.value === e.status).title
                    return e
                })
            }
        },

        async onChangePage(page) {
            await this.getUsers(page);
        },

        searchTimeOut(page) {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
            this.timer = setTimeout(async () => {
                await this.getUsers(page);
            }, 400);
        },

        onClickedCustomAction(data, key) {
            const user = this.users.data.find((e) => e.id === data);
            if (key === USER_CUSTOM_ACTIONS.VIEW_USER_KTP_PASSPORT.key) {
                this.modalUpdateKtpPassport = {
                    show: true,
                    userId: user.id,
                }
            } else if (key === USER_CUSTOM_ACTIONS.VIEW_USER_VERIFY_SELFIE.key) {
                this.$refs.modalVerifySelfie.showModal(user)
            }
        },

        onHideUpdateKtpPassportModal() {
            this.modalUpdateKtpPassport = {
                show: false,
                userId: null,
            }
        },

        onVerifiedSelfie(user) {
            if (this.users.data) {
                this.users.data = this.users.data.map(e => {
                    if (e.id === user.id) {
                        return user
                    }
                    return e
                })
            }
        },
    }
};
</script>
<style scoped></style>
