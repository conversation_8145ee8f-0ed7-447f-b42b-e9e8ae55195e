<template>
    <b-modal v-model="showModal" id="full-screen-modal" size="huge" ref="modal" :title="title" @show="updateModal"
        header-bg-variant="dark" header-class="modal-header" footer-class="modal-footer" header-text-variant="light"
        @hidden="$emit('on-close')" @ok="handleOk">
        <div class="d-flex justify-content-center">
            <form class="col-10" ref="form" @submit.stop.prevent="handleSubmit">
                <div class="d-flex w-100 mt-3">
                    <b-form-group class="w-100" label="Type" label-for="value-type" :state="validateState('type')">
                        <b-form-select v-model="form.type" :state="validateState('type')" required>
                            <option v-for="type in types" :value="type.value">{{ type.label }}</option>
                        </b-form-select>
                    </b-form-group>
                    <div v-if="isSultanJuraganEvent" class="d-flex w-100 ml-3">
                        <div class="w-100">
                            <b-form-group label="Amount Threshold" label-for="value-threshold"
                                :state="validateState('threshold')">
                                <b-form-input id="value-threshold" type="number" placeholder="" v-model="form.threshold"
                                    :state="validateState('threshold')">
                                </b-form-input>
                            </b-form-group>
                            <p><strong>Sultan: </strong>Total investment {{ '>' }} Rp{{ formatAmount(form.threshold ||
                                0) }}</p>
                            <p><strong>Juragan: </strong>Total investment {{ '<=' }} Rp{{ formatAmount(form.threshold ||
                                0) }}</p>
                        </div>
                        <b-form-group class="ml-3 w-100" label="Display Top Users(Top Scorer View)"
                            label-for="value-display-top-users-2">
                            <b-form-input id="value-display-top-users-2" type="number" placeholder="Default is 10"
                                v-model="form.display_top_scorer_users">
                            </b-form-input>
                        </b-form-group>
                    </div>
                    <b-form-group v-else-if="isReferralEvent" class="ml-3 w-100" label="Min Referees"
                        label-for="value-min-referees">
                        <b-form-input id="value-min-referees" type="number" placeholder="Default is 5"
                            v-model="form.min_referees">
                        </b-form-input>
                    </b-form-group>
                    <div v-else class="w-100"></div>
                </div>
                <div class="d-flex w-100 mt-3">
                    <b-form-group class="w-100" label="Name EN" label-for="value-name-en"
                        :state="validateState('name_en')">
                        <b-form-input id="value-name-en" placeholder="Event Name EN" v-model="form.name_en"
                            :state="validateState('name_en')" required>
                        </b-form-input>
                    </b-form-group>
                    <b-form-group class="w-100 ml-3" label="Name ID" label-for="value-title-id"
                        :state="validateState('name_id')">
                        <b-form-input id="value-name_id" placeholder="Event Name" v-model="form.name_id"
                            :state="validateState('name_id')" required>
                        </b-form-input>
                    </b-form-group>
                </div>
                <div class="d-flex w-100 mt-3">
                    <div class="w-100">
                        <p>Start Time</p>
                        <VueDatePicker
                            :class="[{ 'input-valid': validStartTime === true }, { 'input-normal': validStartTime === null }, { 'input-invalid': validStartTime === false }]"
                            v-model="form.start_time" :min-date="minDate" format="yyyy-MM-dd HH:mm:ss"
                            model-type="yyyy-MM-dd HH:mm:ss" placeholder="Select Start Time" />
                    </div>

                    <div class="w-100 ml-3">
                        <p class="">End Time</p>
                        <VueDatePicker
                            :class="[{ 'input-valid': validEndTime === true }, { 'input-normal': validEndTime === null }, { 'input-invalid': validEndTime === false }]"
                            v-model="form.end_time" :min-date="minDate" format="yyyy-MM-dd HH:mm:ss"
                            model-type="yyyy-MM-dd HH:mm:ss" placeholder="Select End Time" />
                    </div>

                </div>

                <div class="d-flex w-100 mt-3">
                    <b-form-group class="w-100" label="Properties" label-for="key-properties">
                        <multiselect id="key-properties" v-model="form.property_ids" :options="properties"
                            :multiple="true" :close-on-select="false" :clear-on-select="true" :preserve-search="true"
                            placeholder="Select properties" label="property" :custom-label="customLabel" track-by="id"
                            :preselect-first="false" :allowEmpty="true">
                            <template v-slot:selection slot-scope="{ values, search, isOpen }"><span
                                    class="multiselect__single" v-if="values && values.length && !isOpen">{{
                                        values.length
                                    }} properties
                                    selected</span></template>
                        </multiselect>
                    </b-form-group>
                    <b-form-group class="ml-3 w-100" label="Weight" label-for="value-weight">
                        <b-form-input id="value-weight" type="number" placeholder="Default is 0" v-model="form.weight">
                        </b-form-input>
                    </b-form-group>
                    <b-form-group class="ml-3 w-100" label="Display Top Users" label-for="value-display-top-users">
                        <b-form-input id="value-display-top-users" type="number" placeholder="Default is 50"
                            v-model="form.display_top_users">
                        </b-form-input>
                    </b-form-group>
                    <b-form-group class="ml-3 w-100" label="No. of Winners" label-for="value-no-of-winners">
                        <b-form-input id="value-no-of-winners" type="number" placeholder="Default is 3"
                            v-model="form.no_of_winners">
                        </b-form-input>
                    </b-form-group>
                </div>
                <!-- Banner EN / ID -->
                <div class="d-flex w-100 mt-3">
                    <div class="w-100">
                        <p>Banner EN</p>
                        <VueFileAgent :class="{ 'vue-file-agent-error': fileErrors.images }" v-model="images"
                            v-model:rawModelValue="images" :theme="'grid'" :multiple="false" :meta="false"
                            accept="image/*" :maxSize="'5MB'" :maxFiles="1" :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('images', $event)"
                            @select="handleFilesSelected('images', $event)" required>
                        </VueFileAgent>
                    </div>
                    <div class="w-100 ml-3">
                        <p class="">Banner ID</p>
                        <VueFileAgent v-model="idImages" v-model:rawModelValue="idImages" :theme="'grid'"
                            :multiple="false" :meta="false" accept="image/*" :maxSize="'5MB'" :maxFiles="1"
                            :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('idImages', $event)">
                        </VueFileAgent>
                    </div>
                </div>

                <!-- Banner Wide EN / ID -->
                <div class="d-flex w-100 mt-3">
                    <div class="w-100">
                        <p>Banner Wide EN</p>
                        <VueFileAgent :class="{ 'vue-file-agent-error': fileErrors.imageWides }" v-model="imageWides"
                            v-model:rawModelValue="imageWides" :theme="'grid'" :multiple="false" :meta="false"
                            accept="image/*" :maxSize="'5MB'" :maxFiles="1" :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('imageWides', $event)"
                            @select="handleFilesSelected('imageWides', $event)" required>
                        </VueFileAgent>
                    </div>
                    <div class="w-100 ml-3">
                        <p class="">Banner Wide ID</p>
                        <VueFileAgent v-model="idImageWides" v-model:rawModelValue="idImageWides" :theme="'grid'"
                            :multiple="false" :meta="false" accept="image/*" :maxSize="'5MB'" :maxFiles="1"
                            :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('idImageWides', $event)">
                        </VueFileAgent>
                    </div>
                </div>
                <!-- Banner Detail EN / ID -->
                <div class="d-flex w-100 mt-3">
                    <div class="w-100">
                        <p>Detail Banner EN</p>
                        <VueFileAgent :class="{ 'vue-file-agent-error': fileErrors.imageDetails }"
                            v-model="imageDetails" v-model:rawModelValue="imageDetails" :theme="'grid'"
                            :multiple="false" :meta="false" accept="image/*" :maxSize="'5MB'" :maxFiles="1"
                            :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('imageDetails', $event)"
                            @select="handleFilesSelected('imageDetails', $event)" required>
                        </VueFileAgent>
                    </div>
                    <div class="w-100 ml-3">
                        <p class="">Detail Banner ID</p>
                        <VueFileAgent v-model="idImageDetails" v-model:rawModelValue="idImageDetails" :theme="'grid'"
                            :multiple="false" :meta="false" accept="image/*" :maxSize="'5MB'" :maxFiles="1"
                            :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('idImageDetails', $event)">
                        </VueFileAgent>
                    </div>
                </div>

                <!-- Background Image Detail Web / App -->
                <div class="d-flex w-100 mt-3">
                    <div class="w-100">
                        <p>Background Detail Image Web</p>
                        <VueFileAgent :class="{ 'vue-file-agent-error': fileErrors.imageDetails }"
                            v-model="bgImageDetailWeb" v-model:rawModelValue="bgImageDetailWeb" :theme="'grid'"
                            :multiple="false" :meta="false" accept="image/*" :maxSize="'5MB'" :maxFiles="1"
                            :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('bgImageDetailWeb', $event)">
                        </VueFileAgent>
                    </div>
                    <div class="w-100 ml-3">
                        <p class="">Background Detail Image App</p>
                        <VueFileAgent v-model="bgImageDetailApp" v-model:rawModelValue="bgImageDetailApp"
                            :theme="'grid'" :multiple="false" :meta="false" accept="image/*" :maxSize="'5MB'"
                            :maxFiles="1" :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('bgImageDetailApp', $event)">
                        </VueFileAgent>
                    </div>
                </div>

                <!-- Top Score Leaderboard Image -->
                <div class="d-flex w-100 mt-3">
                    <div class="w-100">
                        <p>Top Leaderboard Score Image EN</p>
                        <VueFileAgent v-model="imageTopScore" v-model:rawModelValue="imageTopScore" :theme="'grid'"
                            :multiple="false" :meta="false" accept="image/*" :maxSize="'5MB'" :maxFiles="1"
                            :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('imageTopScore', $event)">
                        </VueFileAgent>
                    </div>
                    <div class="w-100 ml-3">
                        <p class="">Top Leaderboard Score Image ID</p>
                        <VueFileAgent v-model="idImageTopScore" v-model:rawModelValue="idImageTopScore" :theme="'grid'"
                            :multiple="false" :meta="false" accept="image/*" :maxSize="'5MB'" :maxFiles="1"
                            :helpText="'Choose image file'" :errorText="{
                                type: 'Invalid file type. image/* are allowed',
                                size: 'Files should not exceed 5MB in size'
                            }" @beforedelete="handleBeforeDelete('idImageTopScore', $event)">
                        </VueFileAgent>
                    </div>
                </div>

                <div class="d-flex w-100 mt-3">
                    <b-form-group class="w-100" label="Auto Hide After (Days)" label-for="value-auto-hide-after">
                        <b-form-input id="value-auto-hide-after" type="number" placeholder="Default is 7 days"
                            v-model="form.auto_hide_after">
                        </b-form-input>
                    </b-form-group>

                    <b-form-group class="w-100 ml-3" label="Min Purchase Tokens" label-for="value-min-purchase-tokens">
                        <b-form-input id="value-min-purchase-tokens" placeholder="Default is 50" type="number"
                            v-model="form.min_purchase_tokens">
                        </b-form-input>
                    </b-form-group>
                </div>

                <div class="d-flex w-100 mt-3">
                    <b-form-group class="w-100" label="Display Platform" label-for="value-input">
                        <b-form-radio v-model="form.platform" :value="null" class="mt-2">All</b-form-radio>
                        <b-form-radio v-model="form.platform" value="web" class="mt-2">Web only</b-form-radio>
                        <b-form-radio v-model="form.platform" value="mobile" class="mt-2">Mobile only</b-form-radio>
                    </b-form-group>
                    <div class="w-100 ml-3"></div>
                </div>

                <div class="d-flex w-100 mt-3">
                    <div class="w-100">
                        <label>Rules EN</label>
                        <editor :key="editorKey" :cloud-channel="tinyMCECloudChannel" id="editor-rules-en"
                            :api-key="tinyCloudKey" :init="editor" :initial-value="form.rules_en"
                            model-events="change keydown blur focus paste" v-model="form.rules_en" />
                    </div>
                    <div class="w-100 ml-3">
                        <label>Rules ID</label>
                        <editor :key="editorKey" :cloud-channel="tinyMCECloudChannel" id="editor-rules-id"
                            :api-key="tinyCloudKey" :init="editor" :initial-value="form.rules_id"
                            model-events="change keydown blur focus paste" v-model="form.rules_id" />
                    </div>
                </div>

                <b-form-checkbox v-model="form.display" class="mt-3">
                    Display
                </b-form-checkbox>
            </form>
        </div>
    </b-modal>
</template>

<script>

import { useVuelidate } from '@vuelidate/core'
import Editor from "@anhoang/tinymce-vue"
import { required, minValue } from '@vuelidate/validators'
import { getBase64, formatNumberIntl } from "@/helpers/common"
import propertiesService from "../../../services/properties.service"
import eventService from '../../../services/admin/event.service'

export default {
    components: {
        Editor
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        event: {
            type: Object,
            default: null,
        },
    },
    validations() {
        let v = {
            name_en: {
                required,
            },
            name_id: {
                required,
            },
            type: {
                required,
            },
        }
        if (this.form.type === 'SULTAN_JURAGAN') {
            v.threshold = {
                required,
                minValue: minValue(1),
            }
        }
        return {
            form: v,
        }
    },
    emits: ['on-close'],
    setup() {
        return {
            v$: useVuelidate()
        }
    },
    data() {
        return {
            form: {
                type: 'STANDARD',
            },
            images: [],
            imageWides: [],
            imageDetails: [],
            imageTopScore: [],
            idImages: [],
            idImageWides: [],
            idImageDetails: [],
            bgImageDetailWeb: [],
            bgImageDetailApp: [],
            idImageTopScore: [],
            showModal: false,
            fileErrors: {
                images: false,
                imageWides: false,
                imageDetails: false,
            },
            properties: [],
            validStartTime: null,
            validEndTime: null,
            editorKey: 0,
            tinyMCECloudChannel: '4',
            tinyCloudKey: process.env.VUE_APP_TINYMCE_API_KEY,
            showError: false,
            editor: {
                height: 500,
                menubar: true,
                plugins: [
                    "advlist autolink lists link image charmap print preview anchor textcolor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount"
                ],
                toolbar:
                    "undo redo | formatselect | bold italic backcolor forecolor | \
              alignleft aligncenter alignright alignjustify | \
              bullist numlist outdent indent | removeformat | help",
            },
            types: [
                {
                    label: 'Standard Event',
                    value: 'STANDARD',
                },
                {
                    label: 'Sultan & Juragan',
                    value: 'SULTAN_JURAGAN',
                },
                {
                    label: 'Referral',
                    value: 'REFERRAL',
                }
            ],
        }
    },

    async mounted() {
        this.getProperties(1)
    },

    watch: {
        show(value) {
            this.showModal = value
            if (value) {
                setTimeout(() => {
                    this.editorKey += 1
                }, 1000)
            }
        },

        'form.start_time'(value) {
            if (value) {
                this.validStartTime = true
            } else {
                this.validStartTime = false
            }
        },

        'form.end_time'(value) {
            if (value) {
                this.validEndTime = true
            } else {
                this.validEndTime = false
            }
        },

        'form.type'(value) {
            if (value === 'STANDARD' || value === 'REFERRAL') {
                this.form.threshold = null
                this.form.display_top_scorer_users = null
            }
            if (value === 'REFERRAL') {
                this.form.min_purchase_tokens = null
            }
        },
    },

    methods: {

        validateState(name) {
            if (!this.v$.form[name]) {
                return null
            }
            const { $dirty, $error } = this.v$.form[name];
            return $dirty ? !$error : null;
        },

        getImageArray(name, url) {
            return [
                {
                    id: this.event.id,
                    name: name,
                    tempName: name,
                    url: url,
                    path: url,
                    size: 0,
                    type: 'image/*',
                }
            ]
        },

        updateModal() {
            this.resetModal();
            this.v$.$reset()
            if (this.event) {
                if (this.event.image_en) {
                    const name = this.event.image_en.split("/").pop()
                    this.images = this.getImageArray(name, this.event.image_en)
                }
                if (this.event.image_wide_en) {
                    const name = this.event.image_wide_en.split("/").pop()
                    this.imageWides = this.getImageArray(name, this.event.image_wide_en)
                }
                if (this.event.image_detail_en) {
                    const name = this.event.image_detail_en.split("/").pop()
                    this.imageDetails = this.getImageArray(name, this.event.image_detail_en)
                }

                if (this.event.image_id) {
                    const name = this.event.image_id.split("/").pop()
                    this.idImages = this.getImageArray(name, this.event.image_id)
                }
                if (this.event.image_wide_id) {
                    const name = this.event.image_wide_id.split("/").pop()
                    this.idImageWides = this.getImageArray(name, this.event.image_wide_id)
                }
                if (this.event.image_detail_id) {
                    const name = this.event.image_detail_id.split("/").pop()
                    this.idImageDetails = this.getImageArray(name, this.event.image_detail_id)
                }

                if (this.event.bg_image_detail_web) {
                    const name = this.event.bg_image_detail_web.split("/").pop()
                    this.bgImageDetailWeb = this.getImageArray(name, this.event.bg_image_detail_web)
                }

                if (this.event.bg_image_detail_app) {
                    const name = this.event.bg_image_detail_app.split("/").pop()
                    this.bgImageDetailApp = this.getImageArray(name, this.event.bg_image_detail_app)
                }

                if (this.event.image_top_score_en) {
                    const name = this.event.image_top_score_en.split("/").pop()
                    this.imageTopScore = this.getImageArray(name, this.event.image_top_score_en)
                }

                if (this.event.image_top_score_id) {
                    const name = this.event.image_top_score_id.split("/").pop()
                    this.idImageTopScore = this.getImageArray(name, this.event.image_top_score_id)
                }

                this.form.name_en = this.event.name_en
                this.form.name_id = this.event.name_id
                this.form.type = this.event.type
                this.form.weight = this.event.weight
                this.form.start_time = this.event.start_time
                this.form.end_time = this.event.end_time
                this.form.display = this.event.display
                this.form.auto_hide_after = this.event.auto_hide_after
                this.form.min_purchase_tokens = this.event.min_purchase_tokens
                this.form.property_ids = this.properties.filter(e => this.event.property_ids.indexOf(e.id) > -1)
                this.form.rules_en = this.event.rules_en
                this.form.rules_id = this.event.rules_id
                this.form.display_top_users = this.event.display_top_users
                this.form.display_top_scorer_users = this.event.display_top_scorer_users
                this.form.threshold = this.event.threshold
                this.form.no_of_winners = this.event.no_of_winners
                this.form.min_referees = this.event.min_referees
                this.form.platform = this.event.platform
            }
        },

        resetModal() {
            this.form = { display: true, type: 'STANDARD' }

            this.images = []
            this.imageWides = []
            this.imageDetails = []
            this.idImages = []
            this.idImageWides = []
            this.idImageDetails = []
            this.bgImageDetailWeb = []
            this.bgImageDetailApp = []
            this.imageTopScore = []
            this.idImageTopScore = []

            this.fileErrors.images = false
            this.fileErrors.imageWides = false
            this.fileErrors.imageDetails = false
        },

        async handleOk(bvModalEvent) {
            bvModalEvent.preventDefault()
            await this.handleSubmit()
        },

        async handleSubmit() {
            this.v$.form.$touch();

            this.fileErrors.images = !this.images.length
            this.fileErrors.imageWides = !this.imageWides.length
            this.fileErrors.imageDetails = !this.imageDetails.length
            // if any are missing, bail out
            if (this.fileErrors.images || this.fileErrors.imageWides || this.fileErrors.imageDetails) {
                return
            }

            this.validStartTime = this.form.start_time;
            this.validEndTime = this.form.end_time;
            const isFormCorrect = await this.v$.$validate()
            if (!isFormCorrect || !this.validStartTime || !this.validEndTime) {
                return;
            }

            // Banner
            let base64Image = null;
            if (this.images.length && this.images[0].file) {
                base64Image = await getBase64(this.images[0].file)
            }
            let base64ImageId = null;
            if (this.idImages.length && this.idImages[0].file) {
                base64ImageId = await getBase64(this.idImages[0].file)
            }
            // Banner Wide
            let base64ImageWide = null
            if (this.imageWides.length && this.imageWides[0].file) {
                base64ImageWide = await getBase64(this.imageWides[0].file)
            }
            let base64ImageWideId = null
            if (this.idImageWides.length && this.idImageWides[0].file) {
                base64ImageWideId = await getBase64(this.idImageWides[0].file)
            }
            // Banner Detail
            let base64ImageDetail = null
            if (this.imageDetails.length && this.imageDetails[0].file) {
                base64ImageDetail = await getBase64(this.imageDetails[0].file)
            }
            let base64ImageDetailId = null
            if (this.idImageDetails.length && this.idImageDetails[0].file) {
                base64ImageDetailId = await getBase64(this.idImageDetails[0].file)
            }

            let base64BgImageDetailWeb = null
            if (this.bgImageDetailWeb.length && this.bgImageDetailWeb[0].file) {
                base64BgImageDetailWeb = await getBase64(this.bgImageDetailWeb[0].file)
            }

            let base64BgImageDetailApp = null
            if (this.bgImageDetailApp.length && this.bgImageDetailApp[0].file) {
                base64BgImageDetailApp = await getBase64(this.bgImageDetailApp[0].file)
            }
            // Banner
            let base64TopScore = null;
            if (this.imageTopScore.length && this.imageTopScore[0].file) {
                base64TopScore = await getBase64(this.imageTopScore[0].file)
            }
            let base64TopScoreId = null;
            if (this.idImageTopScore.length && this.idImageTopScore[0].file) {
                base64TopScoreId = await getBase64(this.idImageTopScore[0].file)
            }

            let propertyIds = []
            if (this.form.property_ids) {
                propertyIds = this.form.property_ids.map(e => e.id)
            }

            if (this.event) {
                const res = await eventService.updateEvent(this.event.id, {
                    ...this.form,
                    image_en: base64Image,
                    image_id: base64ImageId,
                    image_wide_en: base64ImageWide,
                    image_wide_id: base64ImageWideId,
                    image_detail_en: base64ImageDetail,
                    image_detail_id: base64ImageDetailId,
                    bg_image_detail_web: base64BgImageDetailWeb,
                    bg_image_detail_app: base64BgImageDetailApp,
                    image_top_score_en: base64TopScore,
                    image_top_score_id: base64TopScoreId,
                    property_ids: propertyIds,
                })
                if (res) {
                    this.$emit('on-close', true)
                }
            } else {
                const res = await eventService.createEvent({
                    ...this.form,
                    image_en: base64Image,
                    image_id: base64ImageId,
                    image_wide_en: base64ImageWide,
                    image_wide_id: base64ImageWideId,
                    image_detail_en: base64ImageDetail,
                    image_detail_id: base64ImageDetailId,
                    bg_image_detail_web: base64BgImageDetailWeb,
                    bg_image_detail_app: base64BgImageDetailApp,
                    image_top_score_en: base64TopScore,
                    image_top_score_id: base64TopScoreId,
                    property_ids: propertyIds,
                })
                if (res) {
                    this.$emit('on-close', true)
                }
            }
        },

        handleBeforeDelete(arrayName, fileRecord) {
            const arr = this[arrayName]
            const idx = arr.findIndex(f => f === fileRecord)
            if (idx !== -1) {
                arr.splice(idx, 1)
            }
            // also clear its error flag if you have one
            if (arrayName === 'images') this.fileErrors.images = false
            else if (arrayName === 'imageWides') this.fileErrors.imageWides = false
            else if (arrayName === 'imageDetails') this.fileErrors.imageDetails = false
        },

        async handleFilesSelected(arrayName) {
            if (arrayName === 'images') this.fileErrors.images = false
            else if (arrayName === 'imageWides') this.fileErrors.imageWides = false
            else if (arrayName === 'imageDetails') this.fileErrors.imageDetails = false
        },

        customLabel({ name, email }) {
            return name;
        },

        async getProperties(page) {
            const filters = {
                page: 1,
                per_page: 1000,
            };
            const result = await propertiesService.getList(filters);
            if (result) {
                this.properties = result.data
                if (this.event) {
                    this.form.property_ids = this.properties.filter(e => this.event.property_ids.indexOf(e.id) > -1)
                }
            }
        },

        formatAmount(value) {
            return formatNumberIntl(value)
        }
    },

    computed: {
        title() {
            return this.event != null ? 'Update Event' : 'Add New Event';
        },

        minDate() {
            return null //moment().toDate()
        },

        isReferralEvent() {
            return this.form.type === 'REFERRAL'
        },

        isSultanJuraganEvent() {
            return this.form.type === 'SULTAN_JURAGAN'
        }
    },
}
</script>

<style lang="scss">
.vue-file-agent-error {
    border: red solid 1px !important;
    border-radius: 5px;
}

.input-invalid {
    border: 1px solid #dc3545;
    border-radius: 5px;
}

.input-valid {
    border: 1px solid #28a745;
    border-radius: 5px;
}

.input-normal {
    border: 1px solid #ced4da;
    border-radius: 5px;
}
</style>