<template>
    <b-modal v-model="showModal" id="full-screen-modal" size="huge" header-bg-variant="dark" header-text-variant="light" @hidden="onHidden()">
        <template #modal-header>
            <h5 class="modal-header-title">User Assets</h5>
            <button type="button" class="close font-weight-bold" aria-label="Close" @click="onClose()">×</button>
        </template>
        <div class="container">
            <b-row class="mb-3" align-v="center" v-if="user">
                <b-col class="font-22">
                    <p>Name: {{ user.name }}</p>
                    <p>Email: {{ user.email }}</p>
                    <p>Phone: {{ user.phone }}</p>
                    <p>Joined: {{ userJoinedAt }}</p>
                </b-col>
            </b-row>
            <b-row class="item" align-v="center" v-for="item in assets">
                <b-col cols="3">
                    <img style="width:100%" :src="getImageUrl(item.images[0])" alt="" />
                </b-col>
                <b-col class="ml-3" cols="8">
                    <p class="font-28 font-weight-bold">{{ item.name }}<b-icon class="link-icon"
                            @click="openPropertyDetails(item)" icon="arrow-up-right-square">
                        </b-icon></p>
                    <p>{{ item.metadata.address }}</p>
                    <p class="mt-1">Owned: {{ item.owning_tokens }}/{{ item.total_tokens }} tokens</p>
                    <p>Price per token: IDR{{ getFormattedCurrency(item.price_per_token) }}</p>
                    <p>Average price per token: IDR{{ getFormattedCurrency(item.avg_price_per_token) }}</p>
                </b-col>
            </b-row>
            <p class="text-center font-18" v-if="!assets.length && !isLoading">There are no assets</p>
        </div>
        <template #modal-footer="{ ok, cancel, hide }">
            <b-button size="md" variant="primary" @click="onClose()">
                Close
            </b-button>
        </template>
    </b-modal>
</template>

<script>

import propertiesService from '../../../services/admin/properties.service';
import { formatNumberIntl, urlImage } from '../../../helpers/common';
import moment from 'moment';

export default {
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        user: {
            type: Object,
            default: null,
        },
    },

    data() {
        return {
            showModal: false,
            assets: [],
        };
    },

    emits: ['on-close'],

    mounted() {

    },

    watch: {
        async user(value) {
            if (value) {
                this.assets = []
                this.getAssets(value.id);
            }
        },
        show(value) {
            this.showModal = value;
        }
    },

    methods: {

        async getAssets(id) {
            const res = await propertiesService.getOwnedByUser(id);
            if (res) {
                this.assets = res;
            }
        },

        onHidden: function () {
            this.$emit("on-close")
        },

        onClose: function () {
            this.showModal = false
        },

        getImageUrl(image) {
            return urlImage(image);
        },

        getPercent(tokens) {
            return Math.round((tokens * 100 / this.property.total_tokens) * 10) / 10;
        },

        async openPropertyDetails(property) {
            const route = this.$router.resolve({ name: 'propertyDetail', params: { uuid: property.uuid } });
            window.open(route.href, '_blank');
        },

        getFormattedCurrency(value) {
            return formatNumberIntl(value)
        },
    },

    computed: {
        isLoading() {
            return this.$store.getters.isLoading;
        },

        userJoinedAt() {
            return moment(this.user.created_at).format('DD/MM/YYYY');
        },
    },
}
</script>

<style scoped>
:deep() #full-screen-modal {
    padding: 0 !important;
    background: #f4f3ef;
}

:deep() #full-screen-modal .modal-dialog {
    width: 100%;
    max-width: 100%;
    height: 100vh;
    max-height: 100vh;
    min-height: 100vh;
    margin: 0;
    background: #f4f3ef;
}

:deep() #full-screen-modal .modal-content {
    height: 100%;
    border: none;
    border-radius: 0;
    background: #f4f3ef;
}

:deep() #full-screen-modal .modal-header {
    border-radius: 0;
}

.modal-header-title {
    margin: 0;
    font-weight: bold;
}

.close {
    color: #ffffff;
    opacity: .8;
    padding-bottom: 20px !important;
    /*!* opacity: .5; */
}

.item {
    background-color: white;
    padding: 20px;
}

.link-icon {
    width: 20px;
    height: 20px;
    margin-left: 15px;
    margin-bottom: 5px;
    cursor: pointer;
}
</style>
