<template>
  <b-modal v-model="showModal" id="modal-prevent-closing" ref="modal" title="Create Voucher"
           size="lg" @hidden="$emit('on-close')" @ok="handleOk">
    <form ref="form" @submit.stop.prevent="handleSubmit">
      <!-- Row 1: Voucher Name and Partner Code -->
      <b-row>
        <b-col md="6">
          <b-form-group label="Voucher Name" label-for="name-input" :state="validateState('name')">
            <b-form-input id="name-input" v-model="form.name" :state="validateState('name')" :readonly="isEdit" required></b-form-input>
          </b-form-group>
        </b-col>
        <b-col md="6">
          <b-form-group label="Partner Code" label-for="partner-code-input" :state="validateState('partner_code')">
            <b-form-input id="partner-code-input" v-model="form.partner_code" :state="validateState('partner_code')"
                          :readonly="isEdit" required></b-form-input>
          </b-form-group>
        </b-col>
      </b-row>

      <!-- Row 2: Description -->
      <b-row>
        <b-col>
          <b-form-group label="Description" label-for="description-input" :state="validateState('description')">
            <b-form-textarea id="description-input" v-model="form.description" :state="validateState('description')"
                             :readonly="isEdit" required></b-form-textarea>
          </b-form-group>
        </b-col>
      </b-row>

      <!-- Row 3: Voucher Type, Reward Type, Random Length, and Amount Unit -->
      <b-row>
        <b-col md="3">
          <b-form-group label="Output" label-for="type-input" :state="validateState('type')">
            <b-form-select id="type-input" v-model="form.type" :options="types" :state="validateState('type')"
                           @input="onTypeChange" :disabled="isEdit" required></b-form-select>
          </b-form-group>
        </b-col>
        <b-col md="3">
          <b-form-group label="Reward Type" label-for="reward-type-input" :state="validateState('reward_type')">
            <b-form-select id="reward-type-input" v-model="form.reward_type" :options="rewardTypes"
                           :state="validateState('reward_type')" :disabled="isEdit" required></b-form-select>
          </b-form-group>
        </b-col>
        <b-col md="3">
          <b-form-group label="Rnd Length" label-for="random-length-input" :state="validateState('random_length')">
            <b-form-input type="number" id="random-length-input" v-model="form.random_length"
                          :state="validateState('random_length')" :readonly="isEdit" required></b-form-input>
          </b-form-group>
        </b-col>
        <b-col md="3">
          <b-form-group label="Amount Unit" label-for="amount-unit-input" :state="validateState('reward_amount_unit')">
            <b-form-select id="amount-unit-input" v-model="form.reward_amount_unit" :options="amountUnits"
                           :state="validateState('reward_amount_unit')" :disabled="isEdit" required></b-form-select>
          </b-form-group>
        </b-col>
      </b-row>

      <!-- Row 4: Tags -->
      <b-row class="align-items-center">
        <b-col>
          <div class="tag-input-container" :class="{ 'is-disabled': isEdit }">
            <div class="tag-list">
              <span v-for="(tag, index) in form.tags" :key="index" class="tag">
                {{ tag }}
                <button @click="removeTag(index)" class="tag-remove" :disabled="isEdit">&times;</button>
              </span>
            </div>
            <input type="text" v-model="newTag" @keydown="onKeyDown" placeholder="Add tags (press Comma to add.)" class="tag-input" :disabled="isEdit">
            <div v-html="tagError" class="tag-error-message"></div>
          </div>
        </b-col>
      </b-row>

      <!-- Row 5: Voucher Code Quantity and Voucher Code Usage Limit -->
      <b-row>
        <b-col md="6">
          <b-form-group label="Codes to Generate" label-for="voucher-total-input"
                        :state="validateState('voucher_code_quantity')">
            <b-form-input type="number" id="voucher-total-input" v-model="form.voucher_code_quantity"
                          :state="validateState('voucher_code_quantity')" :readonly="isEdit || !isMultipleType"
                          :value="isMultipleType ? form.voucher_code_quantity : 1" @input="handleInput" required></b-form-input>
          </b-form-group>
        </b-col>
        <b-col md="6">
          <b-form-group label="Voucher Code Usage Limit" label-for="usage-limit-input"
                        :state="validateState('voucher_code_usage_limit')">
            <b-form-input type="number" id="usage-limit-input" v-model="form.voucher_code_usage_limit"
                          :state="validateState('voucher_code_usage_limit')" :readonly="isEdit || isMultipleType"
                          :value="isMultipleType ? 1 : form.voucher_code_usage_limit" @input="handleInput" required></b-form-input>
          </b-form-group>
        </b-col>
      </b-row>

      <!-- Row 6: Reward Amounts -->
      <b-row>
        <b-col md="4">
          <b-form-group label="Reward Amount" label-for="reward-amount-input" :state="validateState('reward_amount')">
            <b-form-input type="number" id="reward-amount-input" v-model="form.reward_amount"
                          :state="validateState('reward_amount')" :readonly="isEdit" required></b-form-input>
          </b-form-group>
        </b-col>
        <b-col md="4">
          <b-form-group label="Max Amount" label-for="reward-max-amount-input"
                        :state="validateState('reward_max_amount')">
            <b-form-input type="number" id="reward-max-amount-input" v-model="form.reward_max_amount"
                          :readonly="isEdit || form.reward_amount_unit === VOUCHER.REWARD_AMOUNT_UNIT_BALANCE"
                          :state="validateState('reward_max_amount')" required></b-form-input>
          </b-form-group>
        </b-col>
        <b-col md="4">
          <b-form-group label="Required Trx Amount" label-for="min-purchase-input"
                        :state="validateState('required_transaction_amount')">
            <b-form-input type="number" id="min-purchase-input" v-model="form.required_transaction_amount"
                          :state="validateState('required_transaction_amount')" :readonly="isEdit" required></b-form-input>
          </b-form-group>
        </b-col>
      </b-row>

      <!-- Row 7: Active From and Active To -->
      <b-row>
        <b-col md="6">
          <b-form-group label="Active From" label-for="active-from-input" :state="validateState('active_from')">
            <VueDatePicker
              :class="[{ 'input-valid': validActiveFrom === true }, { 'input-normal': validActiveFrom === null }, { 'input-invalid': validActiveFrom === false }]"
              v-model="form.active_from" :min-date="null" format="yyyy-MM-dd HH:mm:ss" :disabled="isEdit"
              model-type="yyyy-MM-dd HH:mm:ss" placeholder="Select Active From" />

          </b-form-group>
        </b-col>
        <b-col md="6">
          <b-form-group label="Active To" label-for="active-to-input" :state="validateState('active_to')">
            <VueDatePicker
              :class="[{ 'input-valid': validActiveTo === true }, { 'input-normal': validActiveTo === null }, { 'input-invalid': validActiveTo === false }]"
              v-model="form.active_to" :min-date="null" format="yyyy-MM-dd HH:mm:ss"
              model-type="yyyy-MM-dd HH:mm:ss" placeholder="Select Active To" />
          </b-form-group>
        </b-col>
      </b-row>
    </form>
  </b-modal>
</template>

<script>
import { notify } from '@kyvg/vue3-notification';
import voucherService from '../../../../services/admin/adminVoucher.service'
import { VOUCHER } from "@/constants/constants";

export default {
  props: {
    voucher: {
      type: Object,
      default: null,
    },
    value: {
      // For v-model binding modal visibility
      type: Boolean,
      default: false,
    },
  },
  updateModal() {
    // if (this.voucher != null) {
    //     this.form = {
    //         name: this.category.name,
    //         id: this.category.id,
    //     };
    // } else {
    //     this.resetModal();
    // }
  },

  data() {
    return {
      showModal: this.value,
      form: {
        name: '',
        partner_code: '',
        description: '',
        type: VOUCHER.TYPE_MULTIPLE,
        reward_type: VOUCHER.REWARD_TYPE_DISCOUNT,
        random_length: 1,
        voucher_code_quantity: 1,
        voucher_code_usage_limit: 1,
        active_from: null,
        active_to: null,
        reward_max_amount: 0,
        reward_amount: 0,
        reward_amount_unit: VOUCHER.REWARD_AMOUNT_UNIT_BALANCE,
        required_transaction_amount: 0,
        tags: [],
      },
      newTag: '',
      tagError: '',
      types: [VOUCHER.TYPE_SINGLE, VOUCHER.TYPE_MULTIPLE],
      rewardTypes: [
        VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES,
        // Buy
        VOUCHER.REWARD_TYPE_DISCOUNT,
        VOUCHER.REWARD_TYPE_REDUCE_FEE_BUY,
        VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_BUY,
        VOUCHER.REWARD_TYPE_REDUCE_ANY_FEES_BUY,
        // Sell
        VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_SELL,
        // Swap
        VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_SWAP,
        // Withdrawal
        VOUCHER.REWARD_TYPE_REDUCE_FEE_WITHDRAWAL,
        //VOUCHER.REWARD_TYPE_REDUCE_TRANSACTION_FEE_WITHDRAWAL,
      ],
      amountUnits: [VOUCHER.REWARD_AMOUNT_UNIT_BALANCE, VOUCHER.REWARD_AMOUNT_UNIT_PERCENTAGE],
      validActiveFrom: null,
      validActiveTo: null,
      errors: {},
    };
  },
  emits: ['on-close'],
  watch: {
    // Watch for changes in the `voucher` prop and populate the form
    voucher: {
      immediate: true, // Populate form on component mount
      handler(newVoucher) {
        if (newVoucher) {
          this.form = { ...newVoucher };
        } else {
          this.resetForm();
        }
      },
    },
    // Sync the `value` prop (modal visibility) with `showModal`
    value(newVal) {
      this.showModal = newVal;
    },
    // Emit visibility changes back to the parent
    showModal(newVal) {
      this.$emit('input', newVal);
    },
  },
  computed: {
    VOUCHER() {
      return VOUCHER
    },
    isEdit() {
      return this.voucher != null;
    },
    isMultipleType() {
      return this.form.type === VOUCHER.TYPE_MULTIPLE;
    },
    tagsValue: {
      set(value) {
        this.form.tags = value; // Update form.tags when tagsValue changes
      },
    },
  },
  methods: {
    handleInput(newValue) {
      if (this.form.type === VOUCHER.TYPE_MULTIPLE) {
        this.form.voucher_code_usage_limit = 1; // Ensure the value stays at 1 for 'multiple' type
      } else if (this.form.type === VOUCHER.TYPE_SINGLE) {
        this.form.voucher_code_quantity = 1;
      }
    },
    onTypeChange(newValue) {
      if (newValue === VOUCHER.TYPE_MULTIPLE) {
        this.form.voucher_code_usage_limit = 1;
      } else {
        this.form.voucher_code_quantity = 1;
      }
    },
    validateState(field) {
      return null;
    },
    async isTagExists(name) {
      const filters = {
        name: name,
        exact: 1,
      }
      const result = await voucherService.getTags(filters)
      if (result && result.data) {
        return result.total > 0;
      }
    },
    onKeyDown(event) {
      if (event.key === ',' || event.key === ';' || event.key === 'Tab') {
        event.preventDefault();
        this.addTag(event);
      }
    },
    async addTag() {
      const tag = this.newTag.trim().replace(',', '');
      if (tag && !this.form.tags.includes(tag)) {
        const isTagInDB = await this.isTagExists(tag)
        if (isTagInDB) {
          this.form.tags.push(tag);
          this.tagError = "";
        } else {
          this.tagError = `Tag -- <strong>${tag}</strong> -- is not found in database. Please add it first.`;
        }
      }
      this.newTag = '';
    },
    removeTag(index) {
      this.form.tags.splice(index, 1);
    },
    resetForm() {
      this.form = {
        name: '',
        partner_code: '',
        description: '',
        type: VOUCHER.TYPE_MULTIPLE,
        reward_type: VOUCHER.REWARD_TYPE_DISCOUNT,
        random_length: 1,
        voucher_code_quantity: 1,
        voucher_code_usage_limit: 1,
        active_from: null,
        active_to: null,
        reward_max_amount: 0,
        reward_amount: 0,
        reward_amount_unit: VOUCHER.REWARD_AMOUNT_UNIT_BALANCE,
        required_transaction_amount: 0,
        tags: [],
      }
    },
    async handleOk(bvModalEvent) {
      bvModalEvent.preventDefault()
      await this.handleSubmit()
    },

    async handleSubmit() {
      let res = null
      if (!this.validateAllFields()) {
        // notify();
        notify({
          text: 'Validation errors:' + JSON.stringify(this.errors, null, 2),
          type: "error"
        })
        return;
      }

      // Check if tags are empty
      if (Array.isArray(this.form.tags) && this.form.tags.length === 0) {
        const proceed = window.confirm('The tags are empty. Are you sure you want to proceed?');
        if (!proceed) {
          return; // Stop the process if the user cancels
        }
      }

      if (this.voucher !== null) {
        res = await voucherService.updateVoucher(this.voucher.id, this.form)
      } else {
        res = await voucherService.createVoucher(this.form);
      }
      if (res) {
        this.resetForm();
        this.$emit('on-close', true);
        this.showModal = false;
      }
    },
    getFieldLabel(field) {
      const labels = {
        voucher_code_quantity: 'Voucher Code Quantity',
        voucher_code_usage_limit: 'Voucher Code Usage Limit',
        reward_amount: 'Reward Amount',
        reward_max_amount: 'Reward Max Amount',
        required_transaction_amount: 'Required Trx Amount',
      };
      return labels[field] || field;
    },
    validateField(field) {
      switch (field) {
        case 'name':
          if (!this.form.name || this.form.name.length < 3) {
            this.errors.name = 'Name must be at least 3 characters long.';
          } else {
            delete this.errors.name;
          }
          break;
        case 'partner_code':
          if (!this.form.partner_code || !/^[a-zA-Z0-9]+$/.test(this.form.partner_code) || this.form.partner_code.length < 2) {
            this.errors.partner_code = 'Partner Code is required and must be alphanumeric and has 2 characters long.';
          } else {
            delete this.errors.partner_code;
          }
          break;
        case 'description':
          if (!this.form.description || this.form.description.length < 5) {
            this.errors.description = 'Description must be at least 10 characters long.';
          } else {
            delete this.errors.description;
          }
          break;
        // case 'tags':
        //   if (!this.form.tags || this.form.tags.length === 0) {
        //     this.errors.tags = 'At least one tag is required.';
        //   } else {
        //     delete this.errors.tags;
        //   }
        //   break;
        case 'random_length':
          if (
            !this.form.random_length ||
            isNaN(this.form.random_length) ||
            this.form.random_length < 1 ||
            this.form.random_length > 10
          ) {
            this.errors.random_length = 'Random Length must be a number between 1 and 10.';
          } else {
            delete this.errors.random_length;
          }
          break;
        case 'voucher_code_quantity':
        case 'voucher_code_usage_limit':
          if (!this.form[field] || isNaN(this.form[field]) || this.form[field] <= 0) {
            this.errors[field] = `${this.getFieldLabel(
              field
            )} must be a positive integer.`;
          } else {
            delete this.errors[field];
          }
          break;
        case 'reward_amount':
        case 'reward_max_amount':
        case 'required_transaction_amount':
          if (
            this.form[field] &&
            (isNaN(this.form[field]) || parseFloat(this.form[field]) < 0)
          ) {
            this.errors[field] = `${this.getFieldLabel(
              field
            )} must be a positive number.`;
          } else {
            delete this.errors[field];
          }
          break;
        case 'active_from':
        case 'active_to':
          if (!this.form.active_from || !this.form.active_to) {
            this.errors.active_from = 'Active From and Active To are required.';
            this.errors.active_to = 'Active From and Active To are required.';
          } else if (new Date(this.form.active_from) >= new Date(this.form.active_to)) {
            this.errors.active_to = 'Active To must be after Active From.';
          } else {
            delete this.errors.active_from;
            delete this.errors.active_to;
          }
          break;
        default:
          break;
      }
    },
    validateAllFields() {
      Object.keys(this.form).forEach(this.validateField);
      return Object.keys(this.errors).length === 0; // Return true if no errors
    },
  },
};
</script>


<style scoped>
.tag-input-container {
  border: 1px solid #ddd;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: white;
}

.tag-input-container.is-disabled {
  background-color: #E8ECEF;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.tag {
  background: #007bff;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  display: inline-flex;
  align-items: center;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  margin-left: 0.5rem;
  cursor: pointer;
  padding: 0 0.2rem;
}

.tag-remove:disabled {
  cursor: not-allowed;
}

.tag-input {
  border: none;
  outline: none;
  width: 100%;
  margin-top: 10px;
  background: transparent;
}

.tag-input:focus {
  outline: none;
}

.tag-error-message {
  color: red;
  font-size: 0.8rem;
  /* font-weight: bold; */
}
</style>
