<template>
    <b-modal v-model="showModal" id="modal-prevent-closing" ref="modal" :title="title" @show="updateModal"
        @hidden="$emit('on-close')" @ok="handleOk">
        <form ref="form" @submit.stop.prevent="handleSubmit">
            <b-form-group label="Tag Name" label-for="key-input" :state="validateState('name')">
                <b-form-input id="key-input" v-model="form.name" :state="validateState('name')" required>
                </b-form-input>
            </b-form-group>
        </form>
    </b-modal>
</template>

<script>
import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import voucherService from '../../../../services/admin/adminVoucher.service'

export default {
    components: {
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        tag: {
            type: Object,
            default: null,
        },
    },
    validations() {
        return {
            form: {
                name: { required }
            }
        }
    },
    emits: ["on-close"],
    setup() {
        return {
            v$: useVuelidate()
        }
    },
    data() {
        return {
            form: {
                name: "",
            },
            showModal: false,
        };
    },

    watch: {
        show(value) {
            this.showModal = value;
        },
    },

    methods: {
        validateState(name) {
            const { $dirty, $error } = this.v$.form[name];
            return $dirty ? !$error : null;
        },

        updateModal() {
            if (this.tag != null) {
                this.form = {
                    name: this.tag.name,
                    id: this.tag.id,
                };
            } else {
                this.resetModal();
            }
        },

        resetModal() {
            this.form = {
                name: "",
            };
        },

        async handleOk(bvModalEvent) {
            bvModalEvent.preventDefault();
            await this.handleSubmit();
        },

        async handleSubmit() {
            let res = null;
            this.v$.form.$touch();
            const isFormCorrect = await this.v$.$validate()
            if (!isFormCorrect) {
                return;
            }
            if (this.tag != null) {
                res = await voucherService.updateTag(this.tag.id, this.form);
            } else {
                res = await voucherService.createTag(this.form);
            }
            if (res) {
                this.resetModal();
                this.$emit('on-close', true);
                this.showModal = false;
            }
        },
    },

    computed: {
        title() {
            return this.tag != null ? "Update Tag" : "Add new Tag";
        },
    },
};
</script>

<style lang="scss" scoped></style>
