<template>
  <div>
    <div class="row">
      <div class="col-12">
        <div class="pb-2">
          <b-button size="sm" id="show-btn" @click="showAddModal()">
            <b-icon icon="plus-lg"></b-icon>
            Add new
          </b-button> &nbsp;
          <b-button size="sm" id="show-btn" @click="goToVoucherCode()">
            <b-icon icon="box-arrow-right"></b-icon>
            Voucher Codes
          </b-button>&nbsp;
          <b-button size="sm" id="show-btn" @click="goToVoucherTag()">
            <b-icon icon="box-arrow-right"></b-icon>
            Tags
          </b-button>&nbsp;
          <b-button size="sm" id="show-btn" @click="goToVoucherRedemptions()">
            <b-icon icon="box-arrow-right"></b-icon>
            Redemptions
          </b-button>
        </div>
        <div class="table-responsive">
          <paper-table :data="vouchers.data" :columns="tableColumns" :showEditAction="true"
                       @onModalEdit="onModalEdit">
            <template #theadSearch>
              <thead class="search">
              <th width="30px"></th>
              <th width="15%">
                <b-input v-model="filters.name"></b-input>
              </th>
              <th>
                <b-input v-model="filters.partner_code"></b-input>
              </th>
              <th>
                <b-input v-model="filters.tags"></b-input>
              </th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              <th></th>
              </thead>
            </template>
          </paper-table>
          <b-pagination v-if="vouchers.total" align="right" v-model="vouchers.current_page"
                        :total-rows="vouchers.total" :per-page="vouchers.per_page" @change="onChangePage"
                        aria-controls="my-table"></b-pagination>
        </div>
      </div>
      <modal-create-voucher :show="showModalCreate" ref="voucherModal" :voucher="voucher" @on-close="onModalClose"/>
    </div>

  </div>
</template>
<script>

import {PaperTable} from "../../../components";
import voucherService from '../../../services/admin/adminVoucher.service';
import ModalCreateVoucher from "./modals/ModalCreateVoucher.vue";

const tableColumns = [
  "Id",
  "Name",
  "Partner_Code",
  "Tag_Data",
  "Reward_Type",
  "Config",
  "Required",
  "Type",
  "Active_From",
  "Active_To",
];

export default {
  components: {
    PaperTable,
    ModalCreateVoucher,
  },

  watch: {
    'filters.name' (newValue) {
      this.searchTimeOut(1)
    },
    'filters.partner_code' (newValue) {
      this.searchTimeOut(1)
    },
    'filters.tags' (newValue) {
      this.searchTimeOut(1)
    },
    'filters.date' (newValue) {
      if (value && value.length === 2) {
        this.filters.active_from = value[0]
        this.filters.active_to = value[1]
        this.searchTimeOut(1);
      } else {
        this.filters.active_from = ''
        this.filters.active_to = ''
        this.searchTimeOut(1);
      }
      this.dataToExport = []
    }
  },

  data () {
    return {
      vouchers: {},
      tableColumns: [...tableColumns],
      filters: {
        name: "",
        tags: "",
        partner_code: "",
        date: null

      },
      showModalCreate: false,
      showModalDetail: false,
      voucher: {},
    }
  },
  async mounted () {
    await this.getVouchers(1)
  },

  methods: {
    async getVouchers (page) {
      const filters = {
        ...this.filters,
        page,
      }
      const result = await voucherService.getVoucher(filters)
      console.error(result.data);
      if (result && result.data) {
        const modifiedData = result.data.map(item => ({
          ...item,
          config: `${item.reward_amount}|${item.reward_amount_unit}|${item.reward_max_amount}`,
          required: item.required_transaction_amount,
          tag_data: item.tags.map(tag => tag.name).join(", "), // Convert tags array to string
          tags: item.tags.map(tag => tag.name),
        }));

        // Assign modified data back to vouchers
        this.vouchers = {
          ...result,
          data: modifiedData,
        };
      }
    },
    async onChangePage (page) {
      await this.getVouchers(page)
    },
    searchTimeOut (page) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        await this.getVouchers(page)
      }, 800)
    },
    goToVoucherCode () {
      this.$router.push({
        name: 'adminVoucherCodes',
      });
    },
    goToVoucherTag () {
      this.$router.push({
        name: 'adminVoucherTags',
      });
    },
    goToVoucherRedemptions () {
      this.$router.push({
        name: 'adminVoucherRedemptions',
      });
    },
    showAddModal () {
      this.voucher = null;

      this.$refs.voucherModal.showModal = true;
    },
    onModalClose (success) {
      this.showModalCreate = false;
      if (success) {
        this.getVouchers(this.vouchers.current_page);
      }
    },
    onModalEdit (data) {
      if (data) {
        this.voucher = this.vouchers.data.find((e) => e.id === data);
        // this.$refs.voucherModal.data["form"] = {...this.voucher};
        this.$refs.voucherModal.showModal = true;
      }
    },
  },
}

</script>
