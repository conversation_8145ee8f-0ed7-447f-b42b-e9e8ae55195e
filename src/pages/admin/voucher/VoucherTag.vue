<template>
    <div>
        <div class="row">
            <div class="col-12">
                <div class="table-responsive">
                    <div class="pb-2">
                        <b-button size="sm" id="show-btn" @click="showAddModal()">
                            <b-icon icon="plus-lg"></b-icon>
                            Add new
                        </b-button> &nbsp;
                        <b-button size="sm" id="show-btn" @click="goToVouchers()">
                            <b-icon icon="box-arrow-right"></b-icon>
                            Back to Vouchers Page
                        </b-button>
                    </div>
                    <paper-table :data="tags.data" :columns="tableColumns" :showEditAction="true"
                        @onModalEdit="onModalEdit">
                        <template #theadSearch>
                            <thead class="search">
                                <th width="10%"></th>
                                <th width="25%">
                                    <b-input v-model="filters.name"></b-input>
                                </th>
                                <th>
                                </th>
                                <th>
                                </th>
                                <!-- <th></th> -->
                                <!-- <th></th> -->

                            </thead>
                        </template>
                    </paper-table>
                    <b-pagination v-if="tags.total" align="right" v-model="tags.current_page" :total-rows="tags.total"
                        :per-page="tags.per_page" @change="onChangePage" aria-controls="my-table"></b-pagination> total
                    data: {{ tags.total
                    }}
                </div>
            </div>
            <modal-create-tag :show="showModalCreate" ref="voucherModal" :tag="tag" @on-close="onModalClose" />
        </div>
    </div>
</template>
<script>

import { PaperTable } from "../../../components";
import voucherService from '../../../services/admin/adminVoucher.service';
import ModalCreateTag from "./modals/ModalCreateVoucherTag.vue";


const tableColumns = [
    "Id",
    "Name",
    "Created_at",
    "Updated_at"
];

export default {
    components: {
        PaperTable,
        ModalCreateTag,
    },

    watch: {
        'filters.name'(newValue) {
            this.searchTimeOut(1)
        },
    },

    data() {
        return {
            tags: {},
            tableColumns: [...tableColumns],
            filters: {
                name: "",
            },
            showModalCreate: false,
            showModalDetail: false,
            tag: {},
        }
    },
    async mounted() {
        await this.getTags(1)
    },

    methods: {
        async getTags(page) {
            const filters = {
                ...this.filters,
                page,
            }
            const result = await voucherService.getTags(filters)
            if (result && result.data) {
                this.tags = result;
            }
        },
        async onChangePage(page) {
            console.log('Page changed:', page);
            await this.getTags(page)
        },
        searchTimeOut(page) {
            if (this.timer) {
                clearTimeout(this.timer)
                this.timer = null
            }
            this.timer = setTimeout(async () => {
                await this.getTags(page)
            }, 800)
        },
        showAddModal() {
            this.tag = null;
            // console.log("it hit this")
            this.$refs.voucherModal.showModal = true;
        },
        goToVouchers() {
            this.$router.push({
                name: 'adminVouchers',
            });
        },
        onModalClose(success) {
            this.showModalCreate = false;
            if (success) {
                this.getTags(this.tags.current_page);
            }
        },
        onModalEdit(data) {
            console.log(data);
            if (data) {

                this.tag = this.tags.data.find((e) => e.id === data);
                this.$refs.voucherModal.showModal = true;
            }
        },
    },

    OnModalDetail(data) {
        if (data) {
            this.tags = this.tags.data.find((e) => e.id === data)
            this.showModalDetail = true
        }
    },
    async hideModalDetail(data) {
        this.showModalDetail = data.show
        if (data.show === false) {
            this.payment = {}
        }
    },

    showAddModal() {
        this.showModalAdd = true;
    },


}

</script>
