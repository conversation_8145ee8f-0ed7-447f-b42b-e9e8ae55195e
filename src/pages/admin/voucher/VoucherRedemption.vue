<template>
  <div>
    <div class="row">
      <div class="col-12">
        <div slot="raw-content" class="table-responsive">
          <!-- Back to Vouchers Page Button and Export Buttons -->
          <div class="d-flex justify-content-between align-items-center pb-2">
            <!-- Back to Vouchers Page Button -->
            <b-button size="sm" id="show-btn" @click="goToVouchers()">
              <b-icon icon="box-arrow-right"></b-icon>
              Back to Vouchers Page
            </b-button>
          </div>
          <paper-table :data="vouchers.data" :columns="tableColumns" :showEditAction="false">
            <template #theadSearch>
              <thead class="search">
              <th width="30px"></th>
              <th>
                <b-input v-model="filters.voucher_code"></b-input>
              </th>
              <th>
                <b-input v-model="filters.voucher_name"></b-input>
              </th>
              <th>
                <b-input v-model="filters.user_email"></b-input>
              </th>
              <th>
                <b-input v-model="filters.transaction_external_id"></b-input>
              </th>
              <th>
              </th>
              <th>
                <b-select v-model="filters.status">
                  <b-select-option v-for="status in statusOptions" :key="status.value" :value="status.value">
                    {{ status.label }}
                  </b-select-option>
                </b-select>
              </th>
              </thead>
            </template>
          </paper-table>

          <!-- Pagination -->
          <b-pagination
            v-if="vouchers.total"
            align="right"
            v-model="vouchers.current_page"
            :total-rows="vouchers.total"
            :per-page="vouchers.per_page"
            @change="onChangePage"
            aria-controls="my-table">
          </b-pagination>
          total data: {{ vouchers.total }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {PaperTable} from "../../../components";
import voucherService from '../../../services/admin/adminVoucher.service';

const tableColumns = [
  "ID",
  "Voucher_Code",
  "Voucher_Name",
  "User_Email",
  "Transaction_External_ID",
  "Reward_Amount",
  "Status",
];

export default {
  components: {
    PaperTable,
  },
  watch: {
    'filters.voucher_code' () {
      this.searchTimeOut(1)
    },
    'filters.voucher_name' () {
      this.searchTimeOut(1)
    },
    'filters.user_email' () {
      this.searchTimeOut(1)
    },
    'filters.transaction_external_id' () {
      this.searchTimeOut(1)
    },
    'filters.status' () {
      this.searchTimeOut(1)
    },
  },
  data () {
    return {
      vouchers: {},
      tableColumns: [...tableColumns],
      filters: {
        voucher_code: "",
        voucher_name: "",
        user_email: "",
        transaction_external_id: "",
        status: "",
      },
      voucher: {},
      statusOptions: [
        { value: null, label: 'Select status..' },
        { value: 'PENDING', label: 'Pending' },
        { value: 'FAILED', label: 'Failed' },
        { value: 'EXPIRED', label: 'Expired' },
        { value: 'REDEEMED', label: 'Redeemed' },
      ],
    }
  },
  async mounted () {
    if (this.statusOptions.length > 0) {
      this.filters.status = this.statusOptions[0].value;
    }
    await this.getVoucherRedemptions(1)
  },

  methods: {
    async getVoucherRedemptions (page) {
      const filters = {
        ...this.filters,
        page,
      }
      const result = await voucherService.getRedemptions(filters)
      if (result && result.data) {
        const modifiedData = result.data.map(item => ({
          ...item,
          voucher_name: item.voucher.name,
          voucher_code: item.voucher_code.code,
          user_email: item.user?.email || '-',
          transaction_external_id: item.transaction?.external_id || '-',
        }));

        // Assign modified data back to vouchers
        this.vouchers = {
          ...result,
          data: modifiedData,
        };
      }
    },
    async onChangePage (page) {
      console.log('Page changed:', page);
      await this.getVoucherRedemptions(page)
    },
    goToVouchers () {
      this.$router.push({
        name: 'adminVouchers',
      });
    },
    searchTimeOut (page) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        await this.getVoucherRedemptions(page)
      }, 800)
    },
  }
}


</script>
