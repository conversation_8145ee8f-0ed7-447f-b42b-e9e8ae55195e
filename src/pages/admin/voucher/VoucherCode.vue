<template>
  <div>
    <div class="row">
      <div class="col-12">
        <div class="table-responsive">
          <!-- Back to Vouchers Page Button and Export Buttons -->
          <div class="d-flex justify-content-between align-items-center pb-2">
            <!-- Back to Vouchers Page Button -->
            <b-button size="sm" id="show-btn" @click="goToVouchers()">
              <b-icon icon="box-arrow-right"></b-icon>
              Back to Vouchers Page
            </b-button>

            <!-- Export Buttons -->
            <div class="d-flex align-items-center">
              <span class="mr-2">Export:</span>
              <download-csv ref="downloadCsv" class="ml-2" :data="dataToExport" :fields="downloadFields"
                            :labels="downloadLabels" :name="csvFileName">
                <div></div>
              </download-csv>
              <div class="ml-2 download-btn" @click="exportCsv">
                csv
                <b-icon icon="download" scale="0.8"></b-icon>
              </div>
              <export-excel ref="downloadExcel" class="ml-2" :data="dataToExport" :fields="excelDownloadFields"
                            :name="excelFileName">
                <div></div>
              </export-excel>
              <div class="ml-2 download-btn" @click="exportExcel">
                xls
                <b-icon icon="download" scale="0.8"></b-icon>
              </div>
            </div>
          </div>

          <!-- Table Content -->
          <paper-table :data="vouchers.data" :columns="tableColumns" :showEditAction="false" @onModalEdit="onModalEdit">
            <template #theadSearch>
              <thead class="search">
              <th width="30px"></th>
              <th width="15%">
                <b-input v-model="filters.code"></b-input>
              </th>
              <th>
                <b-input v-model="filters.voucher_name"></b-input>
              </th>
              <th>
                <b-input v-model="filters.usage"></b-input>
              </th>
              <th></th>
              <th></th>
              <th></th>
              </thead>
            </template>
          </paper-table>

          <!-- Pagination -->
          <b-pagination
            v-if="vouchers.total"
            align="right"
            v-model="vouchers.current_page"
            :total-rows="vouchers.total"
            :per-page="vouchers.per_page"
            @change="onChangePage"
            aria-controls="my-table">
          </b-pagination>
          total data: {{ vouchers.total }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>

import moment, {now} from 'moment';
import {PaperTable} from "../../../components";
import voucherService from '../../../services/admin/adminVoucher.service';

const tableColumns = [
  "Id",
  "Code",
  "Voucher_Name",
  "Usage",
  "Config",
  "Required",
  "Active",
];

export default {
  components: {
    PaperTable,
  },

  watch: {
    'filters.voucher_name' (newValue) {
      this.searchTimeOut(1)
    },
    'filters.code' (newValue) {
      this.searchTimeOut(1)
    },
    'filters.usage' (newValue) {
      this.searchTimeOut(1)
    },
  },

  computed: {
    fileName () {
      const name = `_${this.filters.voucher_name}`
      return `export${name}_voucher_codes`;
    },

    csvFileName () {
      return `${this.fileName}.csv`;
    },

    excelFileName () {
      return `${this.fileName}.xls`;
    },
  },

  data () {
    return {
      vouchers: {},
      tableColumns: [...tableColumns],
      filters: {
        voucher_name: "",
        code: "",
        usage: "",

      },
      voucher: {},
      downloadFields: ['created_at', 'code', 'voucher_name', 'usage',],
      downloadLabels: {
        created_at: 'Created At',
        code: 'Voucher Code',
        voucher_name: 'Voucher Name',
        usage: 'Usage',
      },
      excelDownloadFields: {
        'Created At': 'created_at',
        'Code': 'code',
        'Voucher Name': 'voucher_name',
        'Usage': 'usage',
      },
      dataToExport: [],
    }
  },
  async mounted () {
    await this.getVouchers(1)
  },

  methods: {
    async getVouchers (page) {
      const filters = {
        ...this.filters,
        page,
      }
      const result = await voucherService.getVoucherCodes(filters)
      if (result && result.data) {
        const now = new Date()
        const modifiedData = result.data.map(item => ({
          ...item,
          voucher_name: item.voucher.name,
          config: `${item.voucher.reward_amount}|${item.voucher.reward_amount_unit}|${item.voucher.reward_max_amount}`,
          required: item.voucher.required_transaction_amount,
          active: now > new Date(item.voucher.active_from) && now < new Date(item.voucher.active_to) && item.usage < item.voucher.voucher_code_usage_limit
        }));

        // Assign modified data back to vouchers
        this.vouchers = {
          ...result,
          data: modifiedData,
        };
      }
    },
    async onChangePage (page) {
      await this.getVouchers(page)
    },
    searchTimeOut (page) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        await this.getVouchers(page)
      }, 800)
    },
    goToVouchers () {
      this.$router.push({
        name: 'adminVouchers',
      });
    },

    waitTwoSeconds () {
      return new Promise(resolve => setTimeout(resolve, 2000));
    },

    async getAllData () {
      let allData = [];
      let currentPage = 1;
      let totalPages = 1;
      do {
        const data = await this.getExportData(currentPage);
        allData = allData.concat(data.data);
        totalPages = data.total_pages || 1;
        currentPage++;

      } while (currentPage <= totalPages);
      this.dataToExport = allData;
    },

    async exportCsv () {
      const filters = {
        ...this.filters
      }
      if (typeof filters.voucher_name === 'undefined' || filters.voucher_name === "") {
        throw new Error("Please specify a voucher name before exporting.");
      }

      await this.getAllData();
      this.waitTwoSeconds();
      if (this.$refs.downloadCsv && typeof this.$refs.downloadCsv.generate === "function") {
        this.$refs.downloadCsv.generate();
      } else {
        throw new Error("Export component is not available");
      }
    },

    async exportExcel () {
      const filters = {
        ...this.filters
      }
      if (typeof filters.voucher_name === 'undefined' || filters.voucher_name === "") {
        throw new Error("Please specify a voucher name before exporting.");
      }

      await this.getAllData();

      if (this.$refs.downloadExcel && typeof this.$refs.downloadExcel.generate === "function") {
        this.$refs.downloadExcel.generate();
      } else {
        throw new Error("Export component is not available");
      }
    },

    async getExportData (page) {
      const filters = {
        ...this.filters,
        page,
      };
      const result = await voucherService.getVoucherCodes(filters);
      if (result && result.data) {
        result.data.map(item => {
          item.created_at = moment(item.created_at).format("DD/MM/YYYY");
        })
        const modifiedData = result.data.map(item => ({
          ...item,
          voucher_name: item.voucher.name,
        }));
        return {
          data: modifiedData,
          total_pages: Math.ceil(result.total / result.per_page) || 1,
        };
      }
      return { data: [], total_pages: 1 };
    },
  },
}

</script>
<style lang="scss" scoped>
.download-btn {
  cursor: pointer;
  color: white;
  background-color: black;
  padding: 4px 10px 6px 10px;
  border-radius: 6px;

  &:hover {
    opacity: .7;
  }
}
</style>
