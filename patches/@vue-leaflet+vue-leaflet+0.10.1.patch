diff --git a/node_modules/@vue-leaflet/vue-leaflet/dist/vue-leaflet.es.js b/node_modules/@vue-leaflet/vue-leaflet/dist/vue-leaflet.es.js
index 272b691..c3f9433 100644
--- a/node_modules/@vue-leaflet/vue-leaflet/dist/vue-leaflet.es.js
+++ b/node_modules/@vue-leaflet/vue-leaflet/dist/vue-leaflet.es.js
@@ -1,4 +1,9 @@
-import { watch as He, ref as c, provide as A, inject as O, onUnmounted as pe, h as U, onBeforeUnmount as R, defineComponent as S, onMounted as _, markRaw as j, nextTick as g, render as mt, reactive as vt, computed as oe } from "vue";
+// import { watch as He, ref as c, provide as A, inject as O, onUnmounted as pe, h as U, onBeforeUnmount as R, defineComponent as S, onMounted as _, markRaw as j, nextTick as g, render as mt, reactive as vt, computed as oe } from "vue";
+// line 3 to 6 is a hack to make the vue-leaflet work with vue compat
+import { watch as He, ref as c, provide as A, inject as O, onUnmounted as pe, h as U, onBeforeUnmount as R, defineComponent as defaultDefineComponent, onMounted as _, markRaw as j, nextTick as g, render as mt, reactive as vt, computed as oe } from "vue";
+const S = (obj) => defaultDefineComponent({...obj, compatConfig: {
+  RENDER_FUNCTION: false,
+}})
 const ce = (e, o) => {
   for (const t of Object.keys(o))
     e.on(t, o[t]);
