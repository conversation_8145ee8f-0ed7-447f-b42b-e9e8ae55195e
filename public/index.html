<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="icon" type="image/png" sizes="96x96" href="<%= webpackConfig.output.publicPath %>favicon.png">
<!--  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />-->

  <title>GORO | Property for All</title>
  <!-- Bootstrap core CSS     -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <!-- Facebook OG tags -->
  <meta property="fb:app_id" content="3273337362933112">
  <meta property="og:title" content="GORO | Property for All">
  <meta property="og:description" content="Easy property investing from IDR10,000 and earn up to 10% return per year paid monthly at GORO.">
  <meta property="og:url" content="https://goro.id/">
  <meta property="og:type" content="website">
  <meta property="og:image" content="https://gorostatic.s3.ap-southeast-3.amazonaws.com/meta/og-image-en.png">

  <!--  Fonts and icons     -->
  <link type="text/css" href="https://fonts.googleapis.com/css?family=Muli:400,300&display=swap" rel="stylesheet">
  <link type="text/css" href="https://fonts.googleapis.com/css?family=Montserrat&display=swap" rel="stylesheet">
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

  <!-- Meta Pixel Code -->
  <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '301002555738479');
    // fbq('track', 'PageView');
  </script>
  <noscript><img height="1" width="1" style="display:none"
                 src="https://www.facebook.com/tr?id=301002555738479&ev=PageView&noscript=1"
  /></noscript>
  <!-- End Meta Pixel Code -->
</head>
<body>
<div class="wrapper" id="app">

</div>
<!-- built files will be auto injected -->
<!--  Google Maps Plugin    -->

</body>
</html>
