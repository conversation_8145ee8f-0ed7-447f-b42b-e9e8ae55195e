# renew_before_expiry = 30 days
version = 1.29.0
archive_dir = /etc/letsencrypt/archive/goro.id
cert = /etc/letsencrypt/live/goro.id/cert.pem
privkey = /etc/letsencrypt/live/goro.id/privkey.pem
chain = /etc/letsencrypt/live/goro.id/chain.pem
fullchain = /etc/letsencrypt/live/goro.id/fullchain.pem

# Options used in the renewal process
[renewalparams]
account = 5e512882470618249fb343ba55802813
authenticator = webroot
webroot_path = /var/www/certbot,
server = https://acme-v02.api.letsencrypt.org/directory
key_type = rsa
[[webroot_map]]
goro.id = /var/www/certbot
