server {
  listen 80;
  server_name staging.goro.id www.staging.goro.id;
  server_tokens off;

  if ($host = www.staging.goro.id) {
      return 301 https://staging.goro.id$request_uri;
  }

  if ($host = staging.goro.id) {
      return 301 https://staging.goro.id$request_uri;
  }

  auth_basic "GORO";
  auth_basic_user_file /etc/nginx/staging/.htpasswd;

  location /.well-known/acme-challenge/ {
     auth_basic off;
     root /var/www/certbot;
  }

  include /etc/nginx/extra-conf.d/*.conf;
}

# Enable gzip for pre-compressed static files
gzip_static on;
gzip_vary on;
