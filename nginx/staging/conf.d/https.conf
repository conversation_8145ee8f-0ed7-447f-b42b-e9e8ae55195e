server {
    listen       443 ssl;
    server_name  staging.goro.id www.staging.goro.id;
    server_tokens off;

    ssl_certificate /etc/nginx/ssl/goro.id/staging.pem;
    ssl_certificate_key /etc/nginx/ssl/goro.id/staging-key.pem;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header Content-Security-Policy "frame-ancestors 'self';" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    auth_basic "GORO";
    auth_basic_user_file /etc/nginx/staging/.htpasswd;

    # Redirect www to non-www for staging
    if ($host = www.staging.goro.id) {
        return 301 https://staging.goro.id$request_uri;
    }

    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    location /referrals/portfolio-overview {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass http://webserver/referrals/portfolio-overview;
    }

    location /image-share/portfolio-overview {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass http://webserver/image-share/portfolio-overview;
    }

    location /property/token/ {
          auth_basic off;
          proxy_set_header Host "staginggoro.s3.ap-southeast-3.amazonaws.com";
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass https://staginggoro.s3.ap-southeast-3.amazonaws.com/tokens/;

          # Clear the Authorization header to avoid sending it to S3
          proxy_set_header Authorization "";
    }

    location ~ ^/(?:[a-z]{2}/)?invite/([^/]+)/?$ {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://webserver/referrals/invite/$1;
    }

    # Vue.js static assets (JS, CSS, images, fonts) - serve from Vue container
    location ~* \.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff" always;

        # Critical: Ensure correct MIME types for JavaScript modules
        location ~* \.m?js$ {
            root /usr/share/nginx/html;
            add_header Content-Type "application/javascript; charset=utf-8" always;
        }

        # Ensure correct MIME types for CSS
        location ~* \.css$ {
            root /usr/share/nginx/html;
            add_header Content-Type "text/css; charset=utf-8" always;
        }
    }

    location / {
          root /usr/share/nginx/html;
          index index.html index.htm;
          try_files $uri $uri/ /index.html;
    }

    location /.well-known/apple-app-site-association {
       alias /usr/share/nginx/html/.well-known/;
       index apple-app-site-association.json
       autoindex on;
    }
}
