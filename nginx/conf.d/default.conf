server {
  listen 80;
  server_name goro.id www.goro.id;

  # Redirect www to non-www
  if ($host = www.goro.id) {
      return 301 https://goro.id$request_uri;
  }

  # Redirect HTTP to HTTPS
  if ($host = goro.id) {
      return 301 https://goro.id$request_uri;
  }

  location /.well-known/acme-challenge/ {
     root /var/www/certbot;
  }

  location /login-admin {
    auth_basic "GORO Admin";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    root /usr/share/nginx/html;
    index login-admin;
    try_files $uri $uri/ /index.html =404;
  }

  location /goroadmin {
    auth_basic "GORO Admin";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    root /usr/share/nginx/html;
    index goroadmin;
    try_files $uri $uri/ /index.html =404;
  }

  include /etc/nginx/extra-conf.d/*.conf;
}

# Enable gzip for pre-compressed static files
gzip_static on;
gzip_vary on;
