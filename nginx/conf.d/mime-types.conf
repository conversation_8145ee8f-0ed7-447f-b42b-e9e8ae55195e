# Additional MIME types for modern web applications
# This ensures proper MIME type handling for Vue.js and other modern frameworks

# JavaScript modules and files
location ~* \.m?js$ {
    add_header Content-Type "application/javascript; charset=utf-8" always;
    add_header X-Content-Type-Options "nosniff" always;
}

# CSS files
location ~* \.css$ {
    add_header Content-Type "text/css; charset=utf-8" always;
    add_header X-Content-Type-Options "nosniff" always;
}

# JSON files
location ~* \.json$ {
    add_header Content-Type "application/json; charset=utf-8" always;
    add_header X-Content-Type-Options "nosniff" always;
}

# Web fonts
location ~* \.(woff|woff2)$ {
    add_header Content-Type "font/woff" always;
    add_header Access-Control-Allow-Origin "*" always;
}

location ~* \.ttf$ {
    add_header Content-Type "font/ttf" always;
    add_header Access-Control-Allow-Origin "*" always;
}

location ~* \.eot$ {
    add_header Content-Type "application/vnd.ms-fontobject" always;
    add_header Access-Control-Allow-Origin "*" always;
}

# Images
location ~* \.(png|jpg|jpeg|gif|webp|avif)$ {
    add_header X-Content-Type-Options "nosniff" always;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# SVG files
location ~* \.svg$ {
    add_header Content-Type "image/svg+xml; charset=utf-8" always;
    add_header X-Content-Type-Options "nosniff" always;
}
