server {
    listen       443 ssl;
    root         /var/www/public;
    server_name  goro.id www.goro.id goro.finance www.goro.finance;

    #ssl_certificate /etc/nginx/ssl/live/goro.id/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/live/goro.id/privkey.pem;
    ssl_certificate /etc/nginx/ssl/goro.id/goro-id.pem;
    ssl_certificate_key /etc/nginx/ssl/goro.id/goro-id.key;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header Content-Security-Policy "frame-ancestors 'self';";

    if ($host = www.goro.finance) {
        return 301 https://goro.id$request_uri;
    }

    if ($host = goro.finance) {
        return 301 https://goro.id$request_uri;
    }

    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    location /referrals/portfolio-overview {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass http://webserver/referrals/portfolio-overview;
    }

     location /image-share/portfolio-overview {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass http://webserver/image-share/portfolio-overview;
     }

     location /property/token/ {
          proxy_set_header Host "gorostorage.s3.ap-southeast-3.amazonaws.com";
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass https://gorostorage.s3.ap-southeast-3.amazonaws.com/tokens/;

          # Clear the Authorization header to avoid sending it to S3
          proxy_set_header Authorization "";
     }

    location ~ ^/(en|id)?/?invite/([^/]+)/?$ {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass http://webserver/referrals/invite/$2;
    }

    location / {
          root /usr/share/nginx/html;
          index index.html index.htm;
          try_files $uri $uri/ /index.html =404;
    }

    location /login-admin {
        auth_basic "GORO Admin";
        auth_basic_user_file /etc/nginx/.htpasswd;

        root /usr/share/nginx/html;
        index login-admin;
        try_files $uri $uri/ /index.html =404;
    }

    location /goroadmin {
        auth_basic "GORO Admin";
        auth_basic_user_file /etc/nginx/.htpasswd;

        root /usr/share/nginx/html;
        index goroadmin;
        try_files $uri $uri/ /index.html =404;
    }

    location /.well-known/apple-app-site-association {
       alias /usr/share/nginx/html/.well-known/;
       index apple-app-site-association.json
       autoindex on;
    }

    #location / {
    #    proxy_set_header Host $host;
    #    proxy_set_header X-Real-IP $remote_addr;

    #    proxy_pass http://localhost:80/;
    #}
}
