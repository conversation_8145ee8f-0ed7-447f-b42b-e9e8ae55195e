server {
    listen       443 ssl;
    server_name  goro.id www.goro.id;

    #ssl_certificate /etc/nginx/ssl/live/goro.id/fullchain.pem;
    #ssl_certificate_key /etc/nginx/ssl/live/goro.id/privkey.pem;
    ssl_certificate /etc/nginx/ssl/goro.id/goro-id.pem;
    ssl_certificate_key /etc/nginx/ssl/goro.id/goro-id.key;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header Content-Security-Policy "frame-ancestors 'self';" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Redirect www to non-www
    if ($host = www.goro.id) {
        return 301 https://goro.id$request_uri;
    }

    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    location /referrals/portfolio-overview {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass http://webserver/referrals/portfolio-overview;
    }

     location /image-share/portfolio-overview {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass http://webserver/image-share/portfolio-overview;
     }

     location /property/token/ {
          proxy_set_header Host "gorostorage.s3.ap-southeast-3.amazonaws.com";
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass https://gorostorage.s3.ap-southeast-3.amazonaws.com/tokens/;

          # Clear the Authorization header to avoid sending it to S3
          proxy_set_header Authorization "";
     }

    location ~ ^/(en|id)?/?invite/([^/]+)/?$ {
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_pass http://webserver/referrals/invite/$2;
    }

    # Vue.js static assets (JS, CSS, images, fonts) - serve from Vue container
    location ~* \.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff" always;

        # Critical: Ensure correct MIME types for JavaScript modules
        location ~* \.m?js$ {
            root /usr/share/nginx/html;
            add_header Content-Type "application/javascript; charset=utf-8" always;
        }

        # Ensure correct MIME types for CSS
        location ~* \.css$ {
            root /usr/share/nginx/html;
            add_header Content-Type "text/css; charset=utf-8" always;
        }
    }

    # Vue.js SPA - serve index.html for all non-API routes
    location / {
          root /usr/share/nginx/html;
          index index.html index.htm;
          try_files $uri $uri/ /index.html;
    }

    location /login-admin {
        auth_basic "GORO Admin";
        auth_basic_user_file /etc/nginx/.htpasswd;

        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /goroadmin {
        auth_basic "GORO Admin";
        auth_basic_user_file /etc/nginx/.htpasswd;

        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /.well-known/apple-app-site-association {
       alias /usr/share/nginx/html/.well-known/;
       index apple-app-site-association.json
       autoindex on;
    }

    #location / {
    #    proxy_set_header Host $host;
    #    proxy_set_header X-Real-IP $remote_addr;

    #    proxy_pass http://localhost:80/;
    #}
}
