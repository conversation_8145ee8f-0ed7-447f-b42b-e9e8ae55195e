{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    // ← allow JS files so TS actually has inputs
    "allowJs": true,
  },

  "include": [
    "src/**/*",
    "node_modules/vue-recaptcha/**/*"
  ],

  "exclude": ["node_modules", "dist"]
}
