{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "allowJs": true, "noEmit": true, "isolatedModules": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*", "node_modules/vue-recaptcha/**/*"], "exclude": ["node_modules", "dist"]}