FROM node:18-alpine3.20 as build-stage

WORKDIR /app
COPY package*.json /app/
COPY ./patches /app/patches

# Install Python, make, and g++
RUN apk add --update python3 make g++ \
   && rm -rf /var/cache/apk/*

RUN npm uninstall node-sass && npm install sass
RUN npm install

COPY ./ /app/

RUN npm run build

# add ssg pages
RUN npm run docs:build

# production stage
FROM nginx:stable-alpine as production-stage
COPY --from=build-stage /app/dist/ /usr/share/nginx/html
COPY --from=build-stage /app/docs/.vitepress/dist/ /usr/share/nginx/html/pages

COPY ./nginx.conf /etc/nginx/conf.d/default.conf
COPY ./nginx/.htpasswd /etc/nginx/.htpasswd

RUN mkdir -p /etc/nginx/staging
COPY ./nginx/staging/ /etc/nginx/staging/

ARG DOCKER_BUILD_ENV
ENV DOCKER_BUILD_ENV ${DOCKER_BUILD_ENV}
RUN if [ "$DOCKER_BUILD_ENV" = "staging" ]; then \
      cp -f /etc/nginx/staging/default-nginx.conf /etc/nginx/nginx.conf; \
    fi
