{"name": "demo-app-site", "version": "1.0.0", "private": true, "scripts": {"build": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "e2e": "node test/e2e/runner.js", "lint": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service lint", "lint-fix": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service lint --fix", "serve": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --open", "dev": "npm run serve", "postinstall": "patch-package", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "dependencies": {"@anhoang/tinymce-vue": "^5.1.2-rc", "@anhoang/vue-datepicker-compat": "^5.5.1", "@anhoang/vue-tel-input-compat": "^9.1.5", "@gtm-support/vue-gtm": "^2.2.0", "@jambonn/vue-concise-carousel": "^2.1.29", "@kyvg/vue3-notification": "^2.9.1", "@nhat292/vue-file-agent-next": "^1.0.5", "@tinymce/tinymce-vue": "^5.1.1", "@vee-validate/i18n": "^4.9.6", "@vee-validate/rules": "^4.9.6", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vue/compat": "^3.3.4", "@vueform/slider": "^2.1.7", "@vuelidate/core": "^2.0.2", "@vuelidate/validators": "^2.0.2", "@vuepic/vue-datepicker": "^5.4.0", "apexcharts": "^4.0.0", "axios": "^0.27.2", "bootstrap": "^4.0.0", "bootstrap-vue": "^2.23.0", "browser-id": "^2.0.25", "buffer": "^6.0.3", "chart.js": "^4.4.6", "chartist": "^0.11.0", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "device-detector-js": "^3.0.3", "es6-promise": "^4.2.4", "file-saver": "^2.0.5", "filepond": "^4.30.4", "filepond-plugin-file-validate-type": "^1.2.8", "filepond-plugin-image-preview": "^4.6.11", "firebase": "^10.13.2", "form-wizard-vue3": "^1.1.0", "html2canvas": "^1.4.1", "leaflet": "^1.9.4", "libphonenumber-js": "^1.10.13", "mapbox-gl": "^3.9.1", "moment": "^2.29.4", "numeral": "^2.0.6", "query-string": "^7.1.1", "v-viewer": "^1.6.4", "vee-validate": "^4.9.6", "vue": "^3.3.4", "vue-analytics": "^5.22.1", "vue-chartjs": "^5.2.0", "vue-confetti": "^2.3.0", "vue-datepicker-next": "^1.0.3", "vue-detect-browser": "^1.0.4", "vue-easy-lightbox": "^1.19.0", "vue-excel-export": "^0.1.3", "vue-filepond": "^7.0.3", "vue-i18n": "^9.2.2", "vue-image-chooser": "^0.5.14", "vue-json-csv": "^2.1.0", "vue-json-pretty": "^2.4.0", "vue-loading-overlay": "^6.0.3", "vue-meta": "^3.0.0-alpha.8", "vue-multiselect": "^2.1.6", "vue-number-animation": "^1.1.2", "vue-picture-cropper": "^0.7.0", "vue-recaptcha": "^3.0.0-alpha.2", "vue-router": "^4.2.2", "vue-scroll-picker": "^1.2.3", "vue-select": "^4.0.0-beta.6", "vue-sidebar-menu": "^5.2.8", "vue-slicksort": "^1.2.0", "vue-tel-input": "^9.1.4", "vue3-apexcharts": "^1.7.0", "vue3-carousel": "^0.3.4", "vue3-marquee": "^4.1.0", "vue3-otp-input": "^0.4.1", "vue3-perfect-scrollbar": "^2.0.0", "vue3-popper": "^1.5.0", "vuex": "^4.0.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.5.17", "@vue/eslint-config-prettier": "^5.0.0", "compression-webpack-plugin": "^10.0.0", "node-sass": "^8.0.0", "patch-package": "^7.0.2", "sass": "^1.56.1", "sass-loader": "^10.3.1", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "vitepress": "^1.0.0-rc.20", "webpack": "^5.85.1"}, "description": "A sample admin dashboard based on paper dashboard UI template", "author": "cristian.jora <<EMAIL>>", "engines": {"node": ">= 8.1.4", "npm": ">= 5.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}