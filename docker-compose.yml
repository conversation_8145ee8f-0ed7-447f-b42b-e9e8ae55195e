version: "3.7"

services:
  frontend:
    build:
      context: ./
      args:
        FRONTEND_ENV: ${FRONTEND_ENV-production}
        DOCKER_BUILD_ENV: ${DOCKER_BUILD_ENV:-local}
    container_name: frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./nginx/conf.d/:/etc/nginx/conf.d/:ro
      - ./certbot/www:/var/www/certbot/:ro
      - ./certbot/conf/:/etc/nginx/ssl/:ro
    networks:
      - shared-network
  certbot:
    image: certbot/certbot:latest
    volumes:
      - ./certbot/www/:/var/www/certbot/:rw
      - ./certbot/conf/:/etc/letsencrypt/:rw

networks:
  shared-network:
    external: true
